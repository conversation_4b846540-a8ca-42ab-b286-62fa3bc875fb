---
description: 
globs: 
alwaysApply: true
---
# buz iOS 项目 UI 组件指南

## 概述

buz iOS项目采用了完整的自定义UI组件系统，主要通过`BuzUIKit`库提供统一的UI解决方案。项目UI组件分为两个主要版本：UIKit 1.0（传统组件）和UIKit 2.0（新一代组件），遵循现代化设计系统和组件化架构。

## 组件架构概览

### 整体架构
```
BuzUIKit/
├── UIKit-1.0/           # 传统UI组件（向后兼容）
├── UIKit-2.0/           # 新一代UI组件系统
│   ├── UIControls/      # 基础控件
│   ├── UIComponents/    # 复合组件
│   └── Resources/       # 资源文件
├── UITool/              # UI工具类
└── UIAnimation/         # 动画组件
```

### 设计原则
- **统一性**: 所有组件遵循统一的设计Token和Style系统
- **可配置性**: 支持通过StyleConfig进行样式定制
- **组合性**: 复杂组件通过基础组件组合而成
- **响应式**: 支持RTL语言和动态字体大小
- **可访问性**: 遵循iOS无障碍设计规范

## UIKit 2.0 组件系统（推荐使用）

### 一、基础控件 (UIControls)

#### 1.1 BuzButton - 按钮组件
统一的按钮组件，支持多种样式和布局。

##### 核心特性
- **布局样式**: Regular（常规）、Round（圆形）、Text（文本）、Icon（图标）
- **状态管理**: Normal、Highlighted、Disabled、Loading
- **样式系统**: Primary、Secondary、Tertiary等预设样式
- **尺寸规格**: Large、Medium、Small等尺寸类型

##### 使用示例
```swift
// 创建主要按钮
let layout = BuzButton.RegularStyleLayout(style: .primary, sizeType: .large)
let button = BuzButton(layout: layout)
button.setTitle("确认", for: .normal)

// 创建图标按钮
let iconLayout = BuzButton.IconStyleLayout(style: .primary)
let iconButton = BuzButton(layout: iconLayout)
iconButton.setImage(UIImage.iconFont(...), for: .normal)

// 创建文本按钮
let textLayout = BuzButton.TextStyleLayout(style: .secondary, imagePosition: .left)
let textButton = BuzButton(layout: textLayout)

// 显示加载状态
button.startLoading()
button.endLoading()
```

##### 样式配置
```swift
// 主要样式 (Primary)
- 背景色: .token.color_background_highlight_1_default
- 文字色: .token.color_text_black_primary
- 用途: 主要操作按钮

// 次要样式 (Secondary)  
- 背景色: .token.color_background_5_default
- 文字色: .token.color_text_white_primary
- 用途: 次要操作按钮

// 第三样式 (Tertiary)
- 背景色: 透明
- 文字色: .token.color_text_highlight_default
- 用途: 文本链接类按钮
```

#### 1.2 BuzInputField - 输入框组件
功能完整的文本输入组件。

##### 核心特性
- **输入类型**: Regular（常规）、Search（搜索）
- **状态管理**: 焦点状态、错误状态、禁用状态
- **功能特性**: 自动聚焦、字符限制、清除按钮
- **样式系统**: 统一的边框、背景、文字样式

##### 使用示例
```swift
// 创建常规输入框
let inputField = BuzInputField(type: .regular, 
                              placeholder: "请输入内容",
                              autoFocus: false,
                              maxLength: 100)

// 创建搜索输入框
let searchField = BuzInputField(type: .search,
                               placeholder: "搜索...",
                               autoFocus: true)

// 设置回调
inputField.onTextChanged = { text in
    print("输入内容: \(text)")
}

// 控制焦点
inputField.focus()
inputField.blur()
inputField.clear()
```

#### 1.3 BuzSwitch - 开关组件
自定义开关控件，支持完整的样式定制。

##### 核心特性
- **状态管理**: Normal、Disabled状态
- **动画效果**: 平滑的切换动画
- **样式配置**: 背景色、指示器颜色、尺寸等
- **手势支持**: 点击和滑动切换

##### 使用示例
```swift
// 使用默认配置
let switchControl = BuzSwitch(config: BuzSwitch.defaultConfig())

// 使用新版配置
let newSwitch = BuzSwitch(config: BuzSwitch.newDefault())

// 设置状态变化回调
switchControl.touchUpInsideEventBlock = {
    print("开关状态: \(switchControl.isOn)")
}

// 手动控制切换逻辑
switchControl.shouldSwitch = { newState, callback in
    // 执行异步验证
    validateState(newState) { success in
        callback(success)
    }
}
```

#### 1.4 BuzCheckbox - 复选框组件
自定义复选框控件。

##### 核心特性
- **状态管理**: 未选中、选中、禁用
- **图标系统**: 使用IconFont图标
- **样式配置**: 边框、背景、图标颜色
- **尺寸适配**: 可配置的组件尺寸

##### 使用示例
```swift
// 创建复选框
let checkbox = BuzCheckbox(config: BuzCheckbox.defaultConfig())

// 设置选中状态
checkbox.isChecked = true

// 监听状态变化
checkbox.addTarget(self, action: #selector(checkboxChanged), for: .valueChanged)
```

#### 1.5 BuzRadioButton - 单选框组件
自定义单选框控件。

##### 核心特性
- **圆形设计**: 经典的圆形单选框样式
- **状态管理**: 未选中、选中、禁用
- **组合使用**: 通常与单选组配合使用
- **样式统一**: 与复选框保持一致的设计语言

##### 使用示例
```swift
// 创建单选框
let radioButton = BuzRadioButton(config: BuzRadioButton.defaultConfig())

// 设置选中状态
radioButton.isSelected = true
```

#### 1.6 BuzBadgeView - 徽章组件
用于显示数字徽章和红点提示。

##### 核心特性
- **数字徽章**: 显示具体数字
- **红点模式**: 简单的红点提示
- **自适应尺寸**: 根据内容自动调整大小
- **最小尺寸**: 保证视觉效果的最小尺寸

##### 使用示例
```swift
// 创建数字徽章
let badgeView = BuzBadgeView()
badgeView.setBadge(count: 99)

// 创建红点
let redDotView = BuzRedDotView()
redDotView.isHidden = false
```

### 二、复合组件 (UIComponents)

#### 2.1 BuzNavigation - 导航栏组件
统一的导航栏解决方案。

##### 核心特性
- **布局灵活**: 支持左、中、右三区域布局
- **组件丰富**: 返回按钮、标题、副标题、操作按钮
- **响应式**: 支持RTL语言镜像
- **可扩展**: 支持自定义按钮和视图

##### 组件结构
```swift
BuzNavigation
├── leadingStack       # 左侧区域（通常放返回按钮）
├── centerStack        # 中央区域（标题和副标题）
│   ├── titleLabel     # 主标题
│   └── subtitleLabel  # 副标题
└── trailingStack      # 右侧区域（操作按钮）
```

##### 使用示例
```swift
// 创建导航栏
let navigation = BuzNavigation()

// 设置标题
navigation.setTitle("页面标题")
navigation.setSubtitle("副标题")

// 添加右侧按钮
let rightButton = BuzButton(layout: BuzButton.IconStyleLayout(style: .primary))
rightButton.setImage(UIImage.iconFont(...), for: .normal)
navigation.addTrailingButtons([rightButton])

// 自定义返回按钮图标
navigation.updateBackIcon(BuzUIStyle.R.iconFont.customBack.rawValue)

// 设置返回按钮事件
navigation.backButtonAction = { [weak self] in
    self?.navigationController?.popViewController(animated: true)
}
```

#### 2.2 BuzList - 列表组件系统
用于构建各种列表界面的组件集合。

##### 核心特性
- **状态管理**: Normal、Selected状态
- **尺寸规格**: Large（64pt）、Small（56pt）
- **交互反馈**: 高亮和选中状态
- **组件化**: 可复用的子组件

##### 容器组件
```swift
// 基础容器
BuzList.ContainerView
- 提供统一的内边距和状态管理
- 支持高亮和选中状态

// 协议支持
BuzListCellable          # Cell协议
BuzListContainerable     # Container协议
```

##### 子组件系统
```swift
// 左侧文本栈
BuzList.LeftTextStackView
├── titleLabel    # 主标题
└── descLabel     # 描述文本

// 右侧箭头栈  
BuzList.RightLabelArrowStackView
├── badgeView     # 徽章
├── textLabel     # 右侧文本
└── arrowView     # 箭头图标
```

##### 预设容器类型

**1. ActionContainerView - 操作类型容器**
```swift
BuzList.ActionContainerView
├── iconBgView     # 图标背景（50x50）
├── iconView       # 图标（22x22）
├── titleLabel     # 标题
└── rightStackView # 右侧栈
```

**2. ImageContainerView - 图片类型容器**  
```swift
BuzList.ImageContainerView
├── iconView       # 图片（50x50）
├── leftStackView  # 左侧文本栈
└── rightIconView  # 右侧选中图标
```

**3. InformationContainerView - 信息类型容器**
```swift
BuzList.InformationContainerView
├── iconBgView     # 图标背景（36x36）
├── iconView       # 图标
├── leftStackView  # 左侧文本栈
└── rightIconView  # 右侧图标
```

##### 使用示例
```swift
// 创建操作类型列表项
class ActionListCell: UITableViewCell, BuzListCellable {
    let containerView = BuzList.ActionContainerView(config: styleConfig)
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    private func setupUI() {
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(BuzList.Style.large.size.height)
        }
    }
    
    func configure(title: String, iconFont: String, rightText: String?) {
        containerView.setTitle(title: title)
        containerView.setIconFont(iconFont: iconFont)
        containerView.rightStackView.setText(text: rightText)
    }
}
```

#### 2.3 BuzTable - 表格组件系统
用于设置页面和配置界面的表格组件。

##### 核心特性
- **分组样式**: 支持圆角分组表格
- **位置状态**: Top、Middle、Bottom位置控制
- **状态管理**: Normal、Disabled、Highlighted
- **统一间距**: 20pt标准内边距

##### 容器类型

**1. ContainerView - 基础容器**
```swift
BuzTable.ContainerView
├── 圆角背景       # 16pt圆角
├── 分隔线        # 底部分隔线
└── 位置控制      # Top/Middle/Bottom样式
```

**2. ActionContainerView - 操作容器**
```swift
BuzTable.ActionContainerView  
├── iconBgView     # 图标背景（40x40）
├── iconView       # 图标
├── leftStackView  # 左侧文本栈
└── rightStackView # 右侧栈
```

**3. MenuContainerView - 菜单容器**
```swift
BuzTable.MenuContainerView
├── iconView       # 图标（Medium尺寸）
├── leftStackView  # 左侧文本栈  
└── rightStackView # 右侧栈
```

**4. WheelContainerView - 滚轮容器**
```swift
BuzTable.WheelContainerView
├── leftStackView  # 左侧文本栈
├── wheelView      # 滚轮选择器
└── upDownArrowView # 上下箭头
```

##### 使用示例
```swift
// 创建设置表格
class SettingsTableViewController: UITableViewController {
    
    override func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "ActionCell") as! ActionTableCell
        
        // 设置位置状态
        if indexPath.row == 0 {
            cell.containerView.positions = [.top]
        } else if indexPath.row == tableView.numberOfRows(inSection: indexPath.section) - 1 {
            cell.containerView.positions = [.bottom]
        } else {
            cell.containerView.positions = [.middle]
        }
        
        return cell
    }
}
```

#### 2.4 BuzAvatarView - 头像组件
用户头像显示组件。

##### 核心特性
- **圆角设计**: 完全圆形头像
- **状态指示**: 用户在线状态显示
- **尺寸配置**: 可配置的头像尺寸
- **占位图**: 默认头像占位图

##### 使用示例
```swift
// 创建默认头像
let avatarView = BuzAvatarView()

// 创建自定义尺寸头像
let config = BuzAvatarView.StyleConfig()
config.size = CGSize(width: 60, height: 60)
let largeAvatar = BuzAvatarView(styleConfig: config)

// 设置头像图片
avatarView.imageView.image = userImage
```

#### 2.5 BuzUsernameLabel - 用户名组件
带图标的用户名显示组件。

##### 核心特性
- **文本显示**: 用户名文本
- **图标支持**: 可选的状态图标（如认证标识）
- **自适应**: 根据内容自动调整布局
- **样式统一**: 遵循设计系统的文字样式

##### 使用示例
```swift
// 创建用户名标签
let usernameLabel = BuzUsernameLabel()

// 设置用户名
usernameLabel.setText(text: "用户名")

// 设置认证图标
let verifyIcon = UIImage.iconFont(icon: BuzUIStyle.R.iconFont.verify.rawValue, 
                                 iconColor: .token.color_background_highlight_1_default, 
                                 size: 18)
usernameLabel.setIcon(image: verifyIcon)
```

#### 2.6 BuzPopUp - 弹窗组件
统一的弹窗解决方案。

##### 核心特性
- **类型支持**: Media、Icon等不同类型
- **配置驱动**: 通过Config对象配置行为
- **手势支持**: 支持下滑关闭
- **遮罩控制**: 可配置的背景遮罩

##### 配置选项
```swift
// 图标弹窗配置
BuzIconPopUpConfig(
    showOverlay: true,           # 显示遮罩
    closeOnClickOverlay: true,   # 点击遮罩关闭
    supportGestureDismiss: true  # 支持手势关闭
)
```

##### 使用示例
```swift
// 创建弹窗
let config = BuzIconPopUpConfig()
let popUp = BuzPopUp(with: config)

// 显示弹窗
present(popUp, animated: true)
```

## UIKit 1.0 组件系统（兼容性组件）

### 传统组件概览
UIKit 1.0提供向后兼容的组件，主要用于维护现有功能。

#### 主要组件
- **AlertView** - 传统弹窗组件
- **ActionSheet** - 操作表单组件
- **Toast** - 提示消息组件
- **NavigationBar** - 传统导航栏
- **Button** - 传统按钮组件
- **TextView** - 传统文本视图
- **Label** - 传统标签组件
- **PagView** - PAG动画视图
- **PopupManage** - 弹窗管理器

### AlertView - 弹窗组件
传统的弹窗组件，支持完整的弹窗功能。

```swift
// 创建弹窗
let alertViewController = AlertViewController()
alertViewController.setupView(title: "标题", message: "内容")

// 添加按钮
let confirmAction = AlertAction(title: "确认", style: .default) { _ in
    // 确认操作
}
alertViewController.addAction(confirmAction)

// 显示弹窗
present(alertViewController, animated: true)
```

## 工具组件

### UITool - UI工具类
提供UI开发中常用的工具方法和扩展。

### UIAnimation - 动画组件
提供统一的动画解决方案。

## 资源管理

### 图标字体系统
项目使用IconFont系统管理图标资源。

```swift
// 使用图标字体
let icon = UIImage.iconfont(
    icon: BuzUIStyle.R.iconFont.search.rawValue,
    iconColor: .token.color_text_white_primary,
    size: 24
)

// 支持RTL镜像
let mirroredIcon = icon?.mirroredWhenRTL()
```

### 资源包管理
```swift
// 获取组件库图片资源
let image = UIImage.buzUIKit.image(named: "buz_uikit_table_up_arrow")
```

## 设计Token系统

### 颜色Token
项目使用统一的颜色Token系统。

```swift
// 文本颜色
.token.color_text_white_primary       # 主要文本
.token.color_text_white_secondary     # 次要文本  
.token.color_text_white_tertiary      # 第三级文本

// 背景颜色
.token.color_background_3_default     # 默认背景
.token.color_background_5_default     # 次要背景
.token.color_background_highlight_1_default  # 高亮背景

// 前景颜色
.token.color_foreground_neutral_important_default  # 重要前景
.token.color_foreground_highlight_default          # 高亮前景
```

### 尺寸Token
```swift
// 标准尺寸
.token.small   # 小尺寸
.token.medium  # 中等尺寸  
.token.large   # 大尺寸

// 圆角半径
.radiusToken.small    # 小圆角
.radiusToken.midium   # 中等圆角
.radiusToken.large    # 大圆角
.radiusToken.full(height: 44)  # 完全圆角（高度的一半）
```

### 字体样式
```swift
// 标题样式
.textTitleLarge   # 大标题
.textTitleMedium  # 中等标题
.textTitleSmall   # 小标题

// 正文样式
.textBodyLarge    # 大正文
.textBodyMedium   # 中等正文  
.textBodySmall    # 小正文

// 标签样式
.textLabelSmall   # 小标签
```

## 组件使用规范

### 1. 组件选择指南

#### 优先级原则
1. **优先使用UIKit 2.0组件** - 新功能开发
2. **UIKit 1.0组件维护现状** - 现有功能维护
3. **避免重复造轮子** - 充分利用现有组件

#### 选择决策矩阵
```
功能需求          | 推荐组件           | 备选方案
---------------- | ----------------- | -----------------
按钮交互          | BuzButton         | UIKit 1.0 Button
文本输入          | BuzInputField     | UITextField
开关控制          | BuzSwitch         | UISwitch
列表显示          | BuzList           | UITableView
导航栏           | BuzNavigation     | UIKit 1.0 NavigationBar
弹窗提示          | BuzPopUp          | AlertView
```

### 2. 样式配置原则

#### 配置继承
```swift
// 继承默认配置
var config = BuzButton.RegularStyle.defaultConfig()

// 自定义修改
config.backgroundColors[.normal] = .customColor
config.titleColors[.normal] = .customTextColor

// 应用配置
let button = BuzButton(layout: BuzButton.RegularStyleLayout(config: config))
```

#### 状态管理
```swift
// 统一的状态枚举
BuzButtonState: .normal, .highlighted, .disabled, .loading
BuzList.State: .normal, .selected  
BuzTable.State: .normal, .disabled, .highlighted
```

### 3. 布局约束规范

#### SnapKit集成
所有UI组件都与SnapKit布局库完美集成。

```swift
// 组件布局示例
private func setupConstraints() {
    button.snp.makeConstraints { make in
        make.centerX.equalToSuperview()
        make.top.equalTo(titleLabel.snp.bottom).offset(20)
        make.height.equalTo(44)
        make.leading.trailing.equalToSuperview().inset(20)
    }
}
```

#### 尺寸规范
```swift
// 标准高度
列表行高: 64pt (Large), 56pt (Small)
按钮高度: 44pt (Large), 36pt (Medium), 28pt (Small)  
输入框高度: 44pt
导航栏高度: 44pt + Safe Area

// 标准间距
内边距: 20pt (标准), 16pt (紧凑), 12pt (最小)
组件间距: 16pt (标准), 12pt (紧凑), 8pt (最小)
```

### 4. 性能优化建议

#### 组件复用
```swift
// Cell复用
class ListCell: UITableViewCell {
    static let identifier = "ListCell"
    let containerView = BuzList.ActionContainerView()
    
    // 重置状态
    override func prepareForReuse() {
        super.prepareForReuse()
        containerView.reset()
    }
}
```

#### 懒加载
```swift
// 组件懒加载
private lazy var button: BuzButton = {
    let layout = BuzButton.RegularStyleLayout(style: .primary, sizeType: .large)
    let button = BuzButton(layout: layout)
    return button
}()
```

## 组件扩展指南

### 1. 自定义组件开发

#### 继承现有组件
```swift
// 继承BuzButton创建专用按钮
class CustomActionButton: BuzButton {
    
    override init(layout: any BuzButtonStyleLayoutable) {
        super.init(layout: layout)
        setupCustomStyle()
    }
    
    private func setupCustomStyle() {
        // 自定义样式逻辑
    }
}
```

#### 组合现有组件
```swift
// 组合多个组件创建复合组件
class UserCardView: UIView {
    private let avatarView = BuzAvatarView()
    private let usernameLabel = BuzUsernameLabel()
    private let actionButton = BuzButton(layout: BuzButton.RegularStyleLayout(style: .primary, sizeType: .medium))
    
    // 组合布局逻辑
}
```

### 2. 样式系统扩展

#### 添加新样式
```swift
// 扩展按钮样式
extension BuzButton.RegularStyle {
    static let custom: BuzButton.RegularStyle = .init(rawValue: 100)
    
    func customConfig(sizeType: BuzButton.RegularSizeType) -> BuzButton.RegularStyleConfig {
        // 自定义样式配置
    }
}
```

#### Token系统扩展
```swift
// 扩展颜色Token
extension UIColor {
    static let customPrimary = UIColor(hex: "#FF6B35")
    static let customSecondary = UIColor(hex: "#4ECDC4")
}
```

## 最佳实践

### 1. 组件使用最佳实践

#### 统一的组件初始化
```swift
// ✅ 推荐：使用布局配置初始化
let button = BuzButton(layout: BuzButton.RegularStyleLayout(style: .primary, sizeType: .large))

// ❌ 避免：直接修改属性
button.backgroundColor = .blue  // 破坏样式系统
```

#### 合理的组件组合
```swift
// ✅ 推荐：使用专用容器组件
let cell = BuzList.ActionContainerView()
cell.setTitle(title: "设置")
cell.setIconFont(iconFont: BuzUIStyle.R.iconFont.setting.rawValue)

// ❌ 避免：手动组装布局
let iconView = UIImageView()
let titleLabel = UILabel()
let arrowView = UIImageView()
// 大量布局代码...
```

#### 响应式设计
```swift
// ✅ 支持RTL语言
let image = UIImage.iconFont(...)?.mirroredWhenRTL()

// ✅ 支持动态字体
label.fontStyle = .textBodyMedium  // 自动适配字体大小

// ✅ 支持深色模式
backgroundColor = .token.color_background_3_default  // 自动适配主题
```

### 2. 性能最佳实践

#### 避免重复创建
```swift
// ✅ 推荐：复用组件实例
private let dateFormatter: DateFormatter = {
    let formatter = DateFormatter()
    formatter.dateStyle = .medium
    return formatter
}()

// ✅ 推荐：使用Cell复用
func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
    let cell = tableView.dequeueReusableCell(withIdentifier: "Cell", for: indexPath)
    return cell
}
```

#### 优化图片加载
```swift
// ✅ 推荐：使用IconFont避免图片资源
let icon = UIImage.iconfont(icon: BuzUIStyle.R.iconFont.search.rawValue, ...)

// ✅ 推荐：合理使用图片缓存
imageView.sd_setImage(with: url, placeholderImage: defaultImage)
```

### 3. 维护性最佳实践

#### 组件版本管理
```swift
// 新功能使用UIKit 2.0组件
import BuzUIKit  // 自动包含UIKit 2.0组件

// 维护现有功能时保持UIKit 1.0组件
let alertView = AlertViewController()  // 继续使用现有组件
```

#### 配置管理
```swift
// ✅ 推荐：集中管理样式配置
struct AppTheme {
    static let primaryButtonLayout = BuzButton.RegularStyleLayout(style: .primary, sizeType: .large)
    static let secondaryButtonLayout = BuzButton.RegularStyleLayout(style: .secondary, sizeType: .medium)
}

// 使用统一配置
let button = BuzButton(layout: AppTheme.primaryButtonLayout)
```

#### 文档和注释
```swift
/// 用户信息卡片组件
/// 
/// 用于显示用户基本信息，包括头像、用户名、认证状态等
/// 支持点击交互和状态更新
///
/// - Usage:
///   ```swift
///   let userCard = UserCardView()
///   userCard.configure(user: userInfo)
///   ```
class UserCardView: UIView {
    // 实现...
}
```

## 总结

buz iOS项目的UI组件系统为开发团队提供了完整、统一、可扩展的UI解决方案。通过合理使用这些组件，可以：

1. **提高开发效率** - 减少重复代码，快速构建界面
2. **保证设计一致性** - 统一的样式系统和设计Token
3. **提升用户体验** - 专业的交互效果和无障碍支持
4. **便于团队协作** - 标准化的组件使用规范
5. **降低维护成本** - 集中式的组件管理和版本控制

建议开发团队：
- 新功能开发优先使用UIKit 2.0组件
- 遵循组件使用规范和最佳实践
- 积极参与组件库的改进和扩展
- 保持与设计系统的同步更新