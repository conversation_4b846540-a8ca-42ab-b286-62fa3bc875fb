---
description: 
globs: 
alwaysApply: true
---
# buz iOS 组件开发规范

## 组件方向

## 一、组件定义与架构

### 1.1 组件定义
组件是用于实现具体功能模块或服务的独立单元。在buz项目中，组件独立于业务逻辑，提供可复用的功能模块，通过分离关注点增强代码的可维护性和复用性。

### 1.2 组件分类

#### 基础组件 (Foundation Components)
- **LocalPods/BuzFoundation** - 基础工具库
- **LocalPods/BuzUIKit** - UI组件库
- **LocalPods/BuzNetworking** - 网络通信库
- **Services/Centers/** - 核心服务中心

#### 业务组件 (Business Components)
- **Modules/BuzAuthModule** - 认证模块
- **Modules/BuzChatModule** - 聊天模块
- **Modules/BuzCallModule** - 通话模块
- **Modules/BuzSocialModule** - 社交模块

#### 功能组件 (Feature Components)
- **LocalPods/VoderX** - IM通讯核心
- **LocalPods/LZAudioEngine** - 音频引擎
- **LocalPods/ITNetLibrary** - 网络库

### 1.3 组件职责
1. **可复用功能提供**: 实现具体功能模块，简化业务逻辑实现
2. **关注点分离**: 将功能模块与业务逻辑分离，增强可维护性
3. **独立开发测试**: 每个组件可独立开发、测试和部署
4. **标准化接口**: 定义统一接口和协议，确保组件无缝集成

## 二、组件结构规范

### 2.1 设计原则
1. **单一职责原则**: 每个组件只负责一个功能或职责
2. **明确接口**: 通过明确接口与外界交互，隐藏实现细节
3. **高内聚低耦合**: 组件内功能紧密相关，减少外部依赖
4. **可扩展性**: 设计为易于扩展，便于未来增加新功能
5. **配置管理**: 配置独立于代码，通过配置文件管理
6. **完善文档**: 编写详细文档和注释，说明用途和使用方法
7. **错误处理**: 适当的日志记录和错误处理机制
8. **性能优化**: 避免不必要的资源消耗和延迟
9. **版本管理**: 明确版本号，便于依赖管理
10. **测试驱动**: 先编写测试，确保功能正确性

### 2.2 标准组件结构

#### LocalPods组件结构
```
BuzComponentName/
├── Classes/
│   ├── Interface/                    # 对外接口
│   │   ├── BuzComponentNameProtocol.swift
│   │   └── BuzComponentNameDelegate.swift
│   ├── Implementation/               # 核心实现
│   │   ├── BuzComponentName.swift
│   │   └── BuzComponentNameManager.swift
│   ├── Models/                       # 数据模型
│   │   └── BuzComponentNameModel.swift
│   ├── Views/                        # UI组件
│   │   └── BuzComponentNameView.swift
│   ├── Extensions/                   # 扩展
│   │   └── UIView+BuzComponent.swift
│   ├── Utilities/                    # 工具类
│   │   └── BuzComponentNameUtilities.swift
│   └── Configuration/                # 配置
│       └── BuzComponentNameConfig.swift
├── Assets/                           # 资源文件
│   └── BuzComponentName.bundle
├── Tests/                            # 测试文件
│   └── BuzComponentNameTests.swift
├── Example/                          # 示例代码
│   └── ExampleViewController.swift
├── BuzComponentName.podspec          # Pod规格文件
├── README.md                         # 文档
└── LICENSE                           # 许可证
```

#### Business Module组件结构
```
BuzBusinessModule/
├── Features/
│   └── FeatureName/
│       ├── Controllers/              # 控制器
│       │   └── FeatureViewController.swift
│       ├── Presenters/               # 业务逻辑
│       │   └── FeaturePresenter.swift
│       ├── Views/                    # 视图
│       │   ├── FeatureView.swift
│       │   └── Widgets/
│       │       └── FeatureWidget.swift
│       ├── Entities/                 # 数据模型
│       │   └── FeatureModel.swift
│       ├── Interactors/              # 交互器
│       │   └── FeatureInteractor.swift
│       └── Router/                   # 路由
│           └── FeatureRouter.swift
├── Common/                           # 共通组件
│   ├── Helper/
│   ├── Track/
│   └── Views/
├── Tests/
└── Resources/
```

## 三、命名规范

### 3.1 组件命名
- **规则**: `Buz` + `功能名称`
- **示例**: BuzUIKit, BuzFoundation, BuzNetworking
- **说明**: 确保名称准确反映组件主要功能，避免过于泛化

### 3.2 类和协议命名
```swift
// 类名使用PascalCase
class UserManager { }
class NetworkService { }

// 协议名以Protocol后缀或able/ible结尾
protocol UserManagerProtocol { }
protocol Networkable { }
protocol Configurable { }
```

### 3.3 方法和属性命名
```swift
// 方法和属性使用camelCase
func fetchData() { }
var userName: String
let maxRetryCount: Int
```

### 3.4 枚举命名
```swift
// 枚举类型使用PascalCase，枚举值使用camelCase
enum NetworkStatus {
    case connected
    case disconnected
    case connecting
}

enum BuzCallType {
    case voice
    case video
    case groupCall
}
```

### 3.5 文件命名
- 文件名与类名、协议名保持一致
- Swift文件使用 `.swift` 扩展名
- Objective-C实现文件使用 `.m` 扩展名
- Objective-C头文件使用 `.h` 扩展名

## 四、依赖管理规范

### 4.1 依赖原则
1. **禁止循环依赖**: 组件间不能形成循环依赖
2. **最小依赖**: 仅依赖必要的组件和库
3. **分层依赖**: 按层级依赖，上层可依赖下层，下层不依赖上层

### 4.2 允许的依赖类型
```swift
// ✅ 允许依赖基础组件
import BuzFoundation
import BuzUIKit
import BuzNetworking

// ✅ 允许依赖系统框架
import UIKit
import Foundation
import CoreData

// ❌ 禁止依赖业务模块
// import BuzChatModule (在基础组件中)
```

### 4.3 依赖注入模式
```swift
// 使用协议进行依赖注入
protocol NetworkServiceProtocol {
    func request<T: Codable>(_ endpoint: String) -> AnyPublisher<T, Error>
}

class DataManager {
    private let networkService: NetworkServiceProtocol
    
    init(networkService: NetworkServiceProtocol) {
        self.networkService = networkService
    }
}
```

## 五、代码规范

### 5.1 文档注释
```swift
/// 用户服务，负责从服务器获取用户数据
class UserService {
    
    /// 异步获取用户数据
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - completion: 数据获取完成回调
    /// - Returns: 取消操作的Cancellable对象
    func fetchUserData(
        userId: String, 
        completion: @escaping (Result<UserData, Error>) -> Void
    ) -> Cancellable {
        // Implementation
    }
}
```

### 5.2 错误处理
```swift
// ✅ 使用Result类型处理错误
func fetchData() -> Result<Data, NetworkError> {
    // Implementation
}

// ✅ 使用do-catch处理异常
do {
    let data = try parseJSON(jsonString)
    return .success(data)
} catch {
    return .failure(.parseError(error))
}

// ❌ 避免使用try!和force unwrap
// let data = try! parseJSON(jsonString)
// let value = optionalValue!
```

### 5.3 类型安全
```swift
// ✅ 使用具体类型
protocol UserRepositoryProtocol {
    func fetchUser(id: String) -> User?
}

// ✅ 使用可选类型处理nil值
var optionalUser: User?
if let user = optionalUser {
    // 安全使用user
}

// ❌ 避免使用Any和AnyObject
// var anyValue: Any = "some value"
```

### 5.4 代码格式
- 使用4个空格缩进，不使用制表符
- 行长度不超过120字符
- 使用SwiftLint进行代码检查
- 遵循Swift API Design Guidelines

## 六、SOLID原则应用

### 6.1 单一职责原则 (SRP)
```swift
// ✅ 职责分离
class User {
    var name: String
    var email: String
}

class UserRepository {
    func save(user: User) {
        // 数据持久化逻辑
    }
}

class UserValidator {
    func validate(user: User) -> Bool {
        // 验证逻辑
    }
}
```

### 6.2 开闭原则 (OCP)
```swift
// ✅ 对扩展开放，对修改封闭
protocol PaymentProcessor {
    func process(amount: Double) -> Bool
}

class CreditCardProcessor: PaymentProcessor {
    func process(amount: Double) -> Bool {
        // 信用卡支付逻辑
    }
}

class PayPalProcessor: PaymentProcessor {
    func process(amount: Double) -> Bool {
        // PayPal支付逻辑
    }
}
```

### 6.3 接口隔离原则 (ISP)
```swift
// ✅ 接口隔离
protocol Readable {
    func read() -> String
}

protocol Writable {
    func write(_ content: String)
}

class FileManager: Readable, Writable {
    func read() -> String { /* 实现 */ }
    func write(_ content: String) { /* 实现 */ }
}

class LogViewer: Readable {
    func read() -> String { /* 只需要读取功能 */ }
}
```

### 6.4 依赖倒置原则 (DIP)
```swift
// ✅ 依赖抽象而非具体实现
protocol DataStore {
    func save<T: Codable>(_ object: T, key: String)
    func load<T: Codable>(_ type: T.Type, key: String) -> T?
}

class UserDefaults: DataStore {
    func save<T: Codable>(_ object: T, key: String) { /* 实现 */ }
    func load<T: Codable>(_ type: T.Type, key: String) -> T? { /* 实现 */ }
}

class DataManager {
    private let dataStore: DataStore
    
    init(dataStore: DataStore) {
        self.dataStore = dataStore
    }
}
```

## 七、测试规范

### 7.1 测试结构
```swift
import XCTest
@testable import BuzComponentName

class BuzComponentNameTests: XCTestCase {
    
    var sut: BuzComponentName!
    
    override func setUp() {
        super.setUp()
        sut = BuzComponentName()
    }
    
    override func tearDown() {
        sut = nil
        super.tearDown()
    }
    
    func testComponentFunction() {
        // Given
        let input = "test input"
        
        // When
        let result = sut.process(input)
        
        // Then
        XCTAssertEqual(result, "expected output")
    }
}
```

### 7.2 Mock和Stub
```swift
protocol NetworkServiceProtocol {
    func fetchData() -> AnyPublisher<Data, Error>
}

class MockNetworkService: NetworkServiceProtocol {
    var shouldSucceed = true
    var mockData = Data()
    
    func fetchData() -> AnyPublisher<Data, Error> {
        if shouldSucceed {
            return Just(mockData)
                .setFailureType(to: Error.self)
                .eraseToAnyPublisher()
        } else {
            return Fail(error: NSError(domain: "Test", code: -1))
                .eraseToAnyPublisher()
        }
    }
}
```

## 八、性能优化指南

### 8.1 内存管理
```swift
// ✅ 使用weak引用避免循环引用
class ViewController: UIViewController {
    weak var delegate: ViewControllerDelegate?
}

// ✅ 使用unowned用于确定不会为nil的引用
class DataManager {
    unowned let owner: DataOwner
}

// ✅ 及时释放资源
deinit {
    notificationCenter.removeObserver(self)
    timer?.invalidate()
}
```

### 8.2 异步处理
```swift
// ✅ 使用Combine进行异步操作
func fetchDataAsync() -> AnyPublisher<Data, Error> {
    return URLSession.shared
        .dataTaskPublisher(for: url)
        .map(\.data)
        .receive(on: DispatchQueue.main)
        .eraseToAnyPublisher()
}

// ✅ 使用async/await (iOS 15+)
func fetchDataModern() async throws -> Data {
    let (data, _) = try await URLSession.shared.data(from: url)
    return data
}
```

## 九、版本管理

### 9.1 语义化版本
- **主版本号**: 不兼容的API修改
- **次版本号**: 向下兼容的功能增加
- **修订版本号**: 向下兼容的错误修复

### 9.2 版本兼容性
```swift
// 标记废弃的API
@available(*, deprecated, message: "Use newMethod() instead")
func oldMethod() {
    // 旧实现
}

// 新版本API
@available(iOS 13.0, *)
func newMethod() {
    // 新实现
}
```

---

## 参考文件
- [Podfile](mdc:Podfile) - 项目依赖配置
- [buz/AppDelegate.swift](mdc:buz/AppDelegate.swift) - 应用主入口
- [LocalPods/BuzUIKit/](mdc:LocalPods/BuzUIKit) - UI组件库示例
- [Modules/BuzAuthModule/](mdc:Modules/BuzAuthModule) - 业务模块示例
