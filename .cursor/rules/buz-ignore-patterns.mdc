---
description: 
globs: 
alwaysApply: true
---
# buz iOS 项目忽略规则

本文档定义了在代码分析、搜索和AI辅助开发过程中应该忽略的文件和文件夹，以提高工作效率和专注于业务代码。

## 🚫 完全忽略的目录

### 构建产物和缓存
忽略所有Xcode构建产物和缓存目录：
- `build/` - Xcode构建输出目录
- `DerivedData/` - Xcode派生数据缓存
- `.build/` - Swift Package Manager构建目录
- `Products/` - 编译产物目录
- `*.dSYM/` - 调试符号文件
- `*.app/` - 应用程序包
- `*.ipa` - iOS应用安装包

### 依赖管理目录
忽略第三方依赖和包管理器目录：
- `Pods/` - CocoaPods依赖目录
- `Frameworks/` - 外部框架目录
- `.bundle/` - Ruby Gems缓存
- `Carthage/Build/` - Carthage构建产物
- `Package.resolved` - Swift Package Manager锁定文件

### Xcode用户数据
忽略Xcode个人配置和用户数据：
- `*.xcworkspace/xcuserdata/` - 工作空间用户数据
- `*.xcodeproj/xcuserdata/` - 项目用户数据  
- `*.xcscheme` - 用户方案配置
- `IDEWorkspaceChecks.plist` - Xcode工作空间检查

### 系统和临时文件
忽略系统生成的临时文件：
- `.DS_Store` - macOS目录服务存储
- `Thumbs.db` - Windows缩略图数据库
- `*.tmp` - 临时文件
- `*.temp` - 临时文件
- `*.swp` - Vim交换文件
- `*.swo` - Vim交换文件
- `*~` - 备份文件

## 📁 选择性忽略的目录

### 第三方组件（LocalPods中）
在`LocalPods/`目录中，忽略纯第三方适配组件，保留业务相关组件：

#### 忽略的第三方适配组件：
- `LocalPods/Alamofire*` - 网络请求库
- `LocalPods/AFNetworking*` - 网络请求库
- `LocalPods/SDWebImage*` - 图片加载库
- `LocalPods/MJRefresh*` - 下拉刷新库
- `LocalPods/Masonry*` - 自动布局库
- `LocalPods/SnapKit*` - 自动布局库
- `LocalPods/YYKit*` - iOS工具库
- `LocalPods/FMDB*` - 数据库封装
- `LocalPods/MBProgressHUD*` - 进度指示器
- `LocalPods/IQKeyboardManager*` - 键盘管理
- `LocalPods/SwifterSwift*` - Swift扩展库
- `LocalPods/lottie*` - 动画库
- `LocalPods/HXPhotoPicker*` - 照片选择器

#### 保留的业务组件：
- `LocalPods/BuzUIKit/` - buz UI组件库
- `LocalPods/BuzUIStyle/` - buz样式系统
- `LocalPods/BuzFoundation/` - buz基础库
- `LocalPods/BuzModuleKit/` - buz模块协议库
- `LocalPods/BuzLog/` - buz日志系统
- `LocalPods/AccountCenter/` - 账户中心
- `LocalPods/SocialCenter/` - 社交中心
- `LocalPods/MediaCenter/` - 媒体中心

## 📄 忽略的文件类型

### 日志和调试文件
- `*.log` - 日志文件
- `*.crash` - 崩溃报告
- `*.backtrace` - 堆栈跟踪
- `*.trace` - 追踪文件

### 文档和报告
- `*.coverage` - 代码覆盖率报告
- `docs/generated/` - 自动生成的文档
- `fastlane/report.xml` - Fastlane报告
- `fastlane/Preview.html` - Fastlane预览
- `test_output/` - 测试输出目录

### 配置和密钥文件
- `*.pem` - 证书文件
- `*.p12` - 证书文件
- `*.mobileprovision` - 描述文件
- `*_keys.plist` - 密钥配置文件
- `GoogleService-Info.plist` - Google服务配置（可能包含敏感信息）

### 备份和版本控制
- `*.orig` - 合并冲突备份
- `*.rej` - 补丁拒绝文件
- `.svn/` - Subversion目录
- `.hg/` - Mercurial目录

## ⚡ 性能相关忽略

### 大型资源文件
建议在分析时忽略大型资源文件以提高性能：
- `Resources/**/*.png` - PNG图片资源（除示例图片）
- `Resources/**/*.jpg` - JPEG图片资源
- `Resources/**/*.mp4` - 视频资源
- `Resources/**/*.mov` - 视频资源
- `*.avi` - 视频文件
- `*.bundle` - 资源包文件（除配置文件）

### 字体和音频
- `**/*.ttf` - TrueType字体文件
- `**/*.otf` - OpenType字体文件
- `**/*.mp3` - 音频文件
- `**/*.wav` - 音频文件
- `**/*.m4a` - 音频文件

## 🔧 开发工具生成文件

### Ruby和Fastlane
- `Gemfile.lock` - Ruby依赖锁定文件
- `.bundle/` - Bundle配置目录
- `fastlane/.env*` - Fastlane环境配置

### Node.js和前端工具
- `node_modules/` - Node.js依赖
- `package-lock.json` - npm锁定文件
- `yarn.lock` - Yarn锁定文件

### Python工具
- `__pycache__/` - Python缓存
- `*.pyc` - Python编译文件
- `.pytest_cache/` - pytest缓存

## 📋 特殊项目文件

### CI/CD产物
- `.github/workflows/*.yml` - GitHub Actions（保留关键配置）
- `ci_scripts/build_logs/` - CI构建日志
- `TestResults/` - 自动化测试结果

### 分析工具输出
- `swiftlint.result.json` - SwiftLint结果
- `sonar-reports/` - SonarQube报告
- `coverage/` - 覆盖率报告目录

## 🎯 使用建议

### 代码审查时关注
专注于以下目录的代码质量：
- `buz/` - 主应用代码
- `Modules/` - 业务模块代码
- `Services/Centers/` - 业务服务中心
- `LocalPods/Buz*/` - 业务相关组件

### 重点分析文件类型
优先关注这些文件类型：
- `*.swift` - Swift源代码
- `*.m` - Objective-C实现文件
- `*.h` - Objective-C头文件
- `*.storyboard` - 界面文件
- `*.xib` - 界面文件
- `Podfile` - 依赖配置
- `*.plist` - 配置文件

### 忽略但保留监控
以下文件建议忽略分析但保留监控变更：
- `Podfile.lock` - 监控依赖版本变化
- `project.pbxproj` - 监控项目配置变化
- `Info.plist` - 监控应用配置变化

## 📝 配置示例

如果需要在其他工具中使用类似配置，可以参考：

### .gitignore 补充
```
# buz项目特定忽略
build/
DerivedData/
Products/
*.xcuserdata/
.DS_Store
Pods/
LocalPods/SwifterSwift*/
LocalPods/lottie*/
LocalPods/HXPhotoPicker*/
```

### SwiftLint 忽略
```yaml
excluded:
  - build
  - DerivedData
  - Pods
  - LocalPods/SwifterSwift*
  - LocalPods/lottie*
  - LocalPods/HXPhotoPicker*
```

---

## 🔄 定期更新

该忽略规则应该定期更新，特别是在以下情况：
- 添加新的第三方依赖
- 引入新的开发工具
- 项目结构发生重大变化
- 发现新的需要忽略的文件模式

最后更新：2025年5月
