---
description: 
globs: 
alwaysApply: true
---
# buz iOS项目概览

## 项目简介
buz是一个大型iOS即时通讯社交应用，支持文字聊天、语音通话、视频通话、对讲功能等。项目采用Swift与Objective-C混合开发，使用5层模块化架构设计。

## 技术栈概览

### 编程语言
- **Swift** (3,512个文件) - 主要开发语言
- **Objective-C** (1,817个.m文件 + 4,702个.h文件) - 核心底层代码
- **C/C++** (855个文件) - 性能关键模块
- **Python** (8个文件) - 构建工具和自动化脚本
- **Ruby** - CocoaPods依赖管理

### 支持平台
- **iOS 13.0+** - 主手机应用
- **watchOS 7.0+** - Apple Watch应用
- **Widget Extensions** - iOS小组件
- **Notification Service Extensions** - 推送服务扩展
- **Share Extensions** - 分享扩展

### 核心技术框架
- **UIKit** / **SwiftUI** - UI框架
- **VoderX** - IM即时通讯核心
- **LZAudioEngine** - 音频处理
- **ITNetLibrary** - 网络通信
- **MMKV** / **WCDB** - 数据存储
- **LZRouter** - 模块间路由通信
- **Combine** - 响应式编程框架

## 项目架构设计

### 架构概览图
```
┌─────────────────────────────────────────────────────────┐
│                    APP工程层                             │
│  ┌─────────────┬─────────────┬─────────────┬──────────┐   │
│  │   Buz工程   │   更多...   │             │          │   │
│  └─────────────┴─────────────┴─────────────┴──────────┘   │
├─────────────────────────────────────────────────────────┤
│                   业务模块层                             │
│  ┌─────────────┬─────────────┬─────────────┬──────────┐   │
│  │  APP业务    │ Widget小组件 │  Watch手表  │   更多    │   │
│  │ ┌─────────┐ │ ┌─────────┐ │ ┌─────────┐ │          │   │
│  │ │注册模块  │ │ │IM模块   │ │ │IM模块   │ │          │   │
│  │ │首页模块  │ │ │ 更多... │ │ │ 更多... │ │          │   │
│  │ │IM模块   │ │ └─────────┘ │ └─────────┘ │          │   │
│  │ │ 更多... │ │             │             │          │   │
│  │ └─────────┘ │             │             │          │   │
│  └─────────────┴─────────────┴─────────────┴──────────┘   │
│                组件/服务应用                             │
│              UI组件个性化配置                            │
├═════════════════════════════════════════════════════════┤
│                  模块通信                                │
│            路由(LZRouter) + App Group跨进程通信          │
├─────────────────────────────────────────────────────────┤
│                  业务能力层                              │
│ ┌─────────────────┬─────────────────┬─────────────────┐   │
│ │ 用户业务能力     │  IM业务能力      │ RTC业务能力     │   │
│ │ (UserCenter)   │ (IMCenter)     │ (CallCenter)   │   │
│ ├─────────────────┼─────────────────┼─────────────────┤   │
│ │ 社交业务能力     │ 多媒体业务能力   │     更多...      │   │
│ │(SocialCenter)  │(MediaCenter)   │                │   │
│ └─────────────────┴─────────────────┴─────────────────┘   │
├─────────────────────────────────────────────────────────┤
│                基础组件抽象层                            │
│ ┌─────────────────────────────┬─────────────────────────┐ │
│ │        社交通讯组件抽象       │        组件抽象          │ │
│ │  ┌─────────┬─────────────┐  │ ┌─────────┬───────────┐ │ │
│ │  │BuzIMKit │ RTCAdapter  │  │ │BuzMedia │Localizable│ │ │
│ │  ├─────────┼─────────────┤  │ │   Kit   ├───────────┤ │ │
│ │  │PushAd.. │   更多...   │  │ │图片加载  │EffectAd.. │ │ │
│ │  └─────────┴─────────────┘  │ │BuzCloud │  更多...  │ │ │
│ │                             │ │ Config  │           │ │ │
│ │                             │ └─────────┴───────────┘ │ │
│ └─────────────────────────────┴─────────────────────────┘ │
├─────────────────────────────────────────────────────────┤
│                    基础组件层                            │
│ ┌──────────┬──────────┬──────────┬─────────────────────┐ │
│ │社交通讯组件│  业务组件  │  三方组件  │      公司套组件       │ │
│ │ ┌──────┐ │ ┌──────┐ │ ┌──────┐ │ ┌─────────────────┐ │ │
│ │ │VoderX│ │ │BuzUI │ │ │lottie│ │ │    ShareKit     │ │ │
│ │ │      │ │ │ Kit  │ │ │      │ │ ├─────────────────┤ │ │
│ │ │Doraem│ │ │BuzFo │ │ │ MMKV │ │ │    TekiAPM      │ │ │
│ │ │onKit │ │ │undat │ │ │      │ │ ├─────────────────┤ │ │
│ │ │      │ │ │ ion  │ │ │更多..│ │ │     ItNet       │ │ │
│ │ │更多..│ │ │更多..│ │ └──────┘ │ │  TrackerSDK     │ │ │
│ │ └──────┘ │ └──────┘ │          │ │     Logz        │ │ │
│ │          │          │          │ │    更多...      │ │ │
│ └──────────┴──────────┴──────────┴─────────────────────┘ │
└─────────────────────────────────────────────────────────┘
                      系统层 (iOS)
```

### 5层架构详解

#### 1. APP工程层
**职责**: 应用编译和部署管理
- **Buz工程** - 主手机应用工程
- **更多应用目标** - Watch应用、Widget扩展等

#### 2. 业务模块层
**职责**: 业务功能实现和UI交互

##### APP业务模块
- **注册模块** - 用户注册登录功能
- **首页模块** - 主界面和导航
- **IM模块** - 即时通讯核心功能
- **更多业务模块** - 其他核心业务

##### Widget小组件
- **IM模块** - 消息小组件
- **更多小组件** - 联系人、快捷操作等

##### Watch手表应用
- **IM模块** - 手表端消息处理
- **更多手表功能** - 通话、通知等

#### 3. 业务能力层
**职责**: 业务能力沉淀和通用化

##### 核心业务中心
- **用户业务能力(UserCenter)** - 用户管理、认证、资料
- **IM业务能力(IMCenter)** - 消息处理、会话管理
- **社交业务能力(SocialCenter)** - 好友关系、群组管理
- **RTC业务能力(CallCenter)** - 音视频通话、OnAir
- **多媒体业务能力(MediaCenter)** - 音视频处理、媒体播放

#### 4. 基础组件抽象层
**职责**: 组件适配和统一接口

##### 社交通讯组件抽象
- **BuzIMKit** - IM组件统一接口
- **RTCAdapter** - RTC组件适配器
- **PushAdapter** - 推送组件适配器

##### 通用组件抽象
- **BuzMediaKit** - 媒体组件抽象
- **Localizable** - 国际化组件
- **图片加载** - 图片处理统一接口
- **EffectAdapter** - 特效组件适配器
- **BuzCloudConfig** - 云端配置组件

#### 5. 基础组件层
**职责**: 基础功能组件提供

##### 组件分类
- **社交通讯组件**: VoderX、DoraemonKit
- **业务组件**: BuzUIKit、BuzFoundation  
- **三方组件**: lottie、MMKV
- **公司套组件**: ShareKit、TekiAPM、ItNet、TrackerSDK、Logz

### 模块通信机制

#### 通信方式
- **LZRouter** - 模块间路由和服务调用
- **App Group跨进程通信** - 主应用与扩展间数据共享
- **API+订阅发布模式** - 事件驱动的异步通信
- **链路日志** - 调用链追踪和监控

#### 通信原则
- 同级模块禁止直接通信
- 通过上层中转或下层依赖
- 使用基本数据类型传递参数

## 项目目录结构

### 根目录组织
```
buz_ios/
├── buz/                          # 主应用目标
├── Modules/                      # 业务模块
│   ├── BuzAuthModule/           # 认证登录模块
│   ├── BuzChatModule/           # 聊天核心模块
│   ├── BuzCallModule/           # 音视频通话模块
│   ├── BuzSocialModule/         # 社交功能模块
│   ├── BuzUserModule/           # 用户管理模块
│   ├── BuzMainModule/           # 主应用模块
│   └── ...                      # 其他业务模块
├── Services/                     # 服务层
│   └── Centers/                 # 业务能力中心
│       ├── UserCenter/          # 用户中心
│       ├── IMCenter/            # IM中心
│       ├── SocialCenter/        # 社交中心
│       ├── MediaCenter/         # 媒体中心
│       └── ...
├── LocalPods/                    # 本地Pod库
│   ├── BuzFoundation/           # 基础工具库
│   ├── BuzUIKit/               # UI组件库
│   ├── VoderX/                 # IM通讯核心
│   └── ...                     # 其他组件
├── BuzWatchApp/                  # Watch应用
├── BuzNotificationServiceExtension/  # 推送服务扩展
├── ContactsWidget/               # 联系人小组件
├── LatestMessageWidget/          # 最新消息小组件
├── BuzShareExtension/            # 分享扩展
├── Resources/                    # 资源文件
├── Podfile                       # CocoaPods配置
└── buz.xcworkspace              # 工作空间
```

### 核心业务模块详解

#### [BuzAuthModule](mdc:Modules/BuzAuthModule) - 认证登录模块
```
BuzAuthModule/
├── Features/
│   ├── Login/                   # 登录功能
│   │   ├── PhoneLogin/         # 手机号登录
│   │   ├── EmailLogin/         # 邮箱登录
│   │   └── ThirdPartyLogin/    # 第三方登录
│   ├── Register/               # 注册功能
│   └── Recovery/               # 密码找回
├── Services/                   # 认证服务
└── Resources/                  # 认证相关资源
```

#### [BuzChatModule](mdc:Modules/BuzChatModule) - 聊天核心模块
```
BuzChatModule/
├── Features/
│   ├── Conversation/           # 会话列表
│   ├── ChatRoom/              # 聊天室
│   ├── VoiceMessage/          # 语音消息
│   ├── Walkie/                # 对讲功能
│   └── MessageTypes/          # 消息类型
├── Services/                  # 聊天服务
└── Resources/                 # 聊天相关资源
```

#### [BuzCallModule](mdc:Modules/BuzCallModule) - 音视频通话模块
```
BuzCallModule/
├── Features/
│   ├── VoiceCall/             # 语音通话
│   ├── VideoCall/             # 视频通话
│   ├── GroupCall/             # 群组通话
│   └── OnAir/                 # OnAir功能
│       ├── CallRoom/          # 通话房间
│       └── Widgets/           # 通话组件
├── Services/                  # 通话服务
└── Resources/                 # 通话相关资源
```

### 业务能力中心详解

#### [UserCenter](mdc:Services/Centers/UserCenter) - 用户业务能力中心
- **用户认证**: 登录、注册、第三方认证
- **用户资料**: 个人信息管理、头像上传
- **会话管理**: 用户会话状态、token管理
- **权限管理**: 用户权限验证和控制

#### [IMCenter](mdc:Services/Centers/IMCenter) - IM业务能力中心
- **消息处理**: 消息发送、接收、存储
- **会话管理**: 会话列表、未读数管理
- **连接管理**: IM连接状态、重连机制
- **消息同步**: 多端消息同步

#### [SocialCenter](mdc:Services/Centers/SocialCenter) - 社交业务能力中心
- **好友管理**: 好友添加、删除、搜索
- **群组管理**: 群组创建、成员管理
- **联系人同步**: 通讯录同步、推荐好友
- **关系链管理**: 社交关系图谱

#### [MediaCenter](mdc:Services/Centers/MediaCenter) - 多媒体业务能力中心
- **音频处理**: 音频录制、播放、编码
- **视频处理**: 视频录制、播放、编辑
- **图片处理**: 图片压缩、滤镜、裁剪
- **文件管理**: 媒体文件上传、下载、缓存

### 基础组件库详解

#### UI组件库
- **[BuzUIKit](mdc:LocalPods/BuzUIKit)** - UI组件库
  - 基础UI组件、主题管理、适配工具
- **[BuzFoundation](mdc:LocalPods/BuzFoundation)** - 基础工具库
  - 工具类、扩展、常用功能

#### 通讯组件
- **[VoderX](mdc:LocalPods/VoderX)** - IM通讯核心
  - 即时通讯协议、消息处理、连接管理
- **DoraemonKit** - 开发调试工具
  - 调试面板、性能监控、日志查看

#### 三方组件
- **lottie** - 动画库，支持After Effects动画
- **MMKV** - 高性能键值存储
- **YYModel** - JSON模型转换
- **SnapKit** - 自动布局DSL

#### 公司套组件
- **ShareKit** - 分享组件，支持多平台分享
- **TekiAPM** - 应用性能监控
- **ItNet** - 网络库，统一网络请求处理
- **TrackerSDK** - 数据追踪和埋点
- **Logz** - 日志组件，统一日志处理

## 数据流设计

### 响应式架构
- **单向数据流**: Repository → ViewModel → View
- **Combine框架**: 异步数据流处理
- **MVVM模式**: 数据绑定和状态管理

### 数据存储策略
- **MMKV**: 用户偏好、配置信息
- **WCDB**: 聊天记录、用户数据
- **YYMemoryCache**: 图片缓存、临时数据
- **Keychain**: 敏感信息存储

### 网络架构
- **ITNetLibrary**: 统一网络请求处理
- **IDL协议**: 接口定义和代码生成
- **Request/Response**: 标准请求响应模式
- **Error Handling**: 统一错误处理机制

## 开发工具链

### 构建环境
- **Xcode 14+** - 开发IDE
- **CocoaPods 1.11+** - 依赖管理
- **Ruby 2.7+** - 脚本运行环境
- **Git** - 版本控制

### 调试工具
- **DoraemonKit** - 应用内调试面板
- **MLeaksFinder** - 内存泄漏检测
- **LookinServer** - UI层级调试
- **Charles/Proxyman** - 网络抓包

### 性能监控
- **TekiAPM** - 应用性能监控
- **TrackerSDK** - 用户行为追踪
- **Logz** - 日志收集分析
- **Crashlytics** - 崩溃监控

### 测试框架
- **XCTest** - 单元测试框架
- **UI Testing** - UI自动化测试
- **Quick/Nimble** - BDD测试框架
- **OCMock** - Mock测试工具

## 国际化支持

### 支持语言
- 🇨🇳 中文 (简体/繁体)
- 🇺🇸 英文
- 🇯🇵 日文
- 🇰🇷 韩文
- 🇩🇪 德文
- 🇫🇷 法文
- 等16种语言

### 本地化策略
- **Localizable框架** - 统一国际化管理
- **资源本地化** - 图片、音频等资源适配
- **RTL支持** - 阿拉伯语等从右到左语言
- **动态切换** - 应用内语言切换

## 安全特性

### 数据安全
- **端到端加密** - VoderXE2EE消息加密
- **本地数据加密** - 敏感数据加密存储
- **网络传输加密** - HTTPS/TLS安全传输
- **数据脱敏** - 日志和埋点数据脱敏

### 隐私保护
- **权限最小化** - 按需申请系统权限
- **数据匿名化** - 用户数据匿名化处理
- **隐私协议** - 符合GDPR、CCPA等法规
- **审计日志** - 敏感操作审计记录

## 性能优化

### 启动优化
- **二进制重排** - 使用OrderFile优化启动
- **动态库优化** - 减少动态库加载时间
- **懒加载** - 非关键模块延迟初始化
- **预加载** - 关键资源预加载策略

### 内存优化
- **图片优化** - 图片压缩和缓存策略
- **对象池** - 复用频繁创建的对象
- **弱引用** - 避免循环引用导致内存泄漏
- **及时释放** - 及时释放不再使用的资源

### 包大小优化
- **资源压缩** - 图片、音频等资源压缩
- **代码混淆** - 减少包大小和提高安全性
- **无用代码清理** - 移除未使用的代码
- **按需加载** - 非核心功能按需下载

## 质量保证

### 代码质量
- **SwiftLint** - Swift代码规范检查
- **OCLint** - Objective-C代码质量检查
- **SonarQube** - 代码质量持续监控
- **Code Review** - 代码审查机制

### 测试策略
- **单元测试** - 核心业务逻辑测试覆盖
- **集成测试** - 模块间集成测试
- **UI测试** - 关键用户流程自动化测试
- **性能测试** - 关键场景性能基准测试

### 发布流程
- **持续集成** - GitLab CI自动构建测试
- **自动化部署** - 多环境自动部署
- **灰度发布** - 分阶段用户验证
- **监控告警** - 发布后实时监控

## 项目配置文件

### [Podfile](mdc:Podfile) - 依赖管理配置
- 私有Pod源配置
- 模块化依赖组织  
- 多Target环境配置
- 条件编译开关

### [Info.plist](mdc:buz/Info.plist) - 应用配置
- 应用元信息配置
- 系统权限声明
- URL Scheme配置
- 后台模式配置

### 环境配置
- **Debug** - 开发调试环境
- **Release** - 生产发布环境
- **功能开关** - A/B测试和灰度功能
- **服务器环境** - 开发/测试/生产服务器切换

---

这个概览为开发者提供了项目的完整认知，从架构设计到具体实现，从开发工具到质量保证，涵盖了项目开发的各个方面，便于新成员快速上手和深入了解。
