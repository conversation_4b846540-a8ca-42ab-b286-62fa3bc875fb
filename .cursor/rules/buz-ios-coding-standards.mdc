---
description: 
globs: 
alwaysApply: true
---
# buz iOS 项目编码规范

## 编码规范

## 项目概述
buz 是一个大型iOS即时通讯社交应用，采用严格的模块化架构设计，使用Swift与Objective-C混合开发，支持多平台（iOS、watchOS、Extensions）。

## 一、模块化架构设计

### 1.1 模块定义
模块是整个系统架构中直接承载业务功能逻辑的层级。每个业务模块都独立负责特定的业务功能，并且通过明确的接口和协议进行通信与协作。

#### 1.1.1 模块组成
- **模块名称**: 以"Buz+模块名+Module"格式命名，如 `BuzUserModule`、`BuzChatModule`
- **页面功能**: 具体页面功能实现，在Features目录中管理
- **业务逻辑**: 模块内部业务逻辑，包括数据处理、业务规则应用
- **接口定义**: 通过模块协议库(BuzModuleKit)与其他模块交互

#### 1.1.2 模块职责
1. **业务功能实现**: 每个模块独立实现特定业务功能，确保代码职责清晰
2. **代码解耦**: 通过明确接口和协议，实现模块间松耦合
3. **独立开发**: 支持模块独立开发、测试和部署

### 1.2 项目模块分层

#### 应用层
- **buz** - 主应用目标
- **BuzWatchApp** - Apple Watch应用
- **Extensions** - 各种扩展（Widget、Share、Notification Service）

#### 业务模块层
- **BuzAuthModule** - 认证登录模块
- **BuzChatModule** - 聊天核心模块  
- **BuzCallModule** - 音视频通话模块
- **BuzSocialModule** - 社交功能模块
- **BuzUserModule** - 用户管理模块
- **BuzMainModule** - 主应用模块

#### 服务层
- **Services/Centers/** - 业务能力中心
- **LocalPods/组件适配器** - 第三方SDK适配
- **LocalPods/基础组件** - 通用功能组件

## 二、模块结构规范

### 2.1 结构原则

#### 2.1.1 模块载体
- 以静态Framework工程承载
- 工程结构通过脚本创建，实现模板化

#### 2.1.2 组织原则
**✅ 正确**: 以功能点为首要维度组织结构
```
Features/
├── 功能点A/
│   ├── Entities/
│   ├── Interactors/
│   ├── Presenters/
│   └── Views/
├── 功能点B/
│   ├── Entities/
│   ├── Presenters/
│   └── Views/
└── 功能点C/
    ├── Managers/
    ├── Interactors/
    └── Views/
```

**❌ 错误**: 按职能维度分散功能代码
```
Features/
├── Views/
│   ├── 功能AView.swift
│   ├── 功能BView.swift
│   └── 功能CView.swift
├── Models/
│   ├── 功能AModel.swift
│   └── 功能BModel.swift
└── ViewControllers/
    ├── 功能AViewController.swift
    └── 功能BViewController.swift
```

### 2.2 标准模块结构

```
BuzXxxModule/                          # 模块工程名
├── BuzXxxModule/                      # 模块根文件夹
│   ├── Resources/                     # 🟢 模块资源根文件夹
│   │   ├── Img/
│   │   ├── PAG/
│   │   ├── Json/
│   │   ├── Svga/
│   │   └── BuzXxxModule.xcassets
│   └── BuzXxxModule/                  # 模块代码根文件夹
│       ├── Features/                  # 🔵 模块业务功能根目录
│       │   ├── Commons/               # 通用功能
│       │   ├── 功能分类A/
│       │   │   ├── Commons/
│       │   │   ├── 具体功能点A1/      # 以页面为单位
│       │   │   └── 具体功能点A2/
│       │   └── 功能分类B/
│       ├── Services/                  # 🔵 模块服务根目录
│       │   ├── BuzXxxModuleTask.swift
│       │   ├── BuzXxxService.swift
│       │   ├── Store/                 # 存储功能
│       │   ├── NotifyImp/            # 通知功能
│       │   └── SyncData/             # 数据同步功能
│       ├── Categories/                # 🔵 Objective-C扩展
│       ├── Extensions/                # 🔵 Swift扩展
│       ├── Utils/                     # 🔵 工具类
│       └── 自定义文件夹/               # 🔴 扩展需求
├── BuzXxxModuleTests/                 # 🟠 单元测试
├── Products/                          # 🟠 编译产物
├── Frameworks/                        # 🟠 依赖库
└── Pods/                              # 🟠 依赖配置
```

## 三、VIPER架构规范

### 3.1 VIPER组件定义

#### 3.1.1 Views (视图层)
- **职责**: 显示数据，响应用户交互，向Presenter传递用户输入
- **组成**: ViewController + UIView组件
- **命名规范**:
  ```swift
  // ViewController命名
  LoginViewController.swift
  ProfileViewController.swift
  
  // View命名  
  LoginView.swift
  UserProfileView.swift
  
  // 子组件按功能分文件夹
  Views/
  ├── Cells/
  │   ├── UserListCell.swift
  │   └── MessageCell.swift
  └── Widgets/
      ├── NavigationWidget.swift
      └── BottomWidget.swift
  ```

#### 3.1.2 Interactors (交互器)
- **职责**: 包含业务逻辑，与数据层交互
- **原则**: 按业务逻辑分类，做好分类与聚合控制
- **命名**: `功能名+Interactor.swift`
  ```swift
  LoginInteractor.swift
  UserProfileInteractor.swift
  MessageListInteractor.swift
  ```

#### 3.1.3 Presenters (展示器)
- **职责**: 接收View输入，调用Interactor处理业务逻辑，将结果返回View
- **命名**: `功能名+Presenter.swift`
  ```swift
  LoginPresenter.swift
  UserProfilePresenter.swift
  ```

#### 3.1.4 Entities (实体层)
- **职责**: 定义业务数据结构
- **原则**: 将网络数据模型转换为业务数据模型，增强业务灵活性
- **命名**: `功能名+Entity.swift`
  ```swift
  LoginEntity.swift
  UserEntity.swift
  MessageEntity.swift
  ```

#### 3.1.5 Routers (路由层)
- **创建原则**:
  - 简单路由: 由ViewController承接路由协议处理
  - 复杂路由: 创建独立Router类处理复杂逻辑
- **命名**: `功能名+Router.swift`

### 3.2 VIPER目录结构示例

```
Features/Login/PhoneLogin/
├── Entities/
│   ├── LoginEntity.swift
│   └── CountryCodeEntity.swift
├── Interactors/
│   ├── LoginInteractor.swift
│   └── ValidationInteractor.swift
├── Presenters/
│   └── LoginPresenter.swift
├── Views/
│   ├── PhoneInputViewController.swift
│   ├── PhoneInputView.swift
│   └── CountryCodePicker/
│       ├── CountryCodePickerView.swift
│       └── CountryCodeCell.swift
└── Routers/
    └── LoginRouter.swift
```

## 四、模块服务架构

### 4.1 服务结构设计

每个模块对外提供统一的服务接口，通过`BuzXxxService.swift`作为统一出口：

```swift
@objc(BuzUserService)
class BuzUserService: NSObject, BuzUserServiceable {
    @objc(sharedInstance)
    static let shared: BuzUserService = BuzUserService()
    
    // 通知服务
    lazy var notify: BuzUserNotifyable = BuzUserNotify()
    
    // 存储服务  
    lazy var store: BuzUserStoreable = BuzUserStore()
    
    // 网络服务
    var net: BuzUserNetable {
        return BuzUserNetImp()
    }
    
    @objc public func startListenOp() {
        // 服务初始化逻辑
    }
}
```

### 4.2 服务目录结构

```
Services/
├── BuzUserModuleTask.swift           # 模块配置入口
├── BuzUserService.swift              # 服务统一出口  
├── Store/                            # 存储功能
│   ├── BuzUserStore.swift
│   └── UserDataManager.swift
├── NotifyImp/                        # 通知功能
│   ├── BuzUserNotify.swift
│   └── UserNotificationHandler.swift
└── SyncData/                         # 数据同步
    ├── UserSyncManager.swift
    └── UserSyncOpDispatcher.swift
```

## 五、模块间通信机制

### 5.1 通信架构

模块间通过**LZRouter**和**BuzModuleKit**实现解耦通信:

```
BuzUserModule ←→ LZRouter + BuzModuleKit ←→ BuzLoginModule
      ↕              ↕                        ↕
 其他模块通信     协议/路由地址              其他模块通信
```

### 5.2 通信原则

#### 5.2.1 数据传输规范
1. **路由调用**: 必须定义数据传输模型
   ```swift
   // APP内页面跳转 - 使用标准数据模型
   struct ProfileRouteDto {
       let userId: String
       let displayMode: ProfileDisplayMode
   }
   
   // 外部调用 - 可使用JSON数据，由路由处理类解析
   ```

2. **服务调用**: 必须使用标准数据模型传输
   ```swift
   protocol BuzUserServiceable {
       func getUserInfo(dto: UserInfoRequestDto) -> UserInfoDto?
   }
   ```

### 5.3 BuzModuleKit协议库

#### 5.3.1 协议库结构
```
BuzModuleKit/
├── BuzModuleTask.swift              # 模块基类
├── Modules/                         # 模块协议根目录
│   ├── BuzUserModule/
│   │   ├── Interface/               # 协议定义
│   │   │   ├── BuzUserModuleKit.swift
│   │   │   ├── BuzUserServiceable.swift
│   │   │   └── BuzUserStoreable.swift
│   │   └── Dto/                     # 数据传输模型
│   │       ├── UserInfoDto.swift
│   │       └── UserRouteDto.swift
│   ├── BuzLoginModule/
│   │   ├── Interface/
│   │   └── Dto/
│   └── BuzChatModule/
│       ├── Interface/
│       └── Dto/
└── Tool/                            # 工具类
    └── BuzModuleKitTool.swift
```

#### 5.3.2 模块定义类示例
```swift
@objcMembers
public class BuzUserModuleKit: NSObject {
    // 模块名
    public static let moduleName: String = "user"
    
    // 路由地址
    public static let router: Router = .init()
    
    // 通知定义
    public static let notification: Notification = .init()
    
    /// 路由地址定义
    @objc(BuzUserModuleRouter)
    @objcMembers
    public class Router: NSObject {
        public let halfProfile: String = "user/halfProfile"
        public let follow: String = "user/follow"
        public let profile: String = "user/profile"
    }
    
    /// 通知定义
    @objc(BuzUserModuleNotification)  
    @objcMembers
    public class Notification: NSObject {
        public let followUserSuccessd = NSNotification.Name("followUserSuccessd")
        public let userRelationChanged = NSNotification.Name("userRelationChanged")
    }
    
    /// 枚举定义
    @objc(BuzRelatedUserListRelation)
    public enum RelatedUserListRelation: Int {
        case follow = 1    // 关注的人
        case fans = 2      // 粉丝  
        case friends = 3   // 相互关注
    }
}
```

## 六、命名规范

### 6.1 模块命名
- **格式**: `Buz + 模块名 + Module`
- **示例**: `BuzUserModule`、`BuzChatModule`、`BuzCallModule`

### 6.2 协议命名
```swift
// 使用 -able 后缀描述能力
protocol BuzUserServiceable { }
protocol BuzUserStoreable { }
protocol Networkable { }

// 使用 -ing 后缀描述持续行为  
protocol UserSyncing { }
protocol DataProcessing { }
```

### 6.3 数据传输模型命名
```swift
// 使用 Dto 后缀
struct UserInfoDto { }
struct LoginRequestDto { }
struct ProfileRouteDto { }
```

### 6.4 文件命名规范
- **Swift文件**: `ClassName.swift`
- **Objective-C实现**: `ClassName.m`  
- **Objective-C头文件**: `ClassName.h`
- **资源文件**: `模块名_资源类型_资源名`，如 `user_img_avatar_default`

## 七、资源管理规范

### 7.1 资源组织结构
```
Resources/
├── Img/                             # 图片资源
├── PAG/                             # PAG动画
├── Json/                            # JSON配置
├── Svga/                            # SVGA动画
└── BuzUserModule.xcassets           # 图片资源集
```

### 7.2 资源使用规范

#### 7.2.1 Swift版本
```swift
// 1. 在Extensions下创建扩展
extension BuzUserModule where Base: UIImage {
    static func image(named imageNamed: String) -> UIImage? {
        return UIImage(named: imageNamed, 
                      in: BuzUserModuleConfigurations.bundle, 
                      compatibleWith: nil)
    }
}

// 2. 使用方式
let image = UIImage.user.image(named: "user_profile_avatar")
```

#### 7.2.2 Objective-C版本
```objective-c
// UIImage+BuzUserModule.h
@interface UIImage (BuzUserModule)
+ (UIImage * _Nullable)user_imageNamed:(NSString *)imageNamed;
@end

// UIImage+BuzUserModule.m  
@implementation UIImage (BuzUserModule)
+ (UIImage * _Nullable)user_imageNamed:(NSString *)imageNamed {
    return [UIImage imageNamed:imageNamed 
                      inBundle:BuzUserModuleConfigurations.bundle 
         compatibleWithTraitCollection:nil];
}
@end

// 使用方式
UIImage *image = [UIImage user_imageNamed:@"user_profile_avatar"];
```

## 八、依赖管理规范

### 8.1 依赖层级原则

#### 8.1.1 横向依赖（模块间）
- **禁止**: 业务模块间直接依赖
- **允许**: 通过LZRouter和BuzModuleKit通信

#### 8.1.2 纵向依赖（分层）
- **业务模块** → **业务能力中心**
- **业务模块** → **组件适配器** 
- **业务模块** → **基础组件**
- **组件适配器** → **第三方SDK**

### 8.2 Podfile依赖示例
```ruby
target 'BuzUserModule' do
  project 'Modules/BuzUserModule/BuzUserModule.xcodeproj'
  
  # 路由和协议库（必须）
  pod_router()
  pod 'BuzModuleKit', :path => './LocalPods/BuzModuleKit'
  
  # 业务能力中心
  pod 'AccountCenter', :path => './LocalPods/AccountCenter'
  pod 'SocialCenter', :path => './LocalPods/SocialCenter'
  
  # 组件适配器（禁止同时依赖适配器和被适配组件）
  pod 'EffectAdapter', :path => './LocalPods/EffectAdapter'
  
  # 基础组件
  pod 'BuzUIKit', :path => './LocalPods/BuzUIKit'
  pod 'BuzFoundation', :path => './LocalPods/BuzFoundation'
  
  target 'BuzUserModuleTests' do
    inherit! :search_paths
  end
end
```

## 九、Swift与Objective-C混编规范

### 9.1 Swift调用Objective-C

#### 9.1.1 Umbrella Header
```objective-c
// BuzUserModule-oc-umbrella.h
#import "UserDataManager.h"
#import "UserNetworkManager.h"
#import "UIImage+BuzUserModule.h"
```

#### 9.1.2 modulemap配置
```
module BuzUserModule_OC {
    umbrella header "BuzUserModule-oc-umbrella.h"
    export *
    module * { export * }
}
```

### 9.2 Objective-C调用Swift

```objective-c
// 导入Swift生成的头文件
#import <BuzUserModule/BuzUserModule-Swift.h>

// Swift类必须添加public修饰符才能被OC调用
@objc public class UserManager: NSObject {
    @objc public func fetchUserData() {
        // 实现
    }
}
```

## 十、代码质量规范

### 10.1 代码格式
- 使用4个空格缩进
- 行长度不超过120字符  
- 使用SwiftLint进行检查
- 遵循Swift API Design Guidelines

### 10.2 注释规范
```swift
/// 用户服务管理器，负责用户相关的业务逻辑处理
/// 
/// 主要功能包括：
/// - 用户信息获取和更新
/// - 用户关系管理
/// - 用户状态同步
class UserManager {
    
    /// 获取用户详细信息
    /// - Parameters:
    ///   - userId: 用户唯一标识
    ///   - completion: 获取完成回调
    /// - Returns: 可取消的操作对象
    func fetchUserDetail(
        userId: String,
        completion: @escaping (Result<UserEntity, Error>) -> Void
    ) -> Cancellable {
        // 实现逻辑
    }
}
```

### 10.3 错误处理
```swift
// ✅ 使用Result类型
func fetchData() -> Result<Data, NetworkError> {
    do {
        let data = try networkManager.request()
        return .success(data)
    } catch {
        return .failure(.networkError(error))
    }
}

// ✅ 使用do-catch
do {
    let user = try parseUserData(json)
    updateUI(with: user)
} catch {
    showError(error)
}

// ❌ 避免强制解包和try!
// let data = try! networkManager.request()
// let user = optionalUser!
```

---

## 参考文件

### 项目核心文件
- [Podfile](mdc:Podfile) - 项目依赖配置
- [buz/AppDelegate.swift](mdc:buz/AppDelegate.swift) - 应用主入口

### 模块示例  
- [Modules/BuzUserModule/](mdc:Modules/BuzUserModule) - 用户模块示例
- [Modules/BuzAuthModule/](mdc:Modules/BuzAuthModule) - 认证模块示例
- [Modules/BuzChatModule/](mdc:Modules/BuzChatModule) - 聊天模块示例

### 基础组件
- [LocalPods/BuzUIKit/](mdc:LocalPods/BuzUIKit) - UI组件库
- [LocalPods/BuzFoundation/](mdc:LocalPods/BuzFoundation) - 基础工具库
- [Services/Centers/](mdc:Services/Centers) - 业务服务中心

