---
description: 
globs: 
alwaysApply: true
---
# buz iOS 工程架构设计

## 架构方向

### 1. 模块分层
**目标**: 低耦合、高内聚、可扩展的模块分层

#### 设计原则
- **低耦合**: 各层级模块间依赖关系清晰，减少模块间直接依赖
- **高内聚**: 同一层级内功能相关性强，职责明确
- **可扩展**: 支持模块独立开发、测试和部署，便于功能扩展

#### 分层架构
```
┌─────────────────────────────────────────────────────────┐
│                 Application Layer                        │
│                    (应用层)                             │
├─────────────────────────────────────────────────────────┤
│                 Feature Layer                           │
│                 (功能交互层)                             │
├─────────────────────────────────────────────────────────┤
│                 Domain Layer                            │
│                 (领域层)                                │ 
├─────────────────────────────────────────────────────────┤
│               Business Center Layer                     │
│                (业务中心层)                             │
├─────────────────────────────────────────────────────────┤
│                Component Layer                          │
│                 (组件层)                                │
└─────────────────────────────────────────────────────────┘
```

### 2. 数据分离
**目标**: 数据逻辑分层，数据与UI分离

#### 数据逻辑分层
- **业务中心层**: 数据逻辑下沉至biz-center或domain层
- **内外接口分离**: 内部接口(如UserRemoteDataSource)对外屏蔽，外部接口(如UserRepository)保证数据一致性
- **单一数据源**: 确保数据的唯一性和一致性

#### UI与数据分离
- **ViewModel**: 处理UI状态和数据组装，分离UI展示逻辑
- **Repository**: 处理复杂业务逻辑和数据处理，分离数据获取逻辑
- **清晰边界**: 功能交互层的数据处理与UI展示明确分离

### 3. 数据处理
**目标**: 单一数据源，单向数据流，响应式编程

#### 核心原则
- **单一数据源**: 每个数据实体只有一个权威来源
- **单向数据流**: 数据流向清晰，避免双向绑定的复杂性
- **响应式编程**: 使用响应式编程范式处理异步数据流

#### 实现策略
- **组合替代修改**: 通过数据组合而非直接修改来处理数据变化
- **不可变数据**: 优先使用不可变数据结构，确保数据安全性
- **流式处理**: 使用Combine或类似框架处理数据流转

## 一、架构模块划分

### 1.1 Application Layer (应用层)

#### 层级职责
应用编译层，负责不同目标应用的编译和部署，如手机应用、手表应用、某个模块单独编译等。

#### 应用目标
- **:app:phone** - 主手机应用
- **:app:watch** - Apple Watch应用
- **:app:login** - 独立登录应用
- **其他应用目标** - 根据需要扩展

#### 职责说明
1. **应用入口**: 作为不同平台应用的编译入口
2. **依赖聚合**: 聚合下层模块依赖，组装完整应用
3. **配置管理**: 管理应用级别的配置和启动逻辑
4. **平台适配**: 针对不同平台进行适配和优化

### 1.2 Feature Layer (功能交互层)

#### 层级职责
UI交互层，通过使用通用业务能力的封装完成需求逻辑。

#### 功能模块
- **:feature:login** - 登录功能
- **:feature:home** - 首页功能
- **:feature:contact** - 联系人功能
- **:feature:livechat** - 直播聊天功能
- **:feature:home-watch** - 手表首页功能
- **:feature:login-watch** - 手表登录功能

#### 架构特点
- **UI+交互**: 专注于UI展示和用户交互逻辑
- **业务能力封装**: 通过调用业务中心层完成业务需求
- **平台特化**: 支持不同平台的功能特化实现

#### 设计原则
```swift
// Feature层典型结构
:feature:login/
├── LoginViewModel        # UI状态和数据组装
├── LoginViewController   # UI交互控制
├── LoginRepository      # 业务逻辑和数据处理
└── LoginRoutes          # 路由定义
```

### 1.3 Domain Layer (领域层) [Optional/可选]

#### 层级职责
多feature间需要组装相同数据时，组装逻辑可以放到该模块共享。

#### 领域模块
- **:domain:im** - IM领域逻辑
- **:domain:call** - 通话领域逻辑
- **:domain:media** - 媒体领域逻辑
- **:domain:im-relation** - IM关系领域逻辑

#### 应用场景
- **数据组装**: 多个Feature需要相同的数据组装逻辑时
- **领域规则**: 跨Feature的业务规则和约束
- **共享模型**: Feature间共享的领域模型和转换逻辑

#### 设计示例
```swift
// Domain层数据组装示例
:domain:im/
├── SendMessageRepository     # 发送消息领域逻辑
├── ContactListUseCase       # 联系人列表用例
└── MessageTransformer       # 消息数据转换
```

### 1.4 Business Center Layer (业务中心层)

#### 层级职责
抽象业务逻辑行为，封装标准的通用逻辑。

#### 业务中心
- **:biz-center:relation** - 关系业务中心
  - UserInfo, GroupInfo
  - UserRepository, GroupRepository
  - UserSessionRepository
- **:biz-center:im** - IM业务中心
  - BuzTextMessage, BuzImageMessage
  - IMSdk, IMRemoteDataSource
  - IMRepository
- **:biz-center:livechat** - 直播聊天业务中心
  - LiveChatRoom, LiveChatSeatInfo
  - LiveChatCoreRepository

#### 数据分离架构
```swift
// 业务中心内部架构
:biz-center:relation/
├── Entity/                    # 业务实体
│   ├── UserInfo.swift
│   └── GroupInfo.swift
├── Repository/                # 对外接口
│   ├── UserRepository.swift
│   └── GroupRepository.swift
└── DataSource/               # 内部数据源(对外屏蔽)
    ├── UserLocalDataSource.swift
    ├── UserRemoteDataSource.swift
    └── UserSessionLocalDataSource.swift
```

### 1.5 Component Layer (组件层)

#### 层级职责
提供基础功能组件，支持上层业务开发。

#### 组件分类

##### UI组件
- **BuzUIKit** - UI组件库
- **BuzFoundation** - 基础工具库

##### 通讯组件
- **VoderX** - IM通讯核心
- **DoraemonKit** - 开发调试工具

##### 三方组件
- **lottie** - 动画库
- **MMKV** - 键值存储

##### 公司套组件
- **ShareKit** - 分享组件
- **TekiAPM** - 性能监控
- **ItNet** - 网络库
- **TrackerSDK** - 数据追踪
- **Logz** - 日志组件

## 二、模块通信机制

### 2.1 通信原则
**核心原则**: 同级模块间不能通信（不管直接还是间接）

#### 禁止的通信方式
```
❌ Feature层模块间直接通信
:feature:login ←→ :feature:home

❌ Business Center层模块间直接通信  
:biz-center:relation ←→ :biz-center:im
```

### 2.2 允许的通信方式

#### 1. 依赖下层获取数据
```swift
// Feature层依赖Business Center层
:feature:login → :biz-center:relation
:feature:home → :biz-center:im
```

#### 2. 通过上层级中转
```swift
// 通过Application层中转Feature层通信
:feature:login → :app:phone → :feature:home
```

#### 3. 基本数据类型传递
```swift
// ✅ 使用ID等基本数据传递
func navigateToProfile(userId: String)

// ❌ 避免传递整个对象
func navigateToProfile(user: UserEntity)
```

### 2.3 数据流向图
```
Application Layer    ⟸ 聚合依赖 ⟸ 应用配置
       ↑
Feature Layer       ⟸ UI交互 ⟸ 业务能力调用  
       ↑
Domain Layer        ⟸ 数据组装 ⟸ 共享逻辑
       ↑  
Business Center     ⟸ 业务逻辑 ⟸ 数据处理
       ↑
Component Layer     ⟸ 基础能力 ⟸ 工具支持
```

## 三、数据架构设计

### 3.1 数据逻辑分层

#### Repository模式
```swift
// 对外接口 - 保证数据一致性
protocol UserRepository {
    func getUserInfo(userId: String) -> AnyPublisher<UserInfo, Error>
    func updateUserInfo(_ userInfo: UserInfo) -> AnyPublisher<Void, Error>
}

// 内部数据源 - 对外屏蔽
protocol UserRemoteDataSource {
    func fetchUser(userId: String) -> AnyPublisher<UserDTO, Error>
}

protocol UserLocalDataSource {
    func saveUser(_ user: UserInfo)
    func getUser(userId: String) -> UserInfo?
}
```

#### 数据一致性保证
```swift
class UserRepositoryImpl: UserRepository {
    private let remoteDataSource: UserRemoteDataSource
    private let localDataSource: UserLocalDataSource
    
    func getUserInfo(userId: String) -> AnyPublisher<UserInfo, Error> {
        // 1. 先从本地获取
        if let localUser = localDataSource.getUser(userId: userId) {
            return Just(localUser).setFailureType(to: Error.self).eraseToAnyPublisher()
        }
        
        // 2. 从远端获取并缓存
        return remoteDataSource.fetchUser(userId: userId)
            .map { dto in dto.toUserInfo() }
            .handleEvents(receiveOutput: { [weak self] userInfo in
                self?.localDataSource.saveUser(userInfo)
            })
            .eraseToAnyPublisher()
    }
}
```

### 3.2 UI与数据分离

#### ViewModel模式
```swift
class LoginViewModel: ObservableObject {
    @Published var phoneNumber: String = ""
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?
    
    private let loginRepository: LoginRepository
    
    func login() {
        isLoading = true
        
        loginRepository.login(phoneNumber: phoneNumber)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { [weak self] completion in
                    self?.isLoading = false
                    if case .failure(let error) = completion {
                        self?.errorMessage = error.localizedDescription
                    }
                },
                receiveValue: { [weak self] success in
                    // 处理登录成功
                }
            )
            .store(in: &cancellables)
    }
}
```

### 3.3 响应式数据处理

#### 单向数据流
```swift
// 数据流向：Repository → ViewModel → View
Repository.getData()
    .map { data in data.toViewModel() }           // 数据转换
    .receive(on: DispatchQueue.main)              // 线程切换
    .assign(to: \.viewData, on: viewModel)        // 数据绑定
    .store(in: &cancellables)                     // 生命周期管理
```

#### 组合替代修改
```swift
// ✅ 通过组合创建新数据
func updateUser(userId: String, name: String) -> UserInfo {
    return currentUser.copy(name: name, updatedAt: Date())
}

// ❌ 避免直接修改
func updateUser(userId: String, name: String) {
    currentUser.name = name  // 直接修改可能导致状态不一致
}
```

---

通过以上架构设计，实现了模块化、组件化、规范化、通用化的目标，确保了低耦合、高内聚、可扩展的系统架构。
