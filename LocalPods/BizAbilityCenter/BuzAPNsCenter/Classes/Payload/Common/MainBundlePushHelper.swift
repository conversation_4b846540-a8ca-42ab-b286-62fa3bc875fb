//
//  PushUserInfo.swift
//  buz
//
//  Created by dawen li on 2023/10/12.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import BuzLog

public extension BuzAPNs {
    struct PushHelper { }
}

public extension BuzAPNs.PushHelper {
    
    // for App bundle use only
    // use App bundle's shared instance, do not need to start
    static func needFilterForeground(payload: BuzAPNs.ActionPayload) -> Bool {
        let pushType = payload.appData?.pushType ?? .none
        
        return isIMPush(pushType) || isOtherNeedFilter(pushType)
    }
    
    static func isOtherNeedFilter(_ pushType: BuzAPNs.PushType) -> Bool {
        return pushType == .registeredFriendRecommend
    }
    
    static func isIMPush(payload: BuzAPNs.ActionPayload) -> Bool {
        let pushType = payload.appData?.pushType ?? .none
        
        return isIMPush(pushType)
    }

    static func isIMPush(_ pushType: BuzAPNs.PushType) -> Bool {
        return pushType == .privateChat || pushType == .groupChat || pushType == .quickReactVoicemoji
    }
}

public extension BuzAPNs.PushHelper {
    
    static func isForegroundPushNeedFilter(userInfo: [String : AnyObject], payload: BuzAPNs.ActionPayload) -> Bool {
        if let type = userInfo["type"] as? Int32, let pushType = BuzAPNs.PushType(rawValue: type) {
            if pushType == .registerInvite || pushType == .registerCaptchaInvite {
                return true
            }
        }
        
        return BuzAPNs.PushHelper.needFilterForeground(payload: payload)
    }
}
