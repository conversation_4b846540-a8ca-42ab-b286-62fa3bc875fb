//
//  BuzApnsPushPayload.swift
//  buz
//
//  Created by st.chio on 2024/12/10.
//  Copyright © 2024 lizhi. All rights reserved.
//

import BuzLog

public protocol BuzAPNsPushPayloadable {
    var receivedRawData: [AnyHashable: Any]? { get }
    init(received rawData: [AnyHashable: Any]?)
    
    func dictionary() -> [AnyHashable: Any]?
}

public extension BuzAPNsPushPayloadable {
    
    func dictionary() -> [AnyHashable: Any]? {
        return nil
    }
    
}

// MARK: ---BuzAPNs.PushPayload
public extension BuzAPNs {
    class PushPayload {}
}

// MARK: ---BuzAPNs.PushPayload.Router
public extension BuzAPNs.PushPayload {
    
    class Router: BuzAPNsPushPayloadable {
        // MARK: Used to mark the unparsed data of the received remote notification
        public private(set) var receivedRawData: [AnyHashable: Any]?
        
        // MARK: Parsed Properties
        public var scheme: String = ""
        public var extraData: [String: Any]?
        
        public init(scheme: String, extraData: [String: Any]?) {
            self.scheme = scheme
            self.extraData = extraData
        }
        
        public required init(received rawData: [AnyHashable : Any]?) {
            self.receivedRawData = rawData
            BuzAPNsPushLog.debug("BuzAPNs.PushPayload.Router initreceived rawData: \(rawData ?? [:])")
            
            if let scheme = rawData?["scheme"] as? String {
                self.scheme = scheme
            }
            
            if let extraData = rawData?["extraData"] as? [String: Any] {
                self.extraData = extraData
            }
        }
        
        public func dictionary() -> [String: Any]? {
            var dict: [String: Any] = .init()
            
            if !self.scheme.isEmpty {
                dict["scheme"] = self.scheme
            }else {
                return nil
            }
            
            if let extraData = self.extraData {
                dict["extraData"] = extraData
            }
            
            if !dict.keys.isEmpty {
                return dict
            }
            return nil
        }
    }
}


// MARK: ---Tool------
extension String {
    
    public func toDict() -> [String : Any]?{
        if let data = data(using: String.Encoding.utf8) ,
           let dict = try? JSONSerialization.jsonObject(with: data, options: .mutableContainers) as? [String : Any] {
            return dict
        }
        return nil
    }
}
