//
//  BuzAPNs+LocalPushPayload.swift
//  buz
//
//  Created by st.chio on 2024/12/27.
//  Copyright © 2024 lizhi. All rights reserved.
//

public extension BuzAPNs {
    class LocalPushPayload: BuzAPNsPushPayloadable {
        // MARK: Used to mark the unparsed data of the received remote notification
        public private(set) var receivedRawData: [AnyHashable: Any]?
        
        // MARK: Parsed Properties
        public var pushType: BuzAPNs.PushType = .none
        public var router: BuzAPNs.PushPayload.Router?
        public var reportParams: [String: Any]? //通知点击时埋点参数，如果是页面内的埋点信息请在各页面router内添加
        
        public convenience init() {
            self.init(received: nil)
        }
        
        public required init(received rawData: [AnyHashable: Any]?) {
            self.receivedRawData = rawData
            
            if let value = rawData?["router"] as? [String: Any] {
                self.router = BuzAPNs.PushPayload.Router.init(received: value)
            }
            if let value = rawData?["reportParams"] as? [String: Any] {
                self.reportParams = value
            }
        }
        
        public func dictionary() -> [String: Any]? {
            var dict: [String: Any] = .init()
            
            dict["pushType"] = pushType.rawValue
            if let router = self.router {
                dict["router"] = router.dictionary()
            }
            if let value = self.reportParams {
                dict["reportParams"] = value
            }
            
            if !dict.keys.isEmpty {
                return dict
            }
            return nil
        }
    }
}


// MARK: ---BuzAPNs.LocalPushPayload
public extension BuzAPNs.LocalPushPayload {
    
    var isIMPush: Bool {
        switch self.pushType {
        case .privateChat, .groupChat:
            return true
        default:
            return false
        }
    }
}
