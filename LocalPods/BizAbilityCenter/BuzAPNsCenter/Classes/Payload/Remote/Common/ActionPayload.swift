//
//  BuzAPNs.ActionPayload.swift
//  buz
//
//  Created by st.chio on 2024/12/9.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation
import BuzLog
import BuzDataShareKit

public extension BuzAPNs.ActionPayload {
    enum Version: String {
        case v1 = "1"
        case v2 = "2"
    }
}

public extension BuzAPNs {
    // MARK: ---BuzAPNs.ActionPayload - from Apns Push Notification Data Content Model
    class ActionPayload: BuzAPNsPushPayloadable {
        
        public let version: BuzAPNs.ActionPayload.Version = .v2
        
        // MARK: Used to mark the unparsed data of the received remote notification
        public private(set) var receivedRawData: [AnyHashable: Any]?
        
        // MARK: Parsed Properties
        public var appData: BuzAPNs.ActionPayload.AppDataV2?
        
        public convenience init() {
            self.init(received: nil)
        }
        
        public convenience init(alert: BuzAPNs.RemotePushPayload.Alert, received rawData: [AnyHashable: Any]?) {
            self.init(received: rawData)
            self.appData?.title = alert.title
            self.appData?.pushContent = alert.loc_key
        }
        
        public required init(received rawData: [AnyHashable: Any]?) {
            self.receivedRawData = rawData
            BuzAPNsPushLog.info("BuzAPNs.ActionPayload initreceived rawData: \(rawData ?? [:])")
            
            guard let action = rawData?["action"] as? [String: Any] else { return }
            
            switch self.version(received: action) {
            case .v1:
                BuzAPNsPushLog.info("BuzAPNs.ActionPayload parsedAppDataForV1")
                let v1 = BuzAPNs.ActionPayload.AppDataV1.init(received: action)
                self.appData = BuzAPNs.ActionPayload.AppDataV2.init(conversion: v1)
            case .v2:
                BuzAPNsPushLog.info("BuzAPNs.ActionPayload parsedAppDataForV2")
                self.appData = BuzAPNs.ActionPayload.AppDataV2.init(received: action)
            }
        }
    }
}


// MARK: ---BuzAPNs.ActionPayload
public extension BuzAPNs.ActionPayload {
    
    func version(received rawData: [AnyHashable: Any]?) -> Version {
        if let value = rawData?["ver"] as? String,
            let ver = Version.init(rawValue: value) {
            return ver
        }
        
        return .v1
    }
}


// MARK: ---BuzAPNs.ActionPayload
public extension BuzAPNs.ActionPayload {
    
    var isIMPush: Bool {
        switch self.appData?.pushType {
        case .privateChat, .groupChat, .quickReactVoicemoji:
            return true
        default:
            return false
        }
    }
}

// MARK: ---BuzAPNs.ActionPayload.AppDataV2-----
public extension BuzAPNs.ActionPayload.AppDataV2 {
    var svrMsgId: Int64? {
        if let svrMsgId = self.imInfo?.svrMsgId {
            return Int64(svrMsgId)
        }
        
        return nil
    }
    
    var targetId: Int64? {
        if let im = self.imInfo {
            if im.convType == 3 {
                return self.groupInfo?.groupId
            }else {
                return self.senderInfo?.userId
            }
        }
        return nil
    }
}

// MARK: ---BuzAPNs.ActionPayload.AppDataV2-----
public extension BuzAPNs.ActionPayload.AppDataV2 {
    
    enum ReplaceType: Int32 {
        case normal = 0 //不替换
        case contacts = 1//联系人
        case group = 2 //群聊
        case replaceNull = 3 //把对应字符串置空处理
    }
    
    static func parseMentionKey(_ key : String) -> (userId:Int64?, hasBracket : Bool){
        var userId : Int64?
        
        if key.hasPrefix("["), key.count >= 2 {
            let uidStr = key[key.index(key.startIndex, offsetBy: 1)..<key.endIndex]
            if let tuserId = uidStr.components(separatedBy: "-").first {
                userId = (tuserId as NSString).longLongValue
            }
            
            return (userId : userId, hasBracket : true)
        } else {
            userId = (key as NSString).longLongValue
            return (userId : userId, hasBracket : false)
        }
    }
    
    func getPushContent() -> String? {
        if var content = self.pushContent {
            if let dict = self.contentReplaceInfo,
               let replaceTypeNum = dict["replaceType"] as? Int32,
               let replaceText = dict["replaceText"] as? String,
               let replaceType = ReplaceType(rawValue: replaceTypeNum) {
                if  replaceType == .replaceNull {
                    content = NSString(string: content).replacingOccurrences(of: replaceText, with: "")
                }else{
                    content = self.contentReplace(pushContent: content)
                }
            } else if let textMention = self.pushExtra?.textMentioned,
                      let mentionMap = self.pushExtra?.mentionMap {
                var replaceContent = textMention
                let contactsNameMap: [Int64: String] = PushShareDataTool.loadUserFullnameMap()
                let rules : NSMutableSet = NSMutableSet.init()
                
                mentionMap.forEach { (key: String, value: String) in
                    let replaceKey = key
                    let result = BuzAPNs.ActionPayload.AppDataV2.parseMentionKey(key)
                    let userId : Int64? = result.userId
                    
                    if result.hasBracket {
                        rules.add("and")
                    } else {
                        rules.add("ios")
                    }
                    
                    if let userId = userId, let remark = contactsNameMap[userId] {
                        replaceContent = replaceContent.replacingOccurrences(of: replaceKey, with: "@\(remark)")
                    } else {
                        replaceContent = replaceContent.replacingOccurrences(of: replaceKey, with: "@\(value)")
                    }
                }
                
                if rules.count < 2 {
                    content = replaceContent
                }
            }

            return content
        }
        
        return nil
    }
    
    func titleReplace() -> String? {
        
        guard let dict = self.titleReplaceInfo,
              let replaceText = dict["replaceText"] as? String,
              var title = self.title else {
            
            return nil
        }
        
        let typeValue = dict["replaceType"] as? Int32 ?? 0
        let replaceType = ReplaceType.init(rawValue: typeValue) ?? .normal
        
        let localText = getLocalText(replaceType: replaceType) ?? ""
        title = title.replacingOccurrences(of: replaceText, with: localText)
        return title
    }
    
    private func contentReplace(pushContent: String) -> String {
        guard let dict = self.contentReplaceInfo,
              let replaceText = dict["replaceText"] as? String else {
            return pushContent
        }
                
        let typeValue = dict["replaceType"] as? Int32 ?? 0
        let replaceType = ReplaceType.init(rawValue: typeValue) ?? .normal
        
        let localText = getLocalText(replaceType: replaceType) ?? ""
        let pushContent = pushContent.replacingOccurrences(of: replaceText, with: localText)
        
        return pushContent
    }
    
    private func getLocalText(replaceType: ReplaceType) -> String?{

        switch replaceType {
        case .normal:
            return nil
        case .contacts:
            return getLocalContactsName()
        case .group:
            return getLocalGroupName()
        case .replaceNull:
            return nil
        }
    }
    
    //获取本地联系人备注
    func getLocalContactsName() -> String?{
        guard let senderInfo = self.senderInfo else {
            return nil
        }
        ////这里需要通过共享数据查找备注。
        var senderName = senderInfo.userName ?? ""
        let contactsNameMap : [Int64 : String] = PushShareDataTool.loadContactsMap()
        if let userId = senderInfo.userId, let name = contactsNameMap[userId] , name.count > 0 {
            senderName = name
        }
        return senderName
    }
    
    //获取本地群名称
    func getLocalGroupName() -> String{
        var groupName : String = ""
        if let name = self.groupInfo?.name {
            groupName = name
        }
        
        //动态生成一个群名
        if let v1 = self.appDataV1, groupName.count == 0 {
            guard let groupInfo = v1.groupBaseInfo,
                  let members = groupInfo["firstFewMemberInfos"] as? [[String: Any]] else {
                return groupName
            }
            
            if  members.count > 0 {
                let suffixStr : String = " , "
                let contactsNameMap : [Int64 : String] = PushShareDataTool.loadContactsMap()
                groupName = members.reduce(into: "") { partialResult, member in
                    //这里需要通过共享数据查找备注。
                    var memberName = (member["userName"] as? String) ?? ""
                    if let userId = member["userId"] as? Int64, let name = contactsNameMap[userId] , name.count > 0 {
                        memberName = name
                    }
                    
                    partialResult += memberName
                    partialResult += suffixStr
                }
                //remove last suffix string
                if groupName.count >= suffixStr.count {
                    groupName.removeLast(suffixStr.count)
                }
            }
        }
        
        return groupName
    }
}
