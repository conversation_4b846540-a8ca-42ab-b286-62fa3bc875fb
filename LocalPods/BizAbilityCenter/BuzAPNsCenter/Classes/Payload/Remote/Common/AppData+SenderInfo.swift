//
//  BuzAPNs.ActionPayload.swift
//  buz
//
//  Created by st.chio on 2024/12/9.
//  Copyright © 2024 lizhi. All rights reserved.
//

import BuzLog

// MARK: ---BuzAPNs.ActionPayload.SenderInfo
public extension BuzAPNs.ActionPayload {
    
    //v1.0 与v2.0 共用
    class SenderInfo: BuzAPNsPushPayloadable {
        // MARK: Used to mark the unparsed data of the received remote notification
        public private(set) var receivedRawData: [AnyHashable: Any]?
        
        // MARK: Parsed Properties
        public var userId: Int64?
        public var userName: String?
        public var portrait: String?
        
        public init(userId: Int64?, userName: String?, portrait: String?) {
            self.userId = userId
            self.userName = userName
            self.portrait = portrait
        }
        
        public required init(received rawData: [AnyHashable : Any]?) {
            self.receivedRawData = rawData
            BuzAPNsPushLog.info("BuzAPNs.ActionPayload.SenderInfo initreceived rawData: \(rawData ?? [:])")
            
            if let userId = rawData?["userId"] as? Int64 {
                self.userId = userId
            }
            if let userName = rawData?["userName"] as? String {//1.0 使用字段
                self.userName = userName
            }
            if let userName = rawData?["name"] as? String {//2.0 使用字段
                self.userName = userName
            }
            if let portrait = rawData?["portrait"] as? String {
                self.portrait = portrait
            }
        }
        
        //1.0客户端构造数据需要
        public func dictionary() -> [String: Any]? {
            var dict: [String: Any] = .init()
            
            if let value = self.userId {
                dict["userId"] = value
            }
            if let value = self.userName {
                dict["userName"] = value
            }
            if let value = self.portrait {
                dict["portrait"] = value
            }
            
            if !dict.keys.isEmpty {
                return dict
            }
            return nil
        }
    }
}
