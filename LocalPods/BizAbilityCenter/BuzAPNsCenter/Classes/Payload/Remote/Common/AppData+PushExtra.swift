//
//  BuzAPNs.ActionPayload.swift
//  buz
//
//  Created by st.chio on 2024/12/9.
//  Copyright © 2024 lizhi. All rights reserved.
//

import BuzLog

// MARK: ---BuzAPNs.ActionPayload.PushExtra
public extension BuzAPNs.ActionPayload {
    
    //v1.0 与v2.0 共用
    class PushExtra: BuzAPNsPushPayloadable {
        // MARK: Used to mark the unparsed data of the received remote notification
        public private(set) var receivedRawData: [AnyHashable: Any]?
        
        // MARK: Parsed Properties
        // 仅Vsersion1.0使用-----------
        public var groupId: Int64?
        public var imMsgType: BuzAPNs.ImMessageType = .unknow
        public var name: String?
        public var portrait: String?
        public var serverPortrait: String?
        public var sound: Int?
        public var ntpTime: Int64?
        
        // Vsersion1.0 与Vsersion2.0通用----
        public var voiceFilterId: Int64?
        public var reactionType: BuzAPNs.ReactionType = .none
        public var reactionOpUserId: String?
        public var reactionEmoji: String?
        public var fileName: String?
        public var voicemojiIcon: String?
        public var textMentioned: String?
        public var mentionMap: [String:String]?
        
        public var lastApplyTime: Int64?
        public var mentionedUsers:[String]?
        public var businessType : BuzAPNs.CommandBusinessType?
        public var subBusinessType : BuzAPNs.CommandSubBusinessType?
        
        ///livePlace
        public var placeName: String?
        public var topic: String?
        public var channelType : BuzAPNs.PushExtraChannelType?
        
        public convenience init() {
            self.init(received: nil)
        }
        
        public required init(received rawData: [AnyHashable : Any]?) {
            self.receivedRawData = rawData
            BuzAPNsPushLog.info("BuzAPNs.ActionPayload.PushExtra initreceived rawData: \(rawData ?? [:])")
            
            if let value = rawData?["groupId"] as? Int64 {
                self.groupId = value
            }
            if let value = rawData?["imMsgType"] as? Int, let type = BuzAPNs.ImMessageType.init(rawValue: value) {
                self.imMsgType = type
            }
            if let value = rawData?["name"] as? String {
                self.name = value
            }
            if let value = rawData?["portrait"] as? String {
                self.portrait = value
            }
            if let value = rawData?["serverPortrait"] as? String {
                self.serverPortrait = value
            }
            if let value = rawData?["sound"] as? Int {
                self.sound = value
            }
            if let value = rawData?["ntpTime"] as? Int64 {
                self.ntpTime = value
            }
            if let value = rawData?["filterId"] as? Int64 {
                self.voiceFilterId = value
            }
            if let value = rawData?["reactionType"] as? Int,
                let type = BuzAPNs.ReactionType.init(rawValue: value) {
                self.reactionType = type
            }else if let value = rawData?["reactionType"] as? String,
                        let iValue = Int(value),
                        let type = BuzAPNs.ReactionType.init(rawValue: iValue) {
                self.reactionType = type
            }
            if let value = rawData?["reactionEmoji"] as? String {
                self.reactionEmoji = value
            }
            if let value = rawData?["reactionOpUserId"] as? String {
                self.reactionOpUserId = value
            }
            if let value = rawData?["voicemojiIcon"] as? String {
                self.voicemojiIcon = value
            }
            if let value = rawData?["lastApplyTime"] as? Int64 {
                self.lastApplyTime = value
            }
            if let value = rawData?["mentionedUsers"] as? [String] {
                self.mentionedUsers = value
            }
            if let value = rawData?["businessType"] as? Int, let type = BuzAPNs.CommandBusinessType.init(rawValue: value) {
                self.businessType = type
            }
            if let value = rawData?["subBusinessType"] as? Int, let type = BuzAPNs.CommandSubBusinessType.init(rawValue: value) {
                self.subBusinessType = type
            }
            if let placeName = rawData?["placeName"] as? String {
                self.placeName = placeName
            }
            if let topic = rawData?["topic"] as? String {
                self.topic = topic
            }
            if let fileName = rawData?["fileName"] as? String {
                self.fileName = fileName
            }
            if let value = rawData?["channelType"] as? Int32 , let type = BuzAPNs.PushExtraChannelType.init(rawValue: value) {
                self.channelType = type
            }
            
            if let textMentioned = rawData?["textMentioned"] as? String {
                self.textMentioned = textMentioned
            }
            
            if let mentionMap = rawData?["mentionMap"] as? [String:String] {
                self.mentionMap = mentionMap
            }
        }
        
        public func dictionary() -> [String: Any]? {
            var dict: [String: Any] = .init()
            
            if let groupId = self.groupId {
                dict["groupId"] = groupId
            }
            if self.imMsgType != .unknow {
                dict["imMsgType"] = imMsgType.rawValue
            }
            if let name = self.name {
                dict["name"] = name
            }
            if let portrait = self.portrait {
                dict["portrait"] = portrait
            }
            if let serverPortrait = self.serverPortrait {
                dict["serverPortrait"] = serverPortrait
            }
            if let sound = self.sound {
                dict["sound"] = sound
            }
            if let ntpTime = self.ntpTime {
                dict["ntpTime"] = ntpTime
            }
            if let voiceFilterId = self.voiceFilterId {
                dict["filterId"] = voiceFilterId
            }
            if self.reactionType != .none {
                dict["reactionType"] = reactionType.rawValue
            }
            if let reactionEmoji = self.reactionEmoji {
                dict["reactionEmoji"] = reactionEmoji
            }
            if let reactionOpUserId = self.reactionOpUserId {
                dict["reactionOpUserId"] = reactionOpUserId
            }
            if let voicemojiIcon = self.voicemojiIcon {
                dict["voicemojiIcon"] = voicemojiIcon
            }
            if let textMentioned = self.textMentioned {
                dict["textMentioned"] = textMentioned
            }
            if let mentionMap = self.mentionMap {
                dict["mentionMap"] = mentionMap
            }
            if let fileName = self.fileName {
                dict["fileName"] = fileName
            }
            
            if !dict.keys.isEmpty {
                return dict
            }
            return nil
        }
    }
}
