//
//  BuzApnsPushPayload.swift
//  buz
//
//  Created by st.chio on 2024/12/10.
//  Copyright © 2024 lizhi. All rights reserved.
//

/**
 IM远程推送格式
 {
     aps = {
         alert = {
             groupId = "2550211048792146298443";
             key = "{"action":{"IM5":{"msgType":1,"targetId":"5255237933025205887","report":"{\"p\":1657595652935,\"a\":204922695602143234,\"c\":5}","svrMsgId":"211048792146298443","convType":1,"fromId":"5256013909067827327"},"appData":"{\n  \"title\" : \"ssss rrr\",\n  \"router\" : {\n    \"scheme\" : \"im\\/privatechat\",\n    \"extraData\" : {\n      \"userId\" : 5256013909067827327\n    }\n  },\n  \"pushContent\" : \"test\"\n}"}}";
             loc-key = "test";
             pushType = 0;
             title = "ssss rrr";
             type = 20;
         };
         badge = 0;
         sound = "default";
         token = "dbc593e9e494afeedb14f35e0f3e6856ed6d46ce7b16e914f997cdb3024a7143";
         groupId = "2550211048792146298443";
         mutable-content = 1;
         messageType = 1;
     };
 }
 */


public extension BuzAPNs {
    class RemotePushPayload: BuzAPNsPushPayloadable {
        // MARK: Used to mark the unparsed data of the received remote notification
        public private(set) var receivedRawData: [AnyHashable: Any]?
        
        // MARK: Parsed Properties
        public var aps: APS?
        
        public required init(received rawData: [AnyHashable: Any]?) {
            self.receivedRawData = rawData
            if let aps = rawData?["aps"] as? [String: Any] {
                self.aps = APS.init(received: aps)
            }
        }
    }
}


public extension BuzAPNs.RemotePushPayload {
    
    class APS: BuzAPNsPushPayloadable {
        // MARK: Used to mark the unparsed data of the received remote notification
        public private(set) var receivedRawData: [AnyHashable: Any]?
        
        // MARK: Parsed Properties
        public private(set) var category: String?
        public private(set) var deviceId: String?
        public private(set) var badge: Int?
        public private(set) var userId: String?
        public private(set) var mutable_content: Int?
        public private(set) var token: String?
        public private(set) var messageType: Int?
        public private(set) var alert: Alert?
        public private(set) var groupId: String?
        
        public required init(received rawData: [AnyHashable: Any]?) {
            self.receivedRawData = rawData
            if let value = rawData?["category"] as? String {
                self.category = value
            }
            if let value = rawData?["deviceId"] as? String {
                self.deviceId = value
            }
            if let value = rawData?["badge"] as? Int {
                self.badge = value
            }
            if let value = rawData?["mutable-content"] as? Int {
                self.mutable_content = value
            }
            if let value = rawData?["token"] as? String {
                self.token = value
            }
            if let value = rawData?["messageType"] as? Int {
                self.messageType = value
            }
            if let value = rawData?["alert"] as? [String: Any] {
                self.alert = Alert.init(received: value)
            }
            if let value = rawData?["groupId"] as? String {
                self.groupId = value
            }
        }
    }
    
    class Alert: BuzAPNsPushPayloadable {
        // MARK: Used to mark the unparsed data of the received remote notification
        public private(set) var receivedRawData: [AnyHashable: Any]?
        
        // MARK: Parsed Properties
        public private(set) var groupId: String?
        public private(set) var key: [String: Any]?
        public private(set) var loc_key: String?
        public private(set) var pushType: Int?
        public private(set) var title: String?
        public private(set) var type: Int?
        
        public required init(received rawData: [AnyHashable: Any]?) {
            self.receivedRawData = rawData
            if let value = rawData?["groupId"] as? String {
                self.groupId = value
            }
            if let value = rawData?["key"] as? String, let key = value.toDict() {
                self.key = key
            }
            if let value = rawData?["key"] as? [String: Any] {
                self.key = value
            }
            if let value = rawData?["loc-key"] as? String {
                self.loc_key = value
            }
            if let value = rawData?["pushType"] as? Int {
                self.pushType = value
            }
            if let value = rawData?["title"] as? String {
                self.title = value
            }
            if let value = rawData?["type"] as? Int {
                self.type = value
            }
        }
    }

}
