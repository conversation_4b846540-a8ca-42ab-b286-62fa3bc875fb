//
//  BuzAPNs.AppDataV1.swift
//  buz
//
//  Created by st.chio on 2024/12/9.
//  Copyright © 2024 lizhi. All rights reserved.
//

import BuzLog

// MARK: ---BuzAPNs.ActionPayload.IM5--
public extension BuzAPNs.ActionPayload {
    
    class IM5V1: BuzAPNsPushPayloadable {
        // MARK: Used to mark the unparsed data of the received remote notification
        public private(set) var receivedRawData: [AnyHashable: Any]?
        
        // MARK: Parsed Properties
        public var msgType: Int?
        public var convType: Int?
        public var fromId: String?
        public var targetId: String?
        public var svrMsgId: String?
        public var report: [String: Any]?
        
        public required init(received rawData: [AnyHashable : Any]?) {
            self.receivedRawData = rawData
            BuzAPNsPushLog.info("BuzAPNs.ActionPayload.IM5V1 initreceived rawData")
            if let type = rawData?["msgType"] as? Int {
                self.msgType = type
            }
            if let convType = rawData?["convType"] as? Int {
                self.convType = convType
            }
            if let fromId = rawData?["fromId"] as? String {
                self.fromId = fromId
            }
            if let targetId = rawData?["targetId"] as? String {
                self.targetId = targetId
            }
            if let svrMsgId = rawData?["svrMsgId"] as? String {
                self.svrMsgId = svrMsgId
            }
            if let report = rawData?["report"] as? [String: Any] {
                self.report = report
            }
        }
    }
}

extension BuzAPNs.ActionPayload.IM5V1 {
    
    var messageType: BuzAPNs.ImMessageType {
        if let type = self.msgType,
            let msgType = BuzAPNs.ImMessageType(rawValue: type) {
            return msgType
        }
        return .unknow
    }
    
    var isRecallMessage: Bool {
        if self.messageType == .notifyRecall {
            return true
        }
        return false
    }
    
    var isWalkieTalkieMsgType: Bool {
        if self.messageType == .voice {
            return true
        }
        return false
    }
    
    var isQuickReact: Bool {
        if self.messageType == .reactionChanged {
            return true
        }
        return false
    }
}

// MARK: ---BuzAPNs.ActionPayload.AppDataV1---
public extension BuzAPNs.ActionPayload {
    
    //v1.0，仅客户端构push数据使用
    class AppDataV1: BuzAPNsPushPayloadable {
        // MARK: Used to mark the unparsed data of the received remote notification
        public private(set) var receivedRawData: [AnyHashable: Any]?
        
        // MARK: Parsed Properties
        public var im5: IM5V1?
        public private(set) var traceId: String?
        
        // MARK: AppData------
        public var pushType: BuzAPNs.PushType = .none
        public var title: String?
        public var pushContent: String?
        public var router: BuzAPNs.PushPayload.Router?
        public var senderInfo: BuzAPNs.ActionPayload.SenderInfo?
        public var pushExtra: BuzAPNs.ActionPayload.PushExtra?
        public var recallOrgSvrMsgId: Int64?
        
        public var titleReplaceInfo: [String: Any]?
        public var contentReplaceInfo: [String: Any]?
        public var groupBaseInfo: [String: Any]?
        
        public convenience init() {
            self.init(received: nil)
        }
        
        public required init(received rawData: [AnyHashable : Any]?) {
            
            var data = rawData ?? .init()
            if let action = rawData?["action"] as? [String: Any] {
                data = action
            }
            BuzAPNsPushLog.info("BuzAPNs.ActionPayloadV1 initreceived action")
            
            if let im5 = data["IM5"] as? [String: Any] {
                self.im5 = BuzAPNs.ActionPayload.IM5V1.init(received: im5)
            }
            if let traceId = data["traceId"] as? String {
                self.traceId = traceId
            }
            
            if let appData = data["appData"] as? String, let appDataDict = appData.toDict() {
                self.parsedAppData(received: appDataDict)
            }else if let appData = data["appData"] as? [String: Any] {
                self.parsedAppData(received: appData)
            }
        }
        
        func parsedAppData(received rawData: [AnyHashable: Any]?) {
            self.receivedRawData = rawData
            BuzAPNsPushLog.info("BuzAPNs.ActionPayload.AppDataV1 initreceived rawData")
            
            if let type = rawData?["type"] as? Int32 {
                self.pushType = BuzAPNs.PushType.init(rawValue: type) ?? .none
            }
            if let title = rawData?["title"] as? String {
                self.title = title
            }
            if let pushContent = rawData?["pushContent"] as? String {
                self.pushContent = pushContent
            }
            if let router = rawData?["router"] as? [String: Any] {
                self.router = BuzAPNs.PushPayload.Router.init(received: router)
            }
            if let senderUserInfo = rawData?["senderUserInfo"] as? [String: Any] {
                self.senderInfo = BuzAPNs.ActionPayload.SenderInfo.init(received: senderUserInfo)
            }
            if let pushExtra = rawData?["pushExtra"] as? [String: Any] {
                self.pushExtra = BuzAPNs.ActionPayload.PushExtra.init(received: pushExtra)
            }
            if let value = rawData?["RecallOrgSvrMsgId"] as? Int64 {
                self.recallOrgSvrMsgId = value
            }
            if let value = rawData?["titleReplaceInfo"] as? [String: Any] {
                self.titleReplaceInfo = value
            }
            if let value = rawData?["contentReplaceInfo"] as? [String: Any] {
                self.contentReplaceInfo = value
            }
            if let value = rawData?["groupBaseInfo"] as? [String: Any] {
                self.groupBaseInfo = value
            }
        }
        
        public func dictionary() -> [String: Any]? {
            var dict: [String: Any] = .init()
            
            if self.pushType != .none {
                dict["type"] = pushType.rawValue
            }
            if let title = self.title {
                dict["title"] = title
            }
            if let pushContent = self.pushContent {
                dict["pushContent"] = pushContent
            }
            if let router = self.router {
                dict["router"] = router.dictionary()
            }
            if let senderInfo = self.senderInfo, let toDict = senderInfo.dictionary() {
                dict["senderUserInfo"] = toDict
            }
            if let pushExtra = self.pushExtra, let toDict = pushExtra.dictionary() {
                dict["pushExtra"] = toDict
            }
            
            if !dict.keys.isEmpty {
                return dict
            }
            return nil
        }
    }
}


