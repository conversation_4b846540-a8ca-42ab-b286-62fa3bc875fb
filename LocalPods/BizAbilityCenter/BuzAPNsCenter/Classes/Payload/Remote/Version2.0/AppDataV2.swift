//
//  BuzAPNs.ActionPayload.swift
//  buz
//
//  Created by st.chio on 2024/12/9.
//  Copyright © 2024 lizhi. All rights reserved.
//

import BuzLog

// MARK: ---BuzAPNs.ActionPayload.AppData---
public extension BuzAPNs.ActionPayload {
    
    //v1.0，仅客户端构push数据使用
    class AppDataV2: BuzAPNsPushPayloadable {
        private(set) var appDataV1: AppDataV1?
        
        // MARK: Used to mark the unparsed data of the received remote notification
        public private(set) var receivedRawData: [AnyHashable: Any]?
        
        // MARK: Parsed Properties
        public private(set) var traceId: String?
        //appdata------
        public var title: String?
        public var pushContent: String?
        
        public var pushType: BuzAPNs.PushType = .none
        public var router: BuzAPNs.PushPayload.Router?
        public var senderInfo: SenderInfo?
        
        public var imInfo: IMInfo?
        public var groupInfo: GroupInfo?
        public var pushExtra: PushExtra?
        
        public var titleReplaceInfo: [String: Any]?
        public var contentReplaceInfo: [String: Any]?
        
        convenience init(conversion v1: AppDataV1) {
            self.init(received: nil)
            self.appDataV1 = v1
            self.parsedAppData(v1: v1)
        }
        
        public required init(received rawData: [AnyHashable : Any]?) {
            self.receivedRawData = rawData
            BuzAPNsPushLog.info("BuzAPNs.ActionPayload.AppDataV2 initreceived rawData: \(rawData ?? [:])")
            
            if let value = rawData?["traceId"] as? String {
                self.traceId = value
            }
            if let value = rawData?["type"] as? Int32 {
                self.pushType = BuzAPNs.PushType.init(rawValue: value) ?? .none
            }
            if let value = rawData?["type"] as? Int64 {
                self.pushType = BuzAPNs.PushType.init(rawValue: Int32(value)) ?? .none
            }
            if let value = rawData?["router"] as? [String: Any] {
                self.router = BuzAPNs.PushPayload.Router.init(received: value)
            }
            if let value = rawData?["senderUserInfo"] as? [String: Any] {
                self.senderInfo = SenderInfo.init(received: value)
            }
            
            if let value = rawData?["imInfo"] as? [String: Any] {
                self.imInfo = IMInfo.init(received: value)
            }
            if let value = rawData?["groupInfo"] as? [String: Any] {
                self.groupInfo = GroupInfo.init(received: value)
            }
            if let value = rawData?["pushExtra"] as? [String: Any] {
                self.pushExtra = PushExtra.init(received: value)
            }
            
            if let value = rawData?["titleReplaceInfo"] as? [String: Any] {
                self.titleReplaceInfo = value
            }
            if let value = rawData?["contentReplaceInfo"] as? [String: Any] {
                self.contentReplaceInfo = value
            }
        }
    }
}

public extension BuzAPNs.ActionPayload.AppDataV2 {
    
    //verson1数据转换为sersion2数据
    func parsedAppData(v1: BuzAPNs.ActionPayload.AppDataV1) {
        self.traceId = v1.traceId
        self.pushType = v1.pushType
        self.router = v1.router
        self.senderInfo = v1.senderInfo
        self.pushExtra = v1.pushExtra
        //--------groupInfo----
        do{
            if let groupBaseInfo = v1.groupBaseInfo {
                let groupId = groupBaseInfo["groupId"] as? Int64
                let groupName = groupBaseInfo["groupName"] as? String
                let serverPortraitUrl = groupBaseInfo["serverPortraitUrl"] as? String
                self.groupInfo = .init(groupId: groupId, name: groupName, portrait: serverPortraitUrl)
            }else if let pushExtra = v1.pushExtra {
                self.groupInfo = .init(groupId: pushExtra.groupId, name: pushExtra.name, portrait: pushExtra.serverPortrait ?? pushExtra.portrait)
            }
        }
        //--------imInfo----
        do{
            self.imInfo = BuzAPNs.ActionPayload.IMInfo.init()
            
            self.imInfo?.msgType = BuzAPNs.ImMessageType.init(rawValue: v1.im5?.msgType ?? -1) ?? .unknow
            self.imInfo?.convType = v1.im5?.convType
            if let value = v1.im5?.svrMsgId, let svrMsgId = Int64(value) {
                self.imInfo?.svrMsgId = svrMsgId
            }
            if let im5 = v1.im5, im5.isRecallMessage {
                self.imInfo?.orgSvrMsgId = v1.recallOrgSvrMsgId
            }
            if  self.pushExtra?.reactionType == .quickReact_voicemoji {
                if let serMsgId = router?.extraData?["serMsgId"] as? Int64 {
                    self.imInfo?.orgSvrMsgId = serMsgId
                }else if let serMsgId = router?.extraData?["serMsgId"] as? String {
                    self.imInfo?.orgSvrMsgId = Int64(serMsgId)
                }
            }
        }
        
        //-----else----
        self.titleReplaceInfo = v1.titleReplaceInfo
        self.contentReplaceInfo = v1.contentReplaceInfo
    }
}

public extension BuzAPNs.ActionPayload {
    
    class IMInfo: BuzAPNsPushPayloadable {
        // MARK: Used to mark the unparsed data of the received remote notification
        public private(set) var receivedRawData: [AnyHashable: Any]?
        
        // MARK: Parsed Properties
        public var msgType: BuzAPNs.ImMessageType = .unknow
        public var convType: Int?
        public var svrMsgId: Int64?
        
        public var orgSvrMsgId: Int64?
        
        public convenience init() {
            self.init(received: nil)
        }
        
        public required init(received rawData: [AnyHashable : Any]?) {
            self.receivedRawData = rawData
            BuzAPNsPushLog.info("BuzAPNs.ActionPayload.IMInfo initreceived rawData: \(rawData ?? [:])")
            
            if let value = rawData?["msgType"] as? Int, let type = BuzAPNs.ImMessageType.init(rawValue: value) {
                self.msgType = type
            }
            if let value = rawData?["convType"] as? Int {
                self.convType = value
            }
            if let value = rawData?["svrMsgId"] as? Int64 {
                self.svrMsgId = value
            }
            if let value = rawData?["orgSvrMsgId"] as? Int64 {
                self.orgSvrMsgId = value
            }
        }
    }
}

public extension BuzAPNs.ActionPayload.IMInfo {
    
    var isRecallMessage: Bool {
        if self.msgType == .notifyRecall {
            return true
        }
        return false
    }
    
    var isWalkieTalkieMsgType: Bool {
        if self.msgType == .voice {
            return true
        }
        return false
    }
    
    var isQuickReact: Bool {
        if self.msgType == .reactionChanged {
            return true
        }
        return false
    }
}

// MARK: ---BuzAPNs.ActionPayload.GroupInfo
public extension BuzAPNs.ActionPayload {
    
    class GroupInfo: BuzAPNsPushPayloadable {
        // MARK: Used to mark the unparsed data of the received remote notification
        public private(set) var receivedRawData: [AnyHashable: Any]?
        
        // MARK: Parsed Properties
        public var groupId: Int64?
        public var name: String?
        public var portrait: String?
        
        public init(groupId: Int64?, name: String?, portrait: String?) {
            self.groupId = groupId
            self.name = name
            self.portrait = portrait
        }
        
        public required init(received rawData: [AnyHashable : Any]?) {
            self.receivedRawData = rawData
            BuzAPNsPushLog.info("BuzAPNs.ActionPayload.GroupInfo initreceived rawData: \(rawData ?? [:])")
            
            if let value = rawData?["groupId"] as? Int64 {
                self.groupId = value
            }
            if let value = rawData?["name"] as? String {
                self.name = value
            }
            if let value = rawData?["portrait"] as? String {
                self.portrait = value
            }
        }
    }
}
