//
//  BuzAPNsDefine.swift
//  buz
//
//  Created by st.chio on 2024/12/20.
//  Copyright © 2024 lizhi. All rights reserved.
//

public class BuzAPNs { }

public extension BuzAPNs {
    
    // MARK: ---APNs Push Mode------
    enum PushMode: Int32 {
        case remote = 0
        case local = 1
    }
}

public extension BuzAPNs {
    // MARK: ---APNs Push Type------
    enum PushType: Int32 {
        case none = -1
        // MARK: ---remotePush type------
        case privateChat = 1       //私聊
        case groupChat = 2         //群聊
        case invitedUserOnline = 9 //邀请的好友开启对讲机
        case invitedGroupOnline = 10 //邀请的群组成员开启对讲机
        case friendApply = 11 // 收到好友申请
        case friendApplyPassed = 12 // 收到申请已通过
        case feedback = 15 // 收到客服反馈通知
        case quickReactVoicemoji = 16
        case registeredFriendRecommend = 27 // 已注册联系人推荐
        case registerInvite = 28 // 注册邀请
        case registerCaptchaInvite = 29 // 注册验证码要求
        case privateLivePlaceInvite = 31 // 个人LivePlace邀请
        case groupLivePlaceInvite = 32 // 群LivePlace邀请
        case livePlaceCancel = 33 // 取消呼叫
        case privateLivePlaceOpen = 34 // 个人LivePlace开启通知
        case groupLivePlaceOpen = 35 // 群LivePlace开启通知
        case privateLivePlaceKnock = 36 //个人livePlace敲门knock通知
        
        // MARK: ---localPush type------
        
    }
}

public extension BuzAPNs {
    // MARK: ---ImMessageType------
    enum ImMessageType: Int {
        case decryptFailed = -2
        case unknow = -1
        
        case text = 1
        case voice = 2  // 语音
        case image = 3
        case video = 5
        case recalled = 99
        case notifyRecall = 100
        
        case unread = 1023
        case robotThinking = 1024
        
        case reactionChanged = 5105
        case rtcVoice = 6000
        
        case command = 10003
        case leaveVoice = 10004 // 也是语音
        case voiceText = 10005
        case newVoiceText = 10007
        case voicEmoji = 10008
        case localTip = 10009
        case location = 10010
        case mediaText = 10011
        case mediaActionText = 100111
        case compatibleVoiceEmoji = 10012
        case livePlaceShareCard = 10017
        case voicegif = 10016
        case callCard = 10019
        case fileAttachment = 10020
    }
    
    enum CommandBusinessType : Int {
        case none = -1
        case onAir = 6
        case livePlace = 7
    }

    enum CommandSubBusinessType : Int {
        case none = -1
        case onAirStart = 9
        case onAirEnd = 10
        case onAirMissed = 11
        case onAirLineBusy = 12
        
        case LivePlaceStart = 13
        case LivePlaceEnd = 14
        case LivePlaceMissed = 15
        case LivePlaceLineBusy = 16
        case LivePlaceUpdate = 17
        case LivePlaceCreated = 18
    }
    
    enum PushExtraChannelType : Int32 {
        case none = -1
        case livePlacePeer = 5
        case livePlaceGroup = 6
    }

}

public extension BuzAPNs {
    enum ReactionType : Int {
        case none = -1
        case quickReact_voicemoji = 1
    }
}
