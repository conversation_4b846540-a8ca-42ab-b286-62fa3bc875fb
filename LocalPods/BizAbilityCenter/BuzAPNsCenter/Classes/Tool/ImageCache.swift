//
//  BuzCoreCenter.swift
//  buz
//
//  Created by st.chio on 2024/12/18.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation
import Intents
import SDWebImage

public extension BuzAPNs {
    class ImageCache{}
}

extension BuzAPNs.ImageCache {
    
    static func downImageFromUrl(url: URL, defaultImage: INImage, isShowGreenCircle : Bool = false,completion: ((INImage) -> Void)? = nil) {
        guard let completion else {
            return
        }

        SDWebImageManager.shared.loadImage(with: url, progress: nil) { img, _, _, _, _, _ in
            if let image = img,let image = Self.mergeImagesFromColor(portraitImage: image, backgroundColor: UIColor.white.withAlphaComponent(0.2)) , let png = image.pngData() {
                if isShowGreenCircle , let stateImage = UIImage(named: "livePlace_State_inNotification"){
                    let icon = Self.mergeImages(portraitImage: image, stateImage: stateImage)
                    if let pngData = icon?.pngData(){
                        let avatar = INImage(imageData: pngData)
                        completion(avatar)
                    }else{
                        let avatar = INImage(imageData: png)
                        completion(avatar)
                    }
                }else{
                    let avatar = INImage(imageData: png)
                    completion(avatar)
                }
            } else {
                completion(defaultImage)
            }
        }
    }

    static func getUrlFromSize(url: String, size: Int) -> String? {
        if url.count > 0 {
            var imageUrl = url
            let subStr = "."
            if let range = imageUrl.range(of: subStr, options: .backwards) { /// 地址图片大小限制
                let startIndex = imageUrl.distance(from: imageUrl.startIndex, to: range.lowerBound)
                let strToInsert = "_\(size)x\(size)"
                let indexToInsertAt = imageUrl.index(imageUrl.startIndex, offsetBy: startIndex)
                let charactersToInsert = Array(strToInsert)
                for char in charactersToInsert.reversed() {
                    imageUrl.insert(char, at: indexToInsertAt)
                }
                return imageUrl
            }
            return nil
        }
        return nil
    }

    static func getNotificationAttachmentWithImageUrl(url: URL, withContentHandler contentHandler: ((UNNotificationAttachment?) -> Void)?) {
        guard let contentHandler = contentHandler else {
            return
        }

        let ext = (url.absoluteString as NSString).pathExtension
        let cacheURL = FileManager.cacheDir()
        let localUrl = cacheURL.appendingPathComponent(url.absoluteString.md5).appendingPathExtension(ext)

        SDWebImageManager.shared.loadImage(with: url, progress: nil) { image, _, _, _, _, _ in

            guard let data = image?.pngData() else {
                contentHandler(nil)
                return
            }

            guard let _ = try? data.write(to: localUrl) else {
                contentHandler(nil)
                return
            }

            let attachment = try? UNNotificationAttachment(identifier: "UNNotificationAttachment.Private.Identifier", url: localUrl, options: nil)
            contentHandler(attachment)
        }
    }
    
    static func mergeImages(portraitImage: UIImage, stateImage: UIImage) -> UIImage? {
        
        // 创建一个图形上下文
        let size = CGSize(width: max(portraitImage.size.width, stateImage.size.width), height: max(portraitImage.size.height, stateImage.size.height))
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        
        // 绘制第一张图片
        portraitImage.draw(in: CGRect(origin: .zero, size: size))
        // 绘制第二张图片
        stateImage.draw(in: CGRect(origin: .zero, size: size))
        
        // 从上下文中获取合成后的图片
        let mergedImage = UIGraphicsGetImageFromCurrentImageContext()
        
        // 结束图形上下文
        UIGraphicsEndImageContext()
        
        return mergedImage
    }
    
    static func mergeImagesFromColor(portraitImage: UIImage, backgroundColor: UIColor) -> UIImage? {
        
        // 创建一个图形上下文
        let size = CGSize(width: portraitImage.size.width, height: portraitImage.size.height)
        UIGraphicsBeginImageContextWithOptions(size, false, 0.0)
        
        guard let context = UIGraphicsGetCurrentContext() else {
            return nil
        }
        // 绘制背景颜色
        let rect = CGRect(origin: .zero, size: size)
        context.setFillColor(backgroundColor.cgColor)
        context.fill(rect)
        
        // 绘制第一张图片
        portraitImage.draw(in: CGRect(origin: .zero, size: size))
        // 从上下文中获取合成后的图片
        let mergedImage = UIGraphicsGetImageFromCurrentImageContext()
        
        // 结束图形上下文
        UIGraphicsEndImageContext()
        
        return mergedImage
    }
}

extension FileManager {
    class func cacheDir() -> URL {
        let dirPaths = NSSearchPathForDirectoriesInDomains(.cachesDirectory, .userDomainMask, true)
        let cacheDir = dirPaths[0] as String
        return URL(fileURLWithPath: cacheDir)
    }
}
