//
//  BuzAPNsCenter.swift
//  buz
//
//  Created by lizhi on 2023/5/17.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import Intents
import BuzDataShareKit
import BuzLog


public extension BuzAPNsCenter {
    
    static func communicationContent(params: BuzAPNs.ContentParams, completion: ((UNMutableNotificationContent) -> Void)? = nil) {
   
        let onAction = { (avatar: INImage) -> UNMutableNotificationContent in
            params.icon = avatar
            return BuzAPNs.Communication.content(params: params)
        }
        
        if #available(iOSApplicationExtension 15.0, *) {
            if params.iconUrl?.count ?? 0 <= 0 { /// 没有地址，从本地缓存中获取
                let contactsPortraitMap: [Int64: [String: String]] = PushShareDataTool.loadGroupsMap()
                if let identifier = params.identifier,
                    let groupId = Int64(identifier),
                    let groupInfoMap = contactsPortraitMap[groupId],
                    let portrait = groupInfoMap[PushShareDataKey.groupInfoPortraitKey.rawValue],
                    portrait.count > 0 {
                    params.iconUrl = portrait
                }
            }

            if let portrait = params.iconUrl, portrait.count > 0, let url = URL(string: portrait) {
                
                if let portraitUrl = BuzAPNs.ImageCache.getUrlFromSize(url: portrait, size: 120),
                   let smallSizeUrl = URL(string: portraitUrl) {
                    BuzLog.debug("download portrait:\(smallSizeUrl)")
                    BuzAPNs.ImageCache.downImageFromUrl(url: smallSizeUrl, defaultImage: params.pushType.defaultIcon , isShowGreenCircle: params.isShowGreenCircle) { image in
                        BuzLog.debug("download portrait finished")
                        DispatchQueue.global().async  {
                            completion?(onAction(image))
                        }
                        
                        
                    }
                } else {
                    BuzLog.debug("download portrait:\(url)")
                    BuzAPNs.ImageCache.downImageFromUrl(url: url, defaultImage: params.pushType.defaultIcon , isShowGreenCircle: params.isShowGreenCircle) { image in
                        BuzLog.debug("download portrait finished")
                        DispatchQueue.global().async  {
                            completion?(onAction(image))
                        }
                    }
                }
            } else {
                BuzLog.debug("default portrait")
                DispatchQueue.global().async  {
                    completion?(onAction(params.icon)) /// 灰色默认不生效，会使用系统默认的
                }
                
            }
        } else {
            BuzLog.debug("do not modify portrait")
            DispatchQueue.global().async {
                completion?(params.content)
            }
        }
    }

}
