//
//  BuzCoreCenter.swift
//  buz
//
//  Created by st.chio on 2024/12/18.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation
import Intents
import BuzLog
import BuzConfig

public extension BuzAPNs.PushType {
    var defaultIcon: INImage {
        switch self {
        case .groupChat:
            return INImage(named: "qr_default_group_portrait")
        default:
            return INImage(named: "qr_default_portrait")
        }
    }
}

public extension BuzAPNs {
    
    class ContentParams {
        public var content: UNMutableNotificationContent
        
        public var mode: BuzAPNs.PushMode = .remote
        public var pushType: BuzAPNs.PushType = .none
        
        public var identifier: String?
        
        public var senderName: String
        public var iconUrl: String?
        public var icon: INImage
        
        public var groupName: String = ""
        
        public var isShowGreenCircle: Bool = false
        
        public var categoryIdentifier: String = BuzConfig.notificationCategoryIdentifier
        
        public init(content: UNMutableNotificationContent,
                    mode: BuzAPNs.PushMode = .remote,
                    pushType: BuzAPNs.PushType,
                    identifier: String? = nil,
                    senderName: String = "",
                    iconUrl: String? = nil,
                    groupName: String? = "",
                    isShowGreenCircle : Bool = false) {
            self.content = content
            self.mode = mode
            self.pushType = pushType
            self.identifier = identifier
            
            self.senderName = senderName
            self.iconUrl = iconUrl
            self.icon = self.pushType.defaultIcon
            
            self.groupName = groupName ?? ""
            self.isShowGreenCircle = isShowGreenCircle
        }
    }
}

extension BuzAPNs {
    class Communication {}
}

extension BuzAPNs.Communication {
    
    static func content(params: BuzAPNs.ContentParams) -> UNMutableNotificationContent {
        if #available(iOS 15.0, *) {
            let formatter = PersonNameComponentsFormatter()
            let nameComponents = formatter.personNameComponents(from: params.senderName)
            
            let sender = INPerson(
                personHandle: INPersonHandle(value: nil, type: .unknown),
                nameComponents: nameComponents,
                displayName: params.senderName,
                image: params.icon,
                contactIdentifier: params.identifier,
                customIdentifier: params.identifier,
                isMe: false,
                suggestionType: .none
            )
            
            let intent = INSendMessageIntent(recipients: [sender],
                                             outgoingMessageType: .outgoingMessageText,
                                             content: params.content.body,
                                             speakableGroupName: INSpeakableString(spokenPhrase: params.groupName),
                                             conversationIdentifier: params.identifier,
                                             serviceName: nil,
                                             sender: sender,
                                             attachments: nil)
            
            intent.setImage(params.icon, forParameterNamed: \.speakableGroupName)
            
            let interaction = INInteraction(intent: intent, response: nil)
            interaction.direction = .incoming
            interaction.donate(completion: nil)
            do {
                let content = try params.content.updating(from: intent)
                if let content = (content.mutableCopy() as? UNMutableNotificationContent) {
                    content.badge = params.content.badge
                    content.title = params.content.title
                    content.body = params.content.body
                    content.userInfo = params.content.userInfo
                    content.sound = params.content.sound
                    content.threadIdentifier = params.content.threadIdentifier
                    content.attachments = params.content.attachments
                    content.categoryIdentifier = params.content.categoryIdentifier
                    return content
                } else {
                    return params.content
                }
            } catch {
                BuzLog.debug("NotificationContent: \(error.localizedDescription)")
                return params.content
            }
        } else {
            return params.content
        }
    }
}

