#
#  Be sure to run `pod spec lint PCSocialCenter.podspec' to ensure this is a
#  valid spec and to remove all comments including this before submitting the spec.
#
#  To learn more about Podspec attributes see https://guides.cocoapods.org/syntax/podspec.html
#  To see working Podspecs in the CocoaPods repo see https://github.com/CocoaPods/Specs/
#

Pod::Spec.new do |s|
  
  s.name     = "BuzAPNsCenter"
  s.version  = "0.0.1"
  s.summary  = "A short description of BuzAPNsCenter."
  
  s.homepage = "https://gitlab.lizhi.fm/zhiyaPods/BuzAPNsCenter"
  s.license  = { :type => 'Copyright', :file => 'LICENSE' }
  s.author   = { 'st.chio' => '<EMAIL>' }
  s.source   = { :git => "https://gitlab.lizhi.fm/zhiyaPods/BuzAPNsCenter.git", :tag => s.version.to_s }
  
  s.ios.deployment_target = '10.0'
  
  s.pod_target_xcconfig = {'DEFINES_MODULE' => 'YES'}

  s.source_files  = "Classes", "Classes/**/*.{h,m,swift}"
  
  s.subspec 'Define' do |sp|
    sp.source_files = 'Classes/Define/**/*.{swift}'
  end
  
  s.subspec 'Tool' do |sp|
    sp.source_files = 'Classes/Tool/**/*.{swift}'
    sp.dependency 'SDWebImage'
  end
  
  s.subspec 'Service' do |sp|
    sp.source_files = 'Classes/Content/**/*.{swift}'
    
    sp.dependency 'BuzDataShareKit'
  end
  
  s.subspec 'Content' do |sp|
    sp.source_files = 'Classes/Content/**/*.{swift}'
    
    sp.dependency 'BuzConfig'
  end
  
  s.subspec 'WatchExtension' do |sp|
    sp.platforms = { :ios => "10.0", :watchos => "7.0"}
    sp.source_files = ['Classes/Define/**/*.{swift}']
  end
  
  
  s.dependency 'BuzLog'
  
  
end
