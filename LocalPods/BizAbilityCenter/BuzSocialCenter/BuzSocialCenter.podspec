#
#  Be sure to run `pod spec lint PCSocialCenter.podspec' to ensure this is a
#  valid spec and to remove all comments including this before submitting the spec.
#
#  To learn more about Podspec attributes see https://guides.cocoapods.org/syntax/podspec.html
#  To see working Podspecs in the CocoaPods repo see https://github.com/CocoaPods/Specs/
#

Pod::Spec.new do |s|
  
  s.name     = "BuzSocialCenter"
  s.version  = "0.0.1"
  s.summary  = "A short description of BuzSocialCenter."
  
  s.homepage = "https://gitlab.lizhi.fm/zhiyaPods/BuzSocialCenter"
  s.license  = { :type => 'Copyright', :file => 'LICENSE' }
  s.author   = { 'st.chio' => '<EMAIL>' }
  s.source   = { :git => "https://gitlab.lizhi.fm/zhiyaPods/BuzSocialCenter.git", :tag => s.version.to_s }
  
  s.ios.deployment_target = '10.0'
  
  s.pod_target_xcconfig = {'DEFINES_MODULE' => 'YES'}

  s.source_files  = "Classes", "Classes/*.{swift}"
  
  s.subspec 'UserFriends' do |sp|
    sp.source_files = 'Classes/UserFriends/**/*.{h,m,mm,swift}'
    sp.private_header_files = "Classes/**/*/Private/**/*.{h}"
    
    sp.dependency 'ITNetLibrary'
    sp.dependency 'BuzNetworker'
    sp.dependency 'BuzStorage'
    sp.dependency 'BuzDatabase'
    sp.dependency 'BuzDataShareKit'
    sp.dependency 'BuzCenterKit'
    sp.dependency 'BuzUserSession'
    sp.dependency 'BuzUIKit'
    sp.dependency 'BuzLocalizable'
  end
  
  s.subspec 'UserContacts' do |sp|
    sp.source_files = 'Classes/UserContacts/**/*.{h,m,mm,swift}'
    sp.private_header_files = "Classes/**/*/Private/**/*.{h}"
    
    sp.dependency 'ITNetLibrary'
    sp.dependency 'BuzNetworker'
    sp.dependency 'BuzDatabase'
    sp.dependency 'BuzUserSession'
    sp.dependency 'YYModel'
    sp.dependency 'PhoneNumberKit'
  end
  
  s.subspec 'UserIMGroup' do |sp|
    sp.source_files = 'Classes/UserIMGroup/**/*.{h,m,mm,swift}'
    sp.private_header_files = "Classes/**/*/Private/**/*.{h}"
    
    sp.dependency 'ITNetLibrary'
    sp.dependency 'BuzNetworker'
    sp.dependency 'BuzDatabase'
    sp.dependency 'BuzStorage'
    sp.dependency 'BuzUIKit'
    sp.dependency 'BuzDataShareKit'
    sp.dependency 'BuzUserSession'
    sp.dependency 'BuzLocalizable'
  end
  
  s.subspec 'UserBot' do |sp|
    sp.source_files = 'Classes/UserBot/**/*.{h,m,mm,swift}'
    sp.private_header_files = "Classes/**/*/Private/**/*.{h}"
    
    sp.dependency 'ITNetLibrary'
    sp.dependency 'BuzNetworker'
    sp.dependency 'BuzDatabase'
    sp.dependency 'BuzStorage'
    sp.dependency 'BuzDataShareKit'
    sp.dependency 'BuzUserSession'
    sp.dependency 'YYModel'
    sp.dependency 'BuzCenterKit'
  end
  
  
  s.dependency 'BuzLog'
end
