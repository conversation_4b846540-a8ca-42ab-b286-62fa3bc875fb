//
//  ContactsManager+Combine.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/25.
//

import Combine
import BuzIDL
import BuzCenterKit
import BuzFoundation

public extension BuzCombineKit where Base: ContactsManager {
    // 获取所有联系人(过滤黑名单用户)
    func fetchAllSystemContactsByFilterBlackUser() -> AnyPublisher<[BuzUserContacts], Never> {
        return Future { promise in
            self.object.fetchAllSystemContactsByFilterBlackUser { list in
                promise(.success(list))
            }
        }
        .eraseToAnyPublisher()
    }
    
    // 获取所有联系人
    func fetchAllSystemContacts() -> AnyPublisher<[BuzUserContacts], Never> {
        return Future { promise in
            self.object.fetchAllSystemContacts { list in
                promise(.success(list))
            }
        }
        .eraseToAnyPublisher()
    }
    
    // 获取所有联系人
    func fetchAllContacts() -> AnyPublisher<[BuzUserContacts], Never> {
        return Future { promise in
            self.object.fetchAllContacts { list in
                promise(.success(list))
            }
        }
        .eraseToAnyPublisher()
    }
    
    // 刷新联系人数据
    func reloadAllContacts(isFirst: Bool = false) -> AnyPublisher<Void, Never> {
        return Just(self.object.reloadAllContacts(isFirst: isFirst))
        .eraseToAnyPublisher()
    }
    
    // 缓存联系人信息（手机通讯录的）
    func saveSystemContacts(toMemoryCache list: [BuzUserContacts]) -> AnyPublisher<Void, Never> {
        return Just(self.object.saveSystemContacts(toMemoryCache: list))
        .eraseToAnyPublisher()
    }
    
    // 获取缓存的联系人信息（手机通讯录的）
    func getCachedSystemContactsInfo(phone: String) -> AnyPublisher<BuzUserContacts?, Never> {
        return Just(self.object.getCachedSystemContactsInfo(phone: phone))
            .eraseToAnyPublisher()
    }
    
    // 缓存联系人信息(数据库的)
    func saveDbContacts(toMemoryCache list: [BuzUserContacts]) -> AnyPublisher<Void, Never> {
        return Just(self.object.saveDbContacts(toMemoryCache: list))
        .eraseToAnyPublisher()
    }
    
    // 获取缓存的联系人信息（数据库的）
    func getCachedContactsInfo(phone: String) -> AnyPublisher<BuzUserContacts?, Never> {
        return Just(self.object.getCachedContactsInfo(phone: phone))
            .eraseToAnyPublisher()
    }
    
    // 清除缓存
    func cleanDataWhenLogout() -> AnyPublisher<Void, Never> {
        return Just(self.object.cleanDataWhenLogout())
        .eraseToAnyPublisher()
    }
}

// MARK: - 通讯录过滤
public extension BuzCombineKit where Base: ContactsManager {

    func getFilteredSystemContactsList(subStrings: [String]) -> AnyPublisher<[BuzUserContacts], Never> {
        return Just(self.object.getFilteredSystemContactsList(subStrings: subStrings))
            .eraseToAnyPublisher()
    }
    
    // 包含 formatterPhoneList 的通讯录
    func filteredSystemContacts(formatterPhoneList: [String]) -> AnyPublisher<[BuzUserContacts], Never> {
        return Just(self.object.filteredSystemContacts(formatterPhoneList: formatterPhoneList))
            .eraseToAnyPublisher()
    }
}

// MARK: - 通讯录匹配
public extension BuzCombineKit where Base: ContactsManager {
    // 根据格式化的手机号(xx-xxx)匹配通讯录联系人
    func matchSystemContact(formatterPhone: String) -> AnyPublisher<BuzUserContacts?, Never> {
        return Just(self.object.matchSystemContact(formatterPhone: formatterPhone))
            .eraseToAnyPublisher()
    }
}

// MARK: - 通讯录同步和上传
public extension BuzCombineKit where Base: ContactsManager {
    // 同步本地通讯录和数据库联系人，并上传到服务端
    func syncContactsToServerIfNeed() -> AnyPublisher<Void, Never> {
        return Just(self.object.syncContactsToServerIfNeed())
        .eraseToAnyPublisher()
    }
}
