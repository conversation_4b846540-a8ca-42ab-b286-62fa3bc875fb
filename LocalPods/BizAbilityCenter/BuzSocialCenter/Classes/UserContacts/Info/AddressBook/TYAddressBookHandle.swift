//
//  TYAddressBookHandle.swift
//  Tiya
//
//  Created by lv<PERSON><PERSON> on 2021/1/7.
//  Copyright © 2021 lizhi. All rights reserved.
//

import UIKit
import Contacts

/// 一个联系人信息模型的闭包
public typealias TYAddressBookModelsClosure = (_ models: [TYAddressBookModel] )->(Void)

/// 授权结果的闭包
public typealias AuthorizationHandle = ()->(Void)


@objc(TYAddressBookHandle)
class TYAddressBookHandle:NSObject {
    
    @objc class func isAuthorization()->(Bool){
        return CNContactStore.authorizationStatus(for: .contacts) == .authorized
    }
    
    @objc class func isCanRequestAuthorization()->(Bool){
        return CNContactStore.authorizationStatus(for: .contacts) == .notDetermined
    }
    
    @objc func requestAuthorization(success: @escaping AuthorizationHandle , failure: AuthorizationHandle? = nil) {
        
        if TYAddressBookHandle.isCanRequestAuthorization() == false {
            return
        }
        
        if #available(iOS 9.0, *) {
            // 没经过授权才需要弹起
            if CNContactStore.authorizationStatus(for: .contacts) != .notDetermined {
                return
            }
  
            contactStore.requestAccess(for: .contacts, completionHandler: { (granted, error) in
                DispatchQueue.main.async {
                    if granted {
                        success()
                    } else {
                        failure?()
                    }
                }
            })
        }
    }
    
    // MARK: - 获取通讯录的函数
    func getAddressBookDataSource(personModel success: TYAddressBookModelsClosure, authorizationFailure failure: AuthorizationHandle) {
        
        // 1.获取授权状态
        let status = CNContactStore.authorizationStatus(for: .contacts)
        // 2.如果没有授权,先执行授权失败的闭包后return
        if status != .authorized {
            failure()
            return
        }
        
        // 3.获取联系人
        // keys决定能获取联系人哪些信息,例:姓名,电话,头像等
        let fetchKeys = [CNContactFormatter.descriptorForRequiredKeys(for: .fullName),CNContactPhoneNumbersKey] as [Any]
        let fetchRequest = CNContactFetchRequest.init(keysToFetch: fetchKeys as! [CNKeyDescriptor]);
                
        var contacts = [CNContact]()
        
        do {
            try contactStore.enumerateContacts(with: fetchRequest) { (contact, stop) in
                contacts.append(contact)
            }
        } catch _ as NSError {
            failure()
            return
        }
                   
        // 3.1遍历联系人
        var result = [TYAddressBookModel]()
        for contact in contacts {
            
            let model = TYAddressBookModel()
            model.name = CNContactFormatter.string(from: contact, style: .fullName) ?? ""
            model.firstName = contact.givenName
            model.lastName = contact.familyName
                        
            // 遍历一个人的所有电话号码
            for labelValue in contact.phoneNumbers {
                let phoneNumber = labelValue.value
                model.mobileArray.append(phoneNumber.stringValue)
            }
            
            result.append(model)
        }
        success(result)
    }
    
    /**
     过滤指定字符串(可自定义添加自己过滤的字符串)
     */
    func removeSpecialSubString(string: String) -> String {
        
        let resultString = string.replacingOccurrences(of: "+86", with: "")
            .replacingOccurrences(of: "-", with: "")
            .replacingOccurrences(of: "(", with: "")
            .replacingOccurrences(of: ")", with: "")
            .replacingOccurrences(of: " ", with: "")
            .replacingOccurrences(of: " ", with: "")
        
        return resultString;
    }
    
    
    // MARK: - lazy
    @available(iOS 9.0, *)
    lazy var contactStore: CNContactStore = {
        let contactStore = CNContactStore()
        return contactStore
    }()
}

