//
//  TYAddressBookModel.swift
//  Tiya
//
//  Created by lv<PERSON><PERSON> on 2021/1/7.
//  Copyright © 2021 lizhi. All rights reserved.
//

import Foundation

@objc(TYAddressBookModel)
public class TYAddressBookModel:NSObject {
     
    public var firstName: String = ""
    
    public var lastName: String = ""
    
    ///格式化后的手机号(86-12345678901)
    var phone: String  = ""
    
    //第一个字母
    var firstLetter : String = ""
    
    /// 联系人头像
//    public var headerImage: UIImage?
        
    /// 联系人姓名
    public var name: String = ""
    
    /// 联系人电话数组,一个联系人可能存储多个号码
    public var mobileArray: [String] = []
    
    public override var description:String {
        return "name = \(name) , mobileArray = \(mobileArray)"
    }
    
}
