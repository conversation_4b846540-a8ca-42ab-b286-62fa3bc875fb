//
//  SystemAddressBook.swift
//  buz
//
//  Created by lidawen on 2022/8/1.
//  Copyright © 2022 lizhi. All rights reserved.
//

import UIKit
import Contacts
import BuzLog

class SystemAddressBook: NSObject {
    public static let shared = SystemAddressBook()

    func addContact(model: BuzUserContacts) -> <PERSON><PERSON> {
        // Create a mutable object to add to the contact
        let contact = CNMutableContact()

        if var phone = model.phone {
            phone = ContactsSyncUtil.formatterPhoneToSystemPhone(originPhone: phone) ?? phone
            contact.phoneNumbers = [CNLabeledValue(
                label: CNLabelPhoneNumberMobile,
                value: CNPhoneNumber(stringValue: phone))]
        }
        
        contact.givenName = model.firstName ?? ""
        contact.familyName = model.lastName ?? ""

        // Save the newly created contact
        let store = CNContactStore()
        let saveRequest = CNSaveRequest()
        saveRequest.add(contact, toContainerWithIdentifier: nil)

        do {
            try store.execute(saveRequest)
            return true
        } catch {
            BuzLog.error("Saving contact failed, error: \(error)")
            // Handle the error
            return false
        }
    }
    
    func addOrUpdateContact(model: BuzUserContacts) -> Bool {
        let store = CNContactStore()
        
        var contact: CNMutableContact?

        if var phone = model.phone {
            phone = ContactsSyncUtil.formatterPhoneToSystemPhone(originPhone: phone) ?? phone
            let predicate = CNContact.predicateForContacts(matching: CNPhoneNumber(stringValue: phone))
            let fetchKeys = [CNContactFormatter.descriptorForRequiredKeys(for: .fullName),CNContactPhoneNumbersKey,CNContactThumbnailImageDataKey] as [Any]
            let item = try? store.unifiedContacts(matching: predicate, keysToFetch: fetchKeys as! [CNKeyDescriptor]).first
            if item != nil {
                contact = item?.mutableCopy() as? CNMutableContact
                contact?.givenName = model.firstName ?? ""
                contact?.familyName = model.lastName ?? ""
            }
        }
        guard let contact = contact  else {
            return addContact(model: model)
        }
        
        let saveRequest = CNSaveRequest()
        saveRequest.update(contact)

        do {
            try store.execute(saveRequest)
            return true
        } catch {
            BuzLog.error("update contact failed, error: \(error)")
            // Handle the error
            return false
        }
    }
    
    func deleteContact(model: BuzUserContacts) -> Bool {
        let store = CNContactStore()
        
        var contact: CNMutableContact?

        if var phone = model.phone {
            phone = ContactsSyncUtil.formatterPhoneToSystemPhone(originPhone: phone) ?? phone
            let predicate = CNContact.predicateForContacts(matching: CNPhoneNumber(stringValue: phone))
            let fetchKeys = [CNContactFormatter.descriptorForRequiredKeys(for: .fullName),CNContactPhoneNumbersKey,CNContactThumbnailImageDataKey] as [Any]
            let item = try? store.unifiedContacts(matching: predicate, keysToFetch: fetchKeys as! [CNKeyDescriptor]).first
            if item != nil {
                contact = item?.mutableCopy() as? CNMutableContact
            }
        }
        guard let contact = contact  else {
            return false
        }
        
        let saveRequest = CNSaveRequest()
        saveRequest.delete(contact)

        do {
            try store.execute(saveRequest)
            return true
        } catch {
            BuzLog.error("delete contact failed, error: \(error)")
            // Handle the error
            return false
        }
    }
}
