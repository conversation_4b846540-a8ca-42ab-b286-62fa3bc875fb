//
//  TYGetAddressBook.swift
//  Tiya
//
//  Created by lv<PERSON><PERSON> on 2021/1/7.
//  Copyright © 2021 lizhi. All rights reserved.
//

import Foundation
import AddressBook
import Contacts
import Localizable
import BuzLocalizable
import BuzUIKit

/// 获取原始顺序的所有联系人的闭包
public typealias AddressBookArrayClosure = ((_ addressBookArray: [TYAddressBookModel]) -> Void)
/// 获取按A~Z顺序排列的所有联系人的闭包
public typealias AddressBookDictClosure = ((_ addressBookDict: [String:[TYAddressBookModel]], _ nameKeys: [String])-> Void)

@objc(ContactsPermissionRequester)
public class ContactsPermissionRequester : NSObject {
    
    /**
     请求用户是否授权APP访问通讯录的权限,建议在APPDeletegate.m中的didFinishLaunchingWithOptions方法中调用
     */
    
    ///isFrist 是否第一次授权
    @objc public class func getPermission(completion : ((Bool , _ isFrist : Bool) -> Void)?) {
        if TYAddressBookHandle.isCanRequestAuthorization() == false {
            DispatchQueue.main.async {
                completion?(ContactsPermissionRequester.isAuthorization() , false)
            }
        }
        TYAddressBookHandle().requestAuthorization(success: {
            DispatchQueue.main.async {
                completion?(true , true)
            }
        }, failure: {
            DispatchQueue.main.async {
                completion?(false , true)
            }
        })
    }
    
    public class func isAuthorization()->(Bool){
        return TYAddressBookHandle.isAuthorization()
    }
    
    public class func isCanRequestAuthorization()->(Bool){
        return TYAddressBookHandle.isCanRequestAuthorization()
    }
    
    public class func requestAlertAuthorization(){
        DispatchQueue.main.safeAsyncUIQueue {
            AlertViewController.show(title: Localizable.PleaseAllowAccess(), message: "Buz does not have access to your contacts", cancelTitle: Localizable.NotNow(), cancelStyle: .cancel, cancelClickBlock: nil, confirmTitle: Localizable.settings(), confirmStyle: .hightlight, confirmClickBlock: { _ in
                guard let url = URL(string: UIApplication.openSettingsURLString) else {
                        return
                }
                UIApplication.shared.open(url, options: [:], completionHandler: nil)
            })
        }
    }
    
    // MARK: - 获取原始顺序所有联系人
    public class func getOriginalAddressBook(addressBookArray success: @escaping AddressBookArrayClosure, authorizationFailure failure: @escaping AuthorizationHandle) {
        
        DispatchQueue.defaultIOQueue().async {
            TYAddressBookHandle().getAddressBookDataSource(personModel: { (models) in
                var modelArray = [TYAddressBookModel]()
                for model in models {
                    let firstLetterString = getFirstLetterFromString(aString: model.name)
                    model.firstLetter = firstLetterString
                    modelArray.append(model)
                }
                
                success(modelArray)
                
            }, authorizationFailure: {
                failure()
            })
        }
        
    }
      
    public static let titlesSet : Set<String> =  ["A", "B" , "C" , "D" , "E" , "F" , "G" , "H" , "I" , "J" , "K" , "L" , "M" , "N" , "O" , "P" , "Q" , "R" , "S" , "T" , "U" , "V" , "W" , "X" , "Y" , "Z" , "#"]
    
    
    // MARK: - 获取联系人姓名首字母(传入汉字字符串, 返回大写拼音首字母)
    @objc
    public class func getFirstLetterFromString(aString: String) -> (String) {
        if aString.count == 0 { return "#" }
        // 注意,这里一定要转换成可变字符串
        let mutableString = NSMutableString.init(string: aString)
        // 将中文转换成带声调的拼音
        CFStringTransform(mutableString as CFMutableString, nil, kCFStringTransformToLatin, false)
        // 去掉声调(用此方法大大提高遍历的速度)
        let pinyinString = mutableString.folding(options: String.CompareOptions.diacriticInsensitive, locale: NSLocale.current)
        // 将拼音首字母装换成大写
        let strPinYin = polyphoneStringHandle(nameString: aString, pinyinString: pinyinString).uppercased()
        // 截取大写首字母
        let firstString = String(strPinYin.prefix(1))
        
        return ContactsPermissionRequester.titlesSet.contains(firstString) ? firstString : "#"
        
        
        
        /**
         𝙶𝚞𝚌𝚌𝚒💸🦋 𝕊𝕚𝕖𝕣𝕣𝕒 𝕁𝕒𝕜𝕖🥵 𝔹𝕝𝕦𝕖𝕗𝕒𝕔𝕖🌊🥶 𝐼’𝑚 𝑦𝑜𝑢𝑟 𝑚𝑜𝑚 😩 𝐶𝑎𝑚 シ 𝐂𝐞𝐜𝐞♥♥ ℰ𝓂𝒶𝒹𝓈𝒽𝒾𝓃𝓌𝒶𝓇𝒾
         **/
//        // 判断姓名首位是否为大写字母    --> 此正则表达式应用在特殊字符不准
//        let regexA = "^[A-Z]$"
//        let predA = NSPredicate.init(format: "SELF MATCHES %@", regexA)
//        return predA.evaluate(with: firstString) ? firstString : "#"
        
    }
    /// 多音字处理
    private class func polyphoneStringHandle(nameString:String, pinyinString:String) -> String {
        if nameString.hasPrefix("长") {return "chang"}
        if nameString.hasPrefix("沈") {return "shen"}
        if nameString.hasPrefix("厦") {return "xia"}
        if nameString.hasPrefix("地") {return "di"}
        if nameString.hasPrefix("重") {return "chong"}
        
        return pinyinString;
    }
    
}

