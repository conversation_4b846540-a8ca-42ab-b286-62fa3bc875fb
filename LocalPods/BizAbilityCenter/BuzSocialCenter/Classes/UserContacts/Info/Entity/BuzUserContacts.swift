//
//  BuzUserContacts.swift
//  buz
//
//  Created by st.chio on 2025/2/14.
//  Copyright © 2025 lizhi. All rights reserved.
//

import YYModel
public class BuzUserContacts: NSObject {
    
    // MARK: - Properties
    public var firstName: String? // 名字
    public var lastName: String? // 姓氏
    public var firstLetter: String? // 名字的首字母
    public var phone: String? // 手机号（格式化或原始号码）
    
    public var formatError: Bool = false // 格式化错误
    private var _filteredOriginalPhone: String? // 纯数字手机号（过滤掉非数字字符）
    
    // MARK: - Initializers
    public override init() {
        super.init()
    }
    
    // MARK: - Equatable & Hashable
    public override func isEqual(_ object: Any?) -> Bool {
        guard let other = object as? BuzUserContacts else {
            return super.isEqual(object)
        }
        return phone == other.phone && firstName == other.firstName && lastName == other.lastName
    }
    
    public override var hash: Int {
        return (phone?.hashValue ?? 0) ^ (firstName?.hashValue ?? 0) ^ (lastName?.hashValue ?? 0)
    }
    
    // MARK: - Description (YYModel 兼容)
    public override var description: String {
        return self.yy_modelToJSONString() ?? super.description
    }
}

public extension BuzUserContacts {
    /// 计算 `filteredOriginalPhone`，如果为空则过滤 `phone` 中的非数字字符
    var filteredOriginalPhone: String? {
        if let filteredOriginalPhone = _filteredOriginalPhone, !filteredOriginalPhone.isEmpty {
            return filteredOriginalPhone
        }
        if let phone = phone, !phone.isEmpty {
            return phone.filter { $0.isNumber }
        }
        return phone
    }
}


public extension BuzUserContacts {
    /// 初始化数据库模型，自动生成 `firstLetter`
    convenience init(phone: String, originalPhone: String, firstName: String, lastName: String) {
        self.init()
        self.phone = phone
        self._filteredOriginalPhone = originalPhone
        self.firstName = firstName
        self.lastName = lastName
        self.firstLetter = ContactsPermissionRequester.getFirstLetterFromString(aString: firstName.isEmpty ? lastName : firstName)
    }
    
    // MARK: - Methods
    /// 获取 `firstName`，如果为空，则返回 `lastName`，再为空则返回 `phone`
    func getUserFirstName() -> String {
        if let firstName = firstName, !firstName.isEmpty {
            return firstName
        } else if let lastName = lastName, !lastName.isEmpty {
            return lastName
        } else if let phone = phone, !phone.isEmpty {
            return phone
        }
        return "unknown"
    }
    
    /// 获取完整的用户姓名
    func getUserFullName() -> String {
        var name = ""
        if let firstName = firstName, !firstName.isEmpty {
            name = firstName
        }
        if let lastName = lastName, !lastName.isEmpty {
            name += name.isEmpty ? lastName : " \(lastName)"
        }
        return name
    }
}

public extension BuzUserContacts {
    /// 计算两个 `BuzUserContacts` 数组的差集（arrayA - arrayB）
    static func getDifference(arrayA: [BuzUserContacts], arrayB: [BuzUserContacts]) -> [BuzUserContacts] {
        let setA = Set(arrayA)
        let setB = Set(arrayB)
        return Array(setA.subtracting(setB))
    }
}
