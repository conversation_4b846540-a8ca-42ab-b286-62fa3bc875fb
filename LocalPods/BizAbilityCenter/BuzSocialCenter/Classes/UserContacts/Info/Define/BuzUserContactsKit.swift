//
//  BuzFriendsKit.swift
//  buz
//
//  Created by st.chio on 2025/2/14.
//  Copyright © 2025 lizhi. All rights reserved.
//

public extension Notification.Name {
    static let ContactsUploadSuccessNotic : Notification.Name = Notification.Name.init("ContactsUploadSuccessNotic")
    static let ContactsUploadResultNotic : Notification.Name = Notification.Name.init("ContactsUploadResultNotic")
    static let ContactsUploadEmptyNotic : Notification.Name = Notification.Name.init("ContactsUploadEmptyNotic")
}
