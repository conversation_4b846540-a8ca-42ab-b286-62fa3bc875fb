//
//  ContactsManager.swift
//  buz
//
//  Created by lidawen on 2022/8/1.
//  Copyright © 2022 lizhi. All rights reserved.
//

import BuzIDL
import BuzLog
import BuzDataStore
import BuzNetworker
import BuzUserSession

private enum ContactsManagerStoreKey : String {
    /// 是否全量上传过通讯录
    case hasFullUploaded = "hasFullUploaded_new_"
    
    /// 是否上传成功过通讯录
   case requestUploadContactsKey = "requestUploadContactsKey_"
    
    /// 通讯录上传成功次数
    case contactsUploadedTimes = "contactsUploadedTimes_"
}

public protocol ContactsManagerProviderable {
    func traceId()-> String
    func getLocalEmailCountryCode()-> String?
}

@objc
public protocol BuzContactsCenterInfoObserver: BuzContactsCenterObserver {
    // Buz地址簿更新了
    @objc optional func contactsManagerDidUpdateAddressBook(_ mgr: ContactsManager)
}

public class ContactsManager: NSObject, BuzSocialControllerable {
    typealias Center = BuzContactsCenter
    weak var center: BuzContactsCenter?
    public static let defaultRegionKey : String = "d"
    public var provider: ContactsManagerProviderable?
    
    // 联系人数据（数据库的）
    public private(set) var dbContactsList = [BuzUserContacts]()
    // 联系人原始手机号映射（数据库的）
    private var dbContactsPhoneMap = [String: BuzUserContacts]()
    // 过滤掉非数字字符的手机号映射（数据库的）
    private var dbContactsFilteredOriginalPhoneMap = [String: BuzUserContacts]()

    private var fetchAllSystemContactBlocks : [(((_ list:[BuzUserContacts])-> Void)?)] = []
    private var fetchAllSystemContactLock : NSLock = NSLock.init()
    // 联系人数据（手机系统通讯录的）
    public private(set) var systemContactsList = [BuzUserContacts]()
    // 联系人数据（手机系统通讯录的）
    public private(set) var phoneRegionCodeToContactList : [String : BuzUserContacts] = [:]
    
    // 联系人原始手机号映射（手机系统通讯录的）
    private var systemContactsPhoneMap = [String: BuzUserContacts]()
    // 过滤掉非数字字符的手机号映射（手机系统通讯录的）
    private var systemContactsFilteredOriginalPhoneMap = [String: BuzUserContacts]()
    
    lazy var syncUtil = ContactsSyncUtil()
    
    private lazy var firstSyncData = true
    
    private lazy var needSyncData = false
    
    required init(center: BuzContactsCenter?) {
        super.init()
        self.center = center
        addObserver()
        initData()
    }
        
    private func addObserver(){
        NotificationCenter.default.addObserver(self, selector: #selector(enterForeground), name: UIApplication.willEnterForegroundNotification, object: nil)
    }
    
    private func initData(){
        ContactsDao.sharedInstance().getAllContactsOrder(byFirstLetterFilterBlackUser: true) { [weak self] list in
            guard let self = self else { return }
            self.saveDbContacts(toMemoryCache: list.map({BuzUserContacts.init(entity: $0)}))
        }
    }
       
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
}

//MARK: 数据变动同步
extension ContactsManager: BuzSignalPushEventObserver {
    
    @objc private func enterForeground(){
        
        if false == BuzUserSession.shared.isloginFinish {
            return
        }
        
        // 后台回来时可能罗马推送断开了，导致收不到联系人变更同步，所以在连接成功后再同步一次
        if BuzNetworker.shared.signalPush.connectionState != .connected {
            needSyncData = true
            BuzContactsLog.info("home contacts signalPush not Connected")
            return
        }
        needSyncData = false
        
        BuzContactsLog.info("上传通讯录 - 回到前台，同步通讯录")
        
        //同步通讯录
        self.handleNetworkRecover(complete: nil)
    }
    
    //罗马推送连接成功
    public func signalPushDidChangedConnection(connection: BuzSignalPush.Connection) {
        if connection.state == .connected, needSyncData {
            needSyncData = false
            self.handleNetworkRecover(complete: nil)
        }
    }
}

//MARK: - Public
public extension ContactsManager {
    func isAlreadyUploaded() -> Bool {
        return isUploaded()
    }
    
    func isFirstTimeUploaded() -> Bool {
        return contactsUploadedTimes() == 1
    }
}

//MARK: 数据获取
public extension ContactsManager{
    //获取所有联系人(过滤黑名单用户)
    func fetchAllSystemContactsByFilterBlackUser(complete: ((_ list:[BuzUserContacts])-> Void)?){
        self.fetchAllSystemContactLock.lock()
        if self.fetchAllSystemContactBlocks.count != 0 {
            self.fetchAllSystemContactBlocks.append((complete))
            self.fetchAllSystemContactLock.unlock()
            return
        }
        
        self.fetchAllSystemContactBlocks.append((complete))
        self.fetchAllSystemContactLock.unlock()
        var dataList = [BuzUserContacts]()
        var blackUserList = [BuzBlackUserInfo]()
        
        let group = DispatchGroup.init()
        group.enter()
        
        syncUtil.requestAddressBook(callbackOnGlobalQueue:{ [weak self] contactsArray in
            guard let `self` = self else { return }
            self.saveSystemContacts(toMemoryCache: contactsArray)
            dataList = contactsArray
            group.leave()
        }, authorizationFailure: {
            dataList = []
            group.leave()
        })
        
        group.enter()
        BuzFriendCenter.blacklist.queryAllBlackListFromDB { blackUsers in
            blackUserList = blackUsers
            group.leave()
        }
        
        group.notify(queue: DispatchQueue.contactQueue()) { [weak self] in
            guard let `self` = self else { return }
            let phonesToExclude = blackUserList.compactMap { $0.blackPhone }
            let filterList = self.filteredSystemContacts(formatterPhoneList: phonesToExclude)
            
            let filteredDataList = dataList.filter { item in
                !filterList.contains { filteredItem in
                    item.phone == filteredItem.phone
                }
            }
            dataList = filteredDataList
            self.fetchAllSystemContactLock.lock()
            var blocks : [(((_ list:[BuzUserContacts])-> Void)?)] = []
            blocks.append(contentsOf: self.fetchAllSystemContactBlocks)
            self.fetchAllSystemContactBlocks.removeAll()
            self.fetchAllSystemContactLock.unlock()
            
            blocks.forEach { block in
                block?(dataList)
            }
        }
    }
    
    //获取所有联系人
    func fetchAllSystemContacts(complete: ((_ list:[BuzUserContacts])-> Void)?){
        syncUtil.requestAddressBook(callbackOnGlobalQueue:{ [weak self] contactsArray in
            guard let `self` = self else { return }
            self.saveSystemContacts(toMemoryCache: contactsArray)
            
            DispatchQueue.main.safeAsyncUIQueue {
                complete?(contactsArray)
            }
        }, authorizationFailure: {
            DispatchQueue.main.safeAsyncUIQueue {
                complete?([])
            }
        })
    }
    
    //获取所有联系人
    func fetchAllContacts(complete:((_ list:[BuzUserContacts])-> Void)?){
        ContactsDao.sharedInstance().getAllContactsOrder(byFirstLetterFilterBlackUser: true) {  list in
            complete?(list.map({.init(entity: $0)}))
        }
    }
    
    //刷新联系人数据
    func reloadAllContacts(isFirst: Bool = false ){
        ContactsDao.sharedInstance().getAllContactsOrder(byFirstLetterFilterBlackUser: true) { [weak self] list in
            guard let self = self else { return }
            let contacts = list.map({BuzUserContacts.init(entity: $0)})
            self.dbContactsList = contacts
            if isFirst, list.count == 0 {
                return
            }
            
            saveDbContacts(toMemoryCache: contacts)
            self.center?.notifyObservers(of: BuzContactsCenterInfoObserver.self) { observer in
                observer.contactsManagerDidUpdateAddressBook?(self)
            }
        }
    }
    
    static func contactKey(region : String, phone : String) -> String {
        return Self.defaultRegionKey + region + "-" + phone
    }
    
    static func update( dic : inout [String:BuzUserContacts],
                        region : String?,
                        phone : String,
                        val : BuzUserContacts) {
        var prefixZero = 0
        let chars = phone.charactersArray
        
        for i in 0..<chars.count {
            if chars[i] == "0" {
                prefixZero = i + 1
                continue
            }
            
            break
        }
        
        if prefixZero + 5 > chars.count {
            return
        }
        
        var fullKey = (phone as NSString).substring(from: prefixZero) as String
        
        if let region = region {
            dic[self.contactKey(region: region, phone: phone)] = val
            fullKey = region + fullKey
        }
        
        dic[fullKey] = val
    }
    
    // 缓存联系人信息（手机通讯录的）
    func saveSystemContacts(toMemoryCache list:[BuzUserContacts]) {
        self.systemContactsList = list
        
        self.systemContactsPhoneMap.removeAll()
        self.systemContactsFilteredOriginalPhoneMap.removeAll()
        self.phoneRegionCodeToContactList.removeAll()
        
        for item in self.systemContactsList {
            if let originPhone = item.filteredOriginalPhone {
                self.systemContactsFilteredOriginalPhoneMap[originPhone] = item
                
                Self.update(dic: &self.phoneRegionCodeToContactList,
                            region: nil,
                            phone: originPhone,
                            val: item)
            }
            if let phone = item.phone {
                self.systemContactsPhoneMap[phone] = item
            }
        }
    }
    
    // 获取缓存的联系人信息（手机通讯录的）
    func getCachedSystemContactsInfo(phone: String) -> BuzUserContacts? {
        if let item = systemContactsFilteredOriginalPhoneMap[phone] {
            return item
        }
        if let item = systemContactsPhoneMap[phone] {
            return item
        }
        return nil
    }
    
    // 缓存联系人信息(数据库的)
    func saveDbContacts(toMemoryCache list:[BuzUserContacts]) {
        self.dbContactsList = list
        
        self.dbContactsPhoneMap.removeAll()
        self.dbContactsFilteredOriginalPhoneMap.removeAll()
        
        for item in self.dbContactsList {
            if let originPhone = item.filteredOriginalPhone {
                self.dbContactsFilteredOriginalPhoneMap[originPhone] = item
            }
            if let phone = item.phone {
                self.dbContactsPhoneMap[phone] = item
            }
        }
    }
    
    // 获取缓存的联系人信息（数据库的）
    func getCachedContactsInfo(phone: String) -> BuzUserContacts? {
        if let item = dbContactsFilteredOriginalPhoneMap[phone] {
            return item
        }
        if let item = dbContactsPhoneMap[phone] {
            return item
        }
        return nil
    }
    
    //清除缓存
    func cleanDataWhenLogout(){
        dbContactsList.removeAll()
        dbContactsPhoneMap.removeAll()
        dbContactsFilteredOriginalPhoneMap.removeAll()
        self.firstSyncData = true
    }
}

// MARK: 通讯录过滤
public extension ContactsManager {
    
    func getFilteredSystemContactsList(subStrings: [String]) -> [BuzUserContacts] {
        // 使用了 contains(where:) 函数来检查 keyValuePair.key 是否包含 substrings 数组中的任一子串
        let filteredContacts = self.dbContactsFilteredOriginalPhoneMap.filtered { keyValuePair in
            subStrings.contains(where: keyValuePair.key.contains)
        } map: {
            $0.value
        }
        return filteredContacts
    }
    
    // 包含 formatterPhoneList 的通讯录
    func filteredSystemContacts(formatterPhoneList: [String]) -> [BuzUserContacts] {
        var filterArray = [BuzUserContacts]()
        for phone in formatterPhoneList {
            if let contact = matchSystemContact(formatterPhone: phone) {
                filterArray.append(contact)
            }
        }
        return filterArray
    }
}

// MARK: 通讯录匹配
public extension ContactsManager {
    // 根据格式化的手机号(xx-xxx)匹配通讯录联系人
    func matchSystemContact(formatterPhone: String) -> BuzUserContacts? {
        return Self.matchSystemContact(formatterPhone: formatterPhone,
                                       phoneToContacts: self.phoneRegionCodeToContactList)
    }
    
    // 根据格式化的手机号(xx-xxx)匹配通讯录联系人
    static func matchSystemContact(formatterPhone: String, phoneToContacts: [String:BuzUserContacts]) -> BuzUserContacts? {
        let components = formatterPhone.phoneComponent()
        var phone = formatterPhone
        var region = ""
        var keys : [String] = []
        
        if components.count > 1 {
            region = components[0]
            phone = components[1]
        }
        
        //region + phone has the highest priority,
        //original phone has higher priority to be selected, like p(phone) > p("0'phone'")
        if region.length != 0 {
            keys.append(contentsOf: [self.contactKey(region: region, phone: phone), "\(region)\(phone)",
                                     "\(region)0\(phone)", "\(phone)"])
        } else {
            keys.append(contentsOf: ["\(phone)"])
        }
        
        for i in 0..<keys.count {
            if let value = phoneToContacts[keys[i]] {
                return value
            }
        }
        
        return nil
    }
}

// MARK: 通讯录同步和上传
public extension ContactsManager {
    // 同步本地通讯录和数据库联系人，并上传到服务端
    func syncContactsToServerIfNeed() {
        ContactsPermissionRequester.getPermission { [weak self] isPermission, isFrist in
            guard let `self` = self else { return }
            
            if isPermission {
                self.syncUtil.requestAddressBook(callbackOnGlobalQueue:{ list in
                    let size = list.count
                    if size > 0 {
                        self.syncContactsToServer(contactSize: size)
                    } else {
                        // 通讯录是空
                        DispatchQueue.main.safeAsyncUIQueue {
                            NotificationCenter.default.post(name: NSNotification.Name.ContactsUploadEmptyNotic, object: nil)
                        }
                    }
                }, authorizationFailure: {
                    
                })
            }
        }
    }
}

private extension ContactsManager {
    func syncContactsToServer(contactSize: Int) {
        let size = Int32(contactSize)
        
        if !self.hasFullUploaded() {
            // 首次必定是全量上传
            self.syncAndUploadLocalContacts(uploadType: .full) { success, haveChanged in
                if success {
                    self.setHasFullUploaded()
                }
            }
            return
        }
        
        if BuzUserSession.shared.isAutoLogin {
            // 静默登录，上传过通讯录则用增量上传，否则全量
            self.syncAndUploadLocalContacts(uploadType: self.isUploaded() ? .increment : .full, complete: nil)
        } else {
            // 非静默登录，全量上传过，根据服务端下发的上传方式来区分全量或增量上传
            ContactsNetwork.getUploadContactType(contactSize: size) { [weak self] isSuccess, uploadType in
                guard let `self` = self else { return }
                
                var type: ContactsUploadType?
                
                if isSuccess, let uploadType = uploadType {
                    type = ContactsUploadType(rawValue: uploadType)
                }
                
                if let type = type {
                    self.syncAndUploadLocalContacts(uploadType: type, complete: nil)
                } else {
                    // 兜底逻辑: 上传过通讯录则用增量上传，否则全量
                    self.syncAndUploadLocalContacts(uploadType: self.isUploaded() ? .increment : .full, complete: nil)
                }
            }
        }
    }
    
    func syncAndUploadLocalContacts(uploadType: ContactsUploadType, complete:((_ success: Bool, _ haveChanged: Bool)-> Void)?){
        ContactsPermissionRequester.getPermission { [weak self] isPermission, isFrist in
            guard let `self` = self else { return }
            if isPermission {
                BuzContactsLog.info(uploadType == .full ? "上传通讯录：全量" : "上传通讯录：增量")
                self.syncUtil.syncAndUploadLocalContacts(uploadType: uploadType) { success, haveChanged, isEmpty in
                    
                    if !haveChanged {
                        BuzContactsLog.info("上传通讯录 - 通讯录没有变化")
                    }
                    
                    if success {
                        self.setIsUploaded()
                        
                        if haveChanged {
                            self.updateContactsUploadedTimes()
                            
                            DispatchQueue.main.safeAsyncUIQueue {
                                NotificationCenter.default.post(name: NSNotification.Name.ContactsUploadSuccessNotic, object: nil)
                            }
                        }
                    }
                    
                    if haveChanged || (self.firstSyncData && isEmpty) {
                        self.reloadAllContacts()
                    }
                    self.firstSyncData = false
                    
                    DispatchQueue.main.safeAsyncUIQueue {
                        complete?(success, haveChanged)
                    }
                }
            } else {
                complete?(false, false)
            }
        }
    }
    
    func handleNetworkRecover(complete:((_ success: Bool, _ haveChanged: Bool)-> Void)?) {
        #warning("donghong todo: 优化-断网恢复的情况")
        let uploadType: ContactsUploadType = self.isUploaded() ? .increment : .full
        syncAndUploadLocalContacts(uploadType: uploadType, complete: complete)
    } 
    
    func isUploaded() -> Bool {
        let key = ContactsManagerStoreKey.requestUploadContactsKey.rawValue +  String(BuzUserSession.shared.uid)
        let uploaded = MMKV.buz.bool(forKey:key, defaultValue: false)
        return uploaded
    }
    
    func setIsUploaded() {
        let key = ContactsManagerStoreKey.requestUploadContactsKey.rawValue +  String(BuzUserSession.shared.uid)
        MMKV.buz.set(true, forKey: key)
    }
    
    func hasFullUploaded() -> Bool {
        let key = ContactsManagerStoreKey.hasFullUploaded.rawValue +  String(BuzUserSession.shared.uid)
        return MMKV.buz.bool(forKey: key, defaultValue: false)
    }
    
    func setHasFullUploaded() {
        let key = ContactsManagerStoreKey.hasFullUploaded.rawValue +  String(BuzUserSession.shared.uid)
        MMKV.buz.set(true, forKey: key)
    }
    
    func contactsUploadedTimes() -> Int64 {
        let key = ContactsManagerStoreKey.contactsUploadedTimes.rawValue +  String(BuzUserSession.shared.uid)
        return MMKV.buz.int64(forKey: key, defaultValue: 0)
    }
    
    func updateContactsUploadedTimes() {
        let key = ContactsManagerStoreKey.contactsUploadedTimes.rawValue +  String(BuzUserSession.shared.uid)
        let uploadedTimes = MMKV.buz.int64(forKey: key, defaultValue: 0)
        MMKV.buz.set(uploadedTimes + 1, forKey: key)
    }
}

