//
//  ContactsPresenter.swift
//  buz
//
//  Created by lizhi on 2022/7/6.
//  Copyright © 2022 lizhi. All rights reserved.
//

import PhoneNumberKit
import BuzIDL
import BuzLog
import BuzUserSession

enum OperationContactsStatus: String {
    case success = "success"
    case saveSystemError = "saveSystemError"
    case uploadError = "uploadError"
    case saveDBError = "saveDBError"
}

enum ContactsUploadType: Int32 {
    case full = 1       // 全量
    case increment = 2  // 增量
    case manual = 3     // 手动
}


//联系人同步工具
class ContactsSyncUtil: NSObject {
    private let maxRetryCount = 3
    private var currentRegionCode: String?
    private var currentUserFormatterPhone: String?
    private var phoneNumberKit: PhoneNumberUtility?
    private var phoneCache : [BuzUserContacts]?
    private var traceId: String?
    var isSyncing = false

    override init() {
        super.init()
        initData()
        BuzUserSession.shared.addObserver(self)
    }
     
    // 同步本地通讯录和数据库联系人，并上传到服务端
    // 注意：为了避免大通讯录卡顿（3000+联系人），异步回调是在子线程
    func syncAndUploadLocalContacts(uploadType:ContactsUploadType, completeOnGlobalQueue:((_ success: Bool, _ haveChanged: Bool, _ isEmpty: Bool)-> Void)?){
        var addressBookList = [BuzUserContacts]()
        var dbContactsList = [BuzUserContacts]()
        
        let group = DispatchGroup()
        var authorizationFailure = false
        
        // 1-1 获取通讯录数据
        group.enter()
        requestAddressBook(callbackOnGlobalQueue:{ list in
            // 过滤掉无法格式化的手机号
            addressBookList = list.filter({ model in
                return !model.formatError
            })
            
            group.leave()
        }, authorizationFailure: {
            authorizationFailure = true
            group.leave()
        })
        
        // 1-2 查询本地数据库数据
        group.enter()
        ContactsDao.sharedInstance().getAllContactsOrder(byFirstLetterFilterBlackUser: true) { list in
            dbContactsList = list.map({BuzUserContacts.init(entity: $0)})
            group.leave()
        }
        
        group.notify(queue: DispatchQueue.contactQueue()) { [weak self] in
            guard let `self` = self else { return }
            
            if authorizationFailure { //无权限
                completeOnGlobalQueue?(false, false, true)
                return
            }
            
            var addContacts = [BuzUserContacts]()
            var deleteContacts = [BuzUserContacts]()
            var updateContacts = [BuzUserContacts]()
            
            if uploadType == .full {
                // 2-0 全量上传，直接用通讯录的全量覆盖
                addContacts = addressBookList
            } else {
                // 2-1 增量上传，求出本地通讯录与数据库数据的差异
                let addList = BuzUserContacts.getDifference(arrayA: addressBookList, arrayB: dbContactsList)
                let deleteList = BuzUserContacts.getDifference(arrayA: dbContactsList, arrayB: addressBookList)
                
                // 增量上传 且联系人没有变化
                if addList.count <= 0 && deleteList.count <= 0 {
                    BuzContactsLog.info("ContactsSyncUtil - 联系人信息没有变化")
                    completeOnGlobalQueue?(true, false, addressBookList.count == 0)
                    return
                }
                
                // 2-2 合并，将同一个手机号的新增、删除合并为更新
                let updatesTuple = self.getUpdatesTuple(addArray: addList, deleteArray: deleteList)
                
                addContacts = addList
                deleteContacts = deleteList
                updateContacts = updatesTuple.0
                
                // 2-3 将更新操作的手机号从 addList 和 deleteList 移除
                if updatesTuple.0.count > 0 {
                    addContacts = BuzUserContacts.getDifference(arrayA: addList, arrayB: updatesTuple.0)
                }
                if updatesTuple.1.count > 0 {
                    deleteContacts = BuzUserContacts.getDifference(arrayA: deleteList, arrayB: updatesTuple.1)
                }
            }
            
            // 3.保存联系人变更到服务端和数据库
            if self.isSyncing {
                return
            }
            
            self.isSyncing = true
            self.saveDifferentContacts(uploadType: uploadType, addContacts: addContacts, deleteContacts: deleteContacts, updateContacts: updateContacts) { operationStatus in
                self.isSyncing = false
                completeOnGlobalQueue?(operationStatus == .success, true, addressBookList.count == 0)
                
                let isSucceed = (operationStatus == .success)
                let count = addContacts.count + deleteContacts.count + updateContacts.count
                let isFull = (uploadType == .full)
                let reason = operationStatus.rawValue
                let userInfo = [
                    "isSucceed" : isSucceed,
                    "contactsCount" : count,
                    "isFullUpload" : isFull,
                    "failedReason" : reason
                ]
                
                DispatchQueue.main.safeAsyncUIQueue {
                    NotificationCenter.default.post(name: NSNotification.Name.ContactsUploadResultNotic, object: nil, userInfo: userInfo)
                }
            }
        }
    }
    
    func getUpdatesTuple(addArray: [BuzUserContacts], deleteArray: [BuzUserContacts]) -> ([BuzUserContacts], [BuzUserContacts]) {
        // 存储更新操作的数组
        var updates: ([BuzUserContacts], [BuzUserContacts]) = ([], [])

        // 检查更新操作
        for toAdd in addArray {
            for toDelete in deleteArray {
                if toAdd.phone == toDelete.phone &&
                    (toAdd.firstName != toDelete.firstName || toAdd.lastName != toDelete.lastName) {
                    updates.0.append(toAdd)
                    updates.1.append(toDelete)
                }
            }
        }
        return updates
    }
    
    /// 保存联系人变更到服务端和数据库
    /// - Parameters:
    ///   - isManual: 是否为用户主动操作,影响接口type状态
    ///   - addContacts: 新增的联系人
    ///   - deletePhoneList: 删除的手机号
    ///   - complete: 结果
    func saveDifferentContacts(uploadType:ContactsUploadType, addContacts: [BuzUserContacts], deleteContacts: [BuzUserContacts], updateContacts:[BuzUserContacts], complete:(( _ operationStatus: OperationContactsStatus)-> Void)?){
        if addContacts.count == 0 && deleteContacts.count == 0 && updateContacts.count == 0 {
            DispatchQueue.contactQueue().async {
                complete?(.success)
            }
            return
        }

        var contactList = [Contact]()
        addContacts.forEach { model in
            if let _ = model.phone {
                /// phone: 国家码-手机号的格式手机号,  originalPhone: 通讯录原始手机号，新版本只需传 originalPhone
                let item = Contact.init(firstName: model.firstName, lastName: model.lastName, phone: nil, type: 1, originalPhone: model.phone)
                contactList.append(item)
            }
        }
        deleteContacts.forEach { model in
            if let _ = model.phone {
                /// phone: 国家码-手机号的格式手机号,  originalPhone: 通讯录原始手机号，新版本只需传 originalPhone
                let item = Contact.init(firstName: "", lastName: "", phone: nil, type: 2, originalPhone: model.phone)
                contactList.append(item)
            }
        }
        updateContacts.forEach { model in
            if let _ = model.phone {
                /// phone: 国家码-手机号的格式手机号,  originalPhone: 通讯录原始手机号，新版本只需传 originalPhone
                let item = Contact.init(firstName: model.firstName, lastName: model.lastName, phone: nil, type: 3, originalPhone: model.phone)
                contactList.append(item)
            }
        }
        
        // originalPhone 超过 20 位就截取掉后面的字符，防止用户输入很长的字符
        contactList = contactList.map { item in
            if let originalPhone = item.originalPhone {
                let filteredOriginalPhone = String(originalPhone.prefix(20))
                return Contact.init(firstName: item.firstName, lastName: item.lastName, phone: item.phone, type: item.type, originalPhone: filteredOriginalPhone)
            }
            return item
        }
        
        #warning("donghong todo: 依赖基础组件，不要直接使用gzip")
        // 3-0.使用gzip压缩
        var compressedData: Data?
        do {
            let data = try JSONEncoder().encode(contactList)
            do {
                compressedData = try data.gzipped()
            } catch {
                BuzContactsLog.info("ContactsSyncUtil - gzip data error")
            }
        } catch {
            BuzContactsLog.info("ContactsSyncUtil - contactList json encode error")
        }
        
        guard let compressContacts = compressedData else {
            BuzContactsLog.info("ContactsSyncUtil - contactList can not compress by gzip")
            return
        }
        
        // 如果获取不到，默认传马来 MY
        var uploaderRegionCode: String = "MY"
        if let regionCode = currentRegionCode {
            uploaderRegionCode = regionCode
        }
        
        // 3-1.保存联系人变化到服务端（最多重试3次）
        self.sendUploadContactsRequest(contactList:contactList, compressContacts: compressContacts, type: uploadType.rawValue, uploaderRegionCode: uploaderRegionCode, retryCount: self.maxRetryCount) { [weak self] rcode in
            guard let `self` = self else { return }
            if rcode != 0 {
                DispatchQueue.contactQueue().async {
                    complete?(.uploadError)
                }
                return
            }
            
            //3-2 保存到数据库
            self.saveContactsToDB(with: addContacts, deleteContacts: deleteContacts, updateContacts: updateContacts, complete: complete)
        }
    }
    
    func saveContactsToDB(with addContacts:[BuzUserContacts], deleteContacts:[BuzUserContacts], updateContacts:[BuzUserContacts], complete:(( _ operationStatus: OperationContactsStatus)-> Void)?) {
        
        var deletePhoneList = [String]()
        for model in deleteContacts {
            if let phone = model.phone {
                deletePhoneList.append(phone)
            }
        }
        
        let group = DispatchGroup()
        var isSucceed = true
        
        group.enter()
        ContactsDao.sharedInstance().deleteContacts(withPhoneList: deletePhoneList) { status in
            BuzContactsLog.debug("ContactsSyncUtil - delete to db: \(status) deletePhoneList:\(deletePhoneList)")
            if status == false {
                isSucceed = false
            }
            group.leave()
        }
        
        group.enter()
        ContactsDao.sharedInstance().addOrUpdateContacts(addContacts.map({$0.convertDaoEntity()})) { status in
            BuzContactsLog.debug("ContactsSyncUtil - add to db: \(status) addContacts:\(addContacts)")
            if status == false {
                isSucceed = false
            }
            group.leave()
        }
        
        group.enter()
        ContactsDao.sharedInstance().addOrUpdateContacts(updateContacts.map({$0.convertDaoEntity()})) { status in
            BuzContactsLog.debug("ContactsSyncUtil - update to db: \(status) updateContacts:\(updateContacts)")
            if status == false {
                isSucceed = false
            }
            group.leave()
        }
        
        group.notify(queue: DispatchQueue.contactQueue()) {
            complete?(isSucceed ? .success : .saveDBError)
        }
    }
    
    func sendUploadContactsRequest(contactList: [Contact], compressContacts: Data, type: Int32, uploaderRegionCode: String, retryCount:Int, complete: ((_ rcode: Int) -> Void)?) {
        
        if retryCount <= 0 {
            self.traceId = nil
            complete?(NetworkErrorCode)
            return
        }
        
        var traceId = ""
        if let id = self.traceId {
            traceId = id
        } else {
            traceId = BuzContactsCenter.info.provider?.traceId() ?? ""
            self.traceId = traceId
        }
        let isRetry = (retryCount != self.maxRetryCount)
        ContactsNetwork.uploadContacts(contactList:contactList, compressContacts: compressContacts, type: type, uploaderRegionCode: uploaderRegionCode, traceId: traceId, isRetry: isRetry) { [weak self] rcode in
            guard let `self` = self else { return }
            
            if rcode != 0 {
                // 失败后不立即重试，延迟1s
                DispatchQueue.contactQueue().delay(1) {
                    self.sendUploadContactsRequest(contactList: contactList, compressContacts: compressContacts, type: type, uploaderRegionCode: uploaderRegionCode, retryCount: retryCount-1, complete: complete)
                }
                
            } else {
                self.traceId = nil
                complete?(rcode)
            }
        }
    }
    
    //是否是当前用户手机号
    func isSelfPhone(phone: String) -> Bool{
        // 去除所有非数字字符
        let filteredPhone = phone.filter { $0.isNumber }
        return self.isMatch(formatterPhone: currentUserFormatterPhone, filteredPhone: filteredPhone)
    }
    
    func isMatch(formatterPhone: String?, filteredPhone: String) -> Bool {
        if formatterPhone?.length == 0 || filteredPhone.length == 0 {
            return false
        }
        
        // 如果手机号是 xx-xxx 的格式，先使用后缀进行匹配
        let components = formatterPhone?.phoneComponent()
        if let comp = components, comp.count > 1 {
            let phoneNumber = comp[1]
            return filteredPhone.hasSuffix(phoneNumber)
        } else {
            // 否则整串匹配
            if let phone = formatterPhone {
                return filteredPhone.hasSuffix(phone)
            }
            return false
        }
    }
}

// MARK: SessionManager状态监听
extension ContactsSyncUtil: BuzUserSessionObserver{
    // 登录成功
    func userSessionDidLoginSuccess(_ mgr: BuzUserSession) {
        initData()
    }
    
    // 退登
    func userSessionDidLogoutSuccess(_ mgr: BuzUserSession) {
        self.isSyncing = false
    }
}

//MARK: 本地通讯录信息获取
extension ContactsSyncUtil{
    private func initData(){
        BuzContactsLog.debug("当前用户regionCode 开始计算")

        //初始化比较耗时，需要放在子线程执行
        DispatchQueue.contactQueue().async {
           
            self.initPhoneNumberKit()
            self.initCurrentUesrInfo()
        }
    }
    
    private func initPhoneNumberKit() {
        if self.phoneNumberKit == nil {
            BuzContactsLog.info("ContactsSyncUtil - initPhoneNumberKit")
            self.phoneNumberKit = PhoneNumberUtility()
        }
    }
    
    private func initCurrentUesrInfo(){
        guard var currentUserPhone = BuzUserSession.shared.userInfo?.base.phone else {
            self.currentRegionCode = BuzContactsCenter.info.provider?.getLocalEmailCountryCode()
            self.currentUserFormatterPhone = ""
            return
        }
        currentUserPhone = currentUserPhone.replacingOccurrences(of: "-", with: " ")
        currentUserPhone.insert("+", at: currentUserPhone.startIndex)
        
        self.initPhoneNumberKit()
        
        guard let phoneNumberKit = self.phoneNumberKit else {
            return
        }
        do {
            let currentUserPhoneNumber = try phoneNumberKit.parse(currentUserPhone)
            if let regionCode =  phoneNumberKit.getRegionCode(of: currentUserPhoneNumber) {
                self.currentRegionCode = regionCode
                BuzContactsLog.debug("当前用户regionCode:\(regionCode)")
                let phoneNumber = self.parsePhoneNumber(originPhone: currentUserPhone)
                self.currentUserFormatterPhone = self.formatterPhone(phoneNumber: phoneNumber)
            } else {
                BuzContactsLog.error("获取当前用户regionCode失败")
                self.currentRegionCode = BuzContactsCenter.info.provider?.getLocalEmailCountryCode()
                self.currentUserFormatterPhone = ""
            }
        } catch {
            BuzContactsLog.error("获取当前用户regionCode失败")
            self.currentRegionCode = BuzContactsCenter.info.provider?.getLocalEmailCountryCode()
            self.currentUserFormatterPhone = ""
        }
    }
    
    func requestAddressBook(callbackOnGlobalQueue:((_ contactsArray: [BuzUserContacts]) -> Void)?, authorizationFailure:(()-> Void)?){
        #warning("donghong todo: 优化-这里可能存在重入逻辑，读取大通讯录可能有问题")
        var hasCallback = false
        if let phoneCache = self.phoneCache {
            hasCallback = true
            DispatchQueue.contactQueue().async {
                callbackOnGlobalQueue?(phoneCache)
            }
        }
        
        ContactsPermissionRequester.getOriginalAddressBook { addressBookArray in
            BuzContactsLog.debug("requestAddressBook addressBookArray：\(addressBookArray) ")
            DispatchQueue.contactQueue().async {
                var result :[BuzUserContacts] = []
                var phoneSet = Set<String>()
                
                let insertClosure: (BuzUserContacts) -> Void = { [weak self] contact in
                    guard let `self` = self else { return }
                    guard let phone = contact.phone else {
                        return
                    }
                    
                    if phoneSet.contains(phone) {
                        BuzContactsLog.debug("ContactsSyncUtil - 重复号码 name:\(contact.firstName ?? "")\(contact.lastName ?? "") phone: \(phone)")
                        return
                    }
                    
                    if self.isSelfPhone(phone: phone) {
                        BuzContactsLog.debug("ContactsSyncUtil - 本人号码 name:\(contact.firstName ?? "")\(contact.lastName ?? "") phone: \(phone)")
                        return
                    }
                    
                    phoneSet.insert(phone)
                    result.append(contact)
                }
                
                for item: TYAddressBookModel in addressBookArray {
                    for phone in item.mobileArray {
                        if phone.isEmpty { continue }
                        
                        // 去除所有非数字字符
                        let originalPhone = phone.filter { $0.isNumber }
                        let contact = BuzUserContacts.init(phone: phone,
                                                           originalPhone:originalPhone,
                                                           firstName: item.firstName,
                                                           lastName: item.lastName)
                        insertClosure(contact)
                    }
                }
                
                let list1 = result.filter { model1 in
                    return model1.firstLetter != "#"
                }.sorted { model1, model2 in
                    return model1.firstLetter ?? "#" < model2.firstLetter ?? "#"
                }
                let list2 = result.filter { model1 in
                    return model1.firstLetter == "#"
                }
                result = list1 + list2
                
                if !hasCallback {
                    callbackOnGlobalQueue?(result)
                }
                self.phoneCache = result

            }
        } authorizationFailure: {
            DispatchQueue.contactQueue().async {
                if !hasCallback {
                    authorizationFailure?()
                }
            }
            BuzContactsLog.error("home 通讯录授权失败")
        }
    }
         
    //把手机通讯录中联系人号码转成格式化后的号码 86-13800138000
    private func parsePhoneNumber(originPhone: String) -> PhoneNumber? {
        //通讯录原本带地区编码的按原编码，没有的使用当前登录用户的地区编码(currentRegionCode)
        // 不符合规则的过滤
        self.initPhoneNumberKit()
        
        guard let phoneNumberKit = self.phoneNumberKit else {
            return nil
        }
        
        if currentRegionCode == nil {
            initCurrentUesrInfo()
        }
        
        do {
            let phoneNumber = try phoneNumberKit.parse(originPhone, withRegion: currentRegionCode ?? "")
            return phoneNumber
        }
        catch {
            BuzContactsLog.info("ContactsSyncUtil - 使用 phoneNumberKit 解析失败, originPhone: \(originPhone)")
            return nil
        }
    }

    // 格式化手机号
    private func formatterPhone(phoneNumber: PhoneNumber?) -> String?{
        guard let phoneNumber = phoneNumber else {
            return nil
        }
        
        let formattedString = "\(phoneNumber.countryCode)-\(phoneNumber.nationalNumber)"
        return formattedString
    }
    
    class func isValidPhone(phone: String) -> Bool{
        do {
            let phoneNumberKit = PhoneNumberUtility()
            return phoneNumberKit.isValidPhoneNumber(phone)
        }
       
    }
    
    //转成系统的格式 (发短信等需要使用这个格式)
    // 86-13800138000 -> +8613800138000
    class func formatterPhoneToSystemPhone(originPhone: String) -> String?{
        var phone = originPhone.replacingOccurrences(of: "-", with: " ")
        if false == phone.hasPrefix("+") {
            phone.insert("+", at: phone.startIndex)
        }
        
        return phone
    }
    
    class func formatterPhoneToInternationalPhone(originPhone: String) -> String?{
        let phoneNumberKit = PhoneNumberUtility()
        guard let phoneNumber = try? phoneNumberKit.parse(originPhone) else { return nil }
        var phone = phoneNumberKit.format(phoneNumber, toType: PhoneNumberFormat.international)
        return phone
    }
}
