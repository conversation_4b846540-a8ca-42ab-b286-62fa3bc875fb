//
//  ContactsNetwork.swift
//  buz
//
//  Created by l<PERSON>hi on 2022/7/11.
//  Copyright © 2022 lizhi. All rights reserved.
//

import BuzIDL
import ITNetLibrary
import UIKit
import BuzLog
import BuzNetworker
import BuzUserSession

enum UploadContactRequestStatus : String {
    case none = "no_friend"
    case success = "success"
    case loading = "loading"
    case fail = "fail"
}

public class ContactsNetwork: NSObject {
    typealias CompletionHandler = (_ rcode: Int) -> Void
    public typealias UserInfoListCompletion = (_ rcode: Int, _ userList: [UserInfo]?) -> Void
    
    /// 上传通讯录
    /// - Parameters:
    ///   - contactList：通讯录联系人列表
    ///   - compressContacts: gzip 压缩后的通讯录列表
    ///   - type: 1 全量上传 2 增量上传 3 手动设置（App内修改通讯录）
    ///   - uploaderPhoneCountryCode：上传人的手机号国家码，如 86
    ///   - complete: 结果
    static func uploadContacts(contactList: [Contact], compressContacts: Data, type: Int32, uploaderRegionCode: String, traceId:String, isRetry:Bool, complete: ((_ rcode: Int) -> Void)?) {
#if DEBUG
        BuzLog.debug("home api uploadContacts type:\(type) uploaderRegionCode:\(uploaderRegionCode) =======start")
        for item in contactList {
            if item.type == 1 {
                BuzLog.debug("home api uploadContacts: add phone:\(String(describing: item.phone)) firstName:\(String(describing: item.firstName)) lastName:\(String(describing: item.lastName))")
            } else {
                BuzLog.debug("home api uploadContacts: delete phone:\(String(describing: item.phone))")
            }
        }
        BuzLog.debug("home api uploadContacts =======end")
#endif
        let mgr = BuzUserSession.shared
        mgr.currentUploadContactsStatus = .loading
        
        // 重试的请求使用默认超时时间
        var client = BuzNetUserServiceClient()
        if isRetry == false {
            // 通讯录上传接口的超时时间单独设置，设置为30秒
            client = BuzNetUserServiceClient(timeout: 30)
        }

        let req = RequestUploadContacts(type: type, uploaderRegionCode: uploaderRegionCode, compressContacts: compressContacts, traceId: traceId)
         _ = client.uploadContacts(request: req) { result in
            switch result {
            case let .success(responseInfo):
                mgr.currentUploadContactsStatus = responseInfo.code == 0 ? .success : .fail
                complete?(responseInfo.code)
                BuzLog.debug("home api uploadContacts msgId=======\(String(describing: responseInfo.msg))")
            case .failure:
                mgr.currentUploadContactsStatus = .fail
                complete?(NetworkErrorCode)
            }
        }
    }
    
    /// 上传通讯录
    /// - Parameters:
    ///   - contactList: 通讯录列表
    ///   - type: 1 全量上传(覆盖) 2 增量上传
    ///   - complete: 结果
    static func uploadContacts(contactList: [Contact], type: Int32, complete: ((_ rcode: Int) -> Void)?) {
#if DEBUG
        BuzLog.debug("home api uploadContacts type:\(type) =======start")
        for item in contactList {
            if item.type == 1 {
                BuzLog.debug("home api uploadContacts: add phone:\(String(describing: item.phone)) firstName:\(String(describing: item.firstName)) lastName:\(String(describing: item.lastName))")
            } else {
                BuzLog.debug("home api uploadContacts: delete phone:\(String(describing: item.phone))")
            }
        }
        BuzLog.debug("home api uploadContacts =======end")
#endif
        let mgr = BuzUserSession.shared
        mgr.currentUploadContactsStatus = .loading
        let client = BuzNetUserServiceClient()
         _ = client.uploadContacts(request: RequestUploadContacts(contactList: contactList, type: type)) { result in
            switch result {
            case let .success(responseInfo):
                mgr.currentUploadContactsStatus = responseInfo.code == 0 ? .success : .fail
                complete?(responseInfo.code)
                BuzLog.debug("home api uploadContacts msgId=======\(String(describing: responseInfo.msg))")
            case .failure:
                mgr.currentUploadContactsStatus = .fail
                complete?(NetworkErrorCode)
            }
        }
    }
    
    static func getUploadContactType(contactSize: Int32, completion: ((_ isSuccess: Bool, _ uploadType: Int32?) -> Void)?) {
        let client = BuzNetUserServiceClient(encrypt: true)
        let req = RequestGetUploadContactType(contactSize: contactSize)
        let _ = client.getUploadContactType(request: req) { result in
            switch result {
            case .success(let responseInfo):
                if responseInfo.code == 0 {
                    completion?(true, responseInfo.data?.uploadType)
                } else {
                    completion?(false, nil)
                }
            case .failure(_):
                completion?(false, nil)
            }
        }
    }

    static func isRegistered(phone: String, complete: ((_ rcode: Int, _ isRegistered: Bool) -> Void)?) {
        let client = BuzNetUserServiceClient(encrypt: true)
        let req = RequestJudgeIsRegister(phoneList: [phone])
        _ = client.judgeIsRegister(request: req) { result in
            switch result {
            case let .success(responseInfo):
                if let isRegistered = responseInfo.data?.judgeResults.first?.registered {
                    complete?(responseInfo.code, isRegistered)
                } else {
                    complete?(responseInfo.code, false)
                }
            case let .failure(error):
                BuzLog.error("isRegistered failure:\(error)")
                complete?(NetworkErrorCode, false)
            }
        }
    }

    /// 获取好友列表(全量)
    /// - Parameters:
    ///   - timestamp: 时间戳
    ///   - complete: 结果
    static func getFriendList(timestamp: Int64, complete: ((_ rcode: Int, _ info: ResponseGetFriendList?) -> Void)?) {
        BuzLog.debug("home api getFriendList timestamp:\(timestamp)")
        let client = BuzNetUserServiceClient(encrypt: true)
        _ = client.getFriendList(request: RequestGetFriendList(timestamp: timestamp)) { result in
            switch result {
            case let .success(responseInfo):
                complete?(responseInfo.code, responseInfo.data)
            case .failure:
                complete?(NetworkErrorCode, nil)
            }
        }
    }

    static func black(userId: Int64, completion handler: CompletionHandler?) {
        let client = BuzNetUserServiceClient(encrypt: true)
        let req = RequestOperateBlackList(userIds: [userId], type: 1)
        _ = client.operateBlackList(request: req) { result in
            switch result {
            case let .success(responseInfo):
                handler?(responseInfo.code)
            case .failure:
                handler?(NetworkErrorCode)
            }
        }
    }
    
    static func unblock(userId: Int64, completion handler: CompletionHandler?) {
        let client = BuzNetUserServiceClient(encrypt: true)
        let req = RequestOperateBlackList(userIds: [userId], type: 2)
        _ = client.operateBlackList(request: req) { result in
            switch result {
            case let .success(responseInfo):
                handler?(responseInfo.code)
            case .failure:
                handler?(NetworkErrorCode)
            }
        }
    }
    
    public static func queryBlockedList(completion handler: @escaping UserInfoListCompletion) {
        let client = BuzNetUserServiceClient(encrypt: true)
        let req = RequestGetBlackList()
        _ = client.getBlackList(request: req, completion: { result in
            switch result {
            case let .success(responseInfo):
                BuzLog.info("blackUserList - \(String(describing: responseInfo.data?.blackUserList))")
                handler(responseInfo.code, responseInfo.data?.blackUserList)
            case .failure:
                handler(NetworkErrorCode, nil)
            }
        })
    }
    
    static func getAppShareLink(with type: Int32, params: String, completion: @escaping (_ isSuccess: Bool, _ link: String?) -> Void) {
        let client = BuzNetCommonServiceClient()
        let request = RequestGetAppsflyerLink(type: type, params: params)
        let _ = client.getAppsflyerLink(request: request) { result in
            switch result {
            case let .success(resp):
                completion(resp.code == 0, resp.data?.link)
            case .failure(_):
                completion(false, nil)
            }
        }
    }
    
    
}
