//
//  BuzContactsModel.h
//  buz
//
//  Created by lizhi on 2022/7/7.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>
#import "YYModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface BuzContactsModel : NSObject

@property (nonatomic, copy , nullable) NSString *firstName;

@property (nonatomic, copy , nullable) NSString *lastName;

//第一个字母
@property (nonatomic, copy , nullable) NSString *firstLetter;

/// 旧版本存储的是：格式化后的手机号(86-18112345678)，新版本（加好友流程优化）存储的是通讯录原始的手机号
@property (nonatomic, copy , nullable) NSString *phone;

@end

NS_ASSUME_NONNULL_END
