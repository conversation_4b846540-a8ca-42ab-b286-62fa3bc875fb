//
//  BuzContactsModel.m
//  buz
//
//  Created by lizhi on 2022/7/7.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "BuzContactsModel.h"

@implementation BuzContactsModel

- (BOOL)isEqual:(id)object{
    if ([object class] != [self class]) {
        return [super isEqual:object];
    }
    //此处用来对比数据库和系统通讯录差集， firstName/lastName/phone都相等即认为相等
    BuzContactsModel *model = object;
    if ([self.phone isEqual:model.phone] &&
        [self.firstName isEqualToString:model.firstName] &&
        [self.lastName isEqualToString:model.lastName]
        ) {
        return YES;
    }
    
    return NO;
}

-(NSUInteger)hash{
    return self.phone.hash ^ self.firstName.hash ^ self.lastName.hash;
}

- (NSString *)description{
    return [self yy_modelToJSONString];
}
@end
