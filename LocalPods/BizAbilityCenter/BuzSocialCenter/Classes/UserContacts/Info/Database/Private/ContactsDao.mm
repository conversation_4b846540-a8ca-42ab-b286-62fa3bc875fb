//
//  ContactsDao.m
//  buz
//
//  Created by lizhi on 2022/7/7.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "ContactsDao.h"
#import "BuzContactsModel+WCTTableCoding.h"
#import "BuzDatabaseManager.h"
#import "OCBuzLog.h"
#import "BlackUserDao.h"
#import "BlackUserModel+WCTTableCoding.h"
#import "OCBuzUserSession.h"

#define DATABASE [BuzDatabaseManager database]

@interface ContactsDao ()

@property (nonatomic, strong) NSMutableSet <NSNumber *>* createdTableSet;

@end

@implementation ContactsDao

#pragma mark - public
/** 更新联系人数据**/
- (void)addOrUpdateContacts:(NSArray <BuzContactsModel *>*)contacts complete:(nullable void (^)(BOOL))complete{
    if (contacts.count == 0) {
        [self executeTaskInMainQueue:^{
            complete(YES);
        }];
        return;
    }
    
    [self executeTaskInDatabaseQueue:^{
              
        NSString *tableName =  [self getTableName];
        __block BOOL isUpdateSuccess = YES;
        
        [DATABASE runTransaction:^BOOL{
            for (BuzContactsModel *model in contacts) {
                isUpdateSuccess = [DATABASE insertOrReplaceObject:model into:tableName];
//                BuzDBLogD(@"ContactsDao 批量插入数据结果%d , userName = %@" , isUpdateSuccess, model.phone);
            }
            return isUpdateSuccess;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
//                BuzDBLogD(@"ContactsDao 批量插入数据处理完成 -- 回调外界结果%d" , isUpdateSuccess);
                complete(isUpdateSuccess);
            }];
        }
    }];
}


/** 通过手机号删除用户数据**/
- (void)deleteContactsWithPhoneList:(NSArray <NSString *>*)phoneList complete:(nullable void (^)(BOOL))complete{
    if (phoneList.count == 0) {
        [self executeTaskInMainQueue:^{
            complete(YES);
        }];
       
        return;
    }
    
    [self executeTaskInDatabaseQueue:^{
       
        NSString *tableName = [self getTableName];
        __block BOOL isUpdateSuccess = YES;
        
        [DATABASE runTransaction:^BOOL{
            
            for (NSString *phone in phoneList) {
                isUpdateSuccess = [DATABASE deleteObjectsFromTable:tableName where:BuzContactsModel.phone == phone];
            }
            return isUpdateSuccess;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"ContactsDao - 通过手机号删除用户数据处理完成 -- 回调外界结果%d" , isUpdateSuccess);
                complete(isUpdateSuccess);
            }];
        }
    }];
}


/** 根据手机号获取联系人**/
- (void)getContactsWithPhone:(NSString *)phone complete:(void (^)(BuzContactsModel * _Nullable))complete{
    [self executeTaskInDatabaseQueue:^{
        NSString *tableName = [self getTableName];

        NSArray *arr = [self queryContactsWhere:BuzContactsModel.phone.inTable(tableName) == phone orderBy:{} filterBlackUser:NO];
        BuzContactsModel *model = arr.firstObject;
     
        if (complete) {
            [self executeTaskInMainQueue:^{
                complete(model);
            }];
        }
    }];
}

/** 根据手机列表获取联系人**/
- (void)getContactsWithPhoneList:( NSArray <NSString *> *)phoneList complete:(void (^)(NSArray <BuzContactsModel *> *))complete{
    
    if (phoneList.count == 0) {
        [self executeTaskInMainQueue:^{
            complete(@[]);
        }];
       
        return;
    }
    
    [self executeTaskInDatabaseQueue:^{
       
        __block NSMutableArray *result = [NSMutableArray array];

        [DATABASE runTransaction:^BOOL{
            NSString *tableName = [self getTableName];

            for (NSString *phone in phoneList) {
                NSArray *arr = [self queryContactsWhere:BuzContactsModel.phone.inTable(tableName) == phone orderBy:{} filterBlackUser:NO];
                BuzContactsModel *model = arr.firstObject;
            
                if (model) {
                    [result addObject:model];
                }
            }
            return YES;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                complete(result);
            }];
        }
    }];
}

         

/** 获取所有联系人数据按首字母排序**/
- (void)getAllContactsOrderByFirstLetterFilterBlackUser:(BOOL)filterBlackUser complete:(void (^)(NSArray <BuzContactsModel *>* nullable))complete{
    
    [self executeTaskInDatabaseQueue:^{
        NSString *tableName = [self getTableName];
        
        __block NSMutableArray *result = [NSMutableArray array];
        [DATABASE runTransaction:^BOOL{
            
            //未注册用户（非#号）
            NSArray *array1 = [self queryContactsWhere:
                               BuzContactsModel.firstLetter.inTable(tableName) != @"#" orderBy:{BuzContactsModel.firstLetter.inTable(tableName).order(WCTOrderedAscending)} filterBlackUser:filterBlackUser];
            
            //未注册用户(#号)
            NSArray *array2 = [self queryContactsWhere:
                                    BuzContactsModel.firstLetter.inTable(tableName) == @"#" orderBy:{} filterBlackUser:filterBlackUser];
                    
            [result addObjectsFromArray:array1];
            [result addObjectsFromArray:array2];
            return YES;
        }];
        
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                complete(result);
            }];
        }
    }];
}

#pragma mark - private
//联表查询BuzContactsModel和BuzUserInfo的信息,是否需要过滤黑名单用户
- (NSMutableArray <BuzContactsModel *>*)queryContactsWhere:(const WCTCondition &)condition orderBy:(WCDB::OrderList)orderByList filterBlackUser:(BOOL)filterBlackUser{

    NSString *tableName = [self getTableName];
//    NSString *userInfoTableName = [BuzUserInfoDao.sharedInstance getTableName];
    NSString *blackTableName = [BlackUserDao.sharedInstance getTableName];
    
    char* tableNameChar = (char*) [tableName cStringUsingEncoding:NSUTF8StringEncoding];
//    char* userInfoTableNameChar = (char*) [userInfoTableName cStringUsingEncoding:NSUTF8StringEncoding];
    char* blackTableNameChar = (char*) [blackTableName cStringUsingEncoding:NSUTF8StringEncoding];
    
    NSMutableArray <BuzContactsModel *> *result = [NSMutableArray array];
    
    WCDB::JoinClause join = WCDB::JoinClause(tableNameChar);
        
    if (filterBlackUser) {
        join.join(blackTableNameChar, WCDB::JoinClause::Type::Left).on( BuzContactsModel.phone.inTable(tableName) == BlackUserModel.blackPhone.inTable(blackTableName));
    }
        
    WCTResultList resultList = WCTResultList(
                                             {
                                                BuzContactsModel.firstName.inTable(tableName),
                                                BuzContactsModel.lastName.inTable(tableName),
                                                BuzContactsModel.firstLetter.inTable(tableName),
                                                BuzContactsModel.phone.inTable(tableName),
                                              
                                             });  // 同上不能写在 .select() 里面
    
    //过滤拉黑用户
    if (filterBlackUser) {
        resultList = WCTResultList(
                                {
                                BuzContactsModel.firstName.inTable(tableName),
                                BuzContactsModel.lastName.inTable(tableName),
                                BuzContactsModel.firstLetter.inTable(tableName),
                                BuzContactsModel.phone.inTable(tableName),
                                          
                                BlackUserModel.uid.inTable(blackTableName),
//                                BlackUserModel.blackPhone.inTable(blackTableName)
                        });  // 同上不能写在 .select() 里面
    }

    WCDB::StatementSelect select = WCDB::StatementSelect().select(resultList, false).from(join).where(condition).orderBy(orderByList);

    WCTStatement *statement = [DATABASE prepare:select];
    while (statement && [statement step]) {
        NSMutableDictionary *dict = [NSMutableDictionary dictionary];
        
      
        for (int i = 0; i < statement.getColumnCount; i++) {
            NSString *key = [statement getColumnNameAtIndex:i];
            WCTValue *value = [statement getValueAtIndex:i];
            dict[key] = value;
        }
        
        BOOL isBlackUser = (dict[@"uid"] != nil) || (dict[@"blackPhone"] != nil);
        if (filterBlackUser && isBlackUser) {
            BuzLogD("ContactsDao 黑名单过滤: %@ %@",dict[@"uid"], dict[@"blackPhone"]);
            continue;
        }
        
        BuzContactsModel *model = [BuzContactsModel yy_modelWithDictionary:dict];
        [result addObject:model];
    }
    return result;
}

- (void)executeTaskInDatabaseQueue:(void(^)(void))task
{
    buz_dispatch_queue_async_safe(buz_database_queue(),task);
}

- (void)executeTaskInMainQueue:(void(^)(void))task
{
    buz_dispatch_queue_async_safe(dispatch_get_main_queue(),task);
}

- (NSString *)getTableName
{   
    NSString *tableName = [NSString stringWithFormat:@"ContactsTable"];
    
    NSNumber *uid = @(OCBuzUserSession.uid);
    if (![self.createdTableSet containsObject:uid]) {
        
        BOOL result = [DATABASE createTableAndIndexesOfName:tableName withClass:BuzContactsModel.class];
        if (result) {
            [self.createdTableSet addObject:uid];
        }else{
            NSAssert(NO, @"create Contacts table failure!!!");
            return nil;
        }
    }
    return tableName;
}


+ (instancetype)sharedInstance {
    static id instance = nil;
    if (instance == nil) {
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            instance = [[self alloc] init];
        });
    }
    return instance;
}

- (NSMutableSet<NSNumber *> *)createdTableSet
{
    if (nil == _createdTableSet)
    {
        _createdTableSet = [[NSMutableSet alloc] init];
    }
    return _createdTableSet;
}

@end
