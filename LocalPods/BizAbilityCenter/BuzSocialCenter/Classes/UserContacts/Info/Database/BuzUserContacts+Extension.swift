//
//  BuzUserContacts+Extension.swift
//  buz
//
//  Created by st.chio on 2025/2/14.
//  Copyright © 2025 lizhi. All rights reserved.
//


//MARK: ---BuzUserData----
extension BuzUserContacts {
    /// 数据库对象初始化
    /// - Parameter entity: dao实体对象
    convenience init(entity: BuzContactsModel) {
        self.init()
        self.firstName = entity.firstName
        self.lastName = entity.lastName
        self.firstLetter = entity.firstLetter
        self.phone = entity.phone
    }
    
    /// 数据库转换为DaoEntity
    /// - return: dao实体对象
    func convertDaoEntity() -> BuzContactsModel {
        let entity = BuzContactsModel.init()
        
        entity.firstName = self.firstName
        entity.lastName = self.lastName
        entity.firstLetter = self.firstLetter
        entity.phone = self.phone
        
        return entity
    }
}
