//
//  ContactsDao.h
//  buz
//
//  Created by lizhi on 2022/7/7.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class BuzContactsModel;
@interface ContactsDao : NSObject

+ (instancetype)sharedInstance;

//// 所有操作仅针对当前登录用户操作
///同一个联系人可能对应了设备登录过的多个账号，只对当前登录账号的数据做操作

/** 更新联系人数据**/
- (void)addOrUpdateContacts:(NSArray <BuzContactsModel *>*)contacts complete:(nullable void (^)(BOOL))complete;

/** 通过手机号删除用户数据**/
- (void)deleteContactsWithPhoneList:(NSArray <NSString *>*)phoneList complete:(nullable void (^)(BOOL))complete;

/** 根据手机号获取联系人**/
- (void)getContactsWithPhone:(NSString *)phone complete:(void (^)(BuzContactsModel * _Nullable))complete;

/** 根据手机列表获取联系人**/
- (void)getContactsWithPhoneList:( NSArray <NSString *> *)phoneList complete:(void (^)(NSArray <BuzContactsModel *> *))complete;


/** 获取所有联系人数据按首字母(升)排序, 是否过滤拉黑用户**/
- (void)getAllContactsOrderByFirstLetterFilterBlackUser:(BOOL)filterBlackUser complete:(void (^)(NSArray <BuzContactsModel *>* nullable))complete;

@end

NS_ASSUME_NONNULL_END
