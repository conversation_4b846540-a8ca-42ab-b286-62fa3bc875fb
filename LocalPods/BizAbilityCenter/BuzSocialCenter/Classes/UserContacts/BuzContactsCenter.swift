//
//  BuzContactsCenter.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/19.
//

import BuzNetworker

@objc public protocol BuzContactsCenterObserver: AnyObject {}

public class BuzContactsCenter: NSObject, BuzSocialEventPublisher, BuzSocialEventPublisherInternal {
    public typealias Observer = BuzContactsCenterObserver
    internal let observers = NSHashTable<BuzContactsCenterObserver>.weakObjects()
    
    @objc public static let center = BuzContactsCenter()
    
    private var controllers: [ControllerType: Any] = .init()
    
    override init() {
        super.init()
        BuzNetworker.shared.signalPush.addObserver(self)
    }
}

extension BuzContactsCenter {
    enum ControllerType {
        case info      // 信息模块
    }
    
    //MARK: -info---
    @objc public static var info: ContactsManager {
        return center.controller(for: .info)
    }
    
    private func controller<T>(for type: ControllerType) -> T {
        if let controller = self.controllers[type] as? T {
            return controller
        }
        
        let controller: Any
        switch type {
        case .info:
            controller = ContactsManager(center: self)
        }
        
        self.controllers[type] = controller
        
        return controller as! T
    }
}

extension BuzContactsCenter: BuzSignalPushEventObserver {
    public func signalPushDidReceivedMessage(_ message: BuzSignalPush.Message) {
        
    }
    
    public func signalPushDidChangedConnection(connection: BuzSignalPush.Connection) {
        BuzContactsCenter.info.signalPushDidChangedConnection(connection: connection)
    }
}
