//
//  BuzSocialCenter.swift
//  buz
//
//  Created by st.chio on 2025/2/13.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation

public class BuzSocialCenter { }

// MARK: ---BuzSocialEventPublisher--------
public protocol BuzSocialEventPublisher {
    associatedtype Observer: AnyObject
    
    func addObserver(_ observer: Observer)
    func removeObserver(_ observer: Observer)
}

protocol BuzSocialEventPublisherInternal: BuzSocialEventPublisher {
    var observers: NSHashTable<Observer> { get }
    func notifyObservers<T>(of type: T.Type, _ event: (T) -> Void)
}

extension BuzSocialEventPublisherInternal {
    public func addObserver(_ observer: Observer) {
        observers.add(observer)
    }
    
    public func removeObserver(_ observer: Observer) {
        observers.remove(observer)
    }
    
    func notifyObservers<T>(of type: T.Type, _ event: (T) -> Void) {
        let observers = observers.allObjects.compactMap { $0 as? T }
        for observer in observers {
            event(observer)
        }
    }
}

// MARK: ---BuzSocialControllerable--------
protocol BuzSocialControllerable {
    associatedtype Center: AnyObject
    var center: Center? { get }
    
    init( center: Center?)
}
