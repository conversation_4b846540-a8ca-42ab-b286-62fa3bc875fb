//
//  BuzBotCenter.swift
//  aac_decode-iOS
//
//  Created by st.chio on 2025/2/19.
//

import BuzNetworker

//MARK: --Notification--
public extension Notification.Name {
    static let TranslateBotDidUpdateSourceLanguateSetting : Notification.Name = Notification.Name.init("TranslateBotDidUpdateSourceLanguateSetting")
    static let TranslateBotDidUpdateTargetLanguateSetting : Notification.Name = Notification.Name.init("TranslateBotDidUpdateTargetLanguateSetting")
}

@objc public protocol BuzBotCenterObserver: AnyObject {}

public class BuzBotCenter: NSObject, BuzSocialEventPublisher, BuzSocialEventPublisherInternal {
    public typealias Observer = BuzBotCenterObserver
    internal let observers = NSHashTable<BuzBotCenterObserver>.weakObjects()
    
    @objc
    public static let center = BuzBotCenter()
    private var controllers: [ControllerType: Any] = .init()
    
    override init() {
        super.init()
        BuzNetworker.shared.signalPush.addObserver(self)
    }
    
}

extension BuzBotCenter {
    enum ControllerType {
        case info      // 信息模块
        case imGroup  // 群组机器人模块
    }
    
    //MARK: -info---
    @objc public static var info: BotInfoManager {
        return center.controller(for: .info)
    }
    //MARK: -imGroup---
    public static var imGroup: BuzIMGroupBotManager {
        return center.controller(for: .imGroup)
    }
    
    private func controller<T>(for type: ControllerType) -> T {
        if let controller = self.controllers[type] as? T {
            return controller
        }
        
        let controller: Any
        switch type {
        case .info:
            controller = BotInfoManager(center: self)
        case .imGroup:
            controller = BuzIMGroupBotManager(center: self)
        }
        
        self.controllers[type] = controller
        
        return controller as! T
    }
}

extension BuzBotCenter: BuzSignalPushEventObserver {
    public func signalPushDidReceivedMessage(_ message: BuzSignalPush.Message) {
        BuzBotCenter.imGroup.signalPushDidReceivedMessage(message)
    }
}
