//
//  BotsOfGroupManager.swift
//  buz
//
//  Created by lizhifm on 2023/11/17.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import BuzIDL
import YYModel
import ITNetLibrary
import BuzLog
import BuzDataStore
import BuzNetworker

@objcMembers
private class IMGroupBotCacheData : NSObject {
    var groupBotList : [BuzIMGroupBotConfig] = []
    
    static func modelContainerPropertyGenericClass() -> [String : Any]? {
        return ["groupBotList":BuzIMGroupBotConfig.classForCoder()]
    }
}

@objcMembers
public class BuzIMGroupBotConfig: NSObject {
    public var groupId : Int64 = 0
    public var botIds : [Int64]?
    public var dirty : Bool = false
    
    public override init() {
    }
    
    public init(groupId : Int64, botIds : [Int64]?) {
        self.groupId = groupId
        self.botIds = botIds
    }
    
    public func botInfos() -> [BuzBotInfo] {
        var botInfos : [BuzBotInfo] = []
        
        self.botIds?.forEach({ botId in
            if let botInfo = BuzBotCenter.info.queryBotInfo(botId) {
                botInfos.append(botInfo)
            } else {
                botInfos.append(BuzBotInfo.init(botUserId: botId))
            }
        })
        
        return botInfos
    }
    
    public func validBotInfos() -> [BuzBotInfo] {
        return self.botInfos().filter { info in
            return info.userInfo != nil
        }
    }
    
    public func isAIEmpty() -> Bool {
        return self.botIds?.count ?? 0 == 0
    }
}

@objc public protocol BuzBotCenterIMGroupObserver: BuzBotCenterObserver {
    @objc optional func groupBotConfigDidChange(config : BuzIMGroupBotConfig)
    @objc optional func groupBotConfigDidAdd(config : BuzIMGroupBotConfig)
    @objc optional func groupBotDidStartDeleteAction(groupId : Int64, botId : Int64)
    @objc optional func groupBotDidEndDeleteAction(groupId : Int64, botId : Int64)
}

public typealias IMGroupBotlistResultInMemoryCache = ((BuzIMGroupBotConfig? , Bool) -> Void)
public typealias IMGroupBotlistResultByNetwork = (BuzIMGroupBotConfig?) -> Void

public class BuzIMGroupBotManager: NSObject, BuzSocialControllerable {
    typealias Center = BuzBotCenter
    weak var center: BuzBotCenter?
    
    public private(set) var publisher = Publisher.init()
    
    private static let tipKey = "BotsOfGroupManager_tipKey"
    public static let atTipKey = "BotsOfGroupManager_atTipKey"
    public static let holdingRecordTipKey = "BotsOfGroupManager_holdingRecordTipKey"
    public static let tipTag = 80808
    public static let atTipTag = 80809
    public static let holdingRecordTipTag = 80810
    private var groupAddBotCache : [Int64] = []
    private var groupBotMembers : [Int64 : BuzIMGroupBotConfig] = [:]
    private var groupRequestings : [Int64 : ITFuture] = [:]
    private var deletingMap : [Int64 : NSMutableSet] = [:]
    private var writerlock = NSLock()
    private var isLoadingFinish = false
    private let cacheQueue = DispatchQueue.init(label:"BotsOfGroupManager")
    private let cacheWriterQueue = DispatchQueue.init(label:"BotsOfGroupManager_writer")
    public private(set) var queryBotListCallbackHolderDict : [Int64 : [IMGroupBotlistResultByNetwork]] = [:]
    private lazy var botOfGroupPath : String = {
        var path : NSString = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first! as NSString
        return path.appendingPathComponent("BotsOfGroupManager.json")
    }()
    
    required init(center: BuzBotCenter?) {
        super.init()
        self.center = center
        self.loadData()
    }
}

extension BuzIMGroupBotManager {
    func loadData() {
        let path = self.botOfGroupPath
        cacheQueue.async {
            self.writerlock.lock()
            if let data = NSData.init(contentsOf: URL.init(fileURLWithPath: path)) {
                if let content = String.init(data: data as Data, encoding: .utf8),
                   let cacheData : IMGroupBotCacheData = IMGroupBotCacheData.yy_model(withJSON: content) {
                    DispatchQueue.main.async {
                        cacheData.groupBotList.forEach { config in
                            config.dirty = true
                            self.groupBotMembers[config.groupId] = config
                        }
                        
                        self.isLoadingFinish = true
                    }
                }
            }
            
            self.writerlock.unlock()
        }
    }
    
    func cacheData(members : [Int64 : BuzIMGroupBotConfig]) {
        let path = self.botOfGroupPath
        cacheWriterQueue.async {
            self.writerlock.lock()
            do {
                var botList : [BuzIMGroupBotConfig] = []
                let cacheData : IMGroupBotCacheData = IMGroupBotCacheData.init()
                members.forEach { (key: Int64, value: BuzIMGroupBotConfig) in
                    botList.append(value)
                }
                cacheData.groupBotList = botList
                BuzLog.info("BotInviteGroupPresenter updateBotsOfGroup save data \(botList.count)")
                try cacheData.yy_modelToJSONString()?.write(toFile: path, atomically: true, encoding: .utf8)
            }catch{
                BuzLog.info("BotInviteGroupPresenter updateBotsOfGroup cache error = \(error)")
            }
            self.writerlock.unlock()
        }
    }
}

public extension BuzIMGroupBotManager {
    
    //key : groupId, values : botIds
    func updateBotsOfGroup(configs : [BuzIMGroupBotConfig]) {
        configs.forEach { config in
            self.updateConfigCache(config: config)
        }
        
        cacheData(members: self.groupBotMembers)
    }
    
    func canShowGroupTip(groupId : Int64) -> Bool {
        if !self.groupAddBotCache.contains(where: {$0 == groupId}) {
            return false
        }
        
        let key = Self.tipKey
        if MMKV.buz.bool(forKey: key) {
            return false
        }
        
        MMKV.buz.set(true, forKey: key)
        return true
    }
    
    func hasSeenShowTipKey(keys : [String]) {
        keys.forEach { key in
            MMKV.buz.set(true, forKey: key)
        }
    }
    
    func checkDismissTipKey() -> Bool {
        let key = Self.tipKey
        
        if MMKV.buz.bool(forKey: key) {
            MMKV.buz.set(true, forKey: Self.atTipKey)
            return true
        }
        
        return false
    }
    
    func canShowTipForKey(groupId : Int64, key : String) -> Bool {
        if !self.groupAddBotCache.contains(where: {$0 == groupId}) {
            return false
        }
        
        if MMKV.buz.bool(forKey: key) {
            return false
        }
        
        MMKV.buz.set(true, forKey: key)
        return true
    }
}

public extension BuzIMGroupBotManager {
    
    func addGroup(groupId : Int64, botUserIds : [Int64], complete : @escaping ((BuzNetworkResponse<ITResponse<ResponseBotGroupChange>>) -> Void)) {
        BuzLog.info("BotInviteGroupPresenter start addGroup groupId : \(groupId) botId : \(botUserIds)")
        BotNetwork.joinGroupBotMember(groupId, botUserIds) { response in
            if response.isSuccess, let data = response.rawResponseObj?.data, let botMembers = data.botMemberMap {
                botMembers.forEach { (groupId: String, botMembers: [GroupMember]) in
                    let gId = groupId.nsString.longLongValue
                    self.updateBot(groupId: gId, member: botMembers)
                    self.groupAddBotCache.append(gId)
                }
            }
            complete(response)
            BuzLog.info("BotInviteGroupPresenter end addGroup groupId : \(response.isSuccess)")
        }
    }
    
    func kickOutAi(groupId : Int64, botId : Int64, complete : @escaping ((BuzNetworkResponse<ITResponse<ResponseBotGroupChange>>) -> Void)) {
        if self.isDeleting(groupId: groupId, botId: botId) {
            self.center?.notifyObservers(of: BuzBotCenterIMGroupObserver.self) { observer in
                observer.groupBotDidStartDeleteAction?(groupId: groupId, botId: botId)
            }
            self.publisher.didStartDeleteAction.send((groupId, botId))
            BuzLog.info("BotInviteGroupPresenter kickOutAi is isDeleting")
            return
        } else {
            self.deletingMap.setValue(key: groupId, value: NSMutableSet.init(object: botId))
        }
        
        self.center?.notifyObservers(of: BuzBotCenterIMGroupObserver.self) { observer in
            observer.groupBotDidStartDeleteAction?(groupId: groupId, botId: botId)
        }
        self.publisher.didStartDeleteAction.send((groupId, botId))
        
        BuzLog.info("BotInviteGroupPresenter start kickOutAi groupId : \(groupId) botId : \(botId)")
        BotNetwork.exitGroupBotMember(groupId, botId) { response in
            if response.isSuccess, let data = response.rawResponseObj?.data, let botMembers = data.botMemberMap {
                botMembers.forEach { (groupId: String, botMembers: [GroupMember]) in
                    let gId = groupId.nsString.longLongValue
                    self.updateBot(groupId: gId, member: botMembers)
                }
            }
            
            complete(response)
            BuzLog.info("BotInviteGroupPresenter end kickOutAi groupId : \(groupId) botId : \(botId) result: \(response.isSuccess)")
            self.deletingMap.removeValue(forKey: groupId)
            
            self.center?.notifyObservers(of: BuzBotCenterIMGroupObserver.self) { observer in
                observer.groupBotDidEndDeleteAction?(groupId: groupId, botId: botId)
            }
            self.publisher.didEndDeleteAction.send((groupId, botId))
        }
    }
    
    //only memoryComplete -> isDirty == true will call back networkComplete
    func queryBot(groupId : Int64, memoryResultHandler : IMGroupBotlistResultInMemoryCache? ,
                  networkResultHandler : IMGroupBotlistResultByNetwork? = nil, forceDirty : Bool = false) {
        
        BuzIMGroupCenter.info.queryGroupInfoFromCache(groupId: groupId) { groupInfo in
            if groupInfo?.base.groupType ?? .normal == .large {
                memoryResultHandler?(BuzIMGroupBotConfig.init(groupId: groupId, botIds: []), false)
                return
            }
            
            let loadData : (() -> Void) = {
                DispatchQueue.main.safeAsyncUIQueue {
                    var isDirty = true
                    if let BuzIMGroupBotConfig = self.groupBotMembers[groupId] {
                        isDirty = BuzIMGroupBotConfig.dirty
                        memoryResultHandler?(BuzIMGroupBotConfig , isDirty)
                    }else{
                        memoryResultHandler?(nil , isDirty)
                    }
                    
                    if isDirty || forceDirty {
                        if let networkResultHandler = networkResultHandler {
                            var callBackArray = self.queryBotListCallbackHolderDict[groupId]
                            
                            if nil == callBackArray {
                                callBackArray = [networkResultHandler]
                            }else{
                                callBackArray?.append(networkResultHandler)
                            }
                            
                            self.queryBotListCallbackHolderDict[groupId] = callBackArray
                        }
                        
                        if self.groupRequestings[groupId] != nil {
                            //save callback
                            return
                        }
                        
                        BuzLog.info("BotInviteGroupPresenter queryBot")
                        
                        let future = BotNetwork.queryGroupBotMembers(groupId: groupId) { rcode, botInfos,
                            serverMemberList, queryParam, isLastPage in
                            self.groupRequestings[groupId] = nil
                            botInfos?.forEach({ member in
                                let userInfo = member.userInfo
                                if let userId = userInfo.userId {
                                    BuzBotCenter.info.cacheBotInfo(userId,
                                                                   info: BuzBotInfo.init(botUserId: userInfo.userId,
                                                                                         description: "",
                                                                                         userInfo: userInfo))
                                }
                            })
                                  
                            if let callbackArray = self.queryBotListCallbackHolderDict[groupId] {
                                for callback in callbackArray {
                                    callback(self.groupBotMembers[groupId])
                                }
                                
                                self.queryBotListCallbackHolderDict[groupId] = nil
                            }
                            
                            BuzLog.info("BotInviteGroupPresenter queryBot end :\(rcode)")
                        }
                        
                        self.groupRequestings[groupId] = future
                    }
                }
            }
            
            if self.isLoadingFinish {
                loadData()
            } else {
                self.cacheQueue.async {
                    loadData()
                }
            }
        }
    }
    
    func deletingSet(groupId : Int64) -> NSMutableSet? {
        if let botIds = self.deletingMap.first(where: { (key: Int64, value: NSMutableSet) in
            return groupId == key
        })?.value {
            return botIds
        }
        
        return nil
    }
}

extension BuzIMGroupBotManager {
    
    func updateBot(groupId : Int64, member : [GroupMember]) {
        var ids : [Int64] = []
        member.forEach { member in
            if let uid = member.userInfo.userId {
                ids.append(uid)
            }
        }
        
        self.updateConfigCache(config: BuzIMGroupBotConfig.init(groupId: groupId, botIds: ids))
    }
    
    func updateConfigCache(config : BuzIMGroupBotConfig) {
        self.groupBotMembers[config.groupId] = config
        BuzLog.info("BotsOfGroupManager updateConfigCache \(config) botCount = \(config.validBotInfos().count)")
        
        self.center?.notifyObservers(of: BuzBotCenterIMGroupObserver.self) { observer in
            observer.groupBotConfigDidChange?(config: config)
        }
        self.publisher.didChangedGroupBotConfig.send(config)
    }
    
    func dirtyConfig(groupId : Int64) {
        if let config = self.groupBotMembers[groupId], !config.dirty {
            config.dirty = true
            BuzLog.info("BotInviteGroupPresenter dirtyConfig :\(groupId)")
            
            self.center?.notifyObservers(of: BuzBotCenterIMGroupObserver.self) { observer in
                observer.groupBotConfigDidChange?(config: config)
            }
            self.publisher.didChangedGroupBotConfig.send(config)
        } else {
            BuzLog.info("BotInviteGroupPresenter can not find dirtyConfig :\(groupId)")
        }
    }
    
    private func isDeleting(groupId : Int64, botId : Int64) -> Bool {
        if let deleteSets = self.deletingSet(groupId: groupId) {
            if deleteSets.contains(botId) {
                return true
            }
        }
        
        return false
    }
}


extension BuzIMGroupBotManager : BuzSignalPushEventObserver {
    public func signalPushDidReceivedMessage(_ message: BuzSignalPush.Message) {
        guard let payload = message.payload else {
            return
        }
        switch payload.op {
        case .pushDataChange:
            let type = BuzSignalPush.PushDataChange.init(rawValue: payload.businessType ?? 0)
            let businessId = payload.data?["businessId"] as? String ?? ""
            let businessIdValue = Int64(businessId)
            switch type {
            case .GroupAIState: // 19 - 群组ai成员状态变更
                guard let businessIdValue = businessIdValue else { return }
                BuzSignalPushLog.info("群组机器人成员变更push: \(businessIdValue)")
                self.didReceiveGroupAIStateChangePush(groupId: businessIdValue)
            default:
                break
            }
        default:
            break
        }
    }
    
    func didReceiveGroupAIStateChangePush(groupId: Int64) {
        self.dirtyConfig(groupId: groupId)
    }
}
