//
//  BuzIMGroupBotManager+Combine.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/24.
//

import Combine
import BuzIDL
import ITNetLibrary
import BuzNetworker
import BuzFoundation

public extension BuzIMGroupBotManager {
    class Publisher {
        /// 机器人群配置发生变更
        public let didChangedGroupBotConfig = PassthroughSubject<BuzIMGroupBotConfig, Never>()
        
        /// 机器人群配置新增
        public let didAddGroupBotConfig = PassthroughSubject<BuzIMGroupBotConfig, Never>()
        
        /// 机器人群开始执行删除动作
        public let didStartDeleteAction = PassthroughSubject<(groupId: Int64, botId: Int64), Never>()
        
        /// 机器人群删除动作完成
        public let didEndDeleteAction = PassthroughSubject<(groupId: Int64, botId: Int64), Never>()
    }
}

public extension BuzCombineKit where Base: BuzIMGroupBotManager {
    
    /// 添加群机器人
    func addGroup(groupId: Int64, botUserIds: [Int64]) -> AnyPublisher<BuzNetworkResponse<ITResponse<ResponseBotGroupChange>>, Never> {
        Future { promise in
            self.object.addGroup(groupId: groupId, botUserIds: botUserIds) { response in
                promise(.success(response))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 移除群机器人
    func kickOutAi(groupId: Int64, botId: Int64) -> AnyPublisher<BuzNetworkResponse<ITResponse<ResponseBotGroupChange>>, Never> {
        Future { promise in
            self.object.kickOutAi(groupId: groupId, botId: botId) { response in
                promise(.success(response))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 查询群机器人配置（支持内存 + 网络回调）
    func queryBot(groupId: Int64, forceDirty: Bool = false) -> AnyPublisher<(memoryResult: BuzIMGroupBotConfig?, isDirty: Bool, networkResult: BuzIMGroupBotConfig?), Never> {
        Future { promise in
            self.object.queryBot(groupId: groupId, memoryResultHandler: { memoryConfig, isDirty in
                if isDirty {
                    self.object.queryBot(groupId: groupId, memoryResultHandler: nil, networkResultHandler: { networkConfig in
                        promise(.success((memoryConfig, isDirty, networkConfig)))
                    }, forceDirty: forceDirty)
                } else {
                    promise(.success((memoryConfig, isDirty, nil)))
                }
            }, networkResultHandler: nil, forceDirty: forceDirty)
        }
        .eraseToAnyPublisher()
    }
    
    /// 获取正在删除的机器人集合
    func deletingSet(groupId: Int64) -> AnyPublisher<NSMutableSet?, Never> {
        Just(self.object.deletingSet(groupId: groupId))
            .eraseToAnyPublisher()
    }
}
