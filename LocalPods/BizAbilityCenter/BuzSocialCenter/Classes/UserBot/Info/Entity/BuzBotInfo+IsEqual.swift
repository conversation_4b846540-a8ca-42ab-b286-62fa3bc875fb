//
//  BotInfo+IsEqual.swift
//  buz
//
//  Created by lizhifm on 2023/12/19.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import BuzIDL

public extension BuzBotInfo {
    func botEqual(otherBotInfo : BuzBotInfo?, checkOptions : Bool = false) -> Bool {
        guard let otherBotInfo = otherBotInfo else { return false }
        
        if self.botUserId != otherBotInfo.botUserId
            || self.description != otherBotInfo.description
            || self.shortDescription != otherBotInfo.shortDescription {
            return false
        }
        
        if (self.userInfo != nil && otherBotInfo.userInfo == nil) || (self.userInfo == nil && otherBotInfo.userInfo != nil) {
            return false
        }
        
        if let mUserInfo = self.userInfo, let selfUserInfo = otherBotInfo.userInfo {
            if mUserInfo.userId != selfUserInfo.userId
                || mUserInfo.userName != selfUserInfo.userName
                || mUserInfo.firstName != selfUserInfo.firstName
                || mUserInfo.lastName != selfUserInfo.lastName
                || mUserInfo.portrait != selfUserInfo.portrait
                || mUserInfo.phone != selfUserInfo.phone
                || mUserInfo.registerTime != selfUserInfo.registerTime
                || mUserInfo.walkieTalkieOnlineTime != selfUserInfo.walkieTalkieOnlineTime
                || mUserInfo.quietMode != selfUserInfo.quietMode
                || mUserInfo.userType != selfUserInfo.userType
                || mUserInfo.language != selfUserInfo.language
                || mUserInfo.buzId != selfUserInfo.buzId
                || mUserInfo.email != selfUserInfo.email {
                return false
            }
        }
        
        if self.topics?.count != otherBotInfo.topics?.count {
            return false
        }
        
        if let selfTopics = self.topics, let otherTopics = otherBotInfo.topics {
            for i in 0..<selfTopics.count {
                if selfTopics[i].id != otherTopics[i].id
                    || selfTopics[i].title != otherTopics[i].title
                    || selfTopics[i].prompt != otherTopics[i].prompt
                    || selfTopics[i].description != otherTopics[i].description
                    || selfTopics[i].emoji != otherTopics[i].emoji {
                    return false
                }
            }
        }
        
        if (self.botUIConfig != nil && otherBotInfo.botUIConfig == nil) || (self.botUIConfig == nil && otherBotInfo.botUIConfig != nil) {
            return false
        }
        
        if let selfBotUIConfig = self.botUIConfig, let otherBotUIConfig = otherBotInfo.botUIConfig {
            if otherBotUIConfig.showTopic != selfBotUIConfig.showTopic
                || otherBotUIConfig.showLanguage != selfBotUIConfig.showLanguage
                || otherBotUIConfig.showVoiceStyle != selfBotUIConfig.showVoiceStyle
                || otherBotUIConfig.useRemotePortrait != selfBotUIConfig.useRemotePortrait
                || otherBotUIConfig.showImageButton != selfBotUIConfig.showImageButton
                || otherBotUIConfig.showTranslation != selfBotUIConfig.showTranslation
                || otherBotUIConfig.showJoinGroup != selfBotUIConfig.showJoinGroup {
                return false
            }
        }
        
        if checkOptions {
            if (self.options != nil && otherBotInfo.options == nil) || (self.options == nil && otherBotInfo.options != nil) {
                return false
            }
            
            if let selfOptions = self.options, let otherOptions = otherBotInfo.options {
                if (selfOptions.languageOptions != nil && otherOptions.languageOptions == nil)
                    || (selfOptions.languageOptions == nil && otherOptions.languageOptions != nil) {
                    return false
                }
                if (selfOptions.voiceStyleOptions != nil && otherOptions.voiceStyleOptions == nil)
                    || (selfOptions.voiceStyleOptions == nil && otherOptions.voiceStyleOptions != nil) {
                    return false
                }
                if (selfOptions.translateSourceLanguage != nil && otherOptions.translateSourceLanguage == nil)
                    || (selfOptions.translateSourceLanguage == nil && otherOptions.translateSourceLanguage != nil) {
                    return false
                }
                if (selfOptions.translateTargetLanguage != nil && otherOptions.translateTargetLanguage == nil)
                    || (selfOptions.translateTargetLanguage == nil && otherOptions.translateTargetLanguage != nil) {
                    return false
                }
                
                if let selfLanguages = selfOptions.languageOptions, let otherLanguages = otherOptions.languageOptions {
                    if selfLanguages.count != otherLanguages.count {
                        return false
                    }
                    
                    for i in 0..<selfLanguages.count {
                        if selfLanguages[i] != otherLanguages[i] {
                            return false
                        }
                    }
                }
                
                if let selfVoiceLanguages = selfOptions.voiceStyleOptions, let otherVoiceLanguages = otherOptions.voiceStyleOptions {
                    if selfVoiceLanguages.count != otherVoiceLanguages.count {
                        return false
                    }
                    
                    for i in 0..<selfVoiceLanguages.count {
                        if selfVoiceLanguages[i].languageCode != otherVoiceLanguages[i].languageCode
                            || selfVoiceLanguages[i].voiceStyleId != otherVoiceLanguages[i].voiceStyleId
                            || selfVoiceLanguages[i].displayName != otherVoiceLanguages[i].displayName
                            || selfVoiceLanguages[i].sampleAudioUrl != otherVoiceLanguages[i].sampleAudioUrl {
                            return false
                        }
                    }
                }
                
                if let selfTranslates = selfOptions.translateSourceLanguage, let otherTranslates = otherOptions.translateSourceLanguage {
                    if selfTranslates.count != otherTranslates.count {
                        return false
                    }
                    
                    for i in 0..<selfTranslates.count {
                        if selfTranslates[i].displayName != otherTranslates[i].displayName
                            || selfTranslates[i].languageCode != otherTranslates[i].languageCode {
                            return false
                        }
                    }
                }
                
                if let selfTargetTranslates = selfOptions.translateTargetLanguage, let otherTargetTranslates = otherOptions.translateTargetLanguage {
                    if selfTargetTranslates.count != otherTargetTranslates.count {
                        return false
                    }
                    
                    for i in 0..<selfTargetTranslates.count {
                        if selfTargetTranslates[i].displayName != otherTargetTranslates[i].displayName
                            || selfTargetTranslates[i].languageCode != otherTargetTranslates[i].languageCode {
                            return false
                        }
                    }
                }
            }
        }
        
        return true
    }
}
