//
//  BotInfo+Translator.swift
//  buz
//
//  Created by lizhifm on 2024/2/23.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation
import BuzIDL
import BuzDataStore
import Localizable
import BuzLocalizable
import BuzLog

fileprivate class TranslatorConfig : NSObject {
    enum TranslatorStoreKey: String {
        case translateBotSourceLanguateDataPrefix = "translateBotSourceLanguateData_"
        case translateBotTargetLanguateDataPrefix = "translateBotTargetLanguateData_"
        case recentUsedLanguages = "BuzBotStoreKey_recentUsedLanguages"
    }
    
    fileprivate static var shared : [Int64 : TranslatorConfig] = [:]
    fileprivate let jsonEncoder = JSONEncoder()
    fileprivate let jsonDecoder = JSONDecoder()
    fileprivate let userId : Int64
    fileprivate var botInfo : BuzBotInfo?
    fileprivate static var autoDetectLan : BuzBotTranslateLanguage = BuzBotTranslateLanguage.init(displayName: Localizable.auto_detect, languageCode: "auto")
    fileprivate static var defaultTargetLan : BuzBotTranslateLanguage = BuzBotTranslateLanguage.init(displayName: Localizable.english , languageCode: "en")
    
    init(userId: Int64) {
        self.userId = userId
    }
    
    static func shared(botId : Int64) -> TranslatorConfig {
        if let config = self.shared[botId] {
            return config
        }
        
        let config = TranslatorConfig.init(userId: botId)
        self.shared[botId] = config
        return config
    }
    
    //current source language, target language
    // if nil -> auto detect
    fileprivate lazy var sourceLanguage : BuzBotTranslateLanguage = {
        let key = "\(TranslatorStoreKey.translateBotSourceLanguateDataPrefix.rawValue)\(self.userId)"
        
        if let data = MMKV.asyncBuz().data(forKey: key) {
            do {
                let response = try self.jsonDecoder.decode(BuzBotTranslateLanguage.self, from: data)
                return response
            }catch {
            }
        }
        
        return Self.autoDetectLan
    }() {
        didSet {
            let key = "\(TranslatorStoreKey.translateBotSourceLanguateDataPrefix.rawValue)\(self.userId)"
            
            let json = try! self.jsonEncoder.encode(self.sourceLanguage)
            MMKV.asyncBuz().set(json, forKey: key)
            
            NotificationCenter.default.post(name: NSNotification.Name.TranslateBotDidUpdateSourceLanguateSetting ,
                                            object: self , userInfo: [
                BuzBotNotificationUserInfoKey.language.rawValue : TranslateBotLanguageModel.init(model: self.sourceLanguage),
                BuzBotNotificationUserInfoKey.botUserId.rawValue : self.userId
            ])
            
            self.appendLastedUseLanguage(langugage: self.sourceLanguage)
        }
    }
    
    func appendLastedUseLanguage(langugage : BuzBotTranslateLanguage) {
        if langugage.isAutoDetect() {
           return
        }
        
        if !self.saveHistoryList {
            return
        }
        
        self.lastUsedLanguages.removeAll(where: { tlanguage in
            return tlanguage.languageCode == langugage.languageCode
        })
        
        if self.lastUsedLanguages.count + 1 > Self.maxRecentUseLanguage() {
            self.lastUsedLanguages.removeLast()
        }
        
        self.lastUsedLanguages.insert(langugage, at: 0)
        do {
            let lastJson = try self.jsonEncoder.encode(self.lastUsedLanguages)
            MMKV.asyncBuz().set(lastJson, forKey: TranslatorStoreKey.recentUsedLanguages.rawValue)
        }catch {
            BuzLog.info("appendLastedUseLanguage \(error)")
        }
    }
    
    class func setupLanguageFrom(botInfo : BuzBotInfo?) -> BuzBotTranslateLanguage? {
        if let targetLanguateArray = botInfo?.options?.translateTargetLanguage {
            //2. app language
            if let mgr = LocalizableStringManager.sharedInstance() {
                for language in targetLanguateArray {
                    if let code = language.languageCode , mgr.currentLanguage == code {
                        BuzLog.info("return -> app language")
                        return language
                    }
                }
            }
            
            //3. target languate first item
            if let firstLanguageItem = targetLanguateArray.first {
                BuzLog.info("return -> target languate first item")
                return firstLanguageItem
            }
        }
        
        return nil
    }
    
    fileprivate func saveTargetLanguage(targetLanguage : BuzBotTranslateLanguage) {
        let key = "\(TranslatorStoreKey.translateBotTargetLanguateDataPrefix.rawValue)\(self.userId)"
        let json = try! self.jsonEncoder.encode(targetLanguage)
        MMKV.asyncBuz().set(json, forKey: key)
        
        self.appendLastedUseLanguage(langugage: targetLanguage)
    }
    
    fileprivate lazy var targetLanguage : BuzBotTranslateLanguage = {
        let key = "\(TranslatorStoreKey.translateBotTargetLanguateDataPrefix.rawValue)\(self.userId)"
        
        if let data = MMKV.asyncBuz().data(forKey: key) {
            do {
                let response = try self.jsonDecoder.decode(BuzBotTranslateLanguage.self, from: data)
                return response
            }catch {
            }
        }
        
        if let language = Self.setupLanguageFrom(botInfo: self.botInfo) {
            self.saveTargetLanguage(targetLanguage: language)
            return language
        }
        
        return Self.defaultTargetLan
    }() {
        didSet {
            self.saveTargetLanguage(targetLanguage: self.targetLanguage)
            NotificationCenter.default.post(name: NSNotification.Name.TranslateBotDidUpdateTargetLanguateSetting ,
                                            object: self , userInfo: [
                                                BuzBotNotificationUserInfoKey.language.rawValue : TranslateBotLanguageModel.init(model: self.targetLanguage),
                BuzBotNotificationUserInfoKey.botUserId.rawValue : self.userId
            ])
        }
    }
    
    fileprivate lazy var lastUsedLanguages : [BuzBotTranslateLanguage] = {
        if let data = MMKV.asyncBuz().data(forKey: TranslatorStoreKey.recentUsedLanguages.rawValue) {
            return try! self.jsonDecoder.decode([BuzBotTranslateLanguage].self, from: data)
        }
        
        return []
    }()
    
    fileprivate var saveHistoryList : Bool = true
    
    static func maxRecentUseLanguage() -> Int {
        return 4
    }
}

public extension BuzBotTranslateLanguage {
    func isAutoDetect() -> Bool {
        return self.languageCode == "auto"
    }
}

/// AI机器人扩展
public extension BuzBotInfo {
    func maxRecentUseLanguage() -> Int {
        return TranslatorConfig.maxRecentUseLanguage()
    }
    
    fileprivate func instanceConfig() -> TranslatorConfig {
        let config = TranslatorConfig.shared(botId: self.botUserId ?? 0)
        config.botInfo = self
        return config
    }
    
    func setupLanguageFromServerLanguage() {
        //doesn't need to filter sourceLanguage, targetLanguage, sourceLanguage, targetLanguage is centainly in lastUsed
        if let lastSourceLanguages = self.lastUsedSourceLanguages(), let botId = self.botUserId,
           let translateSourceLanguage = self.options?.translateSourceLanguage{
            if lastSourceLanguages.first(where: { lan in
                return translateSourceLanguage.first(where: { language in
                    return language.languageCode == lan.languageCode
                }) == nil
            }) != nil {
                let key = "\(TranslatorConfig.TranslatorStoreKey.translateBotSourceLanguateDataPrefix.rawValue)\(botId)"
                let listKey = TranslatorConfig.TranslatorStoreKey.recentUsedLanguages.rawValue
                MMKV.asyncBuz().removeValues(forKeys: [key, listKey])
                
                self.selectSourceLanguage(language: TranslatorConfig.autoDetectLan)
            }
        }
        
        if let lastTargetLanguage = self.lastUsedTargetLanguages(), let botId = self.botUserId,
                let translateTargetLanguage = self.options?.translateTargetLanguage {
            if lastTargetLanguage.first(where: { lan in
                return translateTargetLanguage.first(where: { language in
                    return language.languageCode == lan.languageCode
                }) == nil
            }) != nil {
                let key = "\(TranslatorConfig.TranslatorStoreKey.translateBotTargetLanguateDataPrefix.rawValue)\(botId)"
                let listKey = TranslatorConfig.TranslatorStoreKey.recentUsedLanguages.rawValue
                MMKV.asyncBuz().removeValues(forKeys: [key, listKey])
                
                if let language = TranslatorConfig.setupLanguageFrom(botInfo: self) {
                    self.selectTargetLanguage(language: language)
                } else {
                    self.selectTargetLanguage(language: TranslatorConfig.defaultTargetLan)
                }
            }
        }
    }
    
    //保证客户端相关内聚逻辑不外放
    func switchLanguage(sourceLanguage : BuzBotTranslateLanguage, targetLanguage : BuzBotTranslateLanguage) {
        instanceConfig().saveHistoryList = false
        self.selectSourceLanguage(language: sourceLanguage)
        self.selectTargetLanguage(language: targetLanguage)
        instanceConfig().saveHistoryList = true
    }
    
    //保证客户端相关内聚逻辑不外放
    func selectSourceLanguage(language : BuzBotTranslateLanguage) {
        if instanceConfig().sourceLanguage.languageCode == language.languageCode {
            return
        }
        
        instanceConfig().sourceLanguage = language
    }
    
    func selectTargetLanguage(language : BuzBotTranslateLanguage) {
        if instanceConfig().targetLanguage.languageCode == language.languageCode {
            return
        }
        
        instanceConfig().targetLanguage = language
    }
    
    //current source language, target language
    // if nil -> auto detect
    func sourceLanguage() -> BuzBotTranslateLanguage {
        let language = instanceConfig().sourceLanguage
        
        if let inListLanguage = self.options?.translateSourceLanguage?.first(where: { lan in
            return lan.languageCode == language.languageCode
        }) {
            return inListLanguage
        }
        
        return language
    }
    
    func targetLanguage() -> BuzBotTranslateLanguage {
        let language = instanceConfig().targetLanguage
        
        if let inListLanguage = self.options?.translateTargetLanguage?.first(where: { lan in
            return lan.languageCode == language.languageCode
        }) {
            return inListLanguage
        }
        
        return language
    }
    
    //return all languages, doesn't use translateSourceLanguage, translateTargetLanguage
    func sourceLanguages() -> [BuzBotTranslateLanguage]? {
        return self.options?.translateSourceLanguage
    }
    
    func targetLanguages() -> [BuzBotTranslateLanguage]? {
        return self.options?.translateTargetLanguage
    }
    
    func lastUsedSourceLanguages() -> [BuzBotTranslateLanguage]? {
        return instanceConfig().lastUsedLanguages
    }
    
    func lastUsedTargetLanguages() -> [BuzBotTranslateLanguage]? {
        return instanceConfig().lastUsedLanguages
    }
    
    func translateBotSettingLanguateData(complete : @escaping ((TranslateBotLanguageModel, TranslateBotLanguageModel) -> Void)) {
        complete(TranslateBotLanguageModel.init(model: self.sourceLanguage()), TranslateBotLanguageModel.init(model: self.targetLanguage()))
    }
}
//translateBotSourceLanguateData_123451
