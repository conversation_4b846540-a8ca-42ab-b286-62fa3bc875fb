//
//  BuzBotInfo.swift
//  buz
//
//  Created by lizhifm on 2/18/25.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import BuzCenterKit
import ITNetLibrary
import BuzIDL

/// 机器人话题
public struct BuzBotTopic: ITCodable {
    /// id 标记 *
    public var id: Int64?

    /// topic 标题 *
    public var title: String?

    /// 点击start后，发出的内容 *
    public var prompt: String?

    /// topic 的描述（已经支持多语言） *
    public var description: String?

    /// 表情图 *
    public var emoji: String?


    public init(id: Int64? = nil, title: String? = nil, prompt: String? = nil, description: String? = nil, emoji: String? = nil) {
        self.id = id
        self.title = title
        self.prompt = prompt
        self.description = description
        self.emoji = emoji
    }
}

/// 机器人声音风格
public struct BuzBotVoiceStyle: ITCodable {

    /// 声音风格所属语言编码
    public var languageCode: String?

    /// 声音风格对应的唯一ID
    public var voiceStyleId: Int64?

    /// 展示名字
    public var displayName: String?

    /// 声音试听地址
    public var sampleAudioUrl: String?


    public init(languageCode: String? = nil, voiceStyleId: Int64? = nil, displayName: String? = nil, sampleAudioUrl: String? = nil) {
        self.languageCode = languageCode
        self.voiceStyleId = voiceStyleId
        self.displayName = displayName
        self.sampleAudioUrl = sampleAudioUrl
    }

}

/// 机器人翻译功能语言结构体
public struct BuzBotTranslateLanguage: ITCodable {

    /// 展示的名字
    public var displayName: String?

    /// 对应的languageCode
    public var languageCode: String?


    public init(displayName: String? = nil, languageCode: String? = nil) {
        self.displayName = displayName
        self.languageCode = languageCode
    }

}

/// 用户的机器人设置选项
public struct BuzBotSettingOption: ITCodable {

    /// 语言选项
    public var languageOptions: [String]?

    /// 声音风格选项
    public var voiceStyleOptions: [BuzBotVoiceStyle]?

    /// 翻译功能支持语言列表，仅翻译机器人会返回
    public var translateSourceLanguage: [BuzBotTranslateLanguage]?

    public var translateTargetLanguage: [BuzBotTranslateLanguage]?


    public init(languageOptions: [String]? = nil, voiceStyleOptions: [BuzBotVoiceStyle]? = nil,
                translateSourceLanguage: [BuzBotTranslateLanguage]? = nil, translateTargetLanguage: [BuzBotTranslateLanguage]? = nil) {
        self.languageOptions = languageOptions
        self.voiceStyleOptions = voiceStyleOptions
        self.translateSourceLanguage = translateSourceLanguage
        self.translateTargetLanguage = translateTargetLanguage
    }
}

/// AI机器人UI配置
public struct BuzBotUIConfig: ITCodable {

    /// 是否显示Topic
    public var showTopic: Bool?

    /// 是否显示AI语言设置
    public var showLanguage: Bool?

    /// 是否显示AI声音风格设置
    public var showVoiceStyle: Bool?

    /// 是否使用远程服务端的头像，false表示使用客户端本地
    public var useRemotePortrait: Bool?

    /// 是否展示图片上传功能
    public var showImageButton: Bool?

    /// 是否展示翻译功能
    public var showTranslation: Bool?

    /// 是否展示入群入口.
    public var showJoinGroup: Bool?

    /// 与AI私聊是否展示评价反馈入口
    public var showPrivateChatMsgFeedbackEntrance: Bool?


    public init(showTopic: Bool? = nil, showLanguage: Bool? = nil, showVoiceStyle: Bool? = nil, useRemotePortrait: Bool? = nil, showImageButton: Bool? = nil, showTranslation: Bool? = nil, showJoinGroup: Bool? = nil, showPrivateChatMsgFeedbackEntrance: Bool? = nil) {
        self.showTopic = showTopic
        self.showLanguage = showLanguage
        self.showVoiceStyle = showVoiceStyle
        self.useRemotePortrait = useRemotePortrait
        self.showImageButton = showImageButton
        self.showTranslation = showTranslation
        self.showJoinGroup = showJoinGroup
        self.showPrivateChatMsgFeedbackEntrance = showPrivateChatMsgFeedbackEntrance
    }

}

public struct BuzRouterInfo: ITCodable {

    /// 路由标志
    public var scheme: String

    /// 扩展参数
    public var extraData: String


    public init(scheme: String, extraData: String) {
        self.scheme = scheme
        self.extraData = extraData
    }

}

public struct  BuzActionInfo: ITCodable {

    /// 类型
    public var type: Int32

    /// 跳转路由
    public var router: BuzRouterInfo


    public init(type: Int32, router: BuzRouterInfo) {
        self.type = type
        self.router = router
    }

}

/// 机器人描述跳转链接
public struct BuzBotDescriptionLink: ITCodable {

    /// 展示的名字
    public var displayName: String?

    /// 行为路由
    public var actionInfo: BuzActionInfo?


    public init(displayName: String? = nil, actionInfo: BuzActionInfo? = nil) {
        self.displayName = displayName
        self.actionInfo = actionInfo
    }

}

public struct BuzBotInfo {
    /// 机器人id
    public var botUserId: Int64?

    /// 机器人描述
    public var description: String?

    /// 机器人的用户信息
    public var userInfo: BuzUserData.BaseInfo?

    /// topic 列表
    public var topics: [BuzBotTopic]?

    /// 机器人选项
    public var options: BuzBotSettingOption?

    /// 机器人UI控制
    public var botUIConfig: BuzBotUIConfig?

    /// 机器人短描述
    public var shortDescription: String?

    /// 机器人描述链接（支持跳转路由）
    public var descriptionLink: BuzBotDescriptionLink?
    
    public init(botUserId: Int64? = nil,
                description: String? = nil,
                userInfo: UserInfo? = nil,
                topics: [BotTopic]? = nil,
                options: BotSettingOption? = nil,
                botUIConfig: BotUIConfig? = nil,
                shortDescription: String? = nil,
                descriptionLink: BotDescriptionLink? = nil) {
        self.botUserId = botUserId
        self.description = description
        
        if let userInfo = userInfo {
            self.userInfo = BuzUserData.BaseInfo.init(idl: userInfo)
        }
        
        if let topics = topics {
            var buzTopics : [BuzBotTopic] = []
            
            topics.forEach { topic in
                buzTopics.append(BuzBotTopic.init(id: topic.id, title: topic.title, prompt: topic.prompt,
                                                  description: topic.description, emoji: topic.emoji))
            }
            
            self.topics = buzTopics
        }
        
        if let options = options {
            var srcLanguages : [BuzBotTranslateLanguage] = []
            var targetLanguages : [BuzBotTranslateLanguage] = []
            var voiceStyleOptions : [BuzBotVoiceStyle] = []
            
            options.translateSourceLanguage?.forEach({ language in
                srcLanguages.append(BuzBotTranslateLanguage.init(displayName: language.displayName,
                                                                 languageCode: language.languageCode))
            })
            
            options.translateTargetLanguage?.forEach({ language in
                targetLanguages.append(BuzBotTranslateLanguage.init(displayName: language.displayName,
                                                                    languageCode: language.languageCode))
            })
            
            options.voiceStyleOptions?.forEach({ style in
                voiceStyleOptions.append(BuzBotVoiceStyle.init(languageCode: style.languageCode, voiceStyleId: style.voiceStyleId,
                                                               displayName:style.displayName, sampleAudioUrl: style.sampleAudioUrl))
            })
            
            self.options = BuzBotSettingOption.init(languageOptions: options.languageOptions, voiceStyleOptions: voiceStyleOptions,
                                                    translateSourceLanguage: srcLanguages, translateTargetLanguage: targetLanguages)
        }
        
        if let config = botUIConfig {
            self.botUIConfig = BuzBotUIConfig.init(showTopic: config.showTopic, showLanguage:config.showLanguage, showVoiceStyle: config.showVoiceStyle,
                                                   useRemotePortrait: config.useRemotePortrait, showImageButton: config.showImageButton,
                                                   showTranslation: config.showTranslation, showJoinGroup: config.showJoinGroup, showPrivateChatMsgFeedbackEntrance: config.showPrivateChatMsgFeedbackEntrance)
        }
        
        self.shortDescription = shortDescription
        
        if let descriptionLink = descriptionLink {
            var buzActionInfo : BuzActionInfo?
            
            if let actionInfo = descriptionLink.actionInfo {
                buzActionInfo = BuzActionInfo.init(type: actionInfo.type,
                                                   router: BuzRouterInfo.init(scheme: actionInfo.router.scheme,
                                                                              extraData: actionInfo.router.extraData))
            }
            
            self.descriptionLink = BuzBotDescriptionLink.init(displayName: descriptionLink.displayName,
                                                              actionInfo: buzActionInfo)
        }
    }
}
