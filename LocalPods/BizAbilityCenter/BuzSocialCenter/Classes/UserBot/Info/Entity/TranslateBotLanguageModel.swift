//
//  TranslateLanguageSettingModel.swift
//  buz
//
//  Created by liuyufeng on 2023/10/30.
//  Copyright © 2023 lizhi. All rights reserved.
//

import UIKit
import BuzIDL

public struct TranslateBotLanguageModel : Codable {
    
    /// 展示的名字
    public var displayName: String?

    /// 对应的languageCode
    public var languageCode: String?


    public init(displayName: String? = nil, languageCode: String? = nil) {
        self.displayName = displayName
        self.languageCode = languageCode
    }
    
    public init(model : BuzBotTranslateLanguage) {
        self.displayName = model.displayName
        self.languageCode = model.languageCode
    }
    
    public func isSupporSwitch() -> Bool {
        return self.languageCode != "auto"
    }
}
