//
//  BotNetwork.swift
//  buz
//
//  Created by 方煜逵 on 2023/7/11.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import BuzIDL
import ITNetLibrary
import BuzLog
import BuzNetworker

enum BotGroupOperation : Int32 {
    case add = 1
    case quit = 2
}

struct BotNetwork {
    // 获取机器人话题列表
    static func getBotTopics(completion: @escaping (Bool, AITopicContent?) -> ()) {
        let client = BuzNetCommonServiceClient()
        let req = RequestGetAITopicList()
        _ = client.getAITopicList(request: req, completion: {
            result in
            
            switch result {
            case .success(let response):
                completion(response.code == 0, response.data?.topicContent ?? nil )
                break
            case .failure:
                completion(false, nil)
                break
            }
        })
    }
    
    
//    ///请求获取
//    @discardableResult
//    static func requestReportUserInfo(language : String? = nil , isBotPop : Bool , complete : ((BuzNetworkResponse<ITResponse<ResponseReportUserInfo>>) -> Void)?) -> ITFuture
//    {
//        let ts : Int64 = Int64(Date().timeIntervalSince1970 * 1000)
//        let mo: SwitchStatus = SwitchStatus.init(type: 1, status: isBotPop)
//        let requestServiceClient : BuzNetCommonServiceClient = BuzNetCommonServiceClient.init()
//        let userInfo = ReportUserInfo.init(language: language ,  sendTimestamp: ts , switchStatusList:[mo])
//        let request = RequestReportUserInfo.init(reportUserInfo: userInfo)
//        
//        return requestServiceClient.reportUserInfo(request: request) { result in
//           
//            switch result {
//            case .success(let responseInfo):
//    
//                guard responseInfo.code == 0 else{
//                    BuzLog.error("Request reportUserInfo request failure ！！code = \(responseInfo.code)")
//                    complete?(BuzNetworkResponse.init(rcode: responseInfo.code, isSuccess: false , rawResponseObj: responseInfo))
//                    return
//                }
//                complete?(BuzNetworkResponse.init(rcode: 0, isSuccess: true, rawResponseObj: responseInfo))
//                BuzLog.info("Request reportUserInfo success)")
//                
//            case .failure(let error):
//                
//                BuzLog.error("Request reportUserInfo ！！error = \(error)")
//                complete?(BuzNetworkResponse.init(rcode: NetworkRcode.requestFailure.rawValue, isSuccess: false, rawResponseObj: nil , error: error))
//            }
//        }
//        
//    }
    
    /// 批量查询机器人信息
    /// - Parameters:
    ///  - botUserIdList: 机器人ID列表，为空时查询全部
    ///  - completion: 回调
    @discardableResult
    static func getBotInfoList(_ botUserIdList: Set<Int64>, completion: ((BuzNetworkResponse<ITResponse<ResponseGetBotInfoList>>) -> Void)?) -> ITFuture {
        return BuzNetBotServiceClient().getBotInfoList(request: RequestGetBotInfoList.init(botUserIdList: botUserIdList)) { result in
            switch result {
            case .success(let responseInfo):
                completion?(BuzNetworkResponse.init(rcode: responseInfo.code, isSuccess: responseInfo.code == 0, rawResponseObj: responseInfo))
                BuzLog.info("RequestGetBotInfoList request success")
            case .failure(let error):
                completion?(BuzNetworkResponse.init(rcode: NetworkRcode.requestFailure.rawValue, isSuccess: false, rawResponseObj: nil, error: error))
                BuzLog.error("RequestGetBotInfoList request error. error = \(error)")
            }
        }
    }
    
    /// 获取当前用户的机器人设置选项
    /// - Parameters:
    ///  - botUserId: 机器人ID
    ///  - completion: 回调
    @discardableResult
    static func getBotSetting(_ botUserId: Int64, completion: ((BuzNetworkResponse<ITResponse<ResponseGetBotSetting>>) -> Void)?) -> ITFuture {
        return BuzNetBotServiceClient().getBotSetting(request: RequestGetBotSetting.init(botUserId: botUserId)) { result in
            switch result {
            case .success(let responseInfo):
                completion?(BuzNetworkResponse.init(rcode: responseInfo.code, isSuccess: responseInfo.code == 0, rawResponseObj: responseInfo))
                BuzLog.info("RequestGetBotSetting request success")
            case .failure(let error):
                completion?(BuzNetworkResponse.init(rcode: NetworkRcode.requestFailure.rawValue, isSuccess: false, rawResponseObj: nil, error: error))
                BuzLog.error("RequestGetBotSetting request error. error = \(error)")
            }
        }
    }
    
    /// 获取当前用户的机器人设置选项
    /// - Parameters:
    ///  - botUserId: 机器人ID
    ///  - completion: 回调
    @discardableResult
    static func getBotInfo(_ botUserId: Int64, completion: ((BuzNetworkResponse<ITResponse<ResponseGetBotInfo>>) -> Void)?) -> ITFuture {
        return BuzNetBotServiceClient().getBotInfo(request: RequestGetBotInfo.init(botUserId: botUserId)) { result in
            switch result {
            case .success(let responseInfo):
                completion?(BuzNetworkResponse.init(rcode: responseInfo.code, isSuccess: responseInfo.code == 0, rawResponseObj: responseInfo))
                BuzLog.info("getBotInfoFromServer request success")
            case .failure(let error):
                completion?(BuzNetworkResponse.init(rcode: NetworkRcode.requestFailure.rawValue, isSuccess: false, rawResponseObj: nil, error: error))
                BuzLog.error("getBotInfoFromServer request error. error = \(error)")
            }
        }
    }
    
    /// get bot list of every group
    /// - Parameters:
    ///  - botUserId: 机器人ID
    static func queryGroupBotMember(_ groupIds : [Int64], completion: (((BuzNetworkResponse<ITResponse<ResponseAllGroupBotMember>>) -> Void)?)) {
        let _ = BuzNetBotServiceClient().queryAllGroupBotMember(request: RequestQueryGroupBotMember.init(groupIds: groupIds),
                                                                completion: { result in
            switch result {
            case .success(let responseInfo):
                completion?(BuzNetworkResponse.init(rcode: responseInfo.code, isSuccess: responseInfo.code == 0 , rawResponseObj: responseInfo))
                BuzLog.info("queryGroupBotMember request success")
            case .failure(let error):
                completion?(BuzNetworkResponse.init(rcode: NetworkRcode.requestFailure.rawValue, isSuccess: false, rawResponseObj: nil , error: error))
                BuzLog.error("queryGroupBotMember request error. error = \(error)")
            }
        })
    }
    
    /// 更新群组AI数据（进群或退群）
    /// - Parameters:
    ///  - type : operation, like add or exit
    ///  - groupId : group id
    ///  - botUserId: 机器人ID
    static func joinGroupBotMember(_ groupId : Int64, _ botUserIds: [Int64], completion: (((BuzNetworkResponse<ITResponse<ResponseBotGroupChange>>) -> Void)?)) {
        let _ = BuzNetBotServiceClient().botJoinGroup(request: RequestBotJoinGroup.init(botUserIds: botUserIds, groupIds: [groupId])) { result in
            switch result {
            case .success(let responseInfo):
                completion?(BuzNetworkResponse.init(rcode: responseInfo.code, isSuccess: responseInfo.code == 0 , rawResponseObj: responseInfo))
                BuzLog.info("operateGroupBotMember request success")
            case .failure(let error):
                completion?(BuzNetworkResponse.init(rcode: NetworkRcode.requestFailure.rawValue, isSuccess: false, rawResponseObj: nil , error: error))
                BuzLog.error("operateGroupBotMember request error. error = \(error)")
            }
        }
    }
    
    static func exitGroupBotMember(_ groupId : Int64, _ botUserId: Int64, completion: (((BuzNetworkResponse<ITResponse<ResponseBotGroupChange>>) -> Void)?)) {
        let _ = BuzNetBotServiceClient().botKickOutGroup(request: RequestBotExitGroup.init(botUserIds: [botUserId], groupId: groupId)) { result in
            switch result {
            case .success(let responseInfo):
                completion?(BuzNetworkResponse.init(rcode: responseInfo.code, isSuccess: responseInfo.code == 0 , rawResponseObj: responseInfo))
                BuzLog.info("operateGroupBotMember request success")
            case .failure(let error):
                completion?(BuzNetworkResponse.init(rcode: NetworkRcode.requestFailure.rawValue, isSuccess: false, rawResponseObj: nil , error: error))
                BuzLog.error("operateGroupBotMember request error. error = \(error)")
            }
        }
    }
}


extension BotNetwork {
    // 查询群成员AI信息
    static func queryGroupBotMembers(groupId: Int64,
                                     handler: @escaping (_ rcode: Int,
                                                         _ botInfo: [GroupMember]?,
                                                         _ serverMemberList : [GroupMember]?,
                                                         _ queryParams : String?,
                                                         _ isLastPage : Bool) -> Void) -> ITFuture {
        let client = BuzNetGroupServiceClient()
        let req = RequestGetBotMembers(groupId: groupId)
        
        return client.getBotMembers(request: req) { result in
            switch result {
            case let .success(responseInfo):
                if let data = responseInfo.data {
                    if let botInfos = data.botList {
                        var ids : [Int64] = []
                        
                        botInfos.forEach { member in
                            if let userId = member.userInfo.userId {
                                ids.append(userId)
                            }
                        }
                        
                        let botMember = BuzIMGroupBotConfig.init(groupId: groupId, botIds: ids)
                        BuzBotCenter.imGroup.updateBotsOfGroup(configs: [botMember])
                    }
                    
                    
                    handler(responseInfo.code , data.botList, nil, nil, true)
                    
                    
                } else {
                    BuzLog.error("getBotMembers data missed")
                    handler(responseInfo.code, nil, nil, nil, true)
                }
                break
            case let .failure(error):
                BuzLog.error("getBotMembers failed:\(error)")
                handler(NetworkErrorCode, nil, nil, nil, true)
            }
        }
    }
}
