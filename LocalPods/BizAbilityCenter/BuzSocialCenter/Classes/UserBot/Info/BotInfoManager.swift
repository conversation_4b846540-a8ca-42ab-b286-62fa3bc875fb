//
//  BotInfoManager.swift
//  buz
//
//  Created by Angus on 2023/9/13.
//  Copyright © 2023 lizhi. All rights reserved.
//

import UIKit
import BuzIDL
import BuzLocalizable
import Localizable
import BuzLog
import BuzDataStore
import BuzNetworker
import BuzDataShareKit
import BuzUserSession

enum BuzBotStoreKey: String {
    case botInfoList = "BuzBotStoreKey_BotInfoList"
    case botUserSetting = "BuzBotStoreKey_BotUserSetting_"
    case botNewCount = "BuzBotStoreKey_BotNewCount"
    case botRead = "BuzBotStoreKey_botRead"
    case botTipKey = "BotList_hasClose"
    case botTipRedPointKey = "BotList_redPointKeyHadReaded"
    case addToGroupChatRedDotIsShowedKey = "BuzBotStoreKey_addToGroupChatRedDotIsShowedKey"
}

public enum BuzBotNotificationUserInfoKey : String {
    case language = "language"
    case botUserId = "botUserId"
}

@objc
public protocol BuzBotCenterInfoObserver: BuzBotCenterObserver {
    @objc optional func botInfoManagerDidUpdateIsHasNewBot(_ mgr: BotInfoManager)
    @objc optional func botInfoManagerDidUpdateBotTip(_ mgr: BotInfoManager)
    @objc optional func botInfoManagerDidUpdateBotlistDataComplete(_ mgr: BotInfoManager)
}

@objc
public class BotInfoManager: NSObject, BuzSocialControllerable {
    typealias Center = BuzBotCenter
    weak var center: BuzBotCenter?
    
    public private(set) var publisher = Publisher.init()
    
    public private(set) var botInfoDict = [Int64: BuzBotInfo]()
    public private(set) var specialBotInfoDict = [Int64: BuzBotInfo]() //some botinfo not exists in ai market
    public private(set) var botInfoArray : [BuzBotInfo] = []
    public private(set) var botSettingList = [Int64: BotUserSetting]()
    public private(set) var updatedSetting = NSMutableSet()
    public private(set) var isRequestingBotListData : Bool = false
    public private(set) var isRequestingBotListDataFailure : Bool = false
    public var isBotVoiceStylePlaying = false
    private var cacheRcodeMsgOfBots : [Int64 : (code : Int, message : String)] = [:]
    /// 是否有新的机器人
    public var hasNewBot: Bool {
        get {
            return self.currentReadBotCount < self.currentBotCount
        }
    }
    
    public var currentReadBotCount : Int64  {
        get {
            return MMKV.buz.int64(forKey: self.userReadBotKey)
        }
        set {
            MMKV.buz.set(newValue, forKey: self.userReadBotKey)
            BuzLog.info("has new bot value store \(newValue)")
            self.center?.notifyObservers(of: BuzBotCenterInfoObserver.self) { observer in
                observer.botInfoManagerDidUpdateIsHasNewBot?(self)
            }
            self.publisher.didUpdatedIsHasNewBot.send()
        }
    }
    
    public lazy var currentBotCount : Int64 = MMKV.buz.int64(forKey: BuzBotStoreKey.botNewCount.rawValue) {
        didSet {
            MMKV.buz.set(self.currentBotCount, forKey: BuzBotStoreKey.botNewCount.rawValue)
            BuzLog.info("has new bot value store \(self.currentBotCount)")
            self.center?.notifyObservers(of: BuzBotCenterInfoObserver.self) { observer in
                observer.botInfoManagerDidUpdateIsHasNewBot?(self)
            }
            self.publisher.didUpdatedIsHasNewBot.send()
        }
    }
    
    private var userReadBotKey : String {
        get {
            guard let userId = BuzUserSession.shared.userInfo?.userId else {
                return BuzBotStoreKey.botRead.rawValue
            }
            
            return BuzBotStoreKey.botRead.rawValue + "_\(userId)"
        }
    }
    
    /// 是否有新的机器人
    public var hadReadedTip: Bool = MMKV.buz.bool(forKey: BuzBotStoreKey.botTipKey.rawValue) {
        didSet {
            MMKV.buz.set(hadReadedTip, forKey: BuzBotStoreKey.botTipKey.rawValue)
            BuzLog.info("has read tip store \(hadReadedTip)")
        }
    }
    
    /// 是否有新的机器人
    public var hadExposureRedPoint: Bool = MMKV.buz.bool(forKey: BuzBotStoreKey.botTipRedPointKey.rawValue) {
        didSet {
            MMKV.buz.set(hadExposureRedPoint, forKey: BuzBotStoreKey.botTipRedPointKey.rawValue)
            BuzLog.info("has exposure red point store \(hadExposureRedPoint)")
            self.center?.notifyObservers(of: BuzBotCenterInfoObserver.self) { observer in
                observer.botInfoManagerDidUpdateBotTip?(self)
            }
            self.publisher.didUpdatedBotTip.send()
        }
    }
    
    required init(center: BuzBotCenter?) {
        super.init()
        self.center = center
        initBotData()
    }
}

private extension BotInfoManager {
    /// 初始化数据
    func initBotData() {
        queryBotInfoListFromCache(completion: nil)
        queryBotUserSettingFromCache(completion: nil)
    }
}

public extension BotInfoManager {
    /// 判断指定 ID 是否为机器人
    @objc func isBot(_ bid: Int64) -> Bool {
        let bot = queryBotInfo(bid)
        return bot != nil
    }
    
}

//MARK: --bot info
public extension BotInfoManager {
    
    /// 获取全部机器人信息列表
    func queryBotInfoList(completion: (([Int64: BuzBotInfo]) -> Void)?) {
        if self.botInfoDict.isEmpty {
            self.queryBotInfoListFromCache { cacheBotInfoList in
                completion?(cacheBotInfoList)
            }
        } else {
            completion?(self.botInfoDict)
        }
    }
    
    /// 获取指定的机器人信息，从内存中读取
    /// - Parameters
    ///  - bid 机器人ID
    func queryBotInfo(_ bid: Int64) -> BuzBotInfo? {
        if botInfoDict.has(key: bid), let bot = botInfoDict[bid] {
            return bot
        }
        
        return nil
    }
    
    func cacheBotInfo(_ bid: Int64, info : BuzBotInfo) {
        self.specialBotInfoDict[bid] = info
        self.cacheSpecialBotInfo()
    }
}

//MARK: --Bot Info
public extension BotInfoManager {
    /// 从服务器获取全部机器人的信息并更新本地存储
    func requestBotInfoList(completion: (() -> Void)? = nil) {
        isRequestingBotListData = true
        BotNetwork.getBotInfoList([]) { response in
            self.isRequestingBotListData = false
            
            let callbackDelegate = {
                self.center?.notifyObservers(of: BuzBotCenterInfoObserver.self) { observer in
                    observer.botInfoManagerDidUpdateBotlistDataComplete?(self)
                }
                self.publisher.didUpdatedBotListData.send()
                
                completion?()
            }
            guard response.isSuccess else {
                self.isRequestingBotListDataFailure = true
                callbackDelegate()
                return
            }
            
            self.isRequestingBotListDataFailure = false
            let list = response.rawResponseObj?.data?.botInfoList ?? []
            var botList : [BuzBotInfo] = []
            var botInfoList = [Int64: BuzBotInfo]()
            list.forEach { info in
                guard let botUserId = info.botUserId else { return }
                let botInfo = BuzBotInfo.init(botUserId: info.botUserId, description: info.description, userInfo: info.userInfo,
                                              topics: info.topics, options: info.options, botUIConfig: info.botUIConfig, shortDescription: info.shortDescription, descriptionLink: info.descriptionLink)
                botList.append(botInfo)
                botInfoList[botUserId] = botInfo
                
                if let config = info.botUIConfig , config.showTranslation == true {
                    botInfo.setupLanguageFromServerLanguage()
                }
            }
                    
            // has new aibots?
            if botList.count != 0 {
                self.currentBotCount = Int64(list.count)
            }
            
            // 存储到本地
            self.botInfoDict = botInfoList
            self.botInfoArray = botList
            self.cacheSpecialBotInfo()
            
            do {
                var datas : [BuzDBBotInfo] = []
                
                list.forEach { info in
                    if let tinfo = info.convertToBuzDBBotInfo() {
                        datas.append(tinfo)
                    }
                }
                
                BuzDBBotInfoDao.replaceAll(datas)
            }
            
            callbackDelegate()
        }
    }
    
    /// get  botinfo from
    /// - Parameters
    ///  - bid 机器人ID
    func fetchBotInfo(_ bid: Int64, callback :@escaping ((BuzBotInfo?, Int, String, Bool) -> Void)) {
        if let data = self.cacheRcodeMsgOfBots.first(where: { (key: Int64, value: (code: Int, message: String)) in
            return key == bid
        }) {
            if data.value.code != NetworkRcode.requestFailure.rawValue {
                if data.value.code == 0 {
                    callback(self.botInfoDict[bid], data.value.code, data.value.message, true)
                } else {
                    callback(nil, data.value.code, data.value.message, true)
                }
            }
        }
        
        BotNetwork.getBotInfo(bid) { response in
            var botInfo : BuzBotInfo?
            
            if !response.isSuccess {
                switch response.rcode{
                case 1 :
                    self.cacheRcodeMsgOfBots[bid] = (1, Localizable.ai_game_unavailable_for_version)
                case 2 :
                    self.cacheRcodeMsgOfBots[bid] = (2, Localizable.ai_game_unsupport_language)
                case NetworkRcode.requestFailure.rawValue:
                    self.cacheRcodeMsgOfBots[bid] = (NetworkRcode.requestFailure.rawValue, Localizable.ai_game_unavailable_for_reason)
                default:
                    self.cacheRcodeMsgOfBots[bid] = (3, Localizable.ai_game_unavailable_for_reason)
                }
            } else {
                self.cacheRcodeMsgOfBots[bid] = (0, "")
                
                if let info = response.rawResponseObj?.data?.botInfo, let botId = info.botUserId {
                    botInfo = BuzBotInfo.init(botUserId: info.botUserId, description: info.description, userInfo: info.userInfo,
                                                              topics: info.topics, options: info.options, botUIConfig: info.botUIConfig, shortDescription: info.shortDescription, descriptionLink: info.descriptionLink)
                    self.botInfoDict[botId] = botInfo
                    BuzLog.info("BotInfoManager fetchBotInfo replace data: \(botInfo)")
                }
            }
            
            BuzLog.info("BotInfoManager fetchBotInfo rcode: \(response.rcode)")
            callback(botInfo, response.rcode, self.cacheRcodeMsgOfBots[bid]?.message ?? "", false)
        }
    }
}

//MARK: --Bot Info update
private extension BotInfoManager {
    
    func updateBotInfo(uid : Int64 , complete : ((Bool , BuzBotInfo?) -> Void)?) {
        
        BuzLog.debug("begin request getBotInfoList \(uid)")
        BotNetwork.getBotInfoList([uid]) { response in
            BuzLog.debug("response getBotInfoList \(uid)")
            guard response.isSuccess == true else{
                complete?(false , nil)
                return
            }
            
            var result : Bool = false
            var info : BuzBotInfo? = nil
            if let botInfo = response.rawResponseObj?.data?.botInfoList?.first , let uid = botInfo.botUserId {
                let tBotInfo = BuzBotInfo.init(botUserId: botInfo.botUserId, description: botInfo.description, userInfo: botInfo.userInfo,
                                               topics: botInfo.topics, options: botInfo.options, botUIConfig: botInfo.botUIConfig, shortDescription: botInfo.shortDescription, descriptionLink: botInfo.descriptionLink)
                self.botInfoDict[uid] = tBotInfo
                if let index = self.botInfoArray.firstIndex(where: { info in
                    info.botUserId == uid
                }) {
                    self.botInfoArray[index] = tBotInfo
                }
                result = true
                info = tBotInfo
            }
            complete?(result , info)
        }
    }
}

//MARK: --Bot Info DB--
private extension BotInfoManager {
    /// 从本地缓存中获取全部的机器人数据
    func queryBotInfoListFromCache(completion: (([Int64: BuzBotInfo]) -> Void)?) {
        BuzDBBotInfoDao.fetchAll { infos in
            var infoList : [BuzBotInfo] = []
            
            infos.forEach { info in
                if let tinfo = info.convertToUserInfo() {
                    infoList.append(tinfo)
                }
            }
            
            var botInfoList = [Int64: BuzBotInfo]()
            infoList.forEach { info in
                guard let botUserId = info.botUserId else { return }
                botInfoList[botUserId] = info
            }
            
            self.botInfoDict = botInfoList
            self.cacheSpecialBotInfo()
            self.botInfoArray = infoList
            
            DispatchQueue.main.async {
                if self.botInfoDict.isEmpty {
                    self.requestBotInfoList {
                        completion?(self.botInfoDict)
                    }
                } else {
                    completion?(self.botInfoDict)
                }
            }
        }
    }
    
    func cacheSpecialBotInfo() {
        //merge bots that not exist in ai market (for white ai list) and bots in ai market
        self.specialBotInfoDict.forEach({ (skey: Int64, value: BuzBotInfo) in
            if self.botInfoDict.contains(where: { (key: Int64, value: BuzBotInfo) in
                return key == skey
            }) {
                return
            }
            
            self.botInfoDict[skey] = value
        })
    }
    
}

//MARK: --bot Setting
public extension BotInfoManager {
    /// 获取指定的机器人配置信息（可能为空，获取前确认该机器人可配置）
    /// - Parameters
    ///  - bid 机器人ID
    func fetchBotSetting(_ bid: Int64, completion: ((BotUserSetting?) -> Void)?, forceNetwork:Bool = false) {
        if botSettingList.has(key: bid) {
            completion?(botSettingList[bid])
            
            if forceNetwork {
                requestBotUserSetting(bid) { setting in
                    completion?(setting)
                }
            }
        } else {
            requestBotUserSetting(bid) { setting in
                completion?(setting)
            }
        }
    }
    
    /// 更新缓存中的指定机器人的配置信息
    func updateCacheBotSetting(_ setting: BotUserSetting) {
        guard let botUserId = setting.botUserId else { return }
        
        if botSettingList.has(key: botUserId) {
            self.botSettingList[botUserId] = setting
            self.refreshBotSettingDB()
        }
    }
}

private extension BotInfoManager {
    /// 从服务端获取制定机器人设置数据并缓存数据库
    /// - Parameters:
    ///  - bid: 机器人ID
    private func requestBotUserSetting(_ bid: Int64, completion: ((BotUserSetting) -> Void)?) {
        if self.updatedSetting.contains(bid), let setting = self.botSettingList[bid] {
            completion?(setting)
            return
        }
        
        BuzLog.debug("begin request getBotSetting")
        BotNetwork.getBotSetting(bid) { response in
            BuzLog.debug("response getBotSetting")
            guard response.isSuccess else {
                return
            }
            
            let setting = response.rawResponseObj?.data?.userSetting
            
            if let setting = setting {
                completion?(setting)
                
                // 存储到本地
                self.botSettingList[bid] = setting
                self.updatedSetting.add(bid)
                self.refreshBotSettingDB()
            }
        }
    }
}

//MARK: --Bot Setting DB--
private extension BotInfoManager {
    
    func refreshBotSettingDB() {
        var botSettings : [BuzBotUserSetting] = []
        
        self.botSettingList.forEach { (key: Int64, value: BotUserSetting) in
            let botSetting = BuzBotUserSetting.init()
            botSetting.userId = value.userId ?? 0
            botSetting.botUserId = value.botUserId ?? 0
            botSetting.voiceStyleId = value.voiceStyleId ?? 0
            botSetting.languageCode = value.languageCode
            botSettings.append(botSetting)
        }
        
        BuzBotUserSettingDao.replaceAll(botSettings)
    }
    
    
    /// 从本地缓存中获取当前用户机器人设置
    func queryBotUserSettingFromCache(completion: (([Int64: BotUserSetting]) -> Void)?) {
        BuzBotUserSettingDao.fetchAll { settings in
            var botSettingList = [Int64: BotUserSetting]()
            settings.forEach { setting in
                botSettingList[setting.botUserId] = BotUserSetting.init(userId: setting.userId,
                                                                        botUserId: setting.botUserId,
                                                                        languageCode: setting.languageCode,
                                                                        voiceStyleId: setting.voiceStyleId)
            }
            
            
            DispatchQueue.main.async {
                self.botSettingList = botSettingList
                completion?(self.botSettingList)
            }
        }
    }
}
