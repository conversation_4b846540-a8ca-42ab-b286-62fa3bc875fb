//
//  BotInfoManager+Combine.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/24.
//

import Combine
import BuzIDL
import BuzCenterKit
import BuzFoundation

public extension BotInfoManager {
    class Publisher {
        // 机器人是否有新的变更
        public let didUpdatedIsHasNewBot = PassthroughSubject<Void, Never>()
        
        // 机器人提示信息更新
        public let didUpdatedBotTip = PassthroughSubject<Void, Never>()
        
        // 机器人列表数据更新完成
        public let didUpdatedBotListData = PassthroughSubject<Void, Never>()
    }
}

//MARK: --Bot Info
public extension BuzCombineKit where Base: BotInfoManager {
    
    /// 获取全部机器人信息列表
    func queryBotInfoList() -> AnyPublisher<[Int64: BuzBotInfo], Never> {
        Future { promise in
            self.object.queryBotInfoList { botInfoList in
                promise(.success(botInfoList))
            }
        }
        .eraseToAnyPublisher()
    }
}

//MARK: --Bot Info
public extension BuzCombineKit where Base: BotInfoManager {
    
    /// 从服务器获取全部机器人的信息并更新本地存储
    func requestBotInfoList() -> AnyPublisher<Void, Never> {
        Future { promise in
            self.object.requestBotInfoList {
                promise(.success(()))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 获取指定机器人信息
    func fetchBotInfo(_ bid: Int64) -> AnyPublisher<(BuzBotInfo?, Int, String, Bool), Never> {
        Future { promise in
            self.object.fetchBotInfo(bid) { botInfo, code, msg, success in
                promise(.success((botInfo, code, msg, success)))
            }
        }
        .eraseToAnyPublisher()
    }
}

//MARK: --bot Setting
public extension BuzCombineKit where Base: BotInfoManager {
    
    /// 获取指定的机器人配置信息（可能为空，获取前确认该机器人可配置）
    func fetchBotSetting(_ bid: Int64, forceNetwork: Bool = false) -> AnyPublisher<BotUserSetting?, Never> {
        Future { promise in
            self.object.fetchBotSetting(bid, completion: { setting in
                promise(.success(setting))
            }, forceNetwork: forceNetwork)
        }
        .eraseToAnyPublisher()
    }
    
    /// 更新缓存中的指定机器人的配置信息（直接调用，无需返回值）
    func updateCacheBotSetting(_ setting: BotUserSetting) {
        self.object.updateCacheBotSetting(setting)
    }
}
