//
//  BuzDBBotInfo+Extension.swift
//  buz
//
//  Created by lizhifm on 2/13/25.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import BuzIDL

extension BuzDBBotInfo {
    func convertToUserInfo() -> BuzBotInfo? {
        do {
            let decode = JSONDecoder()
            var userInfo : UserInfo?
            var topics : [BotTopic]?
            var options : BotSettingOption?
            var botUIConfig : BotUIConfig?
            var descriptionLink : BotDescriptionLink?
            
            if let unwrapUserInfo = self.userInfo, unwrapUserInfo.count != 0 {
                userInfo = try decode.decode(UserInfo.self, from: unwrapUserInfo)
            }
            
            if let unwrapTopics = self.topics, unwrapTopics.count != 0 {
                topics = try decode.decode([BotTopic].self, from: unwrapTopics)
            }
            
            if let unwrapOptions = self.options, unwrapOptions.count != 0 {
                options = try decode.decode(BotSettingOption.self, from: unwrapOptions)
            }
            
            if let unwrapBotUIConfig = self.botUIConfig, unwrapBotUIConfig.count != 0 {
                botUIConfig = try decode.decode(BotUIConfig.self, from: unwrapBotUIConfig)
            }
            
            if let unwrapDescriptionLink = self.descriptionLink, unwrapDescriptionLink.count != 0 {
                descriptionLink = try decode.decode(BotDescriptionLink.self, from: unwrapDescriptionLink)
            }
            
            return BuzBotInfo.init(botUserId: self.botUserId, description:self.desc, userInfo : userInfo, topics : topics, options:options,
                                botUIConfig : botUIConfig, shortDescription: shortDescription, descriptionLink: descriptionLink)
        } catch {
            return nil
        }
    }
}

extension BotInfo {
    func convertToBuzDBBotInfo() -> BuzDBBotInfo? {
        guard let userId = self.botUserId else {
            return nil
        }
        
        do {
            let encoder = JSONEncoder()
            let botInfo = BuzDBBotInfo.init()
            botInfo.botUserId = userId
            botInfo.desc = self.description
            botInfo.shortDescription = self.shortDescription
            
            if let userInfo = self.userInfo {
                botInfo.userInfo = try encoder.encode(userInfo)
            }
            
            if let topics = self.topics {
                botInfo.topics = try encoder.encode(topics)
            }
            
            if let options = self.options {
                botInfo.options = try encoder.encode(options)
            }
            
            if let botUIConfig = self.botUIConfig {
                botInfo.botUIConfig = try encoder.encode(botUIConfig)
            }
            
            if let descriptionLink = self.descriptionLink {
                botInfo.descriptionLink = try encoder.encode(descriptionLink)
            }
            
            return botInfo
        } catch {
            return nil
        }
    }
}
