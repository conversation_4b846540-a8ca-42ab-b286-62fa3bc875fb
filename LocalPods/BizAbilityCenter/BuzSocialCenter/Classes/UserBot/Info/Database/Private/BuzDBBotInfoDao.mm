//
//  BuzDBBotInfoDao.m
//  buz
//
//  Created by lizhifm on 2/13/25.
//  Copyright © 2025 lizhi. All rights reserved.
//

#import "BuzDBBotInfoDao.h"
#import "BuzDBBotInfo+WCTTableCoding.h"
#import "BuzDatabaseManager.h"
#import "OCBuzUserSession.h"

@implementation BuzDBBotInfoDao

+ (NSString *)tableName {
    NSNumber *uid = @(OCBuzUserSession.uid);
    NSString *tableName = [NSString stringWithFormat:@"BuzDBBotInfo_%@", uid];
    
    static dispatch_once_t onceToken;
    static NSMutableSet *sets = nil;
    
    dispatch_once(&onceToken, ^{
        sets = [[NSMutableSet alloc] init];
    });
    
    if (![sets containsObject:uid]) {
        BOOL result = [DATABASE createTableAndIndexesOfName:tableName withClass:BuzDBBotInfo.class];
        if (result) {
            [sets addObject:uid];
        }else{
            NSAssert(NO, @"create BuzDBBotInfo table failure!!!");
            return nil;
        }
    }
    return tableName;
}

+ (WCTDatabase *)database {
    return [BuzDatabaseManager database];
}

+ (void)fetchAll:(void (^)(NSArray<BuzDBBotInfo *> *))callback {
    dispatch_async(buz_database_queue(), ^{
        [self.database runTransaction:^BOOL{
            NSArray<BuzDBBotInfo *> *datas = [self.database getObjectsOfClass:BuzDBBotInfo.class
                                                                  fromTable:self.tableName
                                                                    orderBy:BuzDBBotInfo.infoId.order(WCTOrderedAscending)];
            
            if (callback) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    callback(datas);
                });
            }
            
            return true;
        }];
    });
}

+ (void)replaceAll:(NSArray<BuzDBBotInfo *> *)datas {
    for (int i = 0; i < datas.count; i++) {
        datas[i].infoId = i + 1;
    }
    
    dispatch_async(buz_database_queue(), ^{
        [self.database runTransaction:^BOOL{
            [self.database deleteAllObjectsFromTable:self.tableName];
            [self.database insertOrReplaceObjects:datas into:self.tableName];
            
            return true;
        }];
    });
}

@end
