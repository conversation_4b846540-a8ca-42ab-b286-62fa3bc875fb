//
//  BuzBotUserSetting.m
//  buz
//
//  Created by lizhifm on 2/14/25.
//  Copyright © 2025 lizhi. All rights reserved.
//

#import "BuzBotUserSettingDao.h"
#import "BuzBotUserSetting+WCTTableCoding.h"
#import "BuzDatabaseManager.h"
#import "OCBuzUserSession.h"

@implementation BuzBotUserSettingDao

+ (NSString *)tableName {
    NSNumber *uid = @(OCBuzUserSession.uid);
    NSString *tableName = [NSString stringWithFormat:@"BotUserSetting_%@", uid];
    
    static dispatch_once_t onceToken;
    static NSMutableSet *sets = nil;
    
    dispatch_once(&onceToken, ^{
        sets = [[NSMutableSet alloc] init];
    });
    
    if (![sets containsObject:uid]) {
        BOOL result = [DATABASE createTableAndIndexesOfName:tableName
                                                  withClass:BuzBotUserSetting.class];
        if (result) {
            [sets addObject:uid];
        }else{
            NSAssert(NO, @"create BotUserSetting Table table failure!!!");
            return nil;
        }
    }
    return tableName;
}

+ (WCTDatabase *)database {
    return [BuzDatabaseManager database];
}

+ (void)fetchAll:(void (^)(NSArray<BuzBotUserSetting *> *))callback {
    dispatch_async(buz_database_queue(), ^{
        [self.database runTransaction:^BOOL{
            NSArray<BuzBotUserSetting *> *datas = [self.database getAllObjectsOfClass:BuzBotUserSetting.class
                                                                            fromTable:self.tableName];
            
            if (callback) {
                dispatch_async(dispatch_get_main_queue(), ^{
                    callback(datas);
                });
            }
            
            return true;
        }];
    });
}

+ (void)replaceAll:(NSArray<BuzBotUserSetting *> *)datas {
    dispatch_async(buz_database_queue(), ^{
        [self.database runTransaction:^BOOL{
            [self.database deleteAllObjectsFromTable:self.tableName];
            [self.database insertOrReplaceObjects:datas into:self.tableName];
            
            return true;
        }];
    });
}

@end
