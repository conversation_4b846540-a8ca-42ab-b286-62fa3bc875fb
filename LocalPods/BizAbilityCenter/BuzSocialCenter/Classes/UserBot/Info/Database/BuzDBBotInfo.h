//
//  BuzDBBotInfo.h
//  buz
//
//  Created by lizhifm on 2/13/25.
//  Copyright © 2025 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface BuzDBBotInfo : NSObject

@property (nonatomic, assign) int64_t infoId;

/// 机器人id
@property (nonatomic, assign) int64_t botUserId;

/// 机器人描述
@property (nonatomic, copy, nullable) NSString *desc;

/// 机器人的用户信息 -> UserInfo :  json
@property (nonatomic, copy, nullable) NSData *userInfo;

/// topic 列表 -> [BotTopic] : json
@property (nonatomic, copy, nullable) NSData *topics;

/// 机器人选项 -> BotSettingOption : json
@property (nonatomic, copy, nullable) NSData *options;

/// 机器人UI控制 -> BotUIConfig : json
@property (nonatomic, copy, nullable) NSData *botUIConfig;

/// 机器人短描述
@property (nonatomic, copy, nullable) NSString *shortDescription;

/// 机器人描述链接（支持跳转路由） -> BotDescriptionLink : json
@property (nonatomic, copy, nullable) NSData *descriptionLink;

@end

NS_ASSUME_NONNULL_END
