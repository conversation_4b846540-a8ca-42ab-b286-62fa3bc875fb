//
//  BuzIMGroupCenter.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/19.
//

import BuzNetworker

@objc public protocol BuzIMGroupCenterObserver: AnyObject {}

public class BuzIMGroupCenter: NSObject, BuzSocialEventPublisher, BuzSocialEventPublisherInternal {
    public typealias Observer = BuzIMGroupCenterObserver
    internal let observers = NSHashTable<BuzIMGroupCenterObserver>.weakObjects()
    
    @objc public static let center = BuzIMGroupCenter()
    
    private var controllers: [ControllerType: Any] = .init()
    
    override init() {
        super.init()
        BuzNetworker.shared.signalPush.addObserver(self)
    }
    
}

extension BuzIMGroupCenter {
    enum ControllerType {
        case info        // 信息模块
        case member      // 群成员模块
        case operation   // 群操作模块
    }
    
    //MARK: -info---
    @objc public static var info: GroupInfoManager {
        return center.controller(for: .info)
    }
    
    //MARK: -member---
    public static var member: BuzIMGroupMemberCtrl {
        return center.controller(for: .member)
    }
    
    //MARK: -operation---
    public static var operation: BuzIMGroupOpsController {
        return center.controller(for: .operation)
    }
    
    private func controller<T>(for type: ControllerType) -> T {
        if let controller = self.controllers[type] as? T {
            return controller
        }
        
        let controller: Any
        switch type {
        case .info:
            controller = GroupInfoManager(center: self)
        case .member:
            controller = BuzIMGroupMemberCtrl(center: self)
        case .operation:
            controller = BuzIMGroupOpsController(center: self)
        }
        
        self.controllers[type] = controller
        
        return controller as! T
    }
}

extension BuzIMGroupCenter: BuzSignalPushEventObserver {
    public func signalPushDidReceivedMessage(_ message: BuzSignalPush.Message) {
        BuzIMGroupCenter.info.signalPushDidReceivedMessage(message)
        BuzIMGroupCenter.member.signalPushDidReceivedMessage(message)
    }
}
