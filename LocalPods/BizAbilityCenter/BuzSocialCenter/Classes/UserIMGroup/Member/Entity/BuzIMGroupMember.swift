//
//  BuzIMGroupMember.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/24.
//

import BuzCenterKit
import BuzIDL

public class BuzIMGroupMember: NSObject {
    /// 群成员用户信息
    public var userInfo: BuzUserData
    /// 1 群主 2 群管理员 3 普通群成员
    public var userRole: BuzChatGroupRole
    /// (弃用)是否在 OnAir 中，true-在此群组的OnAir中
    public var inOnAir: Bool
    
    public var joinGroupTime: Int64
    
    init(idl: GroupMember) {
        self.userInfo = .init(userId: idl.userInfo.userId ?? 0, base: .init(idl: idl.userInfo))
        self.userRole = .init(rawValue: idl.userRole) ?? .member
        self.inOnAir = idl.inOnAir
        self.joinGroupTime = idl.joinGroupTime ?? 0
    }
    
    init(userInfo: BuzUserData, userRole: BuzChatGroupRole, joinGroupTime : Int64) {
        self.userInfo = userInfo
        self.userRole = userRole
        self.inOnAir = false
        self.joinGroupTime = joinGroupTime
    }
}


public struct BuzIMGroupMemberResParams {
    public var rcode: Int
    public var groupMemberList: [BuzIMGroupMember]?
    public var botList: [BuzIMGroupMember]?
    /// 是否为最后一页
    public var isLastPage: Bool = true
    /// 分页透传参数，客户端存储本地后下次请求透传
    public var queryParams: String?
    
    init(rcode: Int, idl: ResponseGetGroupMembers?) {
        self.rcode = rcode
        if let idl = idl {
            self.groupMemberList = idl.groupMemberList?.map { BuzIMGroupMember.init(idl: $0) }
            self.botList = idl.botList?.map { BuzIMGroupMember.init(idl: $0) }
            self.isLastPage = idl.isLastPage ?? true
            self.queryParams = idl.queryParams
        }
    }
    
    init(members: [BuzIMGroupMember]) {
        self.rcode = 0
        self.groupMemberList = members
    }
}
