//
//  GroupMemberDao.m
//  buz
//
//  Created by nurindamia on 11/03/2025.
//  Copyright © 2025 lizhi. All rights reserved.
//

#import "GroupMemberDao.h"
#import "GroupMemberDBModel+WCTTableCoding.h"
#import "BuzDatabaseManager.h"
#import "OCBuzLog.h"
#import "OCBuzUserSession.h"

#define DATABASE [BuzDatabaseManager database]

@interface GroupMemberDao ()

@property (nonatomic, strong) NSMutableSet <NSNumber *>* createdTableSet;

@end

@implementation GroupMemberDao


+ (instancetype)sharedInstance {
    static id instance = nil;
    if (instance == nil) {
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            instance = [[self alloc] init];
        });
    }
    return instance;
}

- (void)deleteAndUpdateGroupMembers: (int64_t)groupId members:(NSArray<GroupMemberDBModel *> *)members completion:(nullable void (^)(BOOL))completion {
    if (members.count == 0) {
        [self executeTaskInMainQueue:^{
            if (completion) {
                completion(YES);
            }
        }];
        return;
    }
    
    [self executeTaskInDatabaseQueue:^{
        NSString *tableName = [self getTableName];

        BOOL transactionSuccess = [DATABASE runTransaction:^BOOL{
            BOOL deleteSuccess = [DATABASE deleteObjectsFromTable:tableName where: WCTCondition(GroupMemberDBModel.groupId == groupId)];
            BuzDBLogD(@"GroupMemberDao -  Group Member Deletion Status [%d] , groupId = %lld" , deleteSuccess, groupId);
            
            if (!deleteSuccess) {
                return NO;
            }
            
            for (GroupMemberDBModel *model in members) {
                BOOL isUpdateSuccess = [DATABASE insertOrReplaceObject:model into:tableName];
                BuzDBLogD(@"GroupMemberDao -  Group Member Insertion Status [%d] , groupId = %lld" , isUpdateSuccess, groupId);
                if (!isUpdateSuccess) {
                    return NO;
                }
            }
            return YES;
        }];

        if (completion) {
            [self executeTaskInMainQueue:^{
                completion(transactionSuccess);
            }];
        }
    }];
}

- (void)addOrUpdateGroupMembers:(nonnull NSArray<GroupMemberDBModel *> *)members completion:(nullable void (^)(BOOL))completion {
    if (members.count == 0) {
        [self executeTaskInMainQueue:^{
            completion(YES);
        }];
        return;
    }
    [self executeTaskInDatabaseQueue:^{
              
        NSString *tableName =  [self getTableName];
        __block BOOL isUpdateSuccess = YES;
        
        [DATABASE runTransaction:^BOOL{
            for (GroupMemberDBModel *model in members) {
                isUpdateSuccess = [DATABASE insertOrReplaceObject:model into:tableName];
                BuzDBLogD(@"GroupMemberDao -  data insert in batch result %d , groupId = %lld" , isUpdateSuccess, model.groupId);
            }
            return isUpdateSuccess;
        }];
        
        if (completion) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"GroupMemberDao - Batch insert data completed -- callback external result %d" , isUpdateSuccess);
                completion(isUpdateSuccess);
            }];
        }
    }];
}


- (void)deleteAllGroupMembersWithCompletion:(nullable void (^)(BOOL))completion {
    [self executeTaskInDatabaseQueue:^{
       
        NSString *tableName = [self getTableName];
         BOOL isUpdateSuccess = [DATABASE deleteAllObjectsFromTable:tableName];
        
        if (completion) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"GroupMembarDao - Delete all group members information complete -- callback external result %d" , isUpdateSuccess);
                completion(isUpdateSuccess);
            }];
        }
    }];
}

- (void)deleteGroupMembersByGroupId:(nonnull NSArray<NSNumber *> *)groupIdList completion:(nullable void (^)(BOOL))completion {
    if (groupIdList.count == 0) {
        [self executeTaskInMainQueue:^{
            completion(YES);
        }];
       
        return;
    }
    
    [self executeTaskInDatabaseQueue:^{
       
        NSString *tableName = [self getTableName];
        __block BOOL isUpdateSuccess = YES;
        
        [DATABASE runTransaction:^BOOL{
            
            for (NSNumber *groupId in groupIdList) {
                isUpdateSuccess = [DATABASE deleteObjectsFromTable:tableName where: WCTCondition(GroupMemberDBModel.groupId == groupId.longLongValue)];
            }
            return isUpdateSuccess;
        }];
        
        if (completion) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"GroupMemberDao - Deleting group data using group id:(%@) Completed -- Callback external result %d" , groupIdList, isUpdateSuccess);
                completion(isUpdateSuccess);
            }];
        }
    }];
}

- (void)getGroupMembersByGroupId:(int64_t)groupId completion:(nonnull void (^)(NSArray<GroupMemberDBModel *> * _Nullable __strong))completion {
    [self executeTaskInDatabaseQueue:^{
        
        NSString *tableName = [self getTableName];
        NSArray *result = [DATABASE getObjectsOfClass:GroupMemberDBModel.class fromTable:tableName where: WCTCondition(GroupMemberDBModel.groupId == groupId)];

        if (completion) {
            [self executeTaskInMainQueue:^{
                completion(result);
            }];
        }
    }];
}

#pragma mark - private

- (void)executeTaskInDatabaseQueue:(void(^)(void))task
{
    buz_dispatch_queue_async_safe(buz_database_queue(),task);
}

- (void)executeTaskInMainQueue:(void(^)(void))task
{
    buz_dispatch_queue_async_safe(dispatch_get_main_queue(),task);
}

- (NSString *)getTableName
{
   
    NSString *tableName = [NSString stringWithFormat:@"GroupMemberTable"];
    NSNumber *uid = @(OCBuzUserSession.uid);
    
    if (![self.createdTableSet containsObject:uid]) {
        
        BOOL result = [DATABASE createTableAndIndexesOfName:tableName withClass: GroupMemberDBModel.class];
        if (result) {
            [self.createdTableSet addObject:uid];
        }else{
            NSAssert(NO, @"create group member table failed!!!");
            return nil;
        }
    }
    return tableName;
}

- (NSMutableSet<NSNumber *> *)createdTableSet
{
    if (nil == _createdTableSet)
    {
        _createdTableSet = [[NSMutableSet alloc] init];
    }
    return _createdTableSet;
}

@end

