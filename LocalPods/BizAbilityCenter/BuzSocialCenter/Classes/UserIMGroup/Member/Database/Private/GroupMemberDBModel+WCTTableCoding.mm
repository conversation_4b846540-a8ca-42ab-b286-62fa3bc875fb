//
//  GroupMemberDBModel+WCTTableCoding.m
//  buz
//
//  Created by nurindamia on 11/03/2025.
//  Copyright © 2025 lizhi. All rights reserved.
//

#import "GroupMemberDBModel+WCTTableCoding.h"

@implementation GroupMemberDBModel (WCTTableCoding)

WCDB_IMPLEMENTATION(GroupMemberDBModel)

WCDB_SYNTHESIZE(GroupMemberDBModel, groupId)
WCDB_SYNTHESIZE(GroupMemberDBModel, userId)
WCDB_SYNTHESIZE(GroupMemberDBModel, userRole)
WCDB_SYNTHESIZE(GroupMemberDBModel, groupJoinTimestamp)

WCDB_MULTI_PRIMARY(GroupMemberDBModel, "groupMember_multi_key", groupId)
WCDB_MULTI_PRIMARY(GroupMemberDBModel, "groupMember_multi_key", userId)
@end
