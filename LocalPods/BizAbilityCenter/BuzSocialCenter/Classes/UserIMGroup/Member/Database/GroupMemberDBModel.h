//
//  GroupMemberDBModel.h
//  buz
//
//  Created by nurindamia on 11/03/2025.
//  Copyright © 2025 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "BuzUserInfo.h"

NS_ASSUME_NONNULL_BEGIN

@interface GroupMemberDBModel : NSObject

@property (nonatomic, assign) int64_t groupId;

@property (nonatomic, assign) int64_t userId;

// 1: Group owner 2: Group administrator 3: Ordinary group member
@property (nonatomic, assign) int32_t userRole;

// To sort the list by join time when fetching
@property (nonatomic, assign) int64_t groupJoinTimestamp;

@end

NS_ASSUME_NONNULL_END

