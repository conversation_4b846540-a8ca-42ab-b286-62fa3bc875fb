//
//  GroupMemberDao.h
//  buz
//
//  Created by nurindamia on 11/03/2025.
//  Copyright © 2025 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class GroupMemberDBModel;

@interface GroupMemberDao : NSObject

+ (instancetype)sharedInstance;

/** Delete existing group members based on the group ID, then add new members to the database **/
- (void)deleteAndUpdateGroupMembers:(int64_t)groupId
                                     members:(NSArray<GroupMemberDBModel *> *)members
                                  completion:(nullable void (^)(BOOL success))completion;

/** Add or update multiple group members **/
- (void)addOrUpdateGroupMembers:(NSArray<GroupMemberDBModel *> *)members completion:(nullable void (^)(BOOL success))completion;

/** Delete group members by group ID list **/
- (void)deleteGroupMembersByGroupId:(NSArray<NSNumber *> *)groupIdList completion:(nullable void (^)(BOOL success))completion;

/** Get group members by group ID list **/
- (void)getGroupMembersByGroupId:(int64_t)groupId completion:(void (^)(NSArray<GroupMemberDBModel *> * _Nullable members))completion;

/** Delete all group members  data**/
- (void)deleteAllGroupMembersWithCompletion:(nullable void (^)(BOOL success))completion;

@end

NS_ASSUME_NONNULL_END
