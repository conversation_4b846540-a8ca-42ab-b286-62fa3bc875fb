//
//  GroupMemberNetwork.swift
//  buz
//
//  Created by lidawen on 2022/8/15.
//  Copyright © 2022 lizhi. All rights reserved.
//

import BuzIDL
import UIKit
import ITNetLibrary
import BuzLog
import BuzCenterKit
import BuzNetworker

class GroupMemberNetwork: NSObject { }

extension GroupMemberNetwork {
    
    // 查询群成员信息
    static func getGroupAllMembers(groupId: Int64,
                                   queryType : Int32,
                                   queryParams : String? = nil,
                                   handler: @escaping (_ resParams: BuzIMGroupMemberResParams) -> Void) {
        let client = BuzNetGroupServiceClient()
        let req = RequestGetGroupMembers(groupId: groupId, queryType: queryType, queryParams: queryParams)
        
        let _ = client.getGroupMembers(request: req) { result in
            switch result {
            case let .success(responseInfo):
                if let data = responseInfo.data {
                    handler(.init(rcode: responseInfo.code, idl: data))
                } else {
                    BuzLog.error("getGroupMembers data missed")
                    handler(.init(rcode: responseInfo.code, idl: nil))
                }
                break
            case let .failure(error):
                BuzLog.error("getGroupMembers failed:\(error)")
                handler(.init(rcode: NetworkErrorCode, idl: nil))
            }
        }
    }
    
    // 查询群成员信息（群资料用）
    static func getGroupMembersForProfile(groupId: Int64,
                                          queryType : Int32,
                                          queryParams : String? = nil,
                                          handler: @escaping (_ resParams: BuzIMGroupMemberResParams) -> Void) -> ITFuture {
        let client = BuzNetGroupServiceClient()
        let req = RequestGetGroupMembers(groupId: groupId, queryType: queryType, queryParams: queryParams)
        
        return client.getGroupMembers(request: req) { result in
            switch result {
            case let .success(responseInfo):
                if let data = responseInfo.data {
                    handler(.init(rcode: responseInfo.code, idl: data))
                } else {
                    BuzLog.error("getGroupMembers data missed")
                    handler(.init(rcode: responseInfo.code, idl: nil))
                }
                break
            case let .failure(error):
                BuzLog.error("getGroupMembers failed:\(error)")
                handler(.init(rcode: NetworkErrorCode, idl: nil))
            }
        }
    }
}

extension GroupMemberNetwork {
    //获取群组在线成员列表
    static func getGroupOnlineMembers(groupId: Int64, complete:((_ response: BuzNetworkResponse<ITResponse<ResponseGetGroupOnlineMembers>>) -> Void)?) {
        let client = BuzNetGroupServiceClient()
                                 
        let _ = client.getGroupOnlineMembers(request: RequestGetGroupOnlineMembers.init(groupId: groupId)) { result in
            switch result {
            case .success(let responseInfo):
                complete?(BuzNetworkResponse.init(rcode: responseInfo.code, isSuccess: responseInfo.code == 0, rawResponseObj: responseInfo))
            case .failure(let err):
                complete?(BuzNetworkResponse.init(rcode: NetworkRcode.requestFailure.rawValue, isSuccess: false, rawResponseObj: nil , error: err))
            }
        }
    }
}
