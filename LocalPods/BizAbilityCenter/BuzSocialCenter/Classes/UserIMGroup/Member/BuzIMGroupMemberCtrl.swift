//
//  BuzIMGroupOperation.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/24.
//

import ITNetLibrary
import BuzIDL
import BuzNetworker
import BuzUIKit
import BuzLog
import BuzUserSession
import Combine

public extension BuzIMGroupMemberCtrl {
    class Publisher {
        /// 群成员更新了
        public let groupMemberUpdate = PassthroughSubject<(groupId: Int64, userInfo: [String: AnyObject]?), Never>()
        /// 群成员在线变更
        public let groupOnlineMemberDidChanged = PassthroughSubject<IMGroupOnlineMemberInfo, Never>()
        
        public let updateGroupMembers =  PassthroughSubject<(groupId: Int64, updatedMemberList: [BuzIMGroupMember]), Never>()
    }
}

@objc
public protocol BuzIMGroupMemberObserver: BuzIMGroupCenterObserver {
    @objc optional func updateGroupMembers(_ ctrl: BuzIMGroupMemberCtrl, _ groupId: Int64, updatedMemberList: [BuzIMGroupMember])
}


public class BuzIMGroupMemberCtrl: NSObject, BuzSocialControllerable {
    typealias Center = BuzIMGroupCenter
    weak var center: BuzIMGroupCenter?
    
    
    public private(set) lazy var publisher = Publisher.init()
    
    var requestServerCache = Set<Int64>()
    private let LogTag : String = "BuzLog_GroupMember"
    
    required init(center: BuzIMGroupCenter?) {
        self.center = center
        super.init()
        BuzUserSession.shared.addObserver(self)
    }
}

public extension BuzIMGroupMemberCtrl {
    
    // Cache management for groud ids
    func isGroupFetched(groupId: Int64) -> Bool {
        return requestServerCache.contains(groupId)
    }
    
    func markGroupAsFetched(groupId: Int64) {
        requestServerCache.insert(groupId)
    }
    
    func clearCache() {
        requestServerCache.removeAll()
    }
}


public extension BuzIMGroupMemberCtrl {
    
    func requestGroupMembersForProfile(groupId: Int64,
                                       queryType : Int32,
                                       queryParams : String? = nil,
                                       handler: @escaping (_ resParams: BuzIMGroupMemberResParams) -> Void) -> ITFuture {
        return GroupMemberNetwork.getGroupMembersForProfile(groupId: groupId, queryType: queryType, queryParams: queryParams) { resParams in
            if let botInfos = resParams.botList {
                var ids : [Int64] = []
                
                botInfos.forEach { member in
                    ids.append(member.userInfo.userId)
                }
                
                let botMember = BuzIMGroupBotConfig.init(groupId: groupId, botIds: ids)
                BuzBotCenter.imGroup.updateBotsOfGroup(configs: [botMember])
            }
            
            handler(resParams)
        }
    }
}

public extension BuzIMGroupMemberCtrl {
    func fetchMembers(groupId: Int64,
                      queryType : Int32,
                      queryParams : String? = nil,
                      isForceFetch : Bool = false,
                      handler: @escaping (_ resParams: BuzIMGroupMemberResParams) -> Void) {
        
        getGroupMembersFromDb(groupId: groupId) { members in
            let isFetchedBefore = self.isGroupFetched(groupId: groupId)
            if isForceFetch {
                self.requestMembers(groupId: groupId, queryType: queryType, queryParams: queryParams, handler: handler)
                return
            }
            
            if isFetchedBefore {
                if !members.isEmpty {
                    handler(.init(members: members))
                    return
                }
            }
            self.requestMembers(groupId: groupId, queryType: queryType, queryParams: queryParams, handler: handler)
        }
        
    }
    
    func requestMembers(groupId: Int64,
                        queryType : Int32,
                        queryParams : String? = nil,
                        handler: @escaping (_ resParams: BuzIMGroupMemberResParams) -> Void) {
        
        let dispatchGroup = DispatchGroup()
        GroupMemberNetwork.getGroupAllMembers(groupId: groupId, queryType: queryType, queryParams: queryParams) { resParams in
            if resParams.rcode == 0 {
                if let botInfos = resParams.botList {
                    var ids : [Int64] = []
                    
                    botInfos.forEach { member in
                        ids.append(member.userInfo.userId)
                    }
                    
                    let botMember = BuzIMGroupBotConfig.init(groupId: groupId, botIds: ids)
                    BuzBotCenter.imGroup.updateBotsOfGroup(configs: [botMember])
                }
                if let memberList = resParams.groupMemberList {
                    
                    var dbMembers: [GroupMemberDBModel] = []
                    let userInfos = memberList.map { $0.userInfo }
                    dispatchGroup.enter()
                    BuzFriendCenter.info.queryUserInfo(buzUsers: userInfos) { users in
                        for member in memberList {
                            if let friend = users.first(where: {member.userInfo.userId == $0.userId}) {
                                friend.base = member.userInfo.base
                                BuzFriendCenter.info.updateLocalUserInfo(info: friend) { success in
                                }
                            }else{
                                BuzFriendCenter.info.updateLocalUserInfo(info: member.userInfo) { success in
                                }
                            }
                            let dbModel = GroupMemberDBModel()
                            dbModel.groupId = groupId
                            dbModel.userId = member.userInfo.userId
                            dbModel.userRole = member.userRole.rawValue
                            dbModel.groupJoinTimestamp = member.joinGroupTime
                            dbMembers.append(dbModel)
                        }
                        
                        dispatchGroup.enter()
                        if let queryParams = resParams.queryParams, queryParams.isEmpty {
                            self.deleteAndUpdateGroupMembers(groupId: groupId, dbMembers: dbMembers) { members in
                                dispatchGroup.leave()
                            }
                        }else{
                            self.addOrUpdateGroupMembers(dbMembers) { members in
                                dispatchGroup.leave()
                            }
                        }
                        dispatchGroup.leave()
                    }
                    
                    dispatchGroup.notify(queue: .main) {
                        if resParams.isLastPage {
                            self.markGroupAsFetched(groupId: groupId)
                        }
                        handler(resParams)
                    }
                } else {
                    BuzLog.error("getGroupMembers data missed")
                    handler(resParams)
                }
            }else {
                BuzLog.error("getGroupMembers failed:\(resParams.rcode)")
                self.getGroupMembersFromDb(groupId: groupId) { members in
                    if members.count > 0 {
                        handler(BuzIMGroupMemberResParams(
                            members: members))
                    }else{
                        handler(resParams)
                    }
                }
            }
        }
    }
}

private extension BuzIMGroupMemberCtrl {
    
    func getGroupMembersFromDb(groupId: Int64, completion: @escaping ([BuzIMGroupMember]) -> Void) {
        GroupMemberDao.sharedInstance().getGroupMembers(byGroupId: groupId) { models in
            guard let members = models, !members.isEmpty else {
                completion([])
                return
            }
            
            let userIds = members.map { $0.userId }
            
            let dbMembersInfo = BuzFriendCenter.info.queryUserInfo(userIds: userIds)
            
            let groupMembers: [BuzIMGroupMember] = members.compactMap { member in
                guard let userInfo = dbMembersInfo[member.userId] else {
                    return nil
                }
                return BuzIMGroupMember(
                    userInfo: userInfo,
                    userRole: .init(rawValue: member.userRole) ?? .member,
                    joinGroupTime: member.groupJoinTimestamp
                )
            }
            completion(groupMembers)
        }
    }
    
    func deleteAndUpdateGroupMembers(groupId: Int64, dbMembers: [GroupMemberDBModel], completion: ((Bool) -> Void)?) {
        GroupMemberDao.sharedInstance().deleteAndUpdateGroupMembers(groupId, members: dbMembers) { success in
            completion?(success)
        }
    }
    
    func addOrUpdateGroupMembers(_ dbMembers: [GroupMemberDBModel], completion: ((Bool) -> Void)?) {
        GroupMemberDao.sharedInstance().addOrUpdateGroupMembers(dbMembers) { success in
            completion?(success)
        }
    }
}

public extension BuzIMGroupMemberCtrl {
    //获取群组在线成员列表
    func requestGroupOnlineMembers(groupId : Int64 , complete:((_ rcode: Int, _ response: ResponseGetGroupOnlineMembers?) -> Void)?) {
        GroupMemberNetwork.getGroupOnlineMembers(groupId: groupId) { response in
            complete?(response.rcode, response.rawResponseObj?.data)
        }
    }
}


extension BuzIMGroupMemberCtrl: BuzSignalPushEventObserver {
    public func signalPushDidReceivedMessage(_ message: BuzSignalPush.Message) {
        guard let payload = message.payload else {
            return
        }
        switch payload.op {
        case .pushDataChange:
            let type = BuzSignalPush.PushDataChange.init(rawValue: payload.businessType ?? 0)
            let businessId = payload.data?["businessId"] as? String ?? ""
            let businessIdValue = Int64(businessId)
            switch type {
            case .GroupMembers: //2-群成员变更(getGroupMembers)
                guard let businessIdValue = businessIdValue else { return }
                BuzSignalPushLog.info("群成员变化push \(businessIdValue)")
                BuzProgressHUD.showDebugToast(withText: "群成员变换推送 \(businessIdValue)")
                self.didReceivedGroupMemberChangePush(groupId: businessIdValue, userInfo: nil)
            case .GroupWalkieOnlineMember: //10 -群组对讲机在线成员变更
                guard let businessIdValue = businessIdValue else { return }
                BuzSignalPushLog.info("群组对讲机在线成员变更push: \(businessIdValue)")
    //            BuzProgressHUD.showDebugToast(withText: "群组对讲机在线成员变更推送:\(businessIdValue)")
                
                let sendTimestamp = payload.sendTimestamp ?? 0
                let onlineMembers = payload.data?["onlineMembers"] as? [[String:AnyObject]] ?? []
                let onlineMemberCount = payload.data?["onlineMemberCount"] as? Int64 ?? 0
                
                let info = IMGroupOnlineMemberInfo.init(groupId: businessIdValue, members: onlineMembers, count: onlineMemberCount, timestamp: sendTimestamp)
                self.center?.notifyObservers(of: BuzIMGroupCenterInfoObserver.self) { observer in
                    observer.groupOnlineMemberDidChanged?(BuzIMGroupCenter.info, onlineMemberInfo: info)
                }
                self.publisher.groupOnlineMemberDidChanged.send(info)
            default:
                break
            }
        default:
            break
        }
    }
    
    // 群成员变更推送
    public func didReceivedGroupMemberChangePush(groupId: Int64, userInfo : [String : AnyObject]?) {
        if groupId <= 0 {
            return
        }
        BuzLog.debug(LogTag,"didReceivedGroupMemberChangePush, groupId:\(groupId)")
        self.center?.notifyObservers(of: BuzIMGroupCenterInfoObserver.self) { observer in
            observer.groupMemberUpdate?(BuzIMGroupCenter.info, groupId, userInfo: userInfo)
        }
        self.publisher.groupMemberUpdate.send((groupId, userInfo))
        
        BuzIMGroupCenter.member.fetchMembers(groupId: groupId, queryType: 0, isForceFetch: true) { [weak self] resParams in
            guard let self = self else { return }
            self.center?.notifyObservers(of: BuzIMGroupMemberObserver.self, { observer in
                observer.updateGroupMembers?(self, groupId, updatedMemberList: resParams.groupMemberList ?? [])
            })
            self.publisher.updateGroupMembers.send((groupId, resParams.groupMemberList ?? []))
        }
        
    }
}

extension BuzIMGroupMemberCtrl:  BuzUserSessionObserver {
    @objc public func userSessionDidLogoutSuccess(_ mgr: BuzUserSession) {
        self.clearCache()
    }
}
