//
//  BuzIMGroupMemberController+Combine.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/25.
//

import Combine
import BuzIDL
import ITNetLibrary
import BuzCenterKit
import BuzFoundation

public extension BuzCombineKit where Base: BuzIMGroupMemberCtrl {
    /// 获取群所有成员
    func requestMembers(groupId: Int64,
                        queryType: Int32,
                        queryParams: String? = nil) -> AnyPublisher<BuzIMGroupMemberResParams, Never> {
        Future { promise in
            self.object.requestMembers(groupId: groupId, queryType: queryType, queryParams: queryParams) { resParams in
                promise(.success(resParams))
            }
        }
        .eraseToAnyPublisher()
    }

    /// 获取群成员（用于 Profile）
    func requestGroupMembersForProfile(groupId: Int64,
                                       queryType: Int32,
                                       queryParams: String? = nil) -> (AnyPublisher<BuzIMGroupMemberResParams, Never>, ITFuture?) {
        var future: ITFuture?
        
        let publisher = Future<BuzIMGroupMemberResParams, Never> { promise in
            future = self.object.requestGroupMembersForProfile(groupId: groupId, queryType: queryType, queryParams: queryParams) { resParams in
                promise(.success(resParams))
            }
        }
        .eraseToAnyPublisher()
        
        return (publisher, future)
    }
}
