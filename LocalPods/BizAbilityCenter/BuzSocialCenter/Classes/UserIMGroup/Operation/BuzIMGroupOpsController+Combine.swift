//
//  BuzIMGroupOpsController+Combine.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/25.
//

import Combine
import BuzIDL
import BuzCenterKit
import BuzFoundation

public extension BuzCombineKit where Base: BuzIMGroupOpsController {

    // MARK: - 修改群信息
    func updateGroupInfo(groupId: Int64, name: String?, portraitUploadId: Int64?) -> AnyPublisher<(rcode: Int, groupInfo: BuzIMGroupData?), Never> {
        Future { promise in
            self.object.updateGroupInfo(groupId: groupId, name: name, portraitUploadId: portraitUploadId) { rcode, groupInfo in
                promise(.success((rcode, groupInfo)))
            }
        }
        .eraseToAnyPublisher()
    }
}

public extension BuzCombineKit where Base: BuzIMGroupOpsController {
    
    // MARK: - 创建群
    func createGroup(name: String?, portraitUploadId: Int64?, members: [Int64]) -> AnyPublisher<(rcode: Int, groupId: Int64, rejectedMessage: String?), Never> {
        Future { promise in
            self.object.createGroup(name: name, portraitUploadId: portraitUploadId, members: members) { rcode, groupId, rejectedMessage in
                promise(.success((rcode, groupId, rejectedMessage)))
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 举报群
    func reportGroup(groupId: Int64) -> AnyPublisher<Int, Never> {
        Future { promise in
            self.object.reportGroup(groupId: groupId) { rcode in
                promise(.success(rcode))
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 退出群聊
    func quitGroup(groupId: Int64) -> AnyPublisher<Int, Never> {
        Future { promise in
            self.object.quitGroup(groupId: groupId) { rcode in
                promise(.success(rcode))
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 邀请入群
    func inviteToJoinGroup(groupId: Int64, inviteUsers: [Int64]) -> AnyPublisher<(rcode: Int, rejectedMsg: String?), Never> {
        Future { promise in
            self.object.inviteToJoinGroup(groupId: groupId, inviteUsers: inviteUsers) { rcode, rejectedMsg in
                promise(.success((rcode, rejectedMsg)))
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 踢人
    func kickOutGroup(groupId: Int64, userId: Int64) -> AnyPublisher<Int, Never> {
        Future { promise in
            self.object.kickOutGroup(groupId: groupId, userId: userId) { rcode in
                promise(.success(rcode))
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 申请加入群
    func requestJoinGroup(groupId: Int64, inviteUserId: Int64) -> AnyPublisher<Int, Never> {
        Future { promise in
            self.object.requestJoinGroup(groupId: groupId, inviteUserId: inviteUserId) { rcode in
                promise(.success(rcode))
            }
        }
        .eraseToAnyPublisher()
    }
}
