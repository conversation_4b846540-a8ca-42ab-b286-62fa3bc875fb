//
//  GroupNetwork.swift
//  buz
//
//  Created by lidawen on 2022/8/15.
//  Copyright © 2022 lizhi. All rights reserved.
//

import BuzIDL
import UIKit
import ITNetLibrary
import BuzLog
import BuzCenterKit

class GroupNetwork: NSObject {
    
    // 创建群
    static func createGroup(name: String?,
                            portraitUploadId: Int64?,
                            members: [Int64],
                            handler: @escaping (_ rcode: Int, _ rejectedMessage: String?, _ groupInfo: GroupInfo?) -> Void) {
        let client = BuzNetGroupServiceClient()
        let req = RequestCreateGroup(groupName: name, invitedUserIds: members, uploadId: portraitUploadId)
        _ = client.createGroup(request: req) { result in
            switch result {
            case let .success(responseInfo):
                let rcode = responseInfo.code
                if rcode != 0 {
                    BuzLog.error("createGroup server error:\(rcode)")
                }
                handler(rcode, responseInfo.data?.rejectedMsg, responseInfo.data?.groupInfo)
                break
            case let .failure(error):
                BuzLog.error("createGroup failed:\(error)")
                handler(NetworkErrorCode, nil, nil)
                break
            }
        }
    }
    
    // 修改群信息
    static func updateGroupInfo(groupId: Int64, name: String?, portraitUploadId: Int64?, handler: @escaping (_ rcode: Int, _ groupInfo: GroupInfo?) -> Void) {
        let client = BuzNetGroupServiceClient()
        let req = RequestUpdateGroupInfo(groupId: groupId, groupName: name, uploadId: portraitUploadId)
        _ = client.updateGroupInfo(request: req) { result in
            switch result {
            case let .success(responseInfo):
                handler(responseInfo.code, responseInfo.data?.groupInfo)
                break
            case let .failure(error):
                BuzLog.error("updateGroupInfo failed:\(error)")
                handler(NetworkErrorCode, nil)
                break
            }
        }
    }

    // 举报群
    static func reportGroup(groupId: Int64, _ handler: @escaping (_ rcode: Int) -> Void) {
        let client = BuzNetGroupServiceClient()
        let req = RequestReportGroup(groupId: groupId)
        _ = client.reportGroup(request: req) { result in
            switch result {
            case let .success(responseInfo):
                handler(responseInfo.code)
                break
            case let .failure(error):
                BuzLog.error("reportGroup failed:\(error)")
                handler(NetworkErrorCode)
            }
        }
    }

    // 离开群
    static func quitGroup(groupId: Int64, _ handler: @escaping (_ rcode: Int) -> Void) {
        let client = BuzNetGroupServiceClient()
        let req = RequestQuitGroup(groupId: groupId)
        _ = client.quitGroup(request: req) { result in
            switch result {
            case let .success(responseInfo):
                handler(responseInfo.code)
                break
            case let .failure(error):
                BuzLog.error("quitGroup failed:\(error)")
                handler(NetworkErrorCode)
            }
        }
    }
    
    // 邀请入群
    static func inviteToJoinGroup(groupId: Int64, inviteUsers: [Int64], _ handler: @escaping (_ rcode: Int , _ rejectedMsg : String?) -> Void) {
        let client = BuzNetGroupServiceClient()
        let req = RequestInviteToJoinGroup(groupId: groupId, invitedUserIds: inviteUsers)
        _ = client.inviteToJoinGroup(request: req) { result in
            switch result {
            case let .success(responseInfo):
                handler(responseInfo.code , responseInfo.data?.rejectedMsg)
                break
            case let .failure(error):
                BuzLog.error("inviteToJoinGroup failed:\(error)")
                handler(NetworkErrorCode , nil)
            }
        }
    }
    
    // 踢人
    static func kickOutGroup(groupId: Int64, userId: Int64, _ handler: @escaping (_ rcode: Int) -> Void) {
        let client = BuzNetGroupServiceClient()
        let req = RequestKickOutGroup(groupId: groupId, targetUserIds: [userId])
        _ = client.kickOutGroup(request: req) { result in
            switch result {
            case let .success(responseInfo):
                handler(responseInfo.code)
                break
            case let .failure(error):
                BuzLog.error("kickOutGroup failed:\(error)")
                handler(NetworkErrorCode)
            }
        }
    }
    
    // 主动加群
    // inviteUserId 邀请者id
    static func requestJoinGroup(groupId: Int64, inviteUserId: Int64, _ handler: @escaping (_ rcode: Int) -> Void) {
        let client = BuzNetGroupServiceClient()
        let req = RequestJoinGroup(groupId: groupId, inviteUserId: inviteUserId)
        _ = client.joinGroup(request: req) { result in
            switch result {
            case let .success(responseInfo):
                handler(responseInfo.code)
                break
            case let .failure(error):
                BuzLog.error("joinGroup groupId:\(groupId) inviteUserId:\(inviteUserId) failed:\(error)")
                handler(NetworkErrorCode)
            }
        }
    }
}
