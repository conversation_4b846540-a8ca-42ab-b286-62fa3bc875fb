//
//  BuzIMGroupOpsController.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/24.
//

import BuzCenterKit

public class BuzIMGroupOpsController: NSObject, BuzSocialControllerable {
    typealias Center = BuzIMGroupCenter
    weak var center: BuzIMGroupCenter?
    
    required init(center: BuzIMGroupCenter?) {
        self.center = center
        super.init()
    }
}

public extension BuzIMGroupOpsController {
    // 修改群信息
    func updateGroupInfo(groupId: Int64, name: String?, portraitUploadId: Int64?, handler: @escaping (_ rcode: Int, _ groupInfo: BuzIMGroupData?) -> Void) {
        GroupNetwork.updateGroupInfo(groupId: groupId, name: name, portraitUploadId: portraitUploadId) { rcode, groupInfo in
            if rcode == 0, let pbInfo = groupInfo {
                BuzIMGroupCenter.info.transformBpGroupInfo(bpInfo: pbInfo) { groupInfo in
                    BuzIMGroupCenter.info.updateGroupInfo(groupInfo: groupInfo) { success, _ in
                        handler(rcode, groupInfo)
                    }
                }
            } else {
                handler(rcode, nil)
            }
        }
    }
}

public extension BuzIMGroupOpsController {
    // 创建群
    func createGroup(name: String?, portraitUploadId: Int64?, members: [Int64], handler: @escaping (_ rcode: Int, _ groupId: Int64, _ rejectedMessage: String?) -> Void) {
        GroupNetwork.createGroup(name: name, portraitUploadId: portraitUploadId, members: members) { rcode, rejectedMessage, groupInfo in
            if rcode == 0 {
                var groupId: Int64 = 0
                if let groupInfo = groupInfo {
                    groupId = groupInfo.groupBaseInfo.groupId
                    // 缓存到本地
                    BuzIMGroupCenter.info.saveNewGroup(bpInfo: groupInfo, complete: nil)
                }
                handler(rcode, groupId, rejectedMessage)
            }else{
                handler(rcode, 0, nil)
            }
        }
    }
    
    // 举报群
    func reportGroup(groupId: Int64, _ handler: @escaping (_ rcode: Int) -> Void) {
        GroupNetwork.reportGroup(groupId: groupId, { rcode in
            handler(rcode)
        })
    }

    // 离开群
    func quitGroup(groupId: Int64, _ handler: @escaping (_ rcode: Int) -> Void) {
        GroupNetwork.quitGroup(groupId: groupId) { rcode in
            if rcode == 0 {
                BuzIMGroupCenter.info.deleteGroupInfo(groupId: groupId) { _ in
                    handler(rcode)
                }
            }else{
                handler(rcode)
            }
        }
    }
    
    // 邀请入群
    func inviteToJoinGroup(groupId: Int64, inviteUsers: [Int64], _ handler: @escaping (_ rcode: Int , _ rejectedMsg : String?) -> Void) {
        GroupNetwork.inviteToJoinGroup(groupId: groupId, inviteUsers: inviteUsers) { rcode, rejectedMsg in
            handler(rcode, rejectedMsg)
        }
    }
    
    // 踢人
    func kickOutGroup(groupId: Int64, userId: Int64, _ handler: @escaping (_ rcode: Int) -> Void) {
        GroupNetwork.kickOutGroup(groupId: groupId, userId: userId) { rcode in
            handler(rcode)
        }
    }
    
    // 主动加群
    // inviteUserId 邀请者id
    func requestJoinGroup(groupId: Int64, inviteUserId: Int64, _ handler: @escaping (_ rcode: Int) -> Void) {
        GroupNetwork.requestJoinGroup(groupId: groupId, inviteUserId: inviteUserId) { rcode in
            handler(rcode)
        }
    }
}
