//
//  GroupInfoManager+Combine.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/24.
//

import Combine
import BuzCenterKit
import BuzFoundation
import BuzIDL

public extension GroupInfoManager {
    class Publisher {
        /// 群信息更新了
        public let groupInfoUpdate = PassthroughSubject<BuzIMGroupData, Never>()
        /// 离开群
        public let groupDidLeave = PassthroughSubject<Int64, Never>()
        /// 群在线状态改变
        public let groupOnlineStateUpdate = PassthroughSubject<Void, Never>()
        /// 群消息静音状态改变
        public let groupMuteMessageUpdate = PassthroughSubject<(groupId: Int64, type: BuzMuteMessagesType), Never>()
        /// 群通知静音状态改变
        public let groupMuteNotificationUpdate = PassthroughSubject<(groupId: Int64, type: BuzMuteNotificationType), Never>()
    }
}

public extension BuzCombineKit where Base: GroupInfoManager {
    /// 清除缓存
    func cleanDataWhenLogout() -> AnyPublisher<Void, Never> {
        self.object.cleanDataWhenLogout()
        return Empty(completeImmediately: true).eraseToAnyPublisher()
    }
}

// MARK: - 查询群信息相关
public extension BuzCombineKit where Base: GroupInfoManager {
    enum GroupInfoSource {
        case cache
        case complete
    }

    struct FetchGroupInfoResult {
        let source: GroupInfoSource
        let status: Bool
        let data: BuzIMGroupData?
    }
    
    /// 查询群对讲机在线状态(本地数据)
    /// - Parameter groupId: 群ID
    /// - Returns: 是否在线
    func getGroupWalkieTalkieOnlineState(groupId: Int64) -> AnyPublisher<Bool, Never> {
        let result = self.object.getGroupWalkieTalkieOnlineState(groupId: groupId)
        return Just(result).eraseToAnyPublisher()
    }
    
    /// 查询并缓存我的所有群
    func queryAndCacheJoinedAllGroupInfo() -> AnyPublisher<(status: Bool, groupInfoList: [BuzIMGroupData]), Never> {
        Future { promise in
            self.object.queryAndCacheJoinedAllGroupInfo {status, groupList in
                promise(.success((status, groupList)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 查询在线的群列表
    func getOnlineJoinedGroups() -> AnyPublisher<Bool, Never> {
        Future { promise in
            self.object.getOnlineJoinedGroups { success in
                promise(.success(success))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 更新在线群
    func updateOnlineJoinedGroup(group: GroupOnlineInfo) -> AnyPublisher<Void, Never> {
        self.object.updateOnlineJoinedGroup(group: group)
        return Empty(completeImmediately: true).eraseToAnyPublisher()
    }
    
    func queryGroupInfoFromCache(groupId: Int64) -> AnyPublisher<BuzIMGroupData?, Never> {
        Future { promise in
            self.object.queryGroupInfoFromCache(groupId: groupId) { groupInfo in
                promise(.success(groupInfo))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 查询群信息（先缓存再网络）
    func queryGroupInfo(groupId: Int64) -> AnyPublisher<FetchGroupInfoResult, Never> {
        let subject = PassthroughSubject<FetchGroupInfoResult, Never>()
        
        self.object.queryGroupInfo(groupId: groupId, cacheCallback: { cacheInfo in
            subject.send(FetchGroupInfoResult(source: .cache, status: true, data: cacheInfo))
        }, complete: {status, networkInfo in
            subject.send(FetchGroupInfoResult(source: .cache, status: status, data: networkInfo))
            subject.send(completion: .finished)
        })
        
        return subject.eraseToAnyPublisher()
    }
    
    /// 查询缓存群信息，没有的时候会去网络查
    /// - Parameters:
    ///   - groupId: 群id
    ///   - complete: 群信息
    func queryCacheGroupInfo(groupId: Int64) -> AnyPublisher<BuzIMGroupData?, Never> {
        Future { promise in
            self.object.queryCacheGroupInfo(groupId: groupId) { groupInfo in
                promise(.success(groupInfo))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 批量查询群信息
    func queryGroupInfo(groupIds: [Int64], forceRefresh: Bool = false) -> AnyPublisher<(status: Bool, groupInfoList: [BuzIMGroupData]), Never> {
        Future { promise in
            self.object.queryGroupInfo(groupIds: groupIds, forceRefresh: forceRefresh) {status, groupList in
                promise(.success((status, groupList)))
            }
        }
        .eraseToAnyPublisher()
    }
}

// MARK: - 群信息操作
public extension BuzCombineKit where Base: GroupInfoManager {
    
    /// 保存新群信息
    func saveNewGroup(bpInfo: GroupInfo) -> AnyPublisher<(status: Bool, groupInfo: BuzIMGroupData?), Never> {
        Future { promise in
            self.object.saveNewGroup(bpInfo: bpInfo) { status, groupInfo in
                promise(.success((status, groupInfo)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 保存新群信息
    func saveNewGroup(groupInfo: BuzIMGroupData) -> AnyPublisher<(status: Bool, groupInfo: BuzIMGroupData?), Never> {
        Future { promise in
            self.object.saveNewGroup(groupInfo: groupInfo) { status, groupInfo in
                promise(.success((status, groupInfo)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 更新群信息
    func updateGroupInfo(bpInfo: GroupInfo) -> AnyPublisher<(status: Bool, groupInfo: BuzIMGroupData?), Never> {
        Future { promise in
            self.object.updateGroupInfo(bpInfo: bpInfo) { status, groupInfo in
                promise(.success((status, groupInfo)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 更新群信息
    func updateGroupInfo(groupInfo: BuzIMGroupData) -> AnyPublisher<(status: Bool, groupInfo: BuzIMGroupData?), Never> {
        Future { promise in
            self.object.updateGroupInfo(groupInfo: groupInfo) { status, groupInfo in
                promise(.success((status, groupInfo)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 删除群信息
    func deleteGroupInfo(groupId: Int64) -> AnyPublisher<Bool, Never> {
        Future { promise in
            self.object.deleteGroupInfo(groupId: groupId) { success in
                promise(.success(success))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 设置群消息静音状态
    func updateMuteMessages(groupId: Int64, mute: Bool) -> AnyPublisher<Int, Never> {
        Future { promise in
            self.object.updateMuteMessages(with: groupId, mute: mute) { rcode in
                promise(.success(rcode))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 设置群通知静音状态
    func updateMuteNotification(groupId: Int64, mute: Bool) -> AnyPublisher<Int, Never> {
        Future { promise in
            self.object.updateMuteNotification(with: groupId, mute: mute) { rcode in
                promise(.success(rcode))
            }
        }
        .eraseToAnyPublisher()
    }
}

// MARK: - 工具方法
public extension BuzCombineKit where Base: GroupInfoManager {
    
    /// 把pb群信息转换为BuzIMGroupData (用户信息会优先匹配本地通讯录备注)
    func transformBpGroupInfo(bpInfo: GroupInfo) -> AnyPublisher<BuzIMGroupData, Never> {
        Future { promise in
            self.object.transformBpGroupInfo(bpInfo: bpInfo) { groupInfo in
                promise(.success(groupInfo))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 保存群信息到本地缓存
    func saveGroupsMapToLocalShareCache() -> AnyPublisher<Void, Never> {
        self.object.saveGroupsMapToLocalShareCache()
        return Empty(completeImmediately: true).eraseToAnyPublisher()
    }
}
