//
//  BuzIMGroupData+Extension.swift
//  buz
//
//  Created by st.chio on 2025/2/13.
//  Copyright © 2025 lizhi. All rights reserved.
//

import BuzCenterKit

extension BuzIMGroupData {
    
    /// 数据库对象初始化
    /// - Parameter entity: dao实体对象
    convenience init(entity: GroupInfoModel) {
        self.init(groupId: entity.groupId,
                  base: BaseInfo.init(entity: entity),
                  extra: .init(entity: entity))
    }
    
    /// 数据库转换为DaoEntity
    /// - return: dao实体对象
    func convertDaoEntity() -> GroupInfoModel {
        let entity = GroupInfoModel.init()
        entity.groupId = self.groupId
        entity.displayGroupName = self.base.displayGroupName
        entity.groupName = self.base.groupName
        entity.portraitUrl = self.base.portraitUrl
        entity.serverPortraitUrl = self.base.serverPortraitUrl
        entity.memberNum = self.base.memberNum
        entity.maxMemberNum = self.base.maxMemberNum
        entity.groupStatus = self.base.groupStatus.rawValue
        entity.groupType = self.base.groupType.rawValue
        entity.firstFewPortraits = self.base.firstFewPortraits
        
        entity.createTime = self.extra.createTime
        entity.canInvite = self.extra.canInvite
        entity.canEdit = self.extra.canEdit
        entity.userRole = Int(self.extra.userRole.rawValue)
        entity.userStatus = Int(self.extra.userStatus.rawValue)
        entity.serviceMuteMessages = Int(self.extra.serviceMuteMessages.rawValue)
        entity.localMuteMessages = Int(self.extra.localMuteMessages.rawValue)
        entity.muteNotification = Int(self.extra.muteNotification.rawValue)
        
        return entity
    }
}

extension BuzIMGroupData.BaseInfo {
    /// 数据库对象初始化
    /// - Parameter entity: dao实体对象
    convenience init(entity: GroupInfoModel) {
        self.init(groupId: entity.groupId)
        self.displayGroupName = entity.displayGroupName
        self.groupName = entity.groupName
        self.portraitUrl = entity.portraitUrl
        self.firstFewPortraits = entity.firstFewPortraits
        self.memberNum = entity.memberNum
        self.maxMemberNum = entity.maxMemberNum
        self.groupStatus = BuzChatGroupStatus.init(rawValue: entity.groupStatus) ?? .normal
        self.serverPortraitUrl = entity.serverPortraitUrl
        self.groupType = BuzChatGroupType.init(rawValue: entity.groupType) ?? .normal
    }
}

extension BuzIMGroupData.ExtraInfo {
    /// 数据库对象初始化
    /// - Parameter entity: dao实体对象
    convenience init(entity: GroupInfoModel) {
        self.init(groupId: entity.groupId)
        self.canInvite = entity.canInvite
        self.canEdit = entity.canEdit
        self.userRole = .init(rawValue: Int32(entity.userRole)) ?? .member
        self.userStatus = .init(rawValue: Int32(entity.userStatus)) ?? .notJoined
        self.serviceMuteMessages = .init(rawValue: Int32(entity.serviceMuteMessages)) ?? .unknow
        self.muteNotification = .init(rawValue: Int32(entity.muteNotification)) ?? .unknow
        self.createTime = entity.createTime
    }
}
