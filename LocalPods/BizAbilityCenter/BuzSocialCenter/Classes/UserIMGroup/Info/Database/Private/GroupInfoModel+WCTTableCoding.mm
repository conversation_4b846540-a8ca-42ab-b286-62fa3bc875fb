//
//  GroupInfoModel+WCTTableCoding.m
//  buz
//
//  Created by l<PERSON>hi on 2022/8/16.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "GroupInfoModel+WCTTableCoding.h"

@implementation GroupInfoModel (WCTTableCoding)

WCDB_IMPLEMENTATION(GroupInfoModel)

WCDB_SYNTHESIZE(GroupInfoModel, groupId)
WCDB_SYNTHESIZE(GroupInfoModel, createTime)
WCDB_SYNTHESIZE(GroupInfoModel, groupName)
WCDB_SYNTHESIZE(GroupInfoModel, displayGroupName)
WCDB_SYNTHESIZE(GroupInfoModel, portraitUrl)
WCDB_SYNTHESIZE(GroupInfoModel, serverPortraitUrl)
WCDB_SYNTHESIZE(GroupInfoModel, firstFewPortraits)
WCDB_SYNTHESIZE(GroupInfoModel, memberNum)
WCDB_SYNTHESIZE(GroupInfoModel, maxMemberNum)
WCDB_SYNTHESIZE(GroupInfoModel, groupStatus)
WCDB_SYNTHESIZE(GroupInfoModel, groupType)

WCDB_SYNTHESIZE(GroupInfoModel, canInvite)
WCDB_SYNTHESIZE(GroupInfoModel, canEdit)
WCDB_SYNTHESIZE(GroupInfoModel, userRole)
WCDB_SYNTHESIZE(GroupInfoModel, userStatus)
WCDB_SYNTHESIZE(GroupInfoModel, serviceMuteMessages)
WCDB_SYNTHESIZE(GroupInfoModel, localMuteMessages)
WCDB_SYNTHESIZE(GroupInfoModel, muteNotification)

WCDB_PRIMARY(GroupInfoModel, groupId)


@end
