//
//  GroupInfoDao.m
//  buz
//
//  Created by lizhi on 2022/8/16.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "GroupInfoDao.h"
#import "GroupInfoModel+WCTTableCoding.h"
#import "BuzDatabaseManager.h"
#import "OCBuzLog.h"
#import "BuzUserInfoDao.h"
#import "BlackUserDao.h"
#import "OCBuzUserSession.h"

#define DATABASE [BuzDatabaseManager database]

//用户群状态枚举
typedef NS_ENUM(NSInteger , BuzGroupUserStatus) {
    BuzGroupUserStatusJoined = 1,   //已加群
    BuzGroupUserStatusNotJoined = 2 , //未加群
    BuzGroupUserStatusRemoved = 3 //被踢出群
};

@interface GroupInfoDao ()

@property (nonatomic, strong) NSMutableSet <NSNumber *>* createdTableSet;

@end

@implementation GroupInfoDao

+ (instancetype)sharedInstance {
    static id instance = nil;
    if (instance == nil) {
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            instance = [[self alloc] init];
        });
    }
    return instance;
}
//// 所有操作仅针对当前登录用户操作

/** 更新联系人数据**/
- (void)addOrUpdateGroupInfos:(NSArray <GroupInfoModel *>*)groupInfos complete:(nullable void (^)(BOOL))complete{
    if (groupInfos.count == 0) {
        [self executeTaskInMainQueue:^{
            complete(YES);
        }];
        return;
    }
    
    [self executeTaskInDatabaseQueue:^{
              
        NSString *tableName =  [self getTableName];
        __block BOOL isUpdateSuccess = YES;
        
        [DATABASE runTransaction:^BOOL{
            for (GroupInfoModel *model in groupInfos) {
                isUpdateSuccess = [DATABASE insertOrReplaceObject:model into:tableName];
                BuzDBLogD(@"GroupInfoDao 批量插入数据结果%d , groupId = %lld" , isUpdateSuccess, model.groupId);
            }
            return isUpdateSuccess;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"GroupInfoDao 批量插入数据处理完成 -- 回调外界结果%d" , isUpdateSuccess);
                complete(isUpdateSuccess);
            }];
        }
    }];
    
}

/** 获取所有加入的群数据**/
- (void)getAllJoinedGroupInfoComplete:(void (^)(NSArray <GroupInfoModel *>* nullable))complete{
    
    [self executeTaskInDatabaseQueue:^{
        NSString *tableName = [self getTableName];
        NSArray *result = [DATABASE getObjectsOfClass:GroupInfoModel.class fromTable:tableName where:GroupInfoModel.userStatus == BuzGroupUserStatusJoined];
        if (complete) {
            [self executeTaskInMainQueue:^{
                complete(result);
            }];
        }
    }];
}

/** 通过群ID删除群数据**/
- (void)deleteGroupInfosWithgroupIdList:(NSArray <NSNumber *>*)groupIdList complete:(nullable void (^)(BOOL))complete{
    if (groupIdList.count == 0) {
        [self executeTaskInMainQueue:^{
            complete(YES);
        }];
       
        return;
    }
    
    [self executeTaskInDatabaseQueue:^{
       
        NSString *tableName = [self getTableName];
        __block BOOL isUpdateSuccess = YES;
        
        [DATABASE runTransaction:^BOOL{
            
            for (NSNumber *groupId in groupIdList) {
                isUpdateSuccess = [DATABASE deleteObjectsFromTable:tableName where:GroupInfoModel.groupId == groupId.longLongValue];
            }
            return isUpdateSuccess;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"GroupInfoDao - 通过群id号:(%@) 删除群数据处理完成 -- 回调外界结果%d" , groupIdList, isUpdateSuccess);
                complete(isUpdateSuccess);
            }];
        }
    }];
}

/** 删除所有群数据**/
- (void)deleteAllGroupComplete:(nullable void (^)(BOOL))complete{
       
    [self executeTaskInDatabaseQueue:^{
       
        NSString *tableName = [self getTableName];
         BOOL isUpdateSuccess = [DATABASE deleteAllObjectsFromTable:tableName];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"UserInfoDao - 删除所有群信息处理完成 -- 回调外界结果%d" , isUpdateSuccess);
                complete(isUpdateSuccess);
            }];
        }
    }];
}

/** 根据群ID获取群数据**/
- (void)getGroupInfoWithGroupId:(int64_t)groupId complete:(void (^)(GroupInfoModel * _Nullable))complete{
    [self executeTaskInDatabaseQueue:^{
        
        NSString *tableName = [self getTableName];
        GroupInfoModel *model = [DATABASE getOneObjectOfClass:GroupInfoModel.class fromTable:tableName where:GroupInfoModel.groupId == groupId];

        if (complete) {
            [self executeTaskInMainQueue:^{
                complete(model);
            }];
        }
    }];
}

#pragma mark - private

- (void)executeTaskInDatabaseQueue:(void(^)(void))task
{
    buz_dispatch_queue_async_safe(buz_database_queue(),task);
}

- (void)executeTaskInMainQueue:(void(^)(void))task
{
    buz_dispatch_queue_async_safe(dispatch_get_main_queue(),task);
}

- (NSString *)getTableName
{
   
    NSString *tableName = [NSString stringWithFormat:@"groupInfoTable"];
    NSNumber *uid = @(OCBuzUserSession.uid);
    
    if (![self.createdTableSet containsObject:uid]) {
        
        BOOL result = [DATABASE createTableAndIndexesOfName:tableName withClass:GroupInfoModel.class];
        if (result) {
            [self.createdTableSet addObject:uid];
        }else{
            NSAssert(NO, @"create groupInfo table failure!!!");
            return nil;
        }
    }
    return tableName;
}

- (NSMutableSet<NSNumber *> *)createdTableSet
{
    if (nil == _createdTableSet)
    {
        _createdTableSet = [[NSMutableSet alloc] init];
    }
    return _createdTableSet;
}

@end
