//
//  GroupInfoModel+WCTTableCoding.h
//  buz
//
//  Created by lizhi on 2022/8/16.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "GroupInfoModel.h"
#import "WCDB.h"

NS_ASSUME_NONNULL_BEGIN

@interface GroupInfoModel (WCTTableCoding)<WCTTableCoding>

WCDB_PROPERTY(groupId)
WCDB_PROPERTY(createTime)
WCDB_PROPERTY(groupName)
WCDB_PROPERTY(displayGroupName)
WCDB_PROPERTY(portraitUrl)
WCDB_PROPERTY(serverPortraitUrl)
WCDB_PROPERTY(firstFewPortraits)
WCDB_PROPERTY(memberNum)
WCDB_PROPERTY(maxMemberNum)
WCDB_PROPERTY(groupStatus)
WCDB_PROPERTY(groupType)

WCDB_PROPERTY(canInvite)
WCDB_PROPERTY(canEdit)
WCDB_PROPERTY(userRole)
WCDB_PROPERTY(userStatus)
WCDB_PROPERTY(serviceMuteMessages)
WCDB_PROPERTY(localMuteMessages)
WCDB_PROPERTY(muteNotification)

@end

NS_ASSUME_NONNULL_END
