//
//  GroupInfoDao.h
//  buz
//
//  Created by l<PERSON>hi on 2022/8/16.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class GroupInfoModel;

@interface GroupInfoDao : NSObject

+ (instancetype)sharedInstance;

//// 所有操作仅针对当前登录用户操作

/** 更新群数据**/
- (void)addOrUpdateGroupInfos:(NSArray <GroupInfoModel *>*)groupInfos complete:(nullable void (^)(BOOL))complete;

/** 获取所有加入的群数据**/
- (void)getAllJoinedGroupInfoComplete:(void (^)(NSArray <GroupInfoModel *>* nullable))complete;

/** 通过群ID删除群数据**/
- (void)deleteGroupInfosWithgroupIdList:(NSArray <NSNumber *>*)groupIdList complete:(nullable void (^)(BOOL))complete;

/** 删除所有群数据**/
- (void)deleteAllGroupComplete:(nullable void (^)(BOOL))complete;

/** 根据群ID获取群数据**/
- (void)getGroupInfoWithGroupId:(int64_t)groupId complete:(void (^)(GroupInfoModel * _Nullable))complete;

@end

NS_ASSUME_NONNULL_END
