//
//  GroupInfoModel.h
//  buz
//  Created by lizhi on 2022/8/16.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>
#import "YYModel.h"

NS_ASSUME_NONNULL_BEGIN

@interface GroupInfoModel : NSObject

@property (nonatomic, assign) int64_t groupId;

@property (nonatomic, assign) int64_t createTime;

//业务显示用的群名，可能是群成员拼装的
@property (nonatomic, copy , nullable) NSString *displayGroupName;
//来自服务器的群名
@property (nonatomic, copy , nullable) NSString *groupName;

@property (nonatomic, copy , nullable) NSString *portraitUrl;

//服务端生成的群头像
@property (nonatomic, copy , nullable) NSString *serverPortraitUrl;

@property (nonatomic, assign) int memberNum;

@property (nonatomic, assign) int maxMemberNum;

// 0 解散 1 正常
@property (nonatomic, assign) int groupStatus;

/// please use groupValueType
/// 群组类型 1-普通群 2-大群
@property (nonatomic, assign) int groupType;

//通过firstFewMemberInfos计算出来
@property (nonatomic, copy , nullable) NSArray <NSString *> *firstFewPortraits;

///GroupExtraInfo
//是否可邀请新成员
@property (nonatomic, assign) BOOL canInvite;
//是否可修改群资料
@property (nonatomic, assign) BOOL canEdit;
//1 群主 2 群管理员 3 普通群成员
@property (nonatomic, assign) NSInteger userRole;
//1 已加群 2 未加群 3 被踢出群
@property (nonatomic, assign) NSInteger userStatus;


// 服务端 静音好友消息,1-静音，2-不静音
@property (nonatomic, assign) NSInteger serviceMuteMessages;

// 本地记录的静音好友消息,1-静音，2-不静音
@property (nonatomic, assign) NSInteger localMuteMessages;

// 静音好友消息通知,1-静音，2-不静音
@property (nonatomic, assign) NSInteger muteNotification;

@end

NS_ASSUME_NONNULL_END
