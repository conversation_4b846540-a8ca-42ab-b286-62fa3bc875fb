//
//  GroupInfoManager.swift
//  buz
//
//  Created by lizhi on 2022/8/16.
//  Copyright © 2022 lizhi. All rights reserved.
//

import BuzIDL
import BuzStorage
import BuzLog
import BuzUIKit
import BuzDataShareKit
import BuzUserSession
import BuzCenterKit
import Localizable
import BuzNetworker

@objc
public protocol BuzIMGroupCenterInfoObserver: BuzIMGroupCenterObserver {
    // 群信息更新了
    @objc optional func groupInfoUpdate(_ mgr: GroupInfoManager, _ groupInfo: BuzIMGroupData)

    // 群成员更新了
    @objc optional func groupMemberUpdate(_ mgr: GroupInfoManager, _ groupId: Int64, userInfo : [String : AnyObject]?)
    //群成员在线变更
    @objc optional func groupOnlineMemberDidChanged(_ mgr: GroupInfoManager, onlineMemberInfo: IMGroupOnlineMemberInfo)

    // 离开群
    @objc optional func groupDidLeave(_ mgr: GroupInfoManager, _ groupId: Int64)
    
    // 群在线状态改变
    @objc optional func groupOnlineStateUpdate(_ mgr: GroupInfoManager)
    
    // 群消息静音状态改变
    @objc optional func groupMuteMessageUpdate(_ mgr: GroupInfoManager, _ groupId: Int64, type: BuzMuteMessagesType)
    
    // 群通知静音状态改变
    @objc optional func groupMuteNotificationUpdate(_ mgr: GroupInfoManager, _ groupId: Int64, type: BuzMuteNotificationType)
}

// 群信息list回调
public typealias GroupInfoListClosure = (_ status: Bool, _ groupInfoList: [BuzIMGroupData]) -> Void

// 群信息list回调
public typealias GroupInfoClosure = (_ status: Bool, _ groupInfo: BuzIMGroupData?) -> Void

let LogTag : String = "BuzLog_GroupInfo"

public enum GroupInfoOperation : String {
    case add = "add"
    
    public static func key() -> String {
        return "operation"
    }
}

public class GroupInfoCache:NSObject {
    public var hasValidCached = false
    private var groupList = [BuzIMGroupData]()
    private var groupMap = [Int64: BuzIMGroupData]()
    
    func cacheAll(list: [BuzIMGroupData]) {
        hasValidCached = true
        groupList = list
        list.forEach { item in
            self.groupMap[item.groupId] = item
        }
        BuzLog.debug(LogTag,"GroupInfoCache cacheAll:\(groupList.compactMap { $0.base.displayGroupName })")
        BuzIMGroupCenter.info.saveGroupsMapToLocalShareCache()
    }
    
    func clearAll() {
        hasValidCached = false
        groupList.removeAll()
        groupMap.removeAll()
    }
    
    @objc
    public func groupInfo(for groupId: Int64) -> BuzIMGroupData? {
        return groupMap[groupId]
    }
    
    func update(group model: BuzIMGroupData) {
        if let index = groupList.firstIndex(where: { $0.groupId == model.groupId }) {
            groupList[index] = model
            groupMap[model.groupId] = model
        } else if model.extra.userStatus == .joined {
            // 现有的缓存找不到, 当新增群, 加到列表最前面
            groupList.prepend(model)
            groupMap[model.groupId] = model
        }
        
        BuzLog.debug(LogTag,"GroupInfoCache updateGroup:\(model)")
        BuzLog.debug(LogTag,"GroupInfoCache update:\(groupList.compactMap { $0.base.displayGroupName })")
    }
    
    func remove(by groupId: Int64) {
        if let index = groupList.firstIndex(where: { $0.groupId == groupId }) {
            groupList.remove(at: index)
            groupMap.removeValue(forKey: groupId)
        }
        
        BuzLog.debug(LogTag,"GroupInfoCache remove:\(groupList.compactMap { $0.base.displayGroupName })")
    }
    
    public var groups: [BuzIMGroupData] {
        groupList
    }
}

public protocol GroupInfoManagerProviderable {
    func deleteConversation(targetId: Int64)
}

// 群信息管理
public class GroupInfoManager: NSObject, BuzSocialControllerable {
    typealias Center = BuzIMGroupCenter
    weak var center: BuzIMGroupCenter?
    
    public private(set) var publisher = Publisher.init()
    
    public var provider: GroupInfoManagerProviderable?
    
    @objc
    public private(set) var groupInfoCache = GroupInfoCache()
    
    //在线的群信息
    public private(set) var onlineGroups = [Int64: OnlineGroup]()
    
    public private(set) var groupInfoRequested: Bool = false
    
    
    required init(center: BuzIMGroupCenter?) {
        self.center = center
        super.init()
        initGroupInfodData()
    }
}

public extension GroupInfoManager {

    // 清除缓存
    func cleanDataWhenLogout() {
        groupInfoRequested = false
        groupInfoCache.clearAll()
        onlineGroups.removeAll()
    }
    
    //初始化数据，查询本地数据库
    private func initGroupInfodData(){
        GroupInfoDao.sharedInstance().getAllJoinedGroupInfoComplete { list in
            self.groupInfoCache.cacheAll(list: list.map({BuzIMGroupData.init(entity: $0)}))
        }
    }
}

public extension GroupInfoManager {
    /// 查询群对讲机在线状态(本地数据)
    /// - Parameter groupId: 群ID
    /// - Returns: 是否在线
    func getGroupWalkieTalkieOnlineState(groupId: Int64)-> Bool{
        return onlineGroups[groupId] != nil
    }
    
    // 查询并缓存我的所有群
    func queryAndCacheJoinedAllGroupInfo(complete: GroupInfoListClosure?) {
        BuzIMGroupNetworker.queryAllJoinedGroups { success, _, groups in
            if false == success {
                complete?(false, [])
                return
            }
            
            var result = [BuzIMGroupData]()
            let group = DispatchGroup()
            
            for item in groups ?? [] {
                group.enter()
                self.transformBpGroupInfo(bpInfo: item) { model in
                    result.append(model)
                    
                    if false == self.groupInfoRequested { //首次获取群列表，同步本地静音状态到服务端
                        self.syncLocalMuteMessageStatusIfNeed(groupInfo: model)
                    }
                    
                    group.leave()
                }
            }
            
            let previousJoinedGroups = self.groupInfoCache.groups.filter({ $0.extra.userStatus != .notJoined})
            BuzLog.debug("调试会话问题 当前全部群数量：\(String(describing: groups?.count)) , groups = \(String(describing: groups))")
            
            group.notify(queue: DispatchQueue.main) {
                self.groupInfoCache.cacheAll(list: result)
                self.groupInfoRequested = true
                // 缓存到数据库
                GroupInfoDao.sharedInstance().addOrUpdateGroupInfos(result.map({$0.convertDaoEntity()})) { success in
                    complete?(success, result)
                    self.checkDeletableTask(previousGroups: previousJoinedGroups, currentGroups: result)
                }
            }
        }
    }
    
    // 查询在线的群列表
    func getOnlineJoinedGroups(complete:((_ success: Bool)-> Void)?){
        BuzIMGroupNetworker.getOnlineJoinedGroups { success, rcode, groups in
            if false == success {
                complete?(false)
                return
            }
            
            var map = [Int64: OnlineGroup]()
            groups?.forEach({ group in
                map[group.groupId] = group
            })
            
            self.onlineGroups = map
            complete?(true)
        }
    }
    
    func updateOnlineJoinedGroup(group: GroupOnlineInfo) {
        self.onlineGroups[group.groupId] = OnlineGroup(groupId: group.groupId, onlineCount: Int32(group.onlineMemberList.count))
    }
    
    func queryGroupInfoFromCache(groupId: Int64, cacheCallback: ((_ groupInfo: BuzIMGroupData?) -> Void)?) {
        if groupId <= 0 {
            cacheCallback?(nil)
        } else if let model = groupInfoCache.groupInfo(for: groupId) {
            cacheCallback?(model)
        } else {
            GroupInfoDao.sharedInstance().getGroupInfo(withGroupId: groupId) { model in
                if let model = model {
                    cacheCallback?(.init(entity: model))
                }else{
                    cacheCallback?(nil)
                }
            }
        }
    }
    
    // 查询群信息，先回调缓存再回调网络信息
    func queryGroupInfo(groupId: Int64, cacheCallback: ((_ groupInfo: BuzIMGroupData?) -> Void)?, complete: GroupInfoClosure?) {
        if groupId <= 0 {
            cacheCallback?(nil)
            complete?(true, nil)
            return
        }
        
        if let model = groupInfoCache.groupInfo(for: groupId) {
            cacheCallback?(model)
        } else {
            GroupInfoDao.sharedInstance().getGroupInfo(withGroupId: groupId) { model in
                if let model = model {
                    cacheCallback?(.init(entity: model))
                }else{
                    cacheCallback?(nil)
                }
            }
        }
        
        queryGroupInfo(groupIds: [groupId], forceRefresh: true) { status, groupInfoList in
            
            if status, let model = groupInfoList.first {
                self.groupInfoCache.update(group: model)
                GroupInfoDao.sharedInstance().addOrUpdateGroupInfos([model.convertDaoEntity()]) { success in
                    complete?(success, model)
                }
            } else {
                BuzContactsLog.error(LogTag,"群信息查询失败: \(groupId)")
                complete?(false, nil)
            }
        }
    }
    
    
    ///  查询缓存群信息，没有的时候会去网络查
    /// - Parameters:
    ///   - groupId: q\群id
    ///   - complete: 群信息
    @objc func queryCacheGroupInfo(groupId: Int64, complete: ((_ groupInfo: BuzIMGroupData?) -> Void)?){
        queryGroupInfo(groupIds: [groupId], forceRefresh: false) { status, groupInfoList in
            guard status == true ,let info = groupInfoList.first else {
                BuzLog.debug("query group info failure")
                complete?(nil)
                return
            }
            
            complete?(info)
        }
    }
    
    // 批量查询群信息
    func queryGroupInfo(groupIds: [Int64], forceRefresh: Bool = false, complete: GroupInfoListClosure?) {
        BuzLog.debug(LogTag, "批量查询群信息 groupIds:\(groupIds)")
        BuzQueryInfoLog.info("begin query groupsInfo count = \(groupIds.count) :  groupIds = \(groupIds)")
        if groupIds.count == 0 {
            DispatchQueue.main.async {
                BuzQueryInfoLog.info("callback complete groupIds.count == 0")
                complete?(true, [])
            }
            return
        }
        
        var result = [BuzIMGroupData]()
        let group = DispatchGroup()
        var waitQueryGroupIds = [Int64]()
        for groupId in groupIds {
            group.enter()
            
            if forceRefresh {
                waitQueryGroupIds.append(groupId)
                group.leave()
                
            } else {
                if let model = groupInfoCache.groupInfo(for: groupId) { // 读取内存缓存
                    result.append(model)
                    group.leave()
                    
                } else {
                    GroupInfoDao.sharedInstance().getGroupInfo(withGroupId: groupId) { model in
                        if let model = model {
                            result.append(.init(entity: model))
                            group.leave()
                        } else {
                            waitQueryGroupIds.append(groupId)
                            group.leave()
                        }
                    }
                }
            }
        }
        
        group.notify(queue: DispatchQueue.main) {
            if waitQueryGroupIds.count == 0 {
                complete?(true, result)
                BuzQueryInfoLog.info("callback complete result.count = \(result.count) ; groupIds = \(groupIds)")
                return
            }
            
            self.queryAndCacheGroupInfoFromNetworking(groupIds: waitQueryGroupIds) { success, groupinfoList in
                result.append(contentsOf: groupinfoList)
                complete?(success, result)
            }
        }
    }
}

extension GroupInfoManager {
    // 查询并缓存群信息
    private func queryAndCacheGroupInfoFromNetworking(groupIds: [Int64], complete: GroupInfoListClosure?) {
        queryGroupInfoFromNetworking(groupIds: groupIds) { success, groupinfoList in
            if false == success {
                complete?(false, groupinfoList)
                return
            }
            
            let ids = NSMutableSet.init()
            groupinfoList.forEach { item in
                ids.add(item.groupId)
            }
            
            let previousJoinedGroups = self.groupInfoCache.groups.filter({ $0.extra.userStatus != .notJoined && ids.contains($0.groupId)})          
            groupinfoList.forEach { item in
                let cacheInfo = self.groupInfoCache.groupInfo(for: item.groupId)
                self.groupInfoCache.update(group: item)
                
                if cacheInfo != nil{
                    if cacheInfo!.isEqual(item) == false {
                        self.notifyGroupInfoUpdate(groupInfo: item)
                    }
                }else{
                    self.notifyGroupInfoUpdate(groupInfo: item)
                }
            }
            
            let cacheDbData = groupinfoList.filter({ $0.extra.userStatus != .notJoined && ids.contains($0.groupId)})
            GroupInfoDao.sharedInstance().addOrUpdateGroupInfos(groupinfoList.map({$0.convertDaoEntity()})) { success in
                complete?(success, groupinfoList)
                self.checkDeletableTask(previousGroups: previousJoinedGroups, currentGroups: cacheDbData)
            }
        }
    }
    
    private func checkDeletableTask(previousGroups : [BuzIMGroupData], currentGroups : [BuzIMGroupData]) {
        let deletableModels : NSMutableDictionary = NSMutableDictionary.init()
        
        previousGroups.forEach { model in
            deletableModels[model.groupId] = model
        }
        
        currentGroups.forEach { model in
            deletableModels.removeObject(forKey: model.groupId)
        }
        
        if let deletableGroups = deletableModels.allValues as? [BuzIMGroupData] {
            deletableGroups.forEach { model in
                StorageManageRegiter.service().delete(session: BuzStorageDeletable.SessionEntity.init(userId: BuzUserSession.shared.uid,
                                                                                                targetId: model.groupId,
                                                                                                conversationType: IM5ConversationType.group.rawValue))
            }
        }
    }

    private func queryGroupInfoFromNetworking(groupIds: [Int64], complete: GroupInfoListClosure?) {
        BuzContactsLog.debug(LogTag,"queryGroupInfoFromNetworking groupIds:\(groupIds)")
        
        BuzQueryInfoLog.info("begin query groupinfos from network ; groupIds = \(groupIds)")
        
        BuzIMGroupNetworker.queryGroupInfoBatch(groupIds: groupIds) { success, rcode, list in
            BuzLog.debug(LogTag,"queryGroupInfoFromNetworking roce:\(rcode) groupIds:\(groupIds)")
            
            if false == success {
                BuzLog.debug(LogTag, "queryGroupInfoFromNetworking error roce:\(rcode) groupIds:\(groupIds)")
                complete?(false, [])
                BuzQueryInfoLog.info("finish query groupinfos from network failure ; groupIds = \(groupIds)")
                return
            }

            var result = [BuzIMGroupData]()
            let group = DispatchGroup()

            for item in list ?? [] {
                group.enter()
                self.transformBpGroupInfo(bpInfo: item) { model in
                    result.append(model)
                    group.leave()
                }
            }

            BuzQueryInfoLog.info("finish query groupinfos from network success ; groupIds = \(groupIds)")
            group.notify(queue: DispatchQueue.main) {
                BuzQueryInfoLog.info("finish query groupinfos from network success and exec complete callback; groupIds = \(groupIds)")
                complete?(true, result)
            }
        }
    }
}

//MARK: --群信息操作
public extension GroupInfoManager {
    // 保存新群信息
    func saveNewGroup(bpInfo: GroupInfo, complete: GroupInfoClosure?) {
        transformBpGroupInfo(bpInfo: bpInfo) { groupInfo in
            self.saveNewGroup(groupInfo: groupInfo, complete: complete)
        }
    }

    // 保存新群信息
    func saveNewGroup(groupInfo: BuzIMGroupData, complete: GroupInfoClosure?) {
        BuzLog.debug(LogTag,"saveNewGroup: \(groupInfo)")
        GroupInfoDao.sharedInstance().addOrUpdateGroupInfos([groupInfo.convertDaoEntity()]) { success in
            complete?(success, groupInfo)
            if success {
                self.groupInfoCache.update(group: groupInfo)
                self.notifyGroupInfoUpdate(groupInfo: groupInfo)
            }
        }
    }

    // 更新群信息
    func updateGroupInfo(bpInfo: GroupInfo, complete: GroupInfoClosure?) {
        transformBpGroupInfo(bpInfo: bpInfo) { groupInfo in
            self.updateGroupInfo(groupInfo: groupInfo, complete: complete)
        }
    }

    // 更新群信息
    func updateGroupInfo(groupInfo: BuzIMGroupData, complete: GroupInfoClosure?) {
        BuzLog.debug(LogTag,"updateGroupInfo: \(groupInfo)")
        
        groupInfoCache.update(group: groupInfo)
        GroupInfoDao.sharedInstance().addOrUpdateGroupInfos([groupInfo.convertDaoEntity()]) { success in
            complete?(success, groupInfo)

            if success {
                self.notifyGroupInfoUpdate(groupInfo: groupInfo)
            }
        }
    }

    // 删除群信息
    func deleteGroupInfo(groupId: Int64, complete: ((_ success: Bool) -> Void)?) {
        if groupId <= 0 {
            BuzLog.error(LogTag,"deleteGroupInfo groupId = 0")
            complete?(false)
            return
        }

        let model = groupInfoCache.groupInfo(for: groupId)
        groupInfoCache.remove(by: groupId)
        
        BuzLog.debug(LogTag,"deleteGroupInfo groupid:\(groupId)")
        
        self.provider?.deleteConversation(targetId: groupId)
        
        GroupInfoDao.sharedInstance().deleteGroupInfosWithgroupIdList([NSNumber(value: groupId)]) { success in
            complete?(success)
            if success {
                self.center?.notifyObservers(of: BuzIMGroupCenterInfoObserver.self) { observer in
                    observer.groupDidLeave?(self, groupId)
                }
                self.publisher.groupDidLeave.send(groupId)
                
                if let model = model {
                    self.checkDeletableTask(previousGroups: [model], currentGroups: [])
                }
            }
        }
    }
    
    // 设置群消息静音状态
    func updateMuteMessages(with groupId: Int64, mute: Bool, complete: ((_ rcode: Int) -> Void)?)  {
                
        BuzIMGroupNetworker.updateGroupMuteState(with: groupId, muteMessages: mute, muteNotification: nil) { rcode in
            if rcode == 0 {
                let muteMessages: BuzMuteMessagesType = mute ? .on : .off
                if let model = self.groupInfoCache.groupInfo(for: groupId) {
                    model.extra.serviceMuteMessages = muteMessages
                    self.updateGroupInfo(groupInfo: model, complete: nil)
                }
                
                self.center?.notifyObservers(of: BuzIMGroupCenterInfoObserver.self) { observer in
                    observer.groupMuteMessageUpdate?(self, groupId, type:  muteMessages)
                }
                self.publisher.groupMuteMessageUpdate.send((groupId, muteMessages))
            }
            
            complete?(rcode)
        }
    }
    
    // 设置群通知静音状态
    func updateMuteNotification(with groupId: Int64, mute: Bool, complete: ((_ rcode: Int) -> Void)?) {
                
        BuzIMGroupNetworker.updateGroupMuteState(with: groupId, muteMessages: nil, muteNotification: mute) { rcode in
            if rcode == 0 {
                let muteNotification:BuzMuteNotificationType = mute ? .on : .off
                if let model = self.groupInfoCache.groupInfo(for: groupId) {
                    model.extra.muteNotification = muteNotification
                    self.updateGroupInfo(groupInfo: model, complete: nil)
                }
                
                self.center?.notifyObservers(of: BuzIMGroupCenterInfoObserver.self) { observer in
                    observer.groupMuteNotificationUpdate?(self, groupId, type: muteNotification)
                }
                self.publisher.groupMuteNotificationUpdate.send((groupId, muteNotification))
            } else {
                BuzProgressHUD.showError(withText: Localizable.network_error_try_again, on: nil)
            }
            complete?(rcode)
        }
        
    }
    
    // 同步本地静音状态到服务端，如果本地设置和服务端不一致的时候
    private func syncLocalMuteMessageStatusIfNeed(groupInfo: BuzIMGroupData){
        if (groupInfo.extra.localMuteMessages == .unknow || groupInfo.extra.localMuteMessages == groupInfo.extra.serviceMuteMessages) {
            return
        }
        
        let mute = groupInfo.extra.localMuteMessages == .on
                        
        self.updateMuteMessages(with: groupInfo.groupId, mute: mute) { rcode in
            BuzLog.info("syncLocalMuteMessageStatusIfNeed groupId:\(groupInfo.groupId) localMuteMessages:\(groupInfo.extra.localMuteMessages.rawValue) serviceMuteMessages:\(groupInfo.extra.serviceMuteMessages.rawValue) muteNotification:\(groupInfo.extra.muteNotification.rawValue) rcode:\(rcode)")
        }
    }
}

// MARK: 工具方法
public extension GroupInfoManager {
    // 把pb群信息转换为BuzIMGroupData (用户信息会优先匹配本地通讯录备注)
    func transformBpGroupInfo(bpInfo: GroupInfo, complete: ((_ groupInfo: BuzIMGroupData) -> Void)?) {
        let model = BuzIMGroupData.init(idl: bpInfo)
        model.extra.localMuteMessages = groupInfoCache.groupInfo(for: model.groupId)?.extra.localMuteMessages ?? .unknow
        // 把用户信息转成本地联系人信息
        BuzFriendCenter.info.queryUserInfo(userInfos: bpInfo.groupBaseInfo.firstFewMemberInfos) { users in
            model.base.setFirstFewMemberInfos(members: users)
            complete?(model)
        }
    }

    private func notifyGroupInfoUpdate(groupInfo: BuzIMGroupData) {
        self.center?.notifyObservers(of: BuzIMGroupCenterInfoObserver.self) { observer in
            observer.groupInfoUpdate?(self, groupInfo)
        }
        self.publisher.groupInfoUpdate.send(groupInfo)
        self.saveGroupsMapToLocalShareCache()
    }
    
    func saveGroupsMapToLocalShareCache(){
        ///缓存到Group本地
        var pushGroupsDict : [Int64 : [String : String]] = [:]
        for groupInfoModel in self.groupInfoCache.groups{
            if groupInfoModel.groupId <= 0 {
                continue
            }
            
            var dict : [String : String] = [:]
            if let portrait = groupInfoModel.base.serverPortraitUrl {
                dict[PushShareDataKey.groupInfoPortraitKey.rawValue] = portrait
            }
            if let displayGroupName = groupInfoModel.base.displayGroupName {
                dict[PushShareDataKey.groupInfoDisplayNameKey.rawValue] = displayGroupName
            }
            
            pushGroupsDict[groupInfoModel.groupId] = dict
        }
        PushShareDataTool.saveGroupsMap(pushGroupsDict)
        #if DEBUG
        let pushGroupShareData = PushShareDataTool.loadGroupsMap()
        BuzLog.debug("save Groups to notificationService:\(pushGroupShareData)")
        #endif
    }
}


//MARK: --BuzSignalPushEventObserver
extension GroupInfoManager: BuzSignalPushEventObserver {
    
    public func signalPushDidReceivedMessage(_ message: BuzSignalPush.Message) {
        guard let payload = message.payload else {
            return
        }
        switch payload.op {
        case .pushDataChange:
            let type = BuzSignalPush.PushDataChange.init(rawValue: payload.businessType ?? 0)
            let businessId = payload.data?["businessId"] as? String ?? ""
            let businessIdValue = Int64(businessId)
            switch type {
            case .GroupInfo: //群组信息变更(getGroupInfo)
                guard let businessIdValue = businessIdValue else { return }
                BuzSignalPushLog.info("群信息变化push: \(businessIdValue)")
                BuzProgressHUD.showDebugToast(withText: "群信息变换推送:\(businessIdValue)")
                self.didReceivedGroupInfoChangePush(groupId: businessIdValue)
            case .GroupOnlineState: //15 - 在线群列表变更
                guard let businessIdValue = businessIdValue else { return }
                BuzSignalPushLog.info("在线群列表变更push")
//                self.didReceiveOnlineGroupUpdatePush()
            default:
                break
            }
        default:
            break
        }
    }
    
    // 群信息变更推送
    func didReceivedGroupInfoChangePush(groupId: Int64) {
        if groupId <= 0 {
            return
        }
        BuzLog.debug(LogTag,"didReceivedGroupInfoChangePush, groupId:\(groupId)")
        queryAndCacheGroupInfoFromNetworking(groupIds: [groupId], complete: nil)
    }
    
    //在线群变更推送
    func didReceiveOnlineGroupUpdatePush() {
        BuzLog.debug(LogTag,"didReceiveOnlineGroupUpdatePush")
        getOnlineJoinedGroups { success in
            if false == success{
                BuzLog.error(LogTag,"didReceiveOnlineGroupUpdatePush getOnlineJoinedGroups failed")
                return
            }
            self.center?.notifyObservers(of: BuzIMGroupCenterInfoObserver.self) { observer in
                observer.groupOnlineStateUpdate?(self)
            }
            self.publisher.groupOnlineStateUpdate.send()
        }
    }
}
