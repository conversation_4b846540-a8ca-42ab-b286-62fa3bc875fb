//
//  GroupOnlineInfo.swift
//  buz
//
//  Created by li666 on 2023/4/7.
//  Copyright © 2023 lizhi. All rights reserved.
//

import BuzIDL

public struct GroupOnlineInfo {
    public let groupId: Int64
    public let onlineMemberList: [GroupMember]
    public var memberCount: Int
    
    public init(groupId: Int64, onlineMemberList: [GroupMember], memberCount: Int) {
        self.groupId = groupId
        self.onlineMemberList = onlineMemberList
        self.memberCount = memberCount
    }
}

@objc
public class IMGroupOnlineMemberInfo: NSObject {
    public let groupId: Int64
    public let onlineMembers: [[String:AnyObject]]
    public let onlineMemberCount: Int64
    public let sendTimestamp: Int64
    
    public init(groupId: Int64,members: [[String:AnyObject]], count: Int64, timestamp: Int64 = 0) {
        self.groupId = groupId
        self.onlineMembers = members
        self.onlineMemberCount = count
        self.sendTimestamp = timestamp
    }
}
