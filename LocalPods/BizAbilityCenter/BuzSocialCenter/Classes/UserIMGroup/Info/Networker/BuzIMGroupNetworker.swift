//
//  BuzFriendNetworker.swift
//  buz
//
//  Created by st.chio on 2025/2/14.
//  Copyright © 2025 lizhi. All rights reserved.
//

import BuzLog
import BuzIDL
import ITNetLibrary
import BuzNetworker

class BuzIMGroupNetworker {
    
    // 查询我加入的所有群
    static func queryAllJoinedGroups(_ handler: ((_ success: Bool, _ rcode: Int, _ groups: [GroupInfo]?) -> Void)?) {
        let client = BuzNetGroupServiceClient()
        let req = RequestGetJoinedGroups()
        BuzLog.info("queryAllJoinedGroups")

        _ = client.getJoinedGroups(request: req) { result in
            switch result {
            case let .success(responseInfo):
                BuzLog.info("queryAllJoinedGroups rcode:\(responseInfo.code) count:\(String(describing: responseInfo.data?.groupInfoList?.count))")
                handler?(responseInfo.code == 0, responseInfo.code, responseInfo.data?.groupInfoList)
            case .failure:
                BuzLog.error("queryAllJoinedGroups failure")
                handler?(false, NetworkErrorCode, nil)
            }
        }
    }
    
    // 查询我加入的在线群群
    static func getOnlineJoinedGroups(_ handler: ((_ success: Bool, _ rcode: Int, _ groups: [OnlineGroup]?) -> Void)?) {
        let client = BuzNetGroupServiceClient()
        let req = RequestGetOnlineJoinedGroups()
        BuzLog.info("getOnlineJoinedGroups")

        _ = client.getOnlineJoinedGroups(request: req) { result in
            switch result {
            case let .success(responseInfo):
                BuzLog.info("getOnlineJoinedGroups rcode:\(responseInfo.code) count:\(String(describing: responseInfo.data?.onlineGroupList?.count))")
                handler?(responseInfo.code == 0, responseInfo.code, responseInfo.data?.onlineGroupList)
            case .failure:
                BuzLog.error("getOnlineJoinedGroups failure")
                handler?(false, NetworkErrorCode, nil)
            }
        }
    }
    
    // 批量查询群信息
    static func queryGroupInfoBatch(groupIds: [Int64], _ handler: ((_ success: Bool, _ rcode: Int, _ groupInfoList: [GroupInfo]?) -> Void)?) {
        let client = BuzNetGroupServiceClient()
        _ = client.getGroupInfoBatch(request: RequestGetGroupInfoBatch(groupIds: groupIds)) { result in
            switch result {
            case let .success(responseInfo):
                handler?(responseInfo.code == 0, responseInfo.code, responseInfo.data?.groupInfoList)
            case .failure:
                handler?(false, NetworkErrorCode, nil)
            }
        }
    }
    
    /// 设置静音状态
    /// - Parameters:
    ///   - groupId: 群id
    ///   - muteMessages: 是否静音群组消息
    ///   - muteNotification: 是否静音群组通知
    ///   - completion: 结果
    static func updateGroupMuteState(with groupId: Int64, muteMessages: Bool?, muteNotification: Bool?, completion:((Int) -> Void)?){
        
        var messagesValue: Int32?
        if let muteMessages = muteMessages {
            messagesValue = muteMessages ? 1 : 2
        }
        
        var notificationValue: Int32?
        if let muteNotification = muteNotification {
            notificationValue = muteNotification ? 1 : 2
        }
        
        if messagesValue == nil, notificationValue == nil {
            completion?(0)
            return
        }
        
        let client = BuzNetGroupServiceClient()
        let req = RequestUpdateGroupMemberConfig(groupId: groupId, muteMessages: messagesValue, muteNotification: notificationValue)
        let _ = client.updateGroupMemberConfig(request: req, completion: {
            result in
            
            switch result {
            case .success(let response):
                completion?(response.code)
                break
            case .failure(let error):
                completion?(error.toITNetError().code)
                break
            }
        })
    }
    
}
