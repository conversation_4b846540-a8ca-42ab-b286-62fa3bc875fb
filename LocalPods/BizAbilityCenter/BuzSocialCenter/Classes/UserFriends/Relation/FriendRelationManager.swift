//
//  FriendRelationManager.swift
//  buz
//
//  Created by yutao on 2022/12/17.
//  Copyright © 2022 lizhi. All rights reserved.
//

import Foundation
import BuzIDL
import BuzLog
import BuzUIKit
import BuzCenterKit
import Localizable
import BuzNetworker
import BuzAppConfiger

@objc public enum FriendApplyActionType: Int32 {
    
    case accept = 1
    case delete
}

@objc public protocol BuzFriendCenterRelationObserver: BuzFriendCenterObserver {
    
    // 添加好友
    @objc optional func friendRelationAddedFriend(_ userId: Int64)
    
    // 好友备注更新
    @objc optional func friendRelationUpdateFriendInfo(_ userId: Int64, remark: String)
    
    // 解除好友关系
    @objc optional func friendRelationDeleteFriend(_ userId: Int64)

    // 好友申请处理
    @objc optional func friendRelationProcessApply(_ userId: Int64, type: FriendApplyActionType)
    
    // 服务端好友消息静音状态改变
    @objc optional func friendRelationUpdateServiceMuteMessage(_ userId: Int64, serviceMuteMessages: BuzMuteMessagesType)
    
    // 好友通知静音状态改变
    @objc optional func friendRelationUpdateMuteNotification(_ userId: Int64, type: BuzMuteNotificationType)
    // 收到好友请求
    @objc optional func didReceiveFriendRequest(data: [String : Any])
    // 好友请求数变更
    @objc optional func didReceiveFriendRequestCountUpdated()
}

public class FriendRelationManager: BuzSocialControllerable {
    typealias Center = BuzFriendCenter
    weak var center: BuzFriendCenter?
    public private(set) var publisher = Publisher.init()
    
    required init(center: BuzFriendCenter?) {
        self.center = center
    }
    
    private lazy var client: BuzNetUserServiceClient = {
        BuzNetUserServiceClient(encrypt: true)
    }()
    

    // 添加好友
    public func addFriend(with userId: Int64, source: Int32, businessId: Int64? = nil, completion: ((_ code: Int, _ userRelation: UserRelationInfo?) -> Void)?) {
        var bizId: String? = nil
        if let businessId = businessId {
            bizId = String(businessId)
        }
        let request = RequestAddFriend(userId: userId,
                                       source: source,
                                       businessId: bizId)
        let _ = client.addFriend(request: request) { result in
            switch result {
            case .success(let response):
                BuzFriendsLog.info("add friend, uid = \(userId), response: \(response)")
                if response.code == 0 {
                    BuzFriendCenter.official.friendRelationAddedFriend(userId)
                    
                    self.center?.notifyObservers(of: BuzFriendCenterRelationObserver.self) { observer in
                        observer.friendRelationAddedFriend?(userId)
                    }
                    self.publisher.friendAdded.send(userId)
                }
                completion?(response.code, response.data?.userRelationInfo)
            case .failure(let err):
                BuzFriendsLog.info("add friend failure, uid = \(userId)")
                completion?(err.toITNetError().code, nil)
            }
        }
    }
    
    // 批量添加好友
    public func addFriendBatch(with userIds: [Int64], source: Int32, businessId: Int64? = nil, completion: ((_ code: Int) -> Void)?) {
        var bizId: String? = nil
        if let businessId = businessId {
            bizId = String(businessId)
        }
        let request = RequestAddFriendBatch.init(userIdList: userIds, source: source, businessId: bizId)
        let _ = client.addFriendBatch(request: request) {  result in
            switch result {
            case .success(let response):
                if response.code == 0 {
                    userIds.forEach { userId in
                        BuzFriendCenter.official.friendRelationAddedFriend(userId)
                        
                        self.center?.notifyObservers(of: BuzFriendCenterRelationObserver.self) { observer in
                            observer.friendRelationAddedFriend?(userId)
                        }
                        self.publisher.friendAdded.send(userId)
                    }
                }
                completion?(response.code)
            case .failure(let err):
                completion?(err.toITNetError().code )
            }
        }
    }

    // 更新好友备注
    public func updateFriendInfo(with userId: Int64,
                          remark: String,
                          completion: @escaping (Int, Bool) -> ()) {
        let req = RequestUpdateFriendInfo.init(userId: userId, remark: remark)
        let _ = client.updateFriendInfo(request: req, completion: {
            result in
            
            switch result {
            case .success(let response):
                if response.code == 0 {
                    self.center?.notifyObservers(of: BuzFriendCenterRelationObserver.self) { observer in
                        observer.friendRelationUpdateFriendInfo?(userId, remark: remark)
                    }
                    self.publisher.friendInfoUpdated.send((userId, remark))
                }
                completion(response.code, response.code == 0)
                break
            case .failure(let error):
                completion(error.toITNetError().code, false)
                break
            }
        })
    }

    // 解除好友关系
    public func deleteFriend(with userId: Int64, completion: @escaping (Int) -> ()) {
        
        let req = RequestDeleteFriend(userId: userId)
        _ = client.deleteFriend(request: req, completion: {
            result in
            
            switch result {
            case .success(let response):
                if response.code == 0 {
                    BuzModuleLogType.BuzConversationList.log("deleteConversation targetId = \(userId), friend")
                    BuzFriendCenter.info.friendRelationDeleteFriend(userId)
                    BuzFriendCenter.official.friendRelationDeleteFriend(userId)
                    
                    self.center?.notifyObservers(of: BuzFriendCenterRelationObserver.self) { observer in
                        observer.friendRelationDeleteFriend?(userId)
                    }
                    self.publisher.friendDeleted.send(userId)
                }
                completion(response.code)
                break
            case .failure(let error):
                
                completion(error.toITNetError().code)
                break
            }
        })
    }

    // 接受/删除好友申请
    public func friendApply(with userId: Int64, type: FriendApplyActionType, completion: @escaping (Int) -> ()) {
        
        let req = RequestProcessFriendApply(userId: userId, type: type.rawValue)
        _ = client.processFriendApply(request: req, completion: {
            result in
            
            switch result {
            case .success(let response):
                if response.code == 0 {
                    self.center?.notifyObservers(of: BuzFriendCenterRelationObserver.self) { observer in
                        observer.friendRelationProcessApply?(userId, type: type)
                    }
                    self.publisher.friendApplyProcessed.send((userId, type))
                }
                completion(response.code)
                break
            case .failure(let error):
                
                completion(error.toITNetError().code)
                break
            }
        })
    }
    // 添加AI好友
    public func addAIFriend(completion: ((_ code: Int ,_ msg : String? , _ user : BuzUserData? ,_ walkieTalkieOnlineTime: Int64?) -> Void)?) {
        let request = RequestAddAIFriend()
        let _ = client.addAIFriend(request: request) {result in
            switch result {
            case .success(let response):
                if response.code == 0 {
                    let userId = response.data?.userRelationInfo?.userInfo.userId ?? 0
                    BuzFriendCenter.official.friendRelationAddedFriend(userId)
                    
                    self.center?.notifyObservers(of: BuzFriendCenterRelationObserver.self) { observer in
                        observer.friendRelationAddedFriend?(userId)
                    }
                    self.publisher.friendAdded.send(userId)
                    if let userRelationInfo = response.data?.userRelationInfo{
                        let user = BuzUserData.init(idl: userRelationInfo)
                        completion?(response.code , nil, user , response.data?.userRelationInfo?.userInfo.walkieTalkieOnlineTime )
                        return
                    }
                }
                completion?(response.code , response.msg , nil , 0)
            case .failure(let err):
                completion?(9999 , err.toITNetError().message ,nil , 0)
            }
        }
    }
}


public extension FriendRelationManager {
    
    // 设置好友消息静音状态
    func updateMuteMessages(with userId: Int64, mute: Bool, complete: ((_ rcode: Int) -> Void)?) {

        BuzFriendNetworker.updateUserMuteState(with: userId, muteMessages: mute, muteNotification: nil) { rcode in
            
            if rcode == 0 {
                let serviceMuteMessages: BuzMuteMessagesType = mute ? .on : .off
                BuzFriendCenter.info.friendRelationUpdateServiceMuteMessage(userId, serviceMuteMessages: serviceMuteMessages)
                
                self.center?.notifyObservers(of: BuzFriendCenterRelationObserver.self) { observer in
                    observer.friendRelationUpdateServiceMuteMessage?(userId, serviceMuteMessages: serviceMuteMessages)
                }
                self.publisher.serviceMuteMessageUpdated.send((userId, serviceMuteMessages))
            }
            if rcode != 0{
                BuzLog.debug("设置好友静音失败---0")
            }
            complete?(rcode)
        }
    }
    
    // 设置好友通知静音状态
    func updateMuteNotification(with userId: Int64, mute: Bool, complete: ((_ rcode: Int) -> Void)?) {
        
        BuzFriendNetworker.updateUserMuteState(with: userId, muteMessages: nil, muteNotification: mute) { rcode in
            if rcode == 0 {
                let type: BuzMuteNotificationType = mute ? .on : .off
                BuzFriendCenter.info.friendRelationUpdateMuteNotification(userId, type: type)
                
                self.center?.notifyObservers(of: BuzFriendCenterRelationObserver.self) { observer in
                    observer.friendRelationUpdateMuteNotification?(userId, type: type)
                }
                self.publisher.muteNotificationUpdated.send((userId, type))
            } else {
                BuzProgressHUD.showError(withText: Localizable.network_error_try_again, on: nil)
            }
            
            complete?(rcode)
        }
    }
}

extension FriendRelationManager: BuzSignalPushEventObserver {
    
    public func signalPushDidReceivedMessage(_ message: BuzSignalPush.Message) {
        guard let payload = message.payload else {
            return
        }
        switch payload.op {
        case .receiveFriendRequest:
            BuzSignalPushLog.info("接收到好友申请")
            guard let data = payload.data else { return }
            self.center?.notifyObservers(of: BuzFriendCenterRelationObserver.self) { observer in
                observer.didReceiveFriendRequest?(data: data)
            }
            self.publisher.friendRequestReceived.send(data)
        case .agreeFriendRequest:
            break
        case .friendRequestCountUpdate:
            BuzSignalPushLog.info("接收到好友申请数有更新")
            self.center?.notifyObservers(of: BuzFriendCenterRelationObserver.self) { observer in
                observer.didReceiveFriendRequestCountUpdated?()
            }
            self.publisher.friendRequestCountUpdated.send()
        default:
            break
        }
    }
}
