//
//  FriendRelationManager+Combine.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/24.
//

import Combine
import BuzIDL
import BuzCenterKit
import BuzFoundation

public extension FriendRelationManager {
    class Publisher {
        // 添加好友
        public let friendAdded = PassthroughSubject<Int64, Never>()
        // 好友备注更新
        public let friendInfoUpdated = PassthroughSubject<(userId: Int64, remark: String), Never>()
        // 解除好友关系
        public let friendDeleted = PassthroughSubject<Int64, Never>()
        // 好友申请处理
        public let friendApplyProcessed = PassthroughSubject<(userId: Int64, type: FriendApplyActionType), Never>()
        // 服务端好友消息静音状态改变
        public let serviceMuteMessageUpdated = PassthroughSubject<(userId: Int64, serviceMuteMessages: BuzMuteMessagesType), Never>()
        // 好友通知静音状态改变
        public let muteNotificationUpdated = PassthroughSubject<(userId: Int64, type: BuzMuteNotificationType), Never>()
        // 收到好友请求
        public let friendRequestReceived = PassthroughSubject<[String: Any], Never>()
        // 好友请求数变更
        public let friendRequestCountUpdated = PassthroughSubject<Void, Never>()
    }
}

public extension BuzCombineKit where Base: FriendRelationManager {
    
    // 添加好友
    func addFriend(with userId: Int64, source: Int32, businessId: Int64? = nil) -> AnyPublisher<(Int, UserRelationInfo?), Never> {
        return Future { promise in
            self.object.addFriend(with: userId, source: source, businessId: businessId) { code, userRelation in
                promise(.success((code, userRelation)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    // 批量添加好友
    func addFriendBatch(with userIds: [Int64], source: Int32, businessId: Int64? = nil) -> AnyPublisher<Int, Never> {
        return Future { promise in
            self.object.addFriendBatch(with: userIds, source: source, businessId: businessId) { code in
                promise(.success(code))
            }
        }
        .eraseToAnyPublisher()
    }
    
    // 更新好友备注
    func updateFriendInfo(with userId: Int64, remark: String) -> AnyPublisher<(Int, Bool), Never> {
        return Future { promise in
            self.object.updateFriendInfo(with: userId, remark: remark) { code, success in
                promise(.success((code, success)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    // 解除好友关系
    func deleteFriend(with userId: Int64) -> AnyPublisher<Int, Never> {
        return Future { promise in
            self.object.deleteFriend(with: userId) { code in
                promise(.success(code))
            }
        }
        .eraseToAnyPublisher()
    }
    
    // 接受/删除好友申请
    func friendApply(with userId: Int64, type: FriendApplyActionType) -> AnyPublisher<Int, Never> {
        return Future { promise in
            self.object.friendApply(with: userId, type: type) { code in
                promise(.success(code))
            }
        }
        .eraseToAnyPublisher()
    }
    
    // 添加 AI 好友
    func addAIFriend() -> AnyPublisher<(Int, String?, BuzUserData?, Int64?), Never> {
        return Future { promise in
            self.object.addAIFriend { code, msg, user, walkieTalkieOnlineTime in
                promise(.success((code, msg, user, walkieTalkieOnlineTime)))
            }
        }
        .eraseToAnyPublisher()
    }
}

public extension BuzCombineKit where Base: FriendRelationManager {
    
    // 设置好友消息静音状态
    func updateMuteMessages(with userId: Int64, mute: Bool) -> AnyPublisher<Int, Never> {
        return Future { promise in
            self.object.updateMuteMessages(with: userId, mute: mute) { rcode in
                promise(.success(rcode))
            }
        }
        .eraseToAnyPublisher()
    }
    
    // 设置好友通知静音状态
    func updateMuteNotification(with userId: Int64, mute: Bool) -> AnyPublisher<Int, Never> {
        return Future { promise in
            self.object.updateMuteNotification(with: userId, mute: mute) { rcode in
                promise(.success(rcode))
            }
        }
        .eraseToAnyPublisher()
    }
}
