//
//  FriendInfoManager.swift
//  buz
//
//  Created by lizhi on 2022/7/14.
//  Copyright © 2022 lizhi. All rights reserved.
//

import BuzIDL
import BuzStorage
import BuzLog
import BuzDataStore
import BuzNetworker
import BuzDataShareKit
import BuzCenterKit
import Combine
import BuzUserSession

@objc
public protocol BuzFriendCenterInfoObserver: BuzFriendCenterObserver {
    
    //用户状态变化
    @objc optional func friendInfoManagerUserStateDidChanged(_ mgr: FriendInfoManager, models: [BuzFriendStatusInfo])
    //用户信息刷新
    @objc optional func friendInfoManagerUserInfoDidUpdate(_ mgr: FriendInfoManager, info: BuzUserData)
    
    ///删除了好友(同时也会触发 friendInfoManagerFriendListDiedChanged方法)
    @objc optional func friendInfoManagerFriendsDiedDelete(_ mgr: FriendInfoManager, friends: [BuzUserData])
    
    //好友列表数据变化
    @objc optional func friendInfoManagerFriendListDiedChanged(_ mgr: FriendInfoManager, friends: [BuzUserData])
    
    //好友列表数据变化
    @objc optional func friendInfoManagerFriendsRelationInfoChange(_ mgr: FriendInfoManager)
}

fileprivate class QueryUserCallback : NSObject {
    var callback : (( _ contactsInfo: BuzUserData?, _ : Bool)-> Void)
    
    init(callback: @escaping (_: BuzUserData?, _ : Bool) -> Void) {
        self.callback = callback
    }
}

//用户信息回调
public typealias QueryUserInfoClosure = (( _ status: Bool, _ users: [BuzUserData])->(Void))

private enum BuzDeletedAccounts: String {
    case deletedAccountsKey = "BuzDeletedAccounts_deletedAccountsKey"
}

private enum BuzDeleteRequestStatus: Int {
    case initial
    case loading
    case success
    case fail
}

public enum BuzFetchUserType: Int {
    case onlyCache = 0
    case cacheOrNetwork
    case cacheAndNetwork
    case onlyNetwork
}

//用户信息查询类
public class FriendInfoManager: NSObject, BuzSocialControllerable {
    typealias Center = BuzFriendCenter
    weak var center: BuzFriendCenter?
    
    public private(set) var publisher = Publisher.init()
    //MARK: --public var-----
    public private(set) var friendInfoRequested: Bool = false
    
    //-------------------
    public private(set) var officalUser: BuzUserData? {
        didSet{
            BuzUserGuidanceModeLog.debug("获取官号数据成功 = \(String(describing: self.officalUser))")
        }
    }
    //-------------------
    //用户信息，非好友也会有
    public private(set) var allUsers = [Int64: BuzUserData]()
    //只存储了我的好友信息
    public private(set) var allFriends = [BuzUserData]() {
        didSet{
            self.handleAllFriendListChange(oldList: oldValue, newList: allFriends)
            updateWidgetContacts()
        }
    }
    //我的好友信息
    private(set) var allFriendInfo = [Int64: UserInfo]()
    
    //-------------------
    // If rcode == 0 and userInfo is nil after request, account is considered deleted and will be stored here
    public private(set) var deletedAccounts: [Int64 : Bool] = {
        if let data = MMKV.asyncBuz().data(forKey: BuzDeletedAccounts.deletedAccountsKey.rawValue) {
            do {
                let deletedAccounts = try JSONDecoder().decode([Int64 : Bool].self, from: data)
                return deletedAccounts
            } catch {
                BuzContactsLog.error("RegisteredFriendTipsLogic - Decode ResponseGetUserTips error \(error)")
            }
        }
        return [:]
    }()
    
    //MARK: --private-----
    private var cancellables = Set<AnyCancellable>()
    //-------------------
    //记录 key:某项推送类型, 建议用"类型_id"的格式， value: 服务端此条最后推送时间，
    // 如果同一个key推送时间戳小于已存在的说明是到达慢了，需要舍弃
    private var lastSendTimestampDict: [String: Int64] = [:]
    //-------------------
    private var deleteFriendInfoRequestStatus : BuzDeleteRequestStatus = .initial
    
    //-------------------
    private var queryUserCallbacks = [Int64 : [QueryUserCallback]]()
    private var queryUserFailCounts = [Int64 : Int64]()
    
    
    required init(center: BuzFriendCenter?) {
        super.init()
        self.center = center
        self.initFriendData()
        addObserver()
    }
}

//MARK: -lifecycle----
extension FriendInfoManager {
    
    //初始化数据，查询本地数据库
    private func initFriendData(){
        var userInfoList = [BuzUserData]()
        
        BuzUserInfoDao.sharedInstance().getAllFriendInfoOrder(byBecomeFriendTimeComplete: {  list in
            list.forEach { entity in
                if entity.userId > 0 {
                    let user = BuzUserData.init(entity: entity)
                    userInfoList.append(user)
                    self.allUsers[user.userId] = user
                    if user.base.userType == .officialAccount {
                        self.officalUser = user
                    }
                }
            }
            self.allFriends = userInfoList
        })
        
        ///缓存非好友
        BuzUserInfoDao.sharedInstance().getAllUnFriendInfoComplete { list in
            list.forEach { user in
                if user.userId > 0 {
                    self.allUsers[user.userId] = BuzUserData.init(entity: user)
                }
            }
        }
    }
    
    //清除缓存
    public func cleanDataWhenLogout() {
        allUsers.removeAll()
        allFriendInfo.removeAll()
        allFriends.removeAll()
        self.queryUserCallbacks.removeAll()
        self.queryUserFailCounts.removeAll()
        officalUser = nil
        self.deleteFriendInfoRequestStatus = .initial
        friendInfoRequested = false
    }
    
    private func addObserver() {
        self.startListening()
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(resetFailCount),
                                               name: UIApplication.didEnterBackgroundNotification,
                                               object: nil)
    }
    
    @objc private func resetFailCount() {
        self.queryUserFailCounts.removeAll()
    }
    
    func startListening() {
        BuzUserSession.shared.publisher.didLogoutSuccess.sink { event in
            self.lastSendTimestampDict.removeAll()
        }.store(in: &cancellables)
    }
    
    func stopListening() {
        self.cancellables.removeAll()
        print("Stopped listening.")
    }
}

//MARK: -sync server data----
public extension FriendInfoManager {
    /// 从服务端获取所有好友数据并缓存数据库
    /// - Parameter complete: 回调
    func syncAllFriends(complete:((_ success: Bool)-> Void)?) {
        let key = FriendPrivateKey.requestFriendListTimestampKey.rawValue +  String(BuzUserSession.shared.uid)
        let timestamp =  MMKV.buz.int64(forKey: key, defaultValue: 0)
        
        BuzFriendNetworker.getFriendList(timestamp:Int64(timestamp)) { rcode, info in
            BuzContactsLog.info("api getFriendList rcode:\(rcode) timestamp:\(timestamp) friendInfoRequested:\(self.friendInfoRequested)")
            
            guard let info = info, rcode == 0 else {
                BuzContactsLog.error("api getFriendList error rcode:\(rcode) timestamp:\(timestamp)")
                complete?(false)
                return
            }
            
            if let newTimestamp = info.timestamp {
                MMKV.buz.set(newTimestamp, forKey: key)
            }
            
            let originFriendInfoRequested = self.friendInfoRequested
            self.friendInfoRequested = true
            guard let friendList = info.friendList else {
                self.allFriendInfo.removeAll()
                self.allFriends.removeAll()
                complete?(true)
                return
            }
            
            var allFriends = [Int64: UserInfo]()
            var userInfoList = [BuzUserData]()
            friendList.forEach { user in
                if let userId = user.userId, userId > 0 {
                    
                    let baseInfo = BuzUserData.BaseInfo.init(idl: user)
                    var relation: BuzUserData.RelationInfo = .init(userId: userId)
                    if let userRelation =  info.userRelationList?.first(where: {$0.userId == userId}) {
                        relation = BuzUserData.RelationInfo.init(idl: userRelation)
                    }
                    relation.isFriendFlag =  true
                    if let localMuteMessages = self.allUsers[userId]?.relation.localMuteMessages {
                        relation.localMuteMessages = localMuteMessages
                    }
                    
                    let item = BuzUserData.init(userId: userId, base: baseInfo, relation: relation)
                    userInfoList.append(item)
                    self.allUsers[userId] = item
                    allFriends[userId] = user
                    
                    if false == originFriendInfoRequested { //首次获取好友列表，上传本地静音状态到服务端
                        self.uploadLocalMuteMessageStatusIfNeed(user: item)
                    }
                    
                    if item.base.userType == .officialAccount{
                        self.officalUser = item
                    }
                }
            }
            self.checkFriendDeletableTask(previousFriends: self.allFriends, currentFriends: userInfoList)
            self.allFriendInfo = allFriends
            self.allFriends = userInfoList
            
            
            BuzUserInfoDao.sharedInstance().deleteAllFriendComplete { status in
                let entitys = userInfoList.map{$0.convertDaoEntity()}
                BuzUserInfoDao.sharedInstance().addOrUpdateUserInfo(entitys) { status in
                    DispatchQueue.main.async {
                        complete?(status)
                    }
                }
            }
        }
    }
    
    
    // 上传本地静音状态到服务端，如果本地设置和服务端不一致的时候
    private func uploadLocalMuteMessageStatusIfNeed(user: BuzUserData) {
        if (user.relation.localMuteMessages == .unknow || user.relation.localMuteMessages == user.relation.serviceMuteMessages) {
            return
        }
        
        let mute = user.relation.localMuteMessages == .on
        let muteNotification = user.relation.muteNotification == .on
        
        BuzFriendCenter.relation.updateMuteMessages(with: user.userId, mute: mute) { rcode in
            BuzLog.info("syncLocalMuteMessageStatusIfNeed userId:\(user.userId) localMuteMessages:\(user.relation.localMuteMessages.rawValue) serviceMuteMessages:\(user.relation.serviceMuteMessages.rawValue) muteNotification:\(user.relation.muteNotification.rawValue) rcode:\(rcode)")
        }
    }
}

//MARK: -query DB data----
public extension FriendInfoManager {
    //查询联系人信息通过UserInfo对象
    func queryUserInfoFromDB(userId: Int64, complete:(( _ user: BuzUserData?)-> Void)?) {
        BuzUserInfoDao.sharedInstance().getUserInfo(withUserId: userId) { entity in
            if let entity = entity {
                complete?(.init(entity: entity))
            }else{
                complete?(nil)
            }
        }
    }
    
    func queryUserInfoFromDB(userIds: [Int64], complete:(( _ users: [BuzUserData]?)-> Void)?) {
        let userIds = userIds.map { NSNumber(value: $0) }
        BuzUserInfoDao.sharedInstance().getUserInfo(withUserIdList: userIds) { entitys in
            if let entitys = entitys {
                let users = entitys.map({BuzUserData.init(entity: $0)})
                complete?(users)
            }else {
                complete?(nil)
            }
        }
    }
    
    func queryUserInfoFromDB(userId: Int64) -> BuzUserData? {
        if let entity = BuzUserInfoDao.sharedInstance().synGetUserInfo(withUserId: userId) {
            return .init(entity: entity)
        }
        return nil
    }
}

//MARK: -query local data(cache data or DB data)----
public extension FriendInfoManager {
    
    // 获取本地缓存信息，没有也不会去网络请求，可能为空
    @objc func queryUserInfo(userId: Int64) -> BuzUserData? {
        return allUsers[userId]
    }
    
    @objc func queryUserInfo(userId: Int64, completion: @escaping ((_ user: BuzUserData?) -> Void)) {
        if let userInfo = allUsers[userId] {
            completion(userInfo)
            return
        }
        self.queryUserInfoFromDB(userId: userId) { entity in
            if let user = entity {
                self.allUsers[userId] = user
            }
            completion(entity)
        }
    }
    
    @objc func queryUserInfo(userIds: [Int64]) -> [Int64:BuzUserData] {
        var userInfos : [Int64 : BuzUserData] = [:]
        var uncacheUids : [NSNumber] = []
        
        userIds.forEach { userId in
            if let userInfo = allUsers[userId] {
                userInfos[userId] = userInfo
            } else {
                uncacheUids.append(userId as NSNumber)
            }
        }
        
        if userInfos.count != userIds.count {
            let entity = BuzUserInfoDao.sharedInstance().syncGetUserInfo(withUserIdList: uncacheUids)
            entity.forEach({ userInfo in
                userInfos[userInfo.userId] = BuzUserData.init(entity: userInfo)
            })
        }
        
        return userInfos
    }
    
    //查询联系人信息通过UserInfo对象
    func queryUserInfo(userInfos: [UserInfo], completion:(( _ users: [BuzUserData] )-> Void)?) {
        var result = [BuzUserData]()
        
        userInfos.forEach { bpInfo in
            let user = BuzUserData.init(userId: bpInfo.userId ?? 0, base: .init(idl: bpInfo))
            result.append(user)
        }
        
        let userIds = userInfos.map({ $0.userId ?? 0 })
        self.queryUserInfoFromDB(userIds: userIds) { users in
            result = result.map { model in
                if let entity = users?.first(where: {$0.userId == model.userId}){
                    model.relation = entity.relation
                }
                return model
            }
            completion?(result)
        }
    }
    
    //查询联系人信息通过UserInfo对象
    func queryUserInfo(baseInfos: [BuzUserData.BaseInfo], completion:(( _ users: [BuzUserData] )-> Void)?) {
        var result = [BuzUserData]()
        
        baseInfos.forEach { info in
            let user = BuzUserData.init(userId: info.userId, base: info)
            result.append(user)
        }
        
        let userIds = baseInfos.map({ $0.userId })
        self.queryUserInfoFromDB(userIds: userIds) { users in
            result = result.map { model in
                if let entity = users?.first(where: {$0.userId == model.userId}){
                    model.relation = entity.relation
                }
                return model
            }
            completion?(result)
        }
    }
    
    //查询联系人信息通过BuzUserInfod对象,补充remark和isFriend信息
    func queryUserInfo(buzUsers: [BuzUserData], completion:(( _ users: [BuzUserData] )-> Void)?){
        var result = buzUsers
        
        let userIds = buzUsers.map({ $0.userId })
        self.queryUserInfoFromDB(userIds: userIds) { list in
            result = result.map { model in
                if let dbUser = list?.first(where: {$0.userId == model.userId}){
                    model.relation = dbUser.relation
                    model.base.buzId = dbUser.base.buzId
                    model.base.email = dbUser.base.email
                }
                return model
            }
            completion?(result)
        }
    }
}

//MARK: -fetch data(get cache or request data)----
public extension FriendInfoManager {
    
    func mergeFetchUserInfo(userId: Int64?,
                            fetchType: BuzFetchUserType = .cacheOrNetwork,
                            completion: @escaping (( _ contactsInfo: BuzUserData?, _ isDeleted: Bool)-> Void)) {
        guard let userId = userId else { return }
        
        if fetchType == .onlyCache {
            completion(self.allUsers[userId], false)
            return
        } else if fetchType == .cacheOrNetwork {
            if let userInfo = self.allUsers[userId] {
                completion(userInfo, false)
                return
            }
        } else if fetchType == .cacheAndNetwork {
            if let userInfo = self.allUsers[userId] {
                completion(userInfo, false)
            }
        }
        
        if var callbacks = self.queryUserCallbacks[userId] {
            callbacks.append(QueryUserCallback.init(callback: completion))
            return
        } else {
            if (self.queryUserFailCounts[userId] ?? 0) > 3
                || BuzNetworker.shared.netMonitor.currentReachabilityStatus == .notReachable {
                completion(nil, false)
                return
            }
            
            self.queryUserCallbacks[userId] = [QueryUserCallback.init(callback: completion)]
        }
        
        self.requestUserInfo(userId: userId, phone: nil) { [weak self] user in
            guard let self = self else {return}
            
            if user == nil {
                self.queryUserFailCounts[userId] = (self.queryUserFailCounts[userId] ?? 0) + 1
            }
            
            switch user?.base.userStatus {
            case .deletedAccount:
                self.queryUserFailCounts[userId] = 100
                if let callbacks = self.queryUserCallbacks[userId] {
                    callbacks.forEach { callback in
                        callback.callback(nil, true)
                    }
                    self.queryUserCallbacks.removeValue(forKey: userId)
                }
            default:
                if let callbacks = self.queryUserCallbacks[userId] {
                    callbacks.forEach { callback in
                        callback.callback(user, false)
                    }
                    self.queryUserCallbacks.removeValue(forKey: userId)
                }
            }
        }
    }
    
    
    /// 查询单个联系人信息,会先返回缓存信息
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - cacheCallback 缓存回调
    ///   - complete: 结果(BuzUserInfo和好友关系 )
    @objc func fetchUserInfo(userId: Int64,
                             fromNetWork: Bool = false,
                             cacheHandler:(( _ user: BuzUserData?)-> Void)?,
                             completion:(( _ user: BuzUserData?)-> Void)? = nil) {
        
        BuzQueryInfoLog.info("begin fetchUserInfo userId = \(userId) ; fromNetWork = \(fromNetWork)")
        if userId <= 0 {
            BuzQueryInfoLog.info("finish fetchUserInfo userId = \(userId) ; fromNetWork = \(fromNetWork) ; result : userId <= 0")
            completion?(nil)
            return
        }
        
        if let cacheUser = allUsers[userId] {
            cacheHandler?(cacheUser)
        } else {
            
            self.queryUserInfoFromDB(userId: userId) { entity in
                
                if let entity = entity{
                    cacheHandler?(entity)
                } else{
                    self.requestUserInfo(userId: userId, phone: nil) { user in
                        cacheHandler?(user)
                    }
                }
            }
        }
        if fromNetWork == false{
            return
        }
        self.requestUserInfo(userId: userId, phone: nil, completion: completion)
    }
    
    /// 批量查询用户信息
    /// - Parameters:
    ///   - useridList: 用户userid列表
    ///   - forceRefresh:强制网络请求刷新
    ///   - complete: 缓存完成回调
    func fetchBatchUserInfos(userIds: [Int64],
                             forceRefresh: Bool = false,
                             completion: QueryUserInfoClosure?) {
        BuzContactsLog.debug("fetchBatchUserInfos: userIds:\(userIds)")
        if userIds.count == 0 {
            completion?(true, [])
            return
        }
        
        let queryBlock = {
            self.requestBatchUerInfos(userIds: userIds) { users in
                
                let ids = users.map({ $0.userId })
                self.queryUserInfoFromDB(userIds: ids) { entitys in
                    //把原来的用户关系先查出来(如果有) 再保存
                    let result = users.map { item -> BuzUserData in
                        if let entity = entitys?.first(where: { $0.userId == item.userId }) {
                            item.relation = entity.relation
                            item.base.buzId = entity.base.buzId
                            item.base.email = entity.base.email
                        }
                        return item
                    }
                    
                    let entitys = result.map{$0.convertDaoEntity()}
                    BuzUserInfoDao.sharedInstance().addOrUpdateUserInfo(entitys) { status in
                        DispatchQueue.main.async {
                            completion?(true, result)
                        }
                    }
                }
            }
        }
        
        if true == forceRefresh{
            queryBlock()
        } else {
            let ids = userIds.map( { $0 })
            self.queryUserInfoFromDB(userIds: ids) { list in
                if list?.count == userIds.count{
                    completion?(true, list ?? [])
                } else{
                    queryBlock()
                }
            }
        }
    }
}

//MARK: -request data(only request and cahce data)
public extension FriendInfoManager {
    
    @objc
    func requestUserInfo(userId: Int64) {
        self.requestUserInfo(userId: userId, phone: nil, completion: nil)
    }
    
    /// 查询单个联系人信息
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - phone: 手机号
    ///   - complete: 回调用户信息和关系
    func requestUserInfo(userId: Int64?,
                         phone: String?,
                         completion:(( _ user: BuzUserData?)-> Void)? = nil) {
        if userId == nil && phone?.length ?? 0 <= 0 {
            completion?(nil)
            return
        }
        BuzQueryInfoLog.info("begin request userInfo ; userId = \(String(describing: userId)) , phone = \(String(describing: phone))")
        BuzFriendNetworker.getUserInfo(userId: userId, phone: phone) { rcode, userInfo, userRelation in
            let buzUser = BuzUserData.init(userId: userId ?? 0)
            guard rcode == 0, let userInfo = userInfo else {
                if rcode == 0 {
                    self.deletedAccounts[userId ?? 0] = false
                    buzUser.base.userStatus = .deletedAccount
                    completion?(buzUser)
                } else {
                    BuzContactsLog.error("request userInfo error: userId:\(String(describing: userId)) phone:\(String(describing: phone)) rcode:\(rcode)")
                    BuzQueryInfoLog.info("request userInfo ; userId = \(String(describing: userId)) , phone = \(String(describing: phone)) ; rcode = \(rcode)")
                    completion?(nil)
                }
                return
            }
            buzUser.base = BuzUserData.BaseInfo.init(idl: userInfo)
            if let relation = userRelation {
                buzUser.relation = BuzUserData.RelationInfo.init(idl: relation)
            }
            buzUser.relation.localMuteMessages = self.allUsers[userId ?? 0]?.relation.localMuteMessages ?? .unknow
            
            let quietModeChanged = userInfo.quietMode != self.allFriendInfo[buzUser.userId]?.quietMode
            self.allFriendInfo[buzUser.userId] = userInfo
            let cacheInfo = self.allUsers[buzUser.userId]
            self.allUsers[buzUser.userId] = buzUser
            
            if false == cacheInfo?.isEqual(buzUser) || quietModeChanged {
                self.center?.notifyObservers(of: BuzFriendCenterInfoObserver.self) { observer in
                    observer.friendInfoManagerUserInfoDidUpdate?(self, info: buzUser)
                }
                self.publisher.userInfoDidUpdated.send(buzUser)
            }
            
            if buzUser.relation.isFriend() && !self.allFriends.contains(where: {$0.userId == buzUser.userId}){
                if buzUser.relation.relation != .me{///排除查询自己信息的时候把自己加入到首页列表
                    self.allFriends.append(buzUser)
                }
            }
            
            BuzQueryInfoLog.info("request userInfo success ; userId = \(String(describing: userId)) , phone = \(String(describing: phone)) ; ")
            BuzUserInfoDao.sharedInstance().addOrUpdateUserInfo([buzUser.convertDaoEntity()]) { _ in
                completion?(buzUser)
                BuzQueryInfoLog.info("request and save userInfo success ; userId = \(String(describing: userId)) , phone = \(String(describing: phone)) ; success")
            }
        }
    }
}

extension FriendInfoManager {
    /// 批量查询用户信息，服务端不会返回用户备注和好友关系
    /// - Parameters:
    ///   - uidList: 查询的用户列表
    ///   - complete: 用户信息
    func requestBatchUerInfos(userIds:[Int64], completion:(( _ users: [BuzUserData])-> Void)?) {
        
        BuzFriendNetworker.getUserInfoList(userIds) { rcode, list in
            guard rcode == 0 else {
                BuzContactsLog.error("requestBatchUerInfos error: userId:\(userIds) rcode:\(rcode)")
                completion?([])
                return
            }
            
            self.queryUserInfo(userInfos: list) { userList in
                for item in userList {
                    let cacheInfo = self.allUsers[item.userId]
                    let userInfo = list.first(where: {$0.userId == item.userId})
                    let quietModeChanged = userInfo?.quietMode != self.allFriendInfo[item.userId]?.quietMode
                    
                    if false == cacheInfo?.isEqual(item) || quietModeChanged{
                        self.center?.notifyObservers(of: BuzFriendCenterInfoObserver.self) { observer in
                            observer.friendInfoManagerUserInfoDidUpdate?(self, info: item)
                        }
                        self.publisher.userInfoDidUpdated.send(item)
                    }
                    self.allUsers[item.userId] = item
                    self.allFriendInfo[item.userId] = userInfo
                }
                
                completion?(userList)
            }
        }
    }
}

//MARK: -用户信息操作
public extension FriendInfoManager {
    /// 更新本地数据库和缓存的好友信息
    // 会触发BuzFriendCenterInfoObserver的 friendInfoManagerUserInfoDidUpdate(mgr:info:)方法
    /// - Parameters:
    ///   - info: 更新的用户信息
    ///   - complete: 回调
    func updateLocalUserInfo(info: BuzUserData, completion:((_ success: Bool)-> Void)?) {
        guard info.userId > 0 else {
            completion?(false)
            return
        }
        
        allUsers[info.userId] = info
        if let index = allFriends.firstIndex(where: {$0.userId == info.userId}){
            allFriends[index] = info
        }
        
        BuzUserInfoDao.sharedInstance().addOrUpdateUserInfo([info.convertDaoEntity()]) { success in
            self.center?.notifyObservers(of: BuzFriendCenterInfoObserver.self) { observer in
                observer.friendInfoManagerUserInfoDidUpdate?(self, info: info)
            }
            self.publisher.userInfoDidUpdated.send(info)
            completion?(success)
        }
    }
    
    func addNewLocalUserInfo(info: BuzUserData, complete:((_ success: Bool)-> Void)?) {
        guard info.userId > 0 else {
            complete?(false)
            return
        }
        
        allUsers[info.userId] = info
        if let index = allFriends.firstIndex(where: {$0.userId == info.userId}){
            allFriends[index] = info
        } else {
            allFriends.insert(info, at: 0)
        }
        
        BuzUserInfoDao.sharedInstance().addOrUpdateUserInfo([info.convertDaoEntity()]) { success in
            self.center?.notifyObservers(of: BuzFriendCenterInfoObserver.self) { observer in
                observer.friendInfoManagerUserInfoDidUpdate?(self, info: info)
            }
            self.publisher.userInfoDidUpdated.send(info)
            complete?(success)
        }
    }
}

//MARK: -checkUnknowUsers----
public extension FriendInfoManager {
    func checkUnknowUsers(useridList: Set<Int64>) {
        // Avoid making redundant requests or performing actions while a previous request is still being processed
        if self.deleteFriendInfoRequestStatus == .loading {
            return
        }
        
        BuzContactsLog.debug("checkUnknowUsers: useridList:\(useridList)")
        if useridList.count == 0 {
            return
        }
        
        func isDataChangedDeleteAccount(deleteAccounts: [Int64 : Bool], originUserIds: [Int64 : Bool]) -> Bool {
            var dataChanged = false
            
            if originUserIds.count != deleteAccounts.count {
                dataChanged = true
            } else {
                deleteAccounts.forEach { userId, value in
                    if originUserIds[userId] == nil {
                        dataChanged = true
                    } else if let originValue = originUserIds[userId], originValue != value {
                        dataChanged = true
                    }
                }
            }
            
            return dataChanged
        }
        var deleteAccountIds = [Int64 : Bool]()
        
        if self.deleteFriendInfoRequestStatus != .initial {
            var tmpResult = [Int64 : Bool]()
            
            useridList.forEach { userId in
                if let value = self.deletedAccounts[userId] {
                    tmpResult[userId] = self.deletedAccounts[userId]
                } else {
                    tmpResult[userId] = false
                }
            }
            
            if !isDataChangedDeleteAccount(deleteAccounts: tmpResult, originUserIds: self.deletedAccounts) {
                return
            }
        }
        
        deleteAccountIds = self.deletedAccounts
        var userIds : [Int64] = []
        var deleteUserIds : [Int64 : Bool] = [Int64 : Bool]()
        
        useridList.forEach { userId in
            userIds.append(userId)
        }
        
        self.deleteFriendInfoRequestStatus = .loading
        BuzFriendNetworker.getUserInfoList(userIds) { code, data in
            self.deleteFriendInfoRequestStatus = .success
            
            if code == 0 {
                userIds.forEach { userId in
                    deleteUserIds[userId] = data.first(where: { userInfo in
                        return userInfo.userId == userId
                    }) != nil
                }
                
                if isDataChangedDeleteAccount(deleteAccounts: deleteUserIds, originUserIds: deleteAccountIds) {
                    MMKV.buz_async { kv in
                        if let data =  try? JSONEncoder().encode(deleteUserIds) {
                            kv.set(data, forKey: BuzDeletedAccounts.deletedAccountsKey.rawValue)
                        }
                    }
                    
                    self.deletedAccounts = deleteUserIds
                    
                    self.center?.notifyObservers(of: BuzFriendCenterInfoObserver.self) { observer in
                        observer.friendInfoManagerFriendListDiedChanged?(self, friends: self.allFriends)
                    }
                    self.publisher.friendListDidChanged.send(self.allFriends)
                }
            }
        }
    }
}

//MARK: -tool----
private extension FriendInfoManager {
    // 处理好友数据变更
    func handleAllFriendListChange(oldList: [BuzUserData], newList: [BuzUserData]) {
        let deleteFriends = oldList.filter { info in
            false == newList.contains(where: {info.userId == $0.userId})
        }
        
        deleteFriends.forEach { item in
            let userId = item.userId
            if let user = allUsers[userId] {
                user.relation.isFriendFlag = false
                user.relation.becomeFriendTime = 0
                allUsers[userId] = user
            }
        }
        
        self.center?.notifyObservers(of: BuzFriendCenterInfoObserver.self) { observer in
            if deleteFriends.count > 0 {
                observer.friendInfoManagerFriendsDiedDelete?(self, friends: deleteFriends)
            }
            observer.friendInfoManagerFriendListDiedChanged?(self, friends: allFriends)
        }
        if deleteFriends.count > 0 {
            self.publisher.friendsDidDeleted.send(deleteFriends)
        }
        self.publisher.friendListDidChanged.send(allFriends)
    }
    
    func checkFriendDeletableTask(previousFriends: [BuzUserData], currentFriends: [BuzUserData]) {
        let deletableModels : NSMutableDictionary = NSMutableDictionary.init()
        
        previousFriends.forEach { model in
            deletableModels[model.userId] = model
        }
        
        currentFriends.forEach { model in
            deletableModels.removeObject(forKey: model.userId)
        }
        let users = deletableModels.allValues
        if let deletableGroups = users as? [BuzUserData] {
            deletableGroups.forEach { model in
                StorageManageRegiter.service().delete(session: BuzStorageDeletable.SessionEntity.init(userId: BuzUserSession.shared.uid,
                                                                                                      targetId: model.userId,
                                                                                                      conversationType: IM5ConversationType.peer.rawValue))
            }
        }
    }
    
    //保存联系人数据到push小组件
    func updateWidgetContacts() {
        
        var pushContactsDict : [Int64 : String] = [:]
        var pushFullnameDict : [Int64 : String] = [:]
        let data = self.allFriends
        for model in data {
            if model.userId <= 0 {
                continue
            }
            pushContactsDict[model.userId] = model.displayName
            pushFullnameDict[model.userId] = model.fullName
        }
        //myself info
        if let myInfo = BuzUserSession.shared.userInfo{
            pushContactsDict[BuzUserSession.shared.uid] = myInfo.displayName
            pushFullnameDict[BuzUserSession.shared.uid] = myInfo.fullName
        }
        
        PushShareDataTool.saveContactsMap(pushContactsDict)
        PushShareDataTool.saveUserFullnameMap(pushFullnameDict)
#if DEBUG
        let pushShareData = PushShareDataTool.loadContactsMap()
        BuzLog.debug("save to notificationService:\(pushShareData)")
#endif
    }
}

internal extension FriendInfoManager {
    func friendRelationUpdateServiceMuteMessage(_ userId: Int64, serviceMuteMessages: BuzMuteMessagesType) {
        
        if let user = allUsers[userId] {
            if user.relation.serviceMuteMessages == serviceMuteMessages {
                return
            }
            
            user.relation.serviceMuteMessages = serviceMuteMessages
            self.updateLocalUserInfo(info: user, completion: nil)
        }
    }
    
    func friendRelationUpdateMuteNotification(_ userId: Int64, type: BuzMuteNotificationType) {
        if let user = allUsers[userId] {
            user.relation.muteNotification = type
            self.updateLocalUserInfo(info: user, completion: nil)
        }
    }
    
    func friendRelationDeleteFriend(_ userId: Int64) {
        if allFriendInfo.keys.contains(userId) {
            BuzUserInfoDao.sharedInstance().deleteUserInfo(withUserIdList: [NSNumber(value: userId)])
            allFriendInfo.removeValue(forKey: userId)
            
            if let userInfo = allFriends.first(where: { userInfo in
                return userInfo.userId == userId
            }) {
                self.checkFriendDeletableTask(previousFriends: [userInfo], currentFriends: [])
            }
            
            allFriends.removeAll(where: {$0.userId == userId})
        }
    }
}

//MARK: -BuzSignalPushEventObserver---
extension FriendInfoManager: BuzSignalPushEventObserver {
    public func signalPushDidReceivedMessage(_ message: BuzSignalPush.Message) {
        guard let payload = message.payload else {
            return
        }
        switch payload.op {
        case .pushDataChange:
            let type = BuzSignalPush.PushDataChange.init(rawValue: payload.businessType ?? 0)
            switch type {
            case .FriendMemberList: //好友列表变化
                self.didReceivedFriendsInfoChangePush()
            default:
                break
            }
        case .friendStatusUpdate: //好友状态有更新
            self.handleFriendStatusChange(message: message)
        default:
            break
        }
    }
    
    //好友信息变化
    func didReceivedFriendsInfoChangePush() {
        self.syncAllFriends(complete: nil)
        self.center?.notifyObservers(of: BuzFriendCenterInfoObserver.self) { observer in
            observer.friendInfoManagerFriendsRelationInfoChange?(self)
        }
        self.publisher.friendsRelationInfoChanged.send()
    }
    
    // 好友状态变化推送
    func handleFriendStatusChange(message: BuzSignalPush.Message) {
        guard let payload = message.payload,
              let dataDict = payload.data,
              let friendStatusInfoList = dataDict["friendStatusInfoList"] as? [[String : Any]] else {
            return
        }
        
        let sendTimestamp = payload.sendTimestamp ?? 0
        BuzSignalPushLog.info("好友状态变化 sendTimestamp:\(String(describing: sendTimestamp)) dataDict:\(dataDict)")
        
        var models = [BuzFriendStatusInfo]()
        for info in friendStatusInfoList {
            if let jsonData = try? JSONSerialization.data(withJSONObject: info, options: []),
               let model = try? JSONDecoder().decode(BuzFriendStatusInfo.self, from: jsonData) {
                
                let key = "friendStatus_\(model.userId)"
                let lastTimestamp = self.lastSendTimestampDict[key] ?? 0
                
                if (sendTimestamp > lastTimestamp) {
                    models.append(model)
                }
                lastSendTimestampDict[key] = sendTimestamp
            }
        }
        
        if models.count <= 0 {
            return
        }
        
        for item in models {
            if var userInfo = allFriendInfo[item.userId] {
                userInfo.quietMode = item.quietMode
                userInfo.walkieTalkieOnlineTime = item.walkieTalkieOnlineTime
                allFriendInfo[item.userId] = userInfo
            }
            
            if let buzUserInfo = allFriends.first(where: {$0.userId == item.userId}) {
                self.center?.notifyObservers(of: BuzFriendCenterInfoObserver.self) { observer in
                    observer.friendInfoManagerUserInfoDidUpdate?(self, info: buzUserInfo)
                }
                self.publisher.userInfoDidUpdated.send(buzUserInfo)
            }
        }
        
        self.center?.notifyObservers(of: BuzFriendCenterInfoObserver.self) { observer in
            observer.friendInfoManagerUserStateDidChanged?(self, models: models)
        }
        self.publisher.userStateDidChanged.send(models)
    }
}
