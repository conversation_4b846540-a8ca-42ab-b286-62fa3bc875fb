//
//  BuzUserDataExtension.swift
//  buz
//
//  Created by st.chio on 2025/2/13.
//  Copyright © 2025 lizhi. All rights reserved.
//

import BuzUserSession
import BuzCenterKit

public extension BuzUserData {
    
    func isDeleteAccount() -> <PERSON>ol {
        return BuzFriendCenter.info.isDeleteAccount(uid: self.userId)
    }
    
}

public extension BuzUserData.RelationInfo {
    
    func isFriend()-> Bool {
        if BuzUserSession.shared.uid == self.userId && self.userId != 0 {
            return true
        }
        
        return self.isFriendFlag
    }
    
}


