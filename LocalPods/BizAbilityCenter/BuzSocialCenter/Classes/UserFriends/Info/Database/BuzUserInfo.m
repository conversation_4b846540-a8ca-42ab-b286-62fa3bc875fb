//
//  BuzUserInfo.m
//  buz
//
//  Created by lizhi on 2022/7/14.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "BuzUserInfo.h"
#import "YYModel.h"
@import BuzCenterKit;

@implementation BuzUserInfo

- (void)encodeWithCoder:(NSCoder *)coder{
    [self yy_modelEncodeWithCoder:coder];
}

- (nullable instancetype)initWithCoder:(nonnull NSCoder *)coder {
    return [self yy_modelInitWithCoder:coder];
}

- (BOOL)isEqual:(id)object{
    if ([object class] != [self class]) {
        return [super isEqual:object];
    }
    
    BuzUserInfo *model = object;
    if ([self.phone ?: @"" isEqualToString:model.phone ?: @""] &&
        [self.firstName ?: @"" isEqualToString:model.firstName ?: @""] &&
        [self.lastName ?: @"" isEqualToString:model.lastName ?: @""] &&
        [self.userName ?: @"" isEqualToString:model.userName ?: @""] &&
        [self.portrait ?: @"" isEqualToString:model.portrait ?: @""] &&
        [self.remark ?: @"" isEqualToString:model.remark ?: @""] &&
        self.userId == model.userId &&
        self.isFriend == model.isFriend &&
        self.registerTime == model.registerTime &&
        self.becomeFriendTime == model.becomeFriendTime &&
        self.userType == model.userType &&
        self.userStatus == model.userStatus &&
        self.serviceMuteMessages == model.serviceMuteMessages &&
        self.localMuteMessages == model.localMuteMessages &&
        self.muteNotification == model.muteNotification &&
        [self.buzId ?: @"" isEqualToString:model.buzId ?: @""] &&
        [self.email ?: @"" isEqualToString:model.email ?: @""]
        ) {
        return YES;
    }
    
    return NO;
}
-(void)setMuteNotification:(NSInteger)muteNotification{
    _muteNotification = muteNotification;
}
-(void)setLocalMuteMessages:(NSInteger)localMuteMessages{
    if (localMuteMessages == BuzMuteMessagesTypeOn) {
//        NSLog(@"设置好友静音失败%ld",(long)localMuteMessages);
    }
   
    
    _localMuteMessages = localMuteMessages;
}

- (BOOL)isMuteMessages{
    if (self.localMuteMessages != BuzMuteMessagesTypeUnknow) {
        return self.localMuteMessages == BuzMuteMessagesTypeOn;
    }    
    return self.serviceMuteMessages == BuzMuteMessagesTypeOn;
}

- (BOOL)isMuteNotification {
    return self.muteNotification == BuzMuteNotificationTypeOn;
}

-(NSUInteger)hash{
    return self.phone.hash ^ self.firstName.hash ^ self.lastName.hash ^ self.portrait.hash;
}

- (NSString *)description
{
    NSString *dataStr = [[NSString alloc] initWithData:[self yy_modelToJSONData] encoding:NSUTF8StringEncoding];
    return dataStr;
}

- (bool)emailLoginNeedBindPhone {
    return self.email.length > 0 && self.phone.length == 0;
}

@end
