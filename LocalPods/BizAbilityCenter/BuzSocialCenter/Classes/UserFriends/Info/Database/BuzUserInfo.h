//
//  BuzUserInfo.h
//  buz
//
//  Created by l<PERSON>hi on 2022/7/14.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface BuzUserInfo : NSObject<NSCoding>

//用户ID
@property (nonatomic, assign) int64_t userId;

/// buzId
@property (nonatomic, copy , nullable) NSString *buzId;

/// email
@property (nonatomic, copy , nullable) NSString *email;

/// 用户昵称
@property (nonatomic, copy , nullable) NSString *userName;

@property (nonatomic, copy , nullable) NSString *firstName;

@property (nonatomic, copy , nullable) NSString *lastName;

///格式化后的手机号(86-12345678901)
@property (nonatomic, copy , nullable) NSString *phone;

//联系人头像，服务端头像
@property (nonatomic, copy , nullable) NSString *portrait;

//备注名
@property (nonatomic, copy , nullable) NSString *remark ;

//注册时间
@property (nonatomic, assign) int64_t registerTime;

//成为好友时间
@property (nonatomic, assign) int64_t becomeFriendTime;

//是否是好友
@property (nonatomic, assign) BOOL isFriend;

//好友类型
@property (nonatomic, assign) NSInteger userType;

// 服务端 静音好友消息,1-静音，2-不静音
@property (nonatomic, assign) NSInteger serviceMuteMessages;

// 本地记录的静音好友消息,1-静音，2-不静音
@property (nonatomic, assign) NSInteger localMuteMessages;

// 静音好友消息通知,1-静音，2-不静音
@property (nonatomic, assign) NSInteger muteNotification;

//用户状态 0-正常 1-临时封禁 2-永久封禁
@property (nonatomic, assign) int32_t userStatus;

// 判断是否静音需要通过这个方法
// serviceMuteMessages和 localMuteMessages 不一致的时候以localMuteMessages为准
- (BOOL)isMuteMessages;
- (BOOL)isMuteNotification;

- (bool)emailLoginNeedBindPhone;
@end

NS_ASSUME_NONNULL_END
