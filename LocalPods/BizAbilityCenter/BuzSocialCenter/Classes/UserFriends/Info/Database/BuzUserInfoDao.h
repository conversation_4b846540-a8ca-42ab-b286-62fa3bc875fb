//
//  BuzUserInfoDao.h
//  buz
//
//  Created by lizhi on 2022/7/14.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class BuzUserInfo;

//用户信息表
@interface BuzUserInfoDao : NSObject

+ (instancetype)sharedInstance;

- (NSString *)getTableName;

///同一个用户可能对应了设备登录过的多个账号，只对当前登录账号的数据做操作

/** 更新用户数据**/
- (void)addOrUpdateUserInfo:(NSArray <BuzUserInfo *>*)userInfoList complete:(nullable void (^)(BOOL))complete;


/** 通过用户id删除用户数据**/
- (void)deleteUserInfoWithUserIdList:(NSArray <NSNumber *>*)uidList complete:(nullable void (^)(BOOL))complete;

/** 通过手机号删除用户数据**/
- (void)deleteUserInfoWithPhoneList:(NSArray <NSString *>*)phoneList complete:(nullable void (^)(BOOL))complete;

/** 删除所有好友关系用户数据**/
- (void)deleteAllFriendComplete:(nullable void (^)(BOOL))complete;


/** 根据uid获取用户**/
- (void)getUserInfoWithUserId:(int64_t)userId complete:(void (^)(BuzUserInfo * _Nullable))complete;
- (nullable BuzUserInfo *)synGetUserInfoWithUserId:(int64_t)userId;

/** 根据uid列表获取用户**/
- (void)getUserInfoWithUserIdList:(NSArray <NSNumber *>*)uidList complete:(void (^)(NSArray <BuzUserInfo *>* _Nullable))complete;
- (NSArray<BuzUserInfo *> *)syncGetUserInfoWithUserIdList:(NSArray <NSNumber *>*)uidList;

/** 根据手机号获取用户**/
- (void)getUserInfoWithPhone:(NSString *)phone complete:(void (^)(BuzUserInfo * _Nullable))complete;

/** 获取所有好友关系的用户数据**/
- (void)getAllFriendInfoComplete:(void (^)(NSArray <BuzUserInfo *>* nullable))complete;

/** 获取所有好友关系的用户数据，通过成为好友时间+注册试剂**/
- (void)getAllFriendInfoOrderByBecomeFriendTimeComplete:(void (^)(NSArray <BuzUserInfo *>* nullable))complete;

/** 获取所有非好友关系的用户数据**/
- (void)getAllUnFriendInfoComplete:(void (^)(NSArray <BuzUserInfo *>* nullable))complete;

@end

NS_ASSUME_NONNULL_END
