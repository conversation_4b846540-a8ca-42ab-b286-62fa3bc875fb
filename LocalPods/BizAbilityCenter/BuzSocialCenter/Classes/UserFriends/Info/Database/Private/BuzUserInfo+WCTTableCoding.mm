//
//  BuzUserInfo+WCTTableCoding.m
//  buz
//
//  Created by l<PERSON>hi on 2022/7/14.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "BuzUserInfo+WCTTableCoding.h"

@implementation BuzUserInfo (WCTTableCoding)

WCDB_IMPLEMENTATION(BuzUserInfo)

WCDB_SYNTHESIZE(BuzUserInfo, userId)
WCDB_SYNTHESIZE(BuzUserInfo, buzId)
WCDB_SYNTHESIZE(BuzUserInfo, email)
WCDB_SYNTHESIZE(BuzUserInfo, firstName)
WCDB_SYNTHESIZE(BuzUserInfo, lastName)
WCDB_SYNTHESIZE(BuzUserInfo, userName)
WCDB_SYNTHESIZE(BuzUserInfo, registerTime)
WCDB_SYNTHESIZE(BuzUserInfo, phone)
WCDB_SYNTHESIZE(BuzUserInfo, portrait)
//WCDB_SYNTHESIZE(BuzUserInfo, relation)
WCDB_SYNTHESIZE(BuzUserInfo, remark)
WCDB_SYNTHESIZE(BuzUserInfo, becomeFriendTime)
WCDB_SYNTHESIZE(BuzUserInfo, isFriend)
WCDB_SYNTHESIZE(BuzUserInfo, userType)
WCDB_SYNTHESIZE(BuzUserInfo, serviceMuteMessages)
WCDB_SYNTHESIZE(BuzUserInfo, localMuteMessages)
WCDB_SYNTHESIZE(BuzUserInfo, muteNotification)
WCDB_SYNTHESIZE(BuzUserInfo, userStatus)

//WCDB_INDEX(BuzUserInfo, "_userid", userId)
WCDB_PRIMARY(BuzUserInfo, userId)

@end
