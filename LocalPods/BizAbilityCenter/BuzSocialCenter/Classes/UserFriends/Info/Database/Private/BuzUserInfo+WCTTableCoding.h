//
//  BuzUserInfo+WCTTableCoding.h
//  buz
//
//  Created by lizhi on 2022/7/14.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "BuzUserInfo.h"
#import "WCDB.h"

NS_ASSUME_NONNULL_BEGIN

@interface BuzUserInfo (WCTTableCoding)<WCTTableCoding>

WCDB_PROPERTY(userId)
WCDB_PROPERTY(buzId)
WCDB_PROPERTY(email)
WCDB_PROPERTY(firstName)
WCDB_PROPERTY(lastName)
WCDB_PROPERTY(userName)
WCDB_PROPERTY(phone)
WCDB_PROPERTY(portrait)
WCDB_PROPERTY(registerTime)
WCDB_PROPERTY(remark)
WCDB_PROPERTY(becomeFriendTime)
//WCDB_PROPERTY(relation)
WCDB_PROPERTY(isFriend)
WCDB_PROPERTY(userType)
WCDB_PROPERTY(serviceMuteMessages)
WCDB_PROPERTY(localMuteMessages)
WCDB_PROPERTY(muteNotification)
WCDB_PROPERTY(userStatus)

@end

NS_ASSUME_NONNULL_END
