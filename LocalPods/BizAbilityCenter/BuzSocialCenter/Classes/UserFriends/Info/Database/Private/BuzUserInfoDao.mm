//
//  BuzUserInfoDao.m
//  buz
//
//  Created by lizhi on 2022/7/14.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "BuzUserInfoDao.h"

#import "BuzUserInfo+WCTTableCoding.h"
#import "BuzDatabaseManager.h"
#import "OCBuzLog.h"
#import "OCBuzUserSession.h"

#define DATABASE [BuzDatabaseManager database]

@interface BuzUserInfoDao ()

@property (nonatomic, strong) NSMutableSet <NSNumber *>* createdTableSet;

@end

@implementation BuzUserInfoDao

#pragma mark - public
/** 更新联系人数据**/
- (void)addOrUpdateUserInfo:(NSArray <BuzUserInfo *>*)userInfoList complete:(nullable void (^)(BOOL))complete{
    
    if (userInfoList.count == 0) {
        [self executeTaskInMainQueue:^{
            complete(YES);
        }];
        return;
    }
    
    [self executeTaskInDatabaseQueue:^{
              
        NSString *tableName =  [self getTableName];
        __block BOOL isUpdateSuccess = YES;
        
        [DATABASE runTransaction:^BOOL{
            isUpdateSuccess = [DATABASE insertOrReplaceObjects:userInfoList into:tableName];
            BuzDBLogD(@"UserInfoDao 批量插入数据结果%d", isUpdateSuccess);
            return isUpdateSuccess;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"UserInfoDao 批量插入数据处理完成 -- 回调外界结果%d" , isUpdateSuccess);
                complete(isUpdateSuccess);
            }];
        }
    }];
}


/** 通过用户id删除用户数据**/
- (void)deleteUserInfoWithUserIdList:(NSArray <NSNumber *>*)uidList complete:(nullable void (^)(BOOL))complete{
    if (uidList.count == 0) {
        [self executeTaskInMainQueue:^{
            complete(YES);
        }];
        return;
    }
    
    [self executeTaskInDatabaseQueue:^{
       
        NSString *tableName = [self getTableName];
       
        __block BOOL isUpdateSuccess = YES;
        
        [DATABASE runTransaction:^BOOL{
            
            for (NSNumber *number in uidList) {
                isUpdateSuccess = [DATABASE deleteObjectsFromTable:tableName where:BuzUserInfo.userId == number.longLongValue];
            }
            return isUpdateSuccess;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"UserInfoDao - 删除数据处理完成 -- 回调外界结果%d" , isUpdateSuccess);
                complete(isUpdateSuccess);
            }];
        }
    }];
}

/** 通过手机号删除用户数据**/
- (void)deleteUserInfoWithPhoneList:(NSArray <NSString *>*)phoneList complete:(nullable void (^)(BOOL))complete{
    if (phoneList.count == 0) {
        [self executeTaskInMainQueue:^{
            complete(YES);
        }];
        return;
    }
    
    [self executeTaskInDatabaseQueue:^{
       
        NSString *tableName = [self getTableName];
        __block BOOL isUpdateSuccess = YES;
        
        [DATABASE runTransaction:^BOOL{
            
            for (NSString *phone in phoneList) {
                isUpdateSuccess = [DATABASE deleteObjectsFromTable:tableName where:BuzUserInfo.phone == phone];
            }
            return isUpdateSuccess;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"UserInfoDao - 通过手机号删除用户数据处理完成 -- 回调外界结果%d" , isUpdateSuccess);
                complete(isUpdateSuccess);
            }];
        }
    }];
}

/** 删除所有好友关系用户数据**/
- (void)deleteAllFriendComplete:(nullable void (^)(BOOL))complete{
       
    [self executeTaskInDatabaseQueue:^{
       
        NSString *tableName = [self getTableName];
         BOOL isUpdateSuccess = [DATABASE deleteObjectsFromTable:tableName where:BuzUserInfo.isFriend == YES];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"UserInfoDao - 删除所有好友关系用户处理完成 -- 回调外界结果%d" , isUpdateSuccess);
                complete(isUpdateSuccess);
            }];
        }
    }];
}

/** 根据uid获取联系人**/
- (void)getUserInfoWithUserId:(int64_t)userId complete:(void (^)(BuzUserInfo * _Nullable))complete{
    [self executeTaskInDatabaseQueue:^{
        
        NSString *tableName = [self getTableName];
        
        BuzUserInfo *model = [DATABASE getOneObjectOfClass:BuzUserInfo.class fromTable:tableName where:BuzUserInfo.userId == userId];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                complete(model);
            }];
        }
    }];
}

- (BuzUserInfo *)synGetUserInfoWithUserId:(int64_t)userId {
    return [DATABASE getOneObjectOfClass:BuzUserInfo.class fromTable:[self getTableName] where:BuzUserInfo.userId == userId];
}


/** 根据uid列表获取联系人**/
- (NSArray<BuzUserInfo *> *)syncGetUserInfoWithUserIdList:(NSArray <NSNumber *>*)uidList{
    NSString *tableName = [self getTableName];
    return [DATABASE getObjectsOfClass:BuzUserInfo.class fromTable:tableName where:BuzUserInfo.userId.in(uidList)];
}

/** 根据uid列表获取联系人**/
- (void)getUserInfoWithUserIdList:(NSArray <NSNumber *>*)uidList complete:(void (^)(NSArray <BuzUserInfo *>* _Nullable))complete{
    if (uidList.count == 0) {
        [self executeTaskInMainQueue:^{
            complete(@[]);
        }];
        return;
    }
    
    
    [self executeTaskInDatabaseQueue:^{
        
        NSString *tableName = [self getTableName];
                
        NSMutableArray *result = [NSMutableArray array];
      
        
        [DATABASE runTransaction:^BOOL{
            for(NSNumber *uid in uidList) {
                BuzUserInfo *model = [DATABASE getOneObjectOfClass:BuzUserInfo.class fromTable:tableName where:BuzUserInfo.userId == uid.longLongValue];

                if (model) {
                    [result addObject:model];
                }
            }
            return YES;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                complete(result);
            }];
        }
    }];
}

/** 根据手机号获取联系人**/
- (void)getUserInfoWithPhone:(NSString *)phone complete:(void (^)(BuzUserInfo * _Nullable))complete{
    [self executeTaskInDatabaseQueue:^{
        
        NSString *tableName = [self getTableName];
        
        BuzUserInfo *model = [DATABASE getOneObjectOfClass:BuzUserInfo.class fromTable:tableName where:BuzUserInfo.phone == phone];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                complete(model);
            }];
        }
    }];
}

/** 获取所有好友关系的用户数据**/
- (void)getAllFriendInfoComplete:(void (^)(NSArray <BuzUserInfo *>* nullable))complete{
    [self executeTaskInDatabaseQueue:^{
        NSString *tableName = [self getTableName];
        NSArray *result = [DATABASE getObjectsOfClass:BuzUserInfo.class fromTable:tableName where:BuzUserInfo.isFriend == YES];
        if (complete) {
            [self executeTaskInMainQueue:^{
                complete(result);
            }];
        }
    }];
}

/** 获取所有好友关系的用户数据，通过成为好友时间+注册试剂**/
- (void)getAllFriendInfoOrderByBecomeFriendTimeComplete:(void (^)(NSArray <BuzUserInfo *>* nullable))complete{
    [self executeTaskInDatabaseQueue:^{
        NSString *tableName = [self getTableName];
        NSArray *result = [DATABASE getObjectsOfClass:BuzUserInfo.class fromTable:tableName where:BuzUserInfo.isFriend == YES orderBy:{BuzUserInfo.becomeFriendTime.order(WCTOrderedDescending),BuzUserInfo.registerTime.order(WCTOrderedDescending)}];
        if (complete) {
            [self executeTaskInMainQueue:^{
                complete(result);
            }];
        }
    }];
}

/** 获取所有非好友关系的用户数据**/
- (void)getAllUnFriendInfoComplete:(void (^)(NSArray <BuzUserInfo *>* nullable))complete{
    [self executeTaskInDatabaseQueue:^{
        NSString *tableName = [self getTableName];
        NSArray *result = [DATABASE getObjectsOfClass:BuzUserInfo.class fromTable:tableName where:BuzUserInfo.isFriend == NO];
        if (complete) {
            [self executeTaskInMainQueue:^{
                complete(result);
            }];
        }
    }];
}

#pragma mark - private

- (void)executeTaskInDatabaseQueue:(void(^)(void))task
{
    buz_dispatch_queue_async_safe(buz_database_queue(),task);
}

- (void)executeTaskInMainQueue:(void(^)(void))task
{
    buz_dispatch_queue_async_safe(dispatch_get_main_queue(),task);
}


- (NSString *)getTableName
{   
    NSString *tableName = [NSString stringWithFormat:@"UserInfoTable"];
    NSNumber *uid = @(OCBuzUserSession.uid);
    
    if (![self.createdTableSet containsObject:uid]) {
        [DATABASE dropTableOfName:@"UserInfo"];
        BOOL result = [DATABASE createTableAndIndexesOfName:tableName withClass:BuzUserInfo.class];
        if (result) {
            [self.createdTableSet addObject:uid];
        }else{
            NSAssert(NO, @"create UserInfoTable table failure!!!");
            return nil;
        }
    }
    return tableName;
}


+ (instancetype)sharedInstance {
    static id instance = nil;
    if (instance == nil) {
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            instance = [[self alloc] init];
        });
    }
    return instance;
}

- (NSMutableSet<NSNumber *> *)createdTableSet
{
    if (nil == _createdTableSet)
    {
        _createdTableSet = [[NSMutableSet alloc] init];
    }
    return _createdTableSet;
}


@end
