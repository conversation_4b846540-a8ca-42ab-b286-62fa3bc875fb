//
//  BuzUserInfoExtion.swift
//  buz
//
//  Created by lizhi on 2022/12/8.
//  Copyright © 2022 lizhi. All rights reserved.
//

import BuzCenterKit

//MARK: ---BuzUserData----
extension BuzUserData {
    /// 数据库对象初始化
    /// - Parameter entity: dao实体对象
    convenience init(entity: BuzUserInfo) {
        self.init(userId: entity.userId,
                  base: BaseInfo.init(entity: entity),
                  relation: RelationInfo.init(entity: entity))
    }
    
    /// 数据库转换为DaoEntity
    /// - return: dao实体对象
    func convertDaoEntity() -> BuzUserInfo {
        let entity = BuzUserInfo.init()
        entity.userId = self.base.userId
        entity.buzId = self.base.buzId
        entity.email = self.base.email
        entity.firstName = self.base.firstName
        entity.lastName = self.base.lastName
        entity.userName = self.base.userName
        entity.registerTime = self.base.registerTime ?? 0
        entity.phone = self.base.phone
        entity.portrait = self.base.portrait
        
        entity.userStatus = self.base.userStatus.rawValue
        
        entity.remark = self.relation.remark
        entity.becomeFriendTime = self.relation.becomeFriendTime
        entity.isFriend = self.relation.isFriendFlag
        entity.userType = Int(self.base.userType.rawValue)
        entity.serviceMuteMessages = Int(self.relation.serviceMuteMessages.rawValue)
        entity.localMuteMessages = Int(self.relation.localMuteMessages.rawValue)
        entity.muteNotification = Int(self.relation.muteNotification.rawValue)
        
        return entity
    }
}

//MARK: ---BuzUserData temp----
public extension BuzUserData {
    static func user(entityData: Data)-> BuzUserData? {
        let info = BuzUserInfo.yy_model(withJSON: entityData)
        if let info = info {
             return .init(entity: info)
        }
        return nil
    }
    
    func entityData()-> Data? {
        let info = self.convertDaoEntity()
        if let data: Data = info.yy_modelToJSONData() {
            return data
        }
        return nil
    }
    
    func jsonString() ->String? {
        let info = self.convertDaoEntity()
        let json = info.yy_modelToJSONString()
        return json
    }
}

//MARK: ---BuzUserData.BaseInfo----
extension BuzUserData.BaseInfo {
    
    /// 数据库对象初始化
    /// - Parameter entity: dao实体对象
    convenience init(entity: BuzUserInfo) {
        self.init(userId: entity.userId)
        self.buzId = entity.buzId
        self.userType = .init(rawValue: Int32(entity.userType)) ?? .default
        self.userStatus = .init(rawValue: entity.userStatus) ?? .normal
        
        self.userName = entity.userName
        self.firstName = entity.firstName
        self.lastName = entity.lastName
        self.portrait = entity.portrait
        
        self.phone = entity.phone
        self.email = entity.email
        
        self.registerTime = entity.registerTime
    }
}

extension BuzUserData.RelationInfo {
    
    /// 数据库对象初始化
    /// - Parameter entity: dao实体对象
    convenience init(entity: BuzUserInfo) {
        self.init(userId: entity.userId)
        self.isFriendFlag = entity.isFriend
        
        self.remark = entity.remark
        self.becomeFriendTime = entity.becomeFriendTime
        self.serviceMuteMessages = .init(rawValue: Int32(entity.serviceMuteMessages)) ?? .unknow
        self.localMuteMessages = .init(rawValue: Int32(entity.localMuteMessages)) ?? .unknow
        self.muteNotification = .init(rawValue: Int32(entity.muteNotification)) ?? .unknow
    }
}
