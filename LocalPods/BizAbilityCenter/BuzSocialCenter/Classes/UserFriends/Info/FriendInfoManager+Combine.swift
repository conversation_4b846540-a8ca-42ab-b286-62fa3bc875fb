//
//  FriendInfoManager+Combine.swift
//  Pods
//
//  Created by st.chio on 2025/2/21.
//

import Combine
import BuzIDL
import BuzCenterKit
import BuzFoundation

public extension FriendInfoManager {
    class Publisher {
        // 用户状态变化
        public let userStateDidChanged = PassthroughSubject<([BuzFriendStatusInfo]), Never>()
        // 用户信息更新
        public let userInfoDidUpdated = PassthroughSubject<BuzUserData, Never>()
        // 删除好友
        public let friendsDidDeleted = PassthroughSubject<[BuzUserData], Never>()
        // 好友列表变化
        public let friendListDidChanged = PassthroughSubject<[BuzUserData], Never>()
        // 好友关系变化
        public let friendsRelationInfoChanged = PassthroughSubject<Void, Never>()
    }
}

// MARK: - sync server data
public extension BuzCombineKit where Base: FriendInfoManager {
    /// 从服务端获取所有好友数据并缓存数据库
    func syncAllFriends() -> AnyPublisher<Bool, Never> {
        return Future { promise in
            self.object.syncAllFriends { success in
                promise(.success((success)))
            }
        }
        .eraseToAnyPublisher()
    }
}

// MARK: - query DB data
public extension BuzCombineKit where Base: FriendInfoManager {
    /// 查询单个联系人信息
    func queryUserInfoFromDB(userId: Int64) -> AnyPublisher<BuzUserData?, Never> {
        return Future { promise in
            self.object.queryUserInfoFromDB(userId: userId) { user in
                promise(.success(user))
            }
        }
        .eraseToAnyPublisher()
    }

    /// 查询多个联系人信息
    func queryUserInfoFromDB(userIds: [Int64]) -> AnyPublisher<[BuzUserData]?, Never> {
        return Future { promise in
            self.object.queryUserInfoFromDB(userIds: userIds) { users in
                promise(.success(users))
            }
        }
        .eraseToAnyPublisher()
    }
}

// MARK: - query local data (cache or DB data)
public extension BuzCombineKit where Base: FriendInfoManager {
    /// 获取本地缓存信息，没有也不会去网络请求，可能为空
    func queryUserInfo(userId: Int64) -> AnyPublisher<BuzUserData?, Never> {
        let userInfo = self.object.queryUserInfo(userId: userId)
        
        return Just(userInfo)
            .setFailureType(to: Never.self)
            .eraseToAnyPublisher()
    }

    /// 查询多个联系人信息（通过 ID）
    func queryUserInfo(userIds: [Int64]) -> AnyPublisher<[Int64:BuzUserData], Never> {
        let users = self.object.queryUserInfo(userIds: userIds)
        return Just(users)
            .setFailureType(to: Never.self)
            .eraseToAnyPublisher()
    }

    /// 查询联系人信息（通过 UserInfo）
    func queryUserInfo(userInfos: [UserInfo]) -> AnyPublisher<[BuzUserData], Never> {
        return Future { promise in
            self.object.queryUserInfo(userInfos: userInfos) { users in
                promise(.success(users))
            }
        }
        .eraseToAnyPublisher()
    }

    /// 查询联系人信息（通过 BuzUserData）
    func queryUserInfo(buzUsers: [BuzUserData]) -> AnyPublisher<[BuzUserData], Never> {
        return Future { promise in
            self.object.queryUserInfo(buzUsers: buzUsers) { users in
                promise(.success(users))
            }
        }
        .eraseToAnyPublisher()
    }
}

// MARK: - fetch data (get cache or request data)
public extension BuzCombineKit where Base: FriendInfoManager {
    func mergeFetchUserInfo(userId: Int64?, fetchType: BuzFetchUserType = .cacheOrNetwork) -> AnyPublisher<(BuzUserData?, Bool), Never> {
        return Future { promise in
            self.object.mergeFetchUserInfo(userId: userId, fetchType: fetchType) { contactsInfo, isDeleted in
                promise(.success((contactsInfo, isDeleted)))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 查询单个联系人信息，先返回缓存信息
    func fetchUserInfo(userId: Int64, fromNetWork: Bool = false) -> AnyPublisher<FetchUserDataResult, Never> {
        let subject = PassthroughSubject<FetchUserDataResult, Never>()
        
        self.object.fetchUserInfo(userId: userId, fromNetWork: fromNetWork, cacheHandler: { user in
            subject.send(FetchUserDataResult(source: .cache, data: user))
        }, completion: { user in
            subject.send(FetchUserDataResult(source: .complete, data: user))
            subject.send(completion: .finished)
        })
        
        return subject.eraseToAnyPublisher()
    }
    
    /// 批量查询用户信息
    func fetchBatchUserInfos(userIds: [Int64], forceRefresh: Bool = false) -> AnyPublisher<(Bool, [BuzUserData]), Never> {
        return Future { promise in
            self.object.fetchBatchUserInfos(userIds: userIds, forceRefresh: forceRefresh, completion: { status, users in
                promise(.success((status, users)))
            })
        }
        .eraseToAnyPublisher()
    }
}

// MARK: - request data (only request and cache data)
public extension BuzCombineKit where Base: FriendInfoManager {
    enum DataSource {
        case cache
        case complete
    }

    struct FetchUserDataResult {
        let source: DataSource
        let data: BuzUserData?
    }
    
    func requestUserInfo(userId: Int64) -> AnyPublisher<Void, Never> {
        self.object.requestUserInfo(userId: userId)
        return Empty(completeImmediately: true).eraseToAnyPublisher()
    }
    
    /// 查询单个联系人信息
    func requestUserInfo(userId: Int64?, phone: String?) -> AnyPublisher<FetchUserDataResult, Never> {
        return Future { promise in
            self.object.requestUserInfo(userId: userId, phone: phone) { user in
                promise(.success(.init(source: .complete, data: user)))
            }
        }.eraseToAnyPublisher()

    }
    
    func requestBatchUserInfos(userIds:[Int64]) -> AnyPublisher<[BuzUserData], Never> {
        return Future { promise in
            self.object.requestBatchUerInfos(userIds: userIds) { users in
                promise(.success(users))
            }
        }
        .eraseToAnyPublisher()
    }
}

// MARK: - checkUnknowUsers
public extension BuzCombineKit where Base: FriendInfoManager {
    func checkUnknowUsers(useridList: Set<Int64>) -> AnyPublisher<Void, Never> {
        self.object.checkUnknowUsers(useridList: useridList)
        return Empty(completeImmediately: true).eraseToAnyPublisher()
    }
}

// MARK: - 用户信息操作
public extension BuzCombineKit where Base: FriendInfoManager {
    func updateLocalUserInfo(info: BuzUserData) -> AnyPublisher<Bool, Never> {
        return Future { promise in
            self.object.updateLocalUserInfo(info: info) { success in
                promise(.success(success))
            }
        }
        .eraseToAnyPublisher()
    }

    func addNewLocalUserInfo(info: BuzUserData) -> AnyPublisher<Bool, Never> {
        return Future { promise in
            self.object.addNewLocalUserInfo(info: info) { success in
                promise(.success(success))
            }
        }
        .eraseToAnyPublisher()
    }
}
