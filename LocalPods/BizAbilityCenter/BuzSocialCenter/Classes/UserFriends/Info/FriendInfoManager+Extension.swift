//
//  Untitled.swift
//  Pods
//
//  Created by st.chio on 2025/2/21.
//

import BuzCenterKit

//MARK: -用户信息查询
public extension FriendInfoManager {
    
    /// 查询好友对讲机在线状态(仅可查好友，本地数据)
    /// - Parameter uid: 用户ID
    /// - Returns: 是否在线
    func getFriendWalkieTalkieOnlineState(uid: Int64)-> Bool {
        if uid == self.officalUser?.userId {
            return true
        }
        return allFriendInfo[uid]?.walkieTalkieOnlineTime ?? 0 > 0
    }
    
    //好友对讲机上线时间
    func getFriendWalkieTalkieOnlineTime(uid: Int64) -> Int64 {
        return allFriendInfo[uid]?.walkieTalkieOnlineTime ?? 0
    }
    
    //在线好友数
    func getFriendWalkieTalkieOnlineCount() -> Int {
        return self.allFriendInfo.values.count(where: {$0.walkieTalkieOnlineTime ?? 0 > 0})
    }
    
    //好友QuietMode状态
    func getFriendQuietMode(uid: Int64) -> BuzUserQuietMode {
        let value = allFriendInfo[uid]?.quietMode ?? 0
        return  BuzUserQuietMode(rawValue: value) ?? BuzUserQuietMode.unknow
    }
    
    
    //好友的语言
    func getFriendLanguage(uid: Int64) -> String? {
        let value = allFriendInfo[uid]?.language
        return value
    }
    
    //是否是好友
    func isFriend(uid: Int64) -> Bool {
        return allFriends.contains(where: {$0.userId == uid})
    }
    
    //是否是好友
    func isDeleteAccount(uid: Int64) -> Bool {
        if let value = self.deletedAccounts[uid] {
            return value == false
        }
        
        return false
    }
}
