//
//  BlackUserDao.m
//  buz
//
//  Created by lizhi on 2022/8/9.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "BlackUserDao.h"
#import "BuzDatabaseManager.h"
#import "OCBuzLog.h"
#import "BlackUserModel+WCTTableCoding.h"
#import "OCBuzUserSession.h"

#define DATABASE [BuzDatabaseManager database]

@interface BlackUserDao ()

@property (nonatomic, strong) NSMutableSet <NSNumber *>* createdTableSet;

@end

@implementation BlackUserDao

//// 所有操作仅针对当前登录用户操作
///同一个联系人可能对应了设备登录过的多个账号，只对当前登录账号的数据做操作

/** 更新黑名单用户数据**/
- (void)addOrUpdateBlackUser:(NSArray <BlackUserModel *>*)blackList complete:(nullable void (^)(BOOL))complete{
    
    if (blackList.count == 0) {
        [self executeTaskInMainQueue:^{
            complete(YES);
        }];
        return;
    }
    
    [self executeTaskInDatabaseQueue:^{
              
        NSString *tableName =  [self getTableName];
        __block BOOL isUpdateSuccess = YES;
        
        [DATABASE runTransaction:^BOOL{
            isUpdateSuccess = [DATABASE insertOrReplaceObjects:blackList.copy into:tableName];
            BuzDBLogD(@"BlackUserDao 批量插入数据结果%d", isUpdateSuccess);
            return isUpdateSuccess;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"BlackUserDao 批量插入数据处理完成 -- 回调外界结果%d" , isUpdateSuccess);
                complete(isUpdateSuccess);
            }];
        }
    }];
}


/** 删除黑名单用户数据**/
- (void)deleteBlackUser:(NSArray <NSNumber *>*)uidList complete:(nullable void (^)(BOOL))complete{
    if (uidList.count == 0) {
        [self executeTaskInMainQueue:^{
            complete(YES);
        }];
        return;
    }
    
    [self executeTaskInDatabaseQueue:^{
       
        NSString *tableName = [self getTableName];
       
        __block BOOL isUpdateSuccess = YES;
        
        [DATABASE runTransaction:^BOOL{
            
            for (NSNumber *number in uidList) {
                isUpdateSuccess = [DATABASE deleteObjectsFromTable:tableName where:BlackUserModel.uid == number.longLongValue];
            }
            return isUpdateSuccess;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"BlackUserDao - 删除数据处理完成 -- 回调外界结果%d" , isUpdateSuccess);
                complete(isUpdateSuccess);
            }];
        }
    }];
}

/** 删除黑名单用户数据**/
- (void)deleteAllBlackUserComplete:(nullable void (^)(BOOL))complete{
    [self executeTaskInDatabaseQueue:^{
       
        NSString *tableName = [self getTableName];
       
        __block BOOL isUpdateSuccess = YES;
        
        [DATABASE runTransaction:^BOOL{
            
            isUpdateSuccess = [DATABASE deleteAllObjectsFromTable:tableName];
            return isUpdateSuccess;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                BuzDBLogD(@"BlackUserDao - 删除数据处理完成 -- 回调外界结果%d" , isUpdateSuccess);
                complete(isUpdateSuccess);
            }];
        }
    }];
}

- (void)checkIsBlackUser:(int64_t)uid complete:(nullable void (^)(BOOL isBlack))complete{
    [self executeTaskInDatabaseQueue:^{
              
        NSString *tableName =  [self getTableName];
        __block BlackUserModel *model;
        
        [DATABASE runTransaction:^BOOL{
            model = [DATABASE getOneObjectOfClass:BlackUserModel.class fromTable:tableName where:BlackUserModel.uid == uid];
            return YES;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                complete(model.uid > 0);
            }];
        }
    }];
}

- (void)getAllBlackUserListOnComplete:(nullable void (^)(NSArray<BlackUserModel *> *blackUsers))complete{
    [self executeTaskInDatabaseQueue:^{
              
        NSString *tableName =  [self getTableName];
        __block NSArray<BlackUserModel *> *dataArray;
        
        [DATABASE runTransaction:^BOOL{
            dataArray = [DATABASE getAllObjectsOfClass:BlackUserModel.class fromTable:tableName];
            return YES;
        }];
        
        if (complete) {
            [self executeTaskInMainQueue:^{
                complete(dataArray);
            }];
        }
    }];
}

#pragma mark - private

- (void)executeTaskInDatabaseQueue:(void(^)(void))task
{
    buz_dispatch_queue_async_safe(buz_database_queue(),task);
}

- (void)executeTaskInMainQueue:(void(^)(void))task
{
    buz_dispatch_queue_async_safe(dispatch_get_main_queue(),task);
}


- (NSString *)getTableName
{
    NSString *tableName = [NSString stringWithFormat:@"BlackListTable"];
    NSNumber *uid = @(OCBuzUserSession.uid);
    
    if (![self.createdTableSet containsObject:uid]) {
        
        BOOL result = [DATABASE createTableAndIndexesOfName:tableName withClass:BlackUserModel.class];
        if (result) {
            [self.createdTableSet addObject:uid];
        }else{
            NSAssert(NO, @"create UserInfo table failure!!!");
            return nil;
        }
    }
    return tableName;
}


+ (instancetype)sharedInstance {
    static id instance = nil;
    if (instance == nil) {
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            instance = [[self alloc] init];
        });
    }
    return instance;
}

- (NSMutableSet<NSNumber *> *)createdTableSet
{
    if (nil == _createdTableSet)
    {
        _createdTableSet = [[NSMutableSet alloc] init];
    }
    return _createdTableSet;
}

@end
