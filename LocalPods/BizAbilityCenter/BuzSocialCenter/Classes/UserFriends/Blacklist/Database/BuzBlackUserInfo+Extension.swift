//
//  BuzUserInfoExtion.swift
//  buz
//
//  Created by lizhi on 2022/12/8.
//  Copyright © 2022 lizhi. All rights reserved.
//

public extension BuzBlackUserInfo {
    
    convenience init(userId: Int64) {
        self.init()
        self.userId = userId
    }
    
    convenience init(userId: Int64, phone: String? = "") {
        self.init()
        self.userId = userId
        self.blackPhone = phone
    }
}

extension BuzBlackUserInfo {
    /// 数据库对象初始化
    /// - Parameter entity: dao实体对象
    convenience init(entity: BlackUserModel) {
        self.init()
        self.userId = entity.uid
        self.blackPhone = entity.blackPhone
    }
    
    /// 数据库转换为DaoEntity
    /// - return: dao实体对象
    func convertDaoEntity() -> BlackUserModel {
        let entity = BlackUserModel.init()
        entity.uid = self.userId
        entity.blackPhone = self.blackPhone
        
        return entity
    }
}
