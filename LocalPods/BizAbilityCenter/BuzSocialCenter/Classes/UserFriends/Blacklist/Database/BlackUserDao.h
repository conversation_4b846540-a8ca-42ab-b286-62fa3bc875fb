//
//  BlackUserDao.h
//  buz
//
//  Created by lizhi on 2022/8/9.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@class BlackUserModel;

@interface BlackUserDao : NSObject

+ (instancetype)sharedInstance;

- (NSString *)getTableName;

//// 所有操作仅针对当前登录用户操作
///同一个联系人可能对应了设备登录过的多个账号，只对当前登录账号的数据做操作

/** 更新黑名单用户数据**/
- (void)addOrUpdateBlackUser:(NSArray <BlackUserModel *>*)blackList complete:(nullable void (^)(BOOL))complete;


/** 删除黑名单用户数据**/
- (void)deleteBlackUser:(NSArray <NSNumber *>*)uidList complete:(nullable void (^)(BOOL))complete;

/** 删除黑名单用户数据**/
- (void)deleteAllBlackUserComplete:(nullable void (^)(BOOL))complete;

//是否是黑名单用户
- (void)checkIsBlackUser:(int64_t)uid complete:(nullable void (^)(BOOL isBlack))complete;

/// 获取所有的黑名单用户
- (void)getAllBlackUserListOnComplete:(nullable void (^)(NSArray<BlackUserModel *> *blackUsers))complete;

@end

NS_ASSUME_NONNULL_END
