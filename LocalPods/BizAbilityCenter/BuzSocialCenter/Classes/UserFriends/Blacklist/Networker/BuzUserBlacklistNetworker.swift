//
//  BuzUserBlacklistNetworker.swift
//  buz
//
//  Created by st.chio on 2025/2/14.
//  Copyright © 2025 lizhi. All rights reserved.
//

import BuzLog
import BuzIDL
import ITNetLibrary
import BuzNetworker


class BuzUserBlacklistNetworker {
    typealias CompletionHandler = (_ rcode: Int) -> Void
    typealias UserInfoListCompletion = (_ rcode: Int, _ userList: [UserInfo]?) -> Void
    
    static func queryBlockedList(completion handler: @escaping UserInfoListCompletion) {
        let client = BuzNetUserServiceClient(encrypt: true)
        let req = RequestGetBlackList()
        _ = client.getBlackList(request: req, completion: { result in
            switch result {
            case let .success(responseInfo):
                BuzLog.info("blackUserList - \(String(describing: responseInfo.data?.blackUserList))")
                handler(responseInfo.code, responseInfo.data?.blackUserList)
            case .failure:
                handler(NetworkErrorCode, nil)
            }
        })
    }
    
    static func black(userId: Int64, completion handler: CompletionHandler?) {
        let client = BuzNetUserServiceClient(encrypt: true)
        let req = RequestOperateBlackList(userIds: [userId], type: 1)
        _ = client.operateBlackList(request: req) { result in
            switch result {
            case let .success(responseInfo):
                handler?(responseInfo.code)
            case .failure:
                handler?(NetworkErrorCode)
            }
        }
    }
    
    static func unblock(userId: Int64, completion handler: CompletionHandler?) {
        let client = BuzNetUserServiceClient(encrypt: true)
        let req = RequestOperateBlackList(userIds: [userId], type: 2)
        _ = client.operateBlackList(request: req) { result in
            switch result {
            case let .success(responseInfo):
                handler?(responseInfo.code)
            case .failure:
                handler?(NetworkErrorCode)
            }
        }
    }
}
