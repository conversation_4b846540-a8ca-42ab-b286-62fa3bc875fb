//
//  BlackUserManager+Combine.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/24.
//

import Combine
import BuzIDL
import BuzCenterKit
import BuzFoundation

public extension BlackUserManager {
    class Publisher {
        /// 拉黑好友
        public let didBlockUser = PassthroughSubject<BuzBlackUserInfo, Never>()
    }
}

public extension BuzCombineKit where Base: BlackUserManager {
    
    /// 清除缓存（无返回值，用 `Empty`）
    func cleanDataWhenLogout() -> AnyPublisher<Void, Never> {
        self.object.cleanDataWhenLogout()
        return Empty().eraseToAnyPublisher()
    }

    /// 用户是否是黑名单用户
    func checkIsBlackUser(userId: Int64) -> AnyPublisher<Bool, Never> {
        Future { promise in
            self.object.checkIsBlackUser(userId: userId) { isBlack in
                promise(.success(isBlack))
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 查询所有拉黑用户
    func queryAllBlackList() -> AnyPublisher<(Bool, [UserInfo]?), Never> {
        Future { promise in
            self.object.queryAllBlackList { success, userList in
                promise(.success((success, userList)))
            }
        }
        .eraseToAnyPublisher()
    }

    /// 从数据库查询所有拉黑用户
    func queryAllBlackListFromDB() -> AnyPublisher<[BuzBlackUserInfo], Never> {
        Future { promise in
            self.object.queryAllBlackListFromDB { userList in
                promise(.success(userList))
            }
        }
        .eraseToAnyPublisher()
    }

    /// 拉黑用户
    func blockUser(blackModel: BuzBlackUserInfo) -> AnyPublisher<Bool, Never> {
        Future { promise in
            self.object.blockUser(blackModel: blackModel) { success in
                promise(.success(success))
            }
        }
        .eraseToAnyPublisher()
    }

    /// 解除拉黑用户
    func unBlockUser(uid: Int64) -> AnyPublisher<Bool, Never> {
        Future { promise in
            self.object.unBlockUser(uid: uid) { success in
                promise(.success(success))
            }
        }
        .eraseToAnyPublisher()
    }
}
