//
//  BlackUserManager.swift
//  buz
//
//  Created by lizhi on 2022/12/9.
//  Copyright © 2022 lizhi. All rights reserved.
//

import UIKit
import BuzIDL

@objc
public protocol BuzFriendCenterBlacklistObserver: BuzFriendCenterObserver {
    ///拉黑好友
    @objc optional func blackUserManagerDidBlockUser(_ mgr: BlackUserManager , userModel : BuzBlackUserInfo)
    
}

public class BlackUserManager: NSObject, BuzSocialControllerable {
    typealias Center = BuzFriendCenter
    weak var center: BuzFriendCenter?
    
    public private(set) var publisher = Publisher.init()
    
    public typealias CompletionHandler = (_ success: Bool) -> Void
    //拉黑用户ID
    public private(set) var blackUserIdSet = Set<Int64>()
    
    required init(center: BuzFriendCenter?) {
        self.center = center
        super.init()
    }
}

public extension BlackUserManager {
    //清除缓存
    func cleanDataWhenLogout(){
        blackUserIdSet.removeAll()
    }
        
    //用户是否是黑名单用户
    func checkIsBlackUser(userId: Int64, complete:((_ isBlack: Bool)-> Void)?){
        BlackUserDao.sharedInstance().checkIsBlackUser(userId) { isBlack in
            complete?(isBlack)
        }
    }
    
    //查询所有拉黑用户
    func queryAllBlackList(complete:((_ success: Bool, _ userList: [UserInfo]?)-> Void)?){
        BuzUserBlacklistNetworker.queryBlockedList { [weak self] rcode, userList in
            guard let this = self else {
                return
            }
            if rcode != 0 {
                complete?(false, nil)
                return
            }
                        
            let list = userList?.map({ info in
                BuzBlackUserInfo.init(userId: Int64(info.userId ?? 0), phone: info.phone)
            })  ?? []
                        
            let ids = userList?.compactMap({$0.userId})
            this.blackUserIdSet = Set(ids ?? [])
            
            this.saveAllBlackList(userList: list) { success in
                complete?(success, userList)
            }
        }
    }
    
    //从数据库查询所有拉黑用户
    func queryAllBlackListFromDB(complete: (([BuzBlackUserInfo]) -> Void)?) {
        BlackUserDao.sharedInstance().getAllBlackUserList { models in
            complete?(models.map({BuzBlackUserInfo.init(entity: $0)}))
        }
    }
    
    //拉黑用户
    func blockUser(blackModel: BuzBlackUserInfo, completion handler: CompletionHandler?){
        if blackModel.userId <= 0 {
            return
        }
        
        var isBlockSucceed = false
        
        let group = DispatchGroup()
        group.enter()
        BuzFriendCenter.info.queryUserInfo(userId: blackModel.userId) { user in
            // 通讯录需要过滤被拉黑的用户，是用手机号进行匹配的，因此入库时必须要有手机号
            // 这里查出用户的手机号，防止外层传空的手机号过来
            blackModel.blackPhone = user?.base.phone ?? ""
            group.leave()
        }
       
        group.enter()
        BuzUserBlacklistNetworker.black(userId: blackModel.userId) { rcode in
            isBlockSucceed = (rcode == 0)
            group.leave()
        }
        
        group.notify(queue: DispatchQueue.main) { [weak self] in
            guard let `self` = self else { return }
            
            guard isBlockSucceed else {
                handler?(false)
                return
            }
            
            self.blackUserIdSet.insert(blackModel.userId)
            
            BlackUserDao.sharedInstance().addOrUpdateBlackUser([blackModel.convertDaoEntity()]) { status in
                self.center?.notifyObservers(of: BuzFriendCenterBlacklistObserver.self) { observer in
                    observer.blackUserManagerDidBlockUser?(self, userModel: blackModel)
                }
                self.publisher.didBlockUser.send(blackModel)
                handler?(status)
            }
        }
    }
    
    //解除拉黑用户
    func unBlockUser(uid: Int64, completion handler: CompletionHandler?){
        if uid <= 0 {
            return
        }
        
        BuzUserBlacklistNetworker.unblock(userId: uid) { rcode in
            if rcode != 0 {
                handler?(false)
                return
            }
            self.blackUserIdSet.remove(uid)
            BlackUserDao.sharedInstance().deleteBlackUser([NSNumber.init(value: uid)]) { status in
                handler?(true)
            }
        }
    }
}

private extension BlackUserManager {
    //更新所有黑名单
    func saveAllBlackList(userList: [BuzBlackUserInfo], completion handler: CompletionHandler?){
        
        BlackUserDao.sharedInstance().deleteAllBlackUserComplete { status in
            if status == false {
                handler?(false)
                return
            }
            BlackUserDao.sharedInstance().addOrUpdateBlackUser(userList.map({$0.convertDaoEntity()})) { status in
                handler?(status)
            }
        }
    }
}
