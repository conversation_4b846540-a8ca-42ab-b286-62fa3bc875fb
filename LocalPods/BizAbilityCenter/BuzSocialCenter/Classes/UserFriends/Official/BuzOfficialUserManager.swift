//
//  OfficialAccountInfoManager.swift
//  buz
//
//  Created by <PERSON> on 26/07/2024.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation
import BuzIDL
import MMKV
import BuzLocalizable
import Localizable
import BuzLog
import BuzDataStore
import BuzCenterKit
import BuzNetworker

enum OfficialUserStoreKey: String {
    case officialUserList = "OfficialAccountStoreKey_officialAccountList"
}

@objc
public protocol BuzFriendCenterOfficialObserver: BuzFriendCenterObserver {
    @objc optional func officialAccountInfoManagerHasNewAccount(_ mgr: BuzOfficialUserManager)
    @objc optional func officialAccountInfoManagerListDataComplete(_ mgr: BuzOfficialUserManager)
}

public class BuzOfficialAccountInfo : NSObject {
    var userInfo : BuzUserData?
}

public class BuzOfficialUserManager: NSObject, BuzSocialControllerable {
    typealias Center = BuzFriendCenter
    weak var center: BuzFriendCenter?
    
    public private(set) var publisher = Publisher.init()
    
    public private(set) var officialAccountDict = [Int64: OfficialAccountUserInfo]()
    public private(set) var officialAccountList = [OfficialAccountUserInfo]()
    private var isRequestingOfficialAccountList: Bool = false
    
    required init(center: BuzFriendCenter?) {
        super.init()
        self.center = center
        queryOfficialAccounts(completion: nil)
    }
}

public extension BuzOfficialUserManager {
    func syncOfficialUsers(completion: (() -> Void)? = nil){
        //Avoid making redundant requests or performing actions while a previous request is still being processed
        if isRequestingOfficialAccountList {
            return
        }
        
        isRequestingOfficialAccountList = true
        OfficialUserNetwork.getOfficialAccounts(completion: { response in
            self.isRequestingOfficialAccountList = false
            
            let callbackDelegate = {
                self.center?.notifyObservers(of: BuzFriendCenterOfficialObserver.self) {observer in
                    observer.officialAccountInfoManagerListDataComplete?(self)
                }
                self.publisher.listDataCompleted.send()
                
                completion?()
            }
            guard response.isSuccess else {
                return
            }
            
            let tempList = response.rawResponseObj?.data?.officialAccountList ?? []
            tempList.forEach { info in
                guard let id = info.userRelationInfo.userInfo.userId else { return }
                self.officialAccountDict[id] = info
            }
            
            // 存储到本地
            self.officialAccountList = tempList
            
            do {
                let data =  try JSONEncoder().encode(tempList)
                MMKV.buz_async { mmkv in
                    mmkv.set(data, forKey: OfficialUserStoreKey.officialUserList.rawValue)
                    BuzLog.info("Encode official account info success \(tempList)")
                }
            } catch {
                BuzLog.error("Encode official account info error \(error) from \(tempList)")
            }
            
            callbackDelegate()
        })
    }
}

public extension BuzOfficialUserManager {
    
    func isOfficialAccount(_ userId: Int64) -> Bool {
        if let officialAccount = self.officialAccountDict[userId],
           let userType = BuzUserType.init(rawValue: officialAccount.userRelationInfo.userInfo.userType),
           userType == .officialAccount{
            return true
        }
        
        return false
    }
    
    func isFriend(_ userId: Int64) -> Bool {
        if let data = self.officialAccountDict[userId],
           let relationType = BuzUserRelation.init(rawValue: data.userRelationInfo.userRelation.relation) {
            return relationType == .friend
        }
        
        return false
    }
}

private extension BuzOfficialUserManager {
    
    func queryOfficialAccounts(completion: (([Int64: OfficialAccountUserInfo]) -> Void)?){
        MMKV.buz_async { [weak self] mmkv in
            if let data = mmkv.data(forKey: OfficialUserStoreKey.officialUserList.rawValue) {
                do {
                    let tempList = try JSONDecoder().decode(Array<OfficialAccountUserInfo>.self, from: data)
                    
                    tempList.forEach { info in
                        guard let id = info.userRelationInfo.userInfo.userId else { return }
                        self?.officialAccountDict[id] = info
                    }
                    
                    self?.officialAccountList = tempList
                    
                } catch {
                    BuzLog.error("Decode OfficialAccountList error \(error)")
                }
            }
        } complete: {
            if self.officialAccountDict.isEmpty {
                self.syncOfficialUsers {
                    completion?(self.officialAccountDict)
                }
            } else {
                completion?(self.officialAccountDict)
            }
        }
    }
}

internal extension BuzOfficialUserManager {
    // 添加好友
    func friendRelationAddedFriend(_ userId: Int64) {
        for i in 0..<self.officialAccountList.count {
            if userId == self.officialAccountList[i].userRelationInfo.userInfo.userId {
                self.officialAccountList[i].userRelationInfo.userRelation.relation = Int32(BuzUserRelation.friend.rawValue)
            }
        }
    }
    
    // 解除好友关系
    func friendRelationDeleteFriend(_ userId: Int64) {
        for i in 0..<self.officialAccountList.count {
            if userId == self.officialAccountList[i].userRelationInfo.userInfo.userId {
                self.officialAccountList[i].userRelationInfo.userRelation.relation = Int32(BuzUserRelation.notFriend.rawValue)
            }
        }
    }
}

extension BuzOfficialUserManager: BuzSignalPushEventObserver {
    
    public func signalPushDidReceivedMessage(_ message: BuzSignalPush.Message) {
        guard let payload = message.payload else {
            return
        }
        switch payload.op {
        case .pushDataChange:
            let type = BuzSignalPush.PushDataChange.init(rawValue: payload.businessType ?? 0)
            switch type {
            case .OfficailAccountStateChange: // 22 - official资料变更
                BuzSignalPushLog.info("official资料变更")
                
                self.syncOfficialUsers()
            default:
                break
            }
        default:
            break
        }
    }
}
