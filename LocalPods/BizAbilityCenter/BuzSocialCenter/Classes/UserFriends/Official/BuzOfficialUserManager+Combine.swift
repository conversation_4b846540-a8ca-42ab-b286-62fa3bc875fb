//
//  BuzOfficialUserManager+Combine.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/24.
//

import Combine
import BuzIDL
import BuzCenterKit
import BuzFoundation

public extension BuzOfficialUserManager {
    class Publisher {
        /// 有新的公众号账号
        public let hasNewAccount = PassthroughSubject<Void, Never>()
        
        /// 公众号列表数据加载完成
        public let listDataCompleted = PassthroughSubject<Void, Never>()
    }
}

public extension BuzCombineKit where Base: BuzOfficialUserManager {
    
    /// 请求并缓存公众号账号列表
    func syncOfficialUsers() -> AnyPublisher<Void, Never> {
        Future { promise in
            self.object.syncOfficialUsers {
                promise(.success(()))
            }
        }
        .eraseToAnyPublisher()
    }
}

