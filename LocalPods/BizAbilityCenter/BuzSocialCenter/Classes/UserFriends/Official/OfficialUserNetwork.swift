//
//  OfficialUserNetwork.swift
//  buz
//
//  Created by <PERSON> on 29/07/2024.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation
import BuzIDL
import ITNetLibrary
import BuzNetworker
import BuzLog

struct OfficialUserNetwork {
    @discardableResult
    static func getOfficialAccounts(completion: ((BuzNetworkResponse<ITResponse<ResponseGetOfficialAccountList>>) -> Void)?) -> ITFuture {
        let client = BuzNetUserServiceClient()
        let request = RequestGetOfficialAccountList()
        
        return client.getOfficialAccountList(request: request) { result in
            switch result {
            case .success(let responseInfo):
                completion?(BuzNetworkResponse.init(rcode: responseInfo.code, isSuccess: responseInfo.code == 0, rawResponseObj: responseInfo))
                BuzLog.info("getOfficialAccountList request success")
            case .failure(let error):
                completion?(BuzNetworkResponse.init(rcode: NetworkRcode.requestFailure.rawValue, isSuccess: false, rawResponseObj: nil, error: error))
                BuzLog.error("getOfficialAccountList request error. error = \(error)")
            }
        }
    }
}


