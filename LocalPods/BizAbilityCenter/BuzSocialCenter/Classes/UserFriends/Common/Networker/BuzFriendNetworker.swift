//
//  BuzFriendNetworker.swift
//  buz
//
//  Created by st.chio on 2025/2/14.
//  Copyright © 2025 lizhi. All rights reserved.
//

import BuzLog
import BuzIDL
import ITNetLibrary
import BuzNetworker

let NetworkErrorCode = NetworkRcode.requestFailure.rawValue

class BuzFriendNetworker {
    
    /// 获取用户信息
    /// - Parameters:
    ///   - userId: 用户id
    ///   - complete: 结果
    static func getUserInfo(userId: Int64?, phone: String?, complete:((_ code: Int, _ userInfo: UserInfo?, _ userRelation: UserRelation?) -> Void)?){
        
        let client = BuzNetUserServiceClient(encrypt: true)
        let _ = client.getUserInfo(request: RequestGetUserInfo.init(userId: userId, phone: phone)) { result in
            switch result {
            case .success(let responseInfo):
                complete?(responseInfo.code , responseInfo.data?.userInfo, responseInfo.data?.userRelation)
            case .failure(let err):
                complete?(err.toITNetError().code , nil, nil)
            }
        }
    }
    
    /// 批量查询用户信息
    /// - Parameters:
    ///   - userIdList: 用户ID列表
    ///   - complete: 回调
    static func getUserInfoList(_ userIdList: [Int64], complete:((_ code: Int, _ data: [UserInfo]) -> Void)?){
        let _ = BuzNetUserServiceClient(encrypt: true).getUserInfoList(request: RequestGetUserInfoList.init(userIdList: userIdList)) { result in
            switch result {
            case .success(let responseInfo):
                complete?(responseInfo.code , responseInfo.data?.userInfoList ?? [])
            case .failure(let err):
                complete?(err.toITNetError().code , [])
            }
        }
    }
}

extension BuzFriendNetworker {
    /// 获取好友列表(全量)
    /// - Parameters:
    ///   - timestamp: 时间戳
    ///   - complete: 结果
    static func getFriendList(timestamp: Int64, complete: ((_ rcode: Int, _ info: ResponseGetFriendList?) -> Void)?) {
        BuzLog.debug("home api getFriendList timestamp:\(timestamp)")
        let client = BuzNetUserServiceClient(encrypt: true)
        _ = client.getFriendList(request: RequestGetFriendList(timestamp: timestamp)) { result in
            switch result {
            case let .success(responseInfo):
                complete?(responseInfo.code, responseInfo.data)
            case .failure:
                complete?(NetworkErrorCode, nil)
            }
        }
    }
}

extension BuzFriendNetworker {
    /// 设置静音状态
    /// - Parameters:
    ///   - userId: 用户id
    ///   - muteMessages: 是否静音群组消息
    ///   - muteNotification: 是否静音群组通知
    ///   - completion: 结果
    static func updateUserMuteState(with userId: Int64, muteMessages: Bool?, muteNotification: Bool?, completion:((Int) -> Void)?) {
        var messagesValue: Int32?
        if let muteMessages = muteMessages {
            messagesValue = muteMessages ? 1 : 2
        }
        
        var notificationValue: Int32?
        if let muteNotification = muteNotification {
            notificationValue = muteNotification ? 1 : 2
        }
        
        if messagesValue == nil, notificationValue == nil {
            completion?(0)
            return
        }
        
        let client = BuzNetUserServiceClient(encrypt: true)
        let req = RequestUpdateFriendInfo(userId: userId, muteMessages: messagesValue, muteNotification: notificationValue)
        let _ = client.updateFriendInfo(request: req, completion: {
            result in
            
            switch result {
            case .success(let response):
                completion?(response.code)
                break
            case .failure(let error):
                completion?(error.toITNetError().code)
                break
            }
        })
    }
}
