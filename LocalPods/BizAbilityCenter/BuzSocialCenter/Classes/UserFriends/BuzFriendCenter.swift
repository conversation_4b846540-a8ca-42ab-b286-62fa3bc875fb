//
//  BuzFriendCenter.swift
//  BuzSocialCenter
//
//  Created by st.chio on 2025/2/19.
//

import BuzNetworker

@objc public protocol BuzFriendCenterObserver: AnyObject {}

public class BuzFriendCenter: NSObject, BuzSocialEventPublisher, BuzSocialEventPublisherInternal {
    public typealias Observer = BuzFriendCenterObserver
    internal let observers = NSHashTable<BuzFriendCenterObserver>.weakObjects()
    
    @objc public static let center = BuzFriendCenter()
    
    private var controllers: [ControllerType: Any] = .init()
    
    override init() {
        super.init()
        BuzNetworker.shared.signalPush.addObserver(self)
    }
    
}

extension BuzFriendCenter {
    enum ControllerType {
        case info      // 好友信息模块
        case relation  // 好友关系模块
        case blacklist // 黑名单模块
        case official  // 官号模块
    }
    
    //MARK: -info---
    @objc public static var info: FriendInfoManager {
        return center.controller(for: .info)
    }
    //MARK: -relation---
    public static var relation: FriendRelationManager {
        return center.controller(for: .relation)
    }
    //MARK: -blacklist---
    @objc public static var blacklist: BlackUserManager {
        return center.controller(for: .blacklist)
    }
    //MARK: -Official---
    @objc public static var official: BuzOfficialUserManager {
        return center.controller(for: .official)
    }
    
    private func controller<T>(for type: ControllerType) -> T {
        if let controller = self.controllers[type] as? T {
            return controller
        }
        
        let controller: Any
        switch type {
        case .info:
            controller = FriendInfoManager(center: self)
        case .relation:
            controller = FriendRelationManager(center: self)
        case .blacklist:
            controller = BlackUserManager(center: self)
        case .official:
            controller = BuzOfficialUserManager(center: self)
        }
        
        self.controllers[type] = controller
        
        return controller as! T
    }
}

extension BuzFriendCenter: BuzSignalPushEventObserver {
    public func signalPushDidReceivedMessage(_ message: BuzSignalPush.Message) {
        BuzFriendCenter.info.signalPushDidReceivedMessage(message)
        BuzFriendCenter.relation.signalPushDidReceivedMessage(message)
        BuzFriendCenter.official.signalPushDidReceivedMessage(message)
    }
}
