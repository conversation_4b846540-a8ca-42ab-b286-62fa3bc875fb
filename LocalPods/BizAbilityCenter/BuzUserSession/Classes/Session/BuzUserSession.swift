//
//  BuzUserSession.swift
//  buz
//
//  Created by liuyufeng on 2022/6/24.
//  Copyright © 2022 lizhi. All rights reserved.
//

import Foundation
import BuzIDL
import SwiftyJSON
import YYModel
import CoreMedia
import BuzAppConfiger
import BuzLog
import BuzFoundation
import BuzDataStore
import BuzDataShareKit
import BuzDatabase
import BuzCenterKit
import Combine

public enum SessionStoreKey : String {
    case uid = "SessionStoreKey_uid"
    case sessionKey = "SessionStoreKey_sessionKey"
    case refreshTokenKey = "SessionStoreKey_refreshTokenKey"
    case loginTypeKey = "SessionStoreKey_loginTypeKey"
    case phoneKey = "SessionStoreKey_phoneKey"
    case isLoginFinish = "SessionStoreKey_isLoginFinish"
    case layoutConfig = "SessionStoreKey_layoutConfig"
    case userInfo = "SessionStoreKey_userInfo"
    case installBuzTimestamp = "SessionStoreKey_installBuzTimestamp"
    case didPopUserInterview = "SessionStoreKey_didPopUserInterview"
    case alertSoundType = "SessionStoreKey_alertSoundType"
    case buzIdKey = "SessionStoreKey_buzIdKey"
    case isRegisterOnThisDevice = "SessionStoreKey_isRegisterOnThisDevice"
    case emailKey = "SessionStoreKey_emailKey"
    case loginTimeKey = "SessionStoreKey_loginTime"
    case useUnifyLogin = "SessionStoreKey_useUnifyLogin"
    case imUin = "SessionStoreKey_imUin"
    case loginOptionKey = "SessionStoreKey_loginOptionKey"
    case googleAccountKey = "SessionStoreKey_googleAccountKey"
   
}

public enum UploadContactRequestStatus : String {
    case none = "no_friend"
    case success = "success"
    case loading = "loading"
    case fail = "fail"
}

public enum LoginOptionType: Int {
    case google = 1
    case apple = 2
    case line = 3
    case facebook = 4
    
    public var name: String {
        switch self {
        case .google: return "google"
        case .apple: return "apple"
        case .line: return "line"
        case .facebook: return "facebook"
        }
    }
    
}

public protocol UserSessionProviderable {
    func fetchUserInfoFromDB() -> BuzUserData?
    func saveUserInfoToDB(user: BuzUserData?)
    func updateUserInfo(userId: Int64?, completion: @escaping(( _ user: BuzUserData?)-> Void))
}

// MARK: ---Combine------
public extension BuzUserSession {
    class Publisher {
        ///登录成功
        public let didLoginSuccess = PassthroughSubject<BuzUserSession, Never>()
        ///登出成功
        public let didLogoutSuccess = PassthroughSubject<BuzUserSession, Never>()
        public let willLogout = PassthroughSubject<BuzUserSession, Never>()
        
        public let userInfoChanged = PassthroughSubject<BuzUserSession, Never>()
        public let userIdChanged = PassthroughSubject<BuzUserSession, Never>()
        public let didSaveUserInfo = PassthroughSubject<BuzUserSession, Never>()
        public let didSaveSessionKeyAndRefreshToken = PassthroughSubject<BuzUserSession, Never>()
    }
}

@objc
public protocol BuzUserSessionObserver {
    ///登录成功  回调
    @objc optional func userSessionDidLoginSuccess(_ mgr: BuzUserSession)
    ///登出成功  回调
    @objc optional func userSessionDidLogoutSuccess(_ mgr: BuzUserSession)
    
    @objc optional func userSessionUserInfoChanged(_ mgr: BuzUserSession)
    
    @objc optional func userSessionUserUserIdChanged(_ mgr: BuzUserSession)
    
    @objc optional func userSessionWillLogout(_ mgr : BuzUserSession)
    
    @objc optional func userSessionDidSaveUserInfo(_ mgr : BuzUserSession)
    
    @objc optional func userSessionDidSaveSessionKeyAndRefreshToken(_ mgr : BuzUserSession)
}

//MARK: --EventPublisher---------
public protocol BuzUserSessionPublisher {
    func addObserver(_ observer: BuzUserSessionObserver)
    func removeObserver(_ observer: BuzUserSessionObserver)
}

protocol BuzUserSessionPublisherInternal {
    var observers: NSHashTable<BuzUserSessionObserver> { get }
    func notifyObservers(_ event: (BuzUserSessionObserver) -> Void)
}

extension BuzUserSessionPublisherInternal {
    public func addObserver(_ observer: BuzUserSessionObserver) {
        observers.add(observer)
    }
    
    public func removeObserver(_ observer: BuzUserSessionObserver) {
        observers.remove(observer)
    }
    
    func notifyObservers(_ event: (BuzUserSessionObserver) -> Void) {
        for observer in observers.allObjects {
            event(observer)
        }
    }
}


@objcMembers
public class BuzUserSession: NSObject, BuzUserSessionPublisher, BuzUserSessionPublisherInternal {
    internal let observers = NSHashTable<BuzUserSessionObserver>.weakObjects()
    public static var sessionProvider: UserSessionProviderable?
    public static let shared = BuzUserSession()
    
    public private(set) var publisher = Publisher.init()
    
    //当前用户是否新注册进来的用户
    public var isNewRegister : Bool = false
    public var isFirstRegisterInDevice: Bool = false
    // 登录状态下重启app，认为是auto静默登录，短信验证码的是手动登录
    public var isAutoLogin: Bool = true
    public var currentUploadContactsStatus : UploadContactRequestStatus = .none {
        didSet{
            BuzLog.debug("currentUploadContactsStatus = \(currentUploadContactsStatus.rawValue)")
        }
    }
    public var isEmailLogin : Bool = {
        let sKey = MMKV.buz.bool(forKey: SessionStoreKey.loginTypeKey.rawValue)
        BuzLog.debug("get sessionKey from mmkv ; sessionKey = \(String(describing: sKey))")
        return sKey
    }() {
        didSet {
            let storeKey : String = SessionStoreKey.loginTypeKey.rawValue
            let result = MMKV.buz.set(self.isEmailLogin, forKey: storeKey)
        }
    }
    
    public var sessionKey : String? = {
        let sKey = MMKV.buz.string(forKey: SessionStoreKey.sessionKey.rawValue)
        BuzLog.debug("get sessionKey from mmkv ; sessionKey = \(String(describing: sKey))")
        return sKey
    }(){
        didSet{
            let storeKey : String = SessionStoreKey.sessionKey.rawValue
            if let sessionKey = self.sessionKey {
                
                let result = MMKV.buz.set(sessionKey, forKey: storeKey)
                BuzLog.debug("存入sessionKey结果 : \(result) , sessionKey = \(sessionKey)")
            }else{
                MMKV.buz.removeValue(forKey: storeKey)
            }
        }
    }
    
    public var loginOptionType : LoginOptionType? = {
        let origin = MMKV.buz.int64(forKey: SessionStoreKey.loginOptionKey.rawValue, defaultValue: 0)
        let type = LoginOptionType(rawValue: Int(origin))
        BuzLog.debug("get loginOptionType from mmkv ; loginOptionType = \(origin)")
        return type
    }(){
        didSet{
            let storeKey : String = SessionStoreKey.loginOptionKey.rawValue
            if let loginOptionType = self.loginOptionType {
                let typeValue =  Int64(loginOptionType.rawValue)
                let result = MMKV.buz.set(typeValue, forKey: storeKey)
                BuzLog.debug("存入loginOptionType结果 : \(result) ,loginOptionType = \(typeValue)")
            }else{
                MMKV.buz.removeValue(forKey: storeKey)
            }
        }
    }
    
    public var googleAccount : String? = {
        let sKey = MMKV.buz.string(forKey: SessionStoreKey.googleAccountKey.rawValue)
        BuzLog.debug("get googleAccountKey from mmkv ; sessionKey = \(String(describing: sKey))")
        return sKey
    }(){
        didSet{
            let storeKey : String = SessionStoreKey.googleAccountKey.rawValue
            if let googleAccount = self.googleAccount {
                let result = MMKV.buz.set(googleAccount, forKey: storeKey)
                BuzLog.debug("存入googleAccountKey结果 : \(result) , googleAccountKey = \(googleAccount)")
            }else{
                MMKV.buz.removeValue(forKey: storeKey)
            }
        }
    }
    
    public var loginTime : Int64? = {
        let sKey = MMKV.buz.int64(forKey: SessionStoreKey.loginTimeKey.rawValue, defaultValue: 0)
        BuzLog.debug("get loginTimeKey from mmkv ; loginTimeKey = \(String(describing: sKey))")
        return sKey
    }(){
        didSet{
            let storeKey : String = SessionStoreKey.loginTimeKey.rawValue
            if let loginTime = self.loginTime {
                let result = MMKV.buz.set(loginTime, forKey: storeKey)
                BuzLog.debug("存入loginTime结果 : \(result) ,loginTime = \(loginTime)")
            }else{
                MMKV.buz.removeValue(forKey: storeKey)
            }
        }
    }

    
    public var refreshToken : String? = {
        let sKey = MMKV.buz.string(forKey: SessionStoreKey.refreshTokenKey.rawValue)
        BuzLog.debug("get refreshTokenKey from mmkv ; refreshTokenKey = \(String(describing: sKey))")
        return sKey
    }(){
        didSet{
            let storeKey : String = SessionStoreKey.refreshTokenKey.rawValue
            if let refreshToken = self.refreshToken {
                
                let result = MMKV.buz.set(refreshToken, forKey: storeKey)
                BuzLog.debug("存入refreshToken结果 : \(result) , refreshToken = \(refreshToken)")
            }else{
                MMKV.buz.removeValue(forKey: storeKey)
            }
        }
    }
    
    //统一登录方式下登录IM的uid
    public private(set) var imUin : Int64 = {
        let value = MMKV.buz.int64(forKey: SessionStoreKey.imUin.rawValue)
        BuzLog.debug("get uin from mmkv ; uin = \(value)")
        return value
    }(){
        didSet{
            let key : String = SessionStoreKey.imUin.rawValue
            let result = MMKV.buz.set(imUin, forKey: key)
            BuzLog.debug("存入uin结果 : \(result) , uin = \(imUin)")
        }
    }
    
    public private(set) var useUnifyLogin : Bool = {
        let value = MMKV.buz.bool(forKey: SessionStoreKey.useUnifyLogin.rawValue, defaultValue: false)
        BuzLog.debug("get useUnifyLogin from mmkv ; useUnifyLogin = \(value)")
        return value
    }(){
        didSet{
            let key : String = SessionStoreKey.useUnifyLogin.rawValue
            let result = MMKV.buz.set(useUnifyLogin, forKey: key)
            BuzLog.debug("存入useUnifyLogin结果 : \(result) , useUnifyLogin = \(useUnifyLogin)")
        }
    }
    
    /// 保存sessionKey和refreshToken
    /// - Parameter sessionKey: sessionKey
    /// - Parameter refreshToken: refreshToken
    public class func saveSessionKey(sessionKey: String?, refreshToken: String?){
        if let sessionKey = sessionKey {
            BuzUserSession.shared.sessionKey = sessionKey
        }
        if let refreshToken = refreshToken {
            BuzUserSession.shared.refreshToken = refreshToken
        }
        BuzLog.info("did dave sessionKey and refreshToken")
        BuzUserSession.shared.notifyDelegatesDidSaveSessionKeyAndRefreshToken()
    }
    
    public class func saveUseUnifyLogin(_ useUnifyLogin: Bool){
        BuzUserSession.shared.useUnifyLogin = useUnifyLogin
    }
    
    public class func saveImUin(_ uin: Int64){
        BuzUserSession.shared.imUin = uin
    }
    
    public class func saveLoginTime(loginTime: Int64?){
        if let loginTime = loginTime {
            BuzUserSession.shared.loginTime = loginTime
        }
        
    }
    
    public class func saveLoginOptionType(type: LoginOptionType?){
        BuzUserSession.shared.loginOptionType = type
        
    }
    
    public class func saveGoogleAccount(googleAccount: String?){
        BuzUserSession.shared.googleAccount = googleAccount
        
    }
    
    public var uid : Int64 = {
        let uid = MMKV.buz.int64(forKey: SessionStoreKey.uid.rawValue)
        BuzLog.debug("get uid from mmkv ; uid = \(uid)")
        return uid
    }(){
        didSet{
            guard uid != oldValue else {
                return
            }
            let storeKey : String = SessionStoreKey.uid.rawValue
            let result = MMKV.buz.set(uid, forKey: storeKey)
            BuzLog.info("存入uid结果 : \(result) , uid = \(uid)")
            PushShareDataTool.saveSessionUserId(uid)
            notifyDelegatesUserIdChanged()
        }
        
        willSet {
            if uid == 0 && newValue != 0 {
                DispatchQueue.defaultIOQueue().async {
                    BuzDatabase.closeDB()
                }
            }
        }
    }
    
    public lazy var phone: String? = MMKV.buz.string(forKey: SessionStoreKey.phoneKey.rawValue) {
        didSet {
            if phone != oldValue {
                MMKV.buz.set(phone ?? "", forKey: SessionStoreKey.phoneKey.rawValue)
            }
        }
    }
    
    public lazy var buzId: String? = MMKV.buz.string(forKey: SessionStoreKey.buzIdKey.rawValue) {
        didSet {
            MMKV.buz.set(buzId ?? "", forKey: SessionStoreKey.buzIdKey.rawValue)
        }
    }
    public lazy var email: String? = MMKV.buz.string(forKey: SessionStoreKey.emailKey.rawValue) {
        didSet {
            if email != oldValue {
                MMKV.buz.set(email ?? "", forKey: SessionStoreKey.emailKey.rawValue)
            }
        }
    }
    
    public var isloginFinish : Bool = MMKV.buz.bool(forKey: SessionStoreKey.isLoginFinish.rawValue){
        didSet{
            let storeKey : String = SessionStoreKey.isLoginFinish.rawValue
            let result = MMKV.buz.set(isloginFinish, forKey: storeKey)
            BuzLog.info("存入isloginFinish结果 : \(result) , isloginFinish = \(isloginFinish)")
            
            if isloginFinish {
                notifyDelegatesDidLoginSuccess()
            }
        }
    }
    
    public private(set) lazy var layoutConfigDict : [String : Any]? = {
       
        var dict : [String : Any]? = nil
        if let str = self.layoutConfigJsonStr {
            dict = JSON.init(parseJSON: str).dictionaryObject
        }
        return dict
    }()
    
    public lazy var layoutConfigJsonStr : String? = MMKV.buz.string(forKey: SessionStoreKey.layoutConfig.rawValue) {
        didSet{
            let storeKey : String = SessionStoreKey.layoutConfig.rawValue
            if let jsonStr = self.layoutConfigJsonStr {
                let result = MMKV.buz.set(jsonStr, forKey: storeKey)
                layoutConfigDict = JSON.init(parseJSON: jsonStr).dictionaryObject
                
                BuzLog.debug("存入layoutConfigJonsStr结果 : \(result) , jsonStr = \(jsonStr)")
            }else{
                MMKV.buz.removeValue(forKey: storeKey)
                layoutConfigDict = nil
            }
        }
    }
    
    public var alertSoundType : SpeakingVoiceType {
        set {
            let storeKey : String = SessionStoreKey.alertSoundType.rawValue
            MMKV.buz.set(newValue.rawValue, forKey: storeKey)
        }
        get {
            let storeKey : String = SessionStoreKey.alertSoundType.rawValue
            let value = MMKV.buz.int32(forKey: storeKey, defaultValue: SpeakingVoiceType.bell.rawValue)
            return SpeakingVoiceType.value(value)
        }
    }
    
    
    //userInfo
    public lazy var userInfo: BuzUserData? = {
        var user : BuzUserData? = nil
        user = Self.sessionProvider?.fetchUserInfoFromDB()
        return user
    }(){
        didSet{
            Self.sessionProvider?.saveUserInfoToDB(user: self.userInfo)
            notifyDelegatesUserInfoChanged()
        }
    }
    
    public func updateUserInfo() {
        //compensate update version for no update buzId
        if self.userInfo?.base.buzId == nil {
            Self.sessionProvider?.updateUserInfo(userId: self.userInfo?.userId, completion: { user in
                self.userInfo?.base.buzId = user?.base.buzId
                self.userInfo?.base.email = user?.base.email
//                self.userInfo?.base.email = user?.base.
                self.forceSaveUserInfo()
            })
        }
    }
    
    public override init() {
        super.init()
        saveInstallBuzTimestamp()
        phone = userInfo?.base.phone
        BuzLog.info("uid = \(self.uid) , isloginFinish = \(self.isloginFinish)")
        BuzAppConfiger.shared.addObserver(self)
    }
    
    public func saveUserInfo(userInfo:UserInfo){
        let model = BuzUserData.init(userId: userInfo.userId ?? 0)
        model.base = .init(idl: userInfo)
        
        self.userInfo = model
        self.notifyObservers { observer in
            observer.userSessionDidSaveUserInfo?(self)
        }
    }
    
    public func forceSaveUserInfo() {
        if let userInfo = self.userInfo {
            let model = BuzUserData.init(userId: userInfo.userId)
            model.base.userName = userInfo.base.userName
            model.base.firstName = userInfo.base.firstName
            model.base.lastName = userInfo.base.lastName
            model.base.portrait = userInfo.base.portrait
            model.base.phone = userInfo.base.phone
            model.base.buzId = userInfo.base.buzId
            model.base.email = userInfo.base.email
            
            self.userInfo = model
        }
    }
    
    public func cleanDataWhenLogout(notifyDelegates : Bool = true)
    {
        self.notifyObservers { observer in
            observer.userSessionWillLogout?(self)
        }
        self.publisher.willLogout.send(self)
        self.sessionKey = nil
        self.refreshToken = nil
        self.loginTime = nil
        self.imUin = 0
        self.uid = 0
        self.phone = nil
        self.isloginFinish = false
        self.userInfo = nil
        self.isNewRegister = false
        self.isEmailLogin = false
        self.useUnifyLogin = false
        self.currentUploadContactsStatus = .none
        if notifyDelegates {        
            notifyDelegatesDidLogoutSuccess()
        }
    }
    
    public func clearInstallBuzBuzTimestamp() {
        MMKV.buz.removeValue(forKey: SessionStoreKey.didPopUserInterview.rawValue)
        MMKV.buz.removeValue(forKey: SessionStoreKey.installBuzTimestamp.rawValue)
    }

    
    public func saveInstallBuzTimestamp() {
        let timestamp = MMKV.buz.double(forKey: SessionStoreKey.installBuzTimestamp.rawValue)
        if timestamp > 0 {
            return
        } else {
            let installTimestamp = Date().timeIntervalSince1970
            MMKV.buz.set(installTimestamp, forKey: SessionStoreKey.installBuzTimestamp.rawValue)
        }
    }
    
    public func fetchInstallBuzeTimestamp() -> Double {
        var timestamp = MMKV.buz.double(forKey: SessionStoreKey.installBuzTimestamp.rawValue)
        if timestamp == 0 {
            let installTimestamp = Date().timeIntervalSince1970
            MMKV.buz.set(installTimestamp, forKey: SessionStoreKey.installBuzTimestamp.rawValue)
            timestamp = installTimestamp
        }
        return timestamp
    }
    
    public func isNeedPopupUserInterview(config: AppGlobalConfigInLoginStatus) -> Bool {
        let didPopUserInterview = MMKV.buz.bool(forKey: SessionStoreKey.didPopUserInterview.rawValue)
        if didPopUserInterview {
            return false
        }
        
        if config.isSupportInterviewInvite == false {
            return false
        }
        
        let installTimestamp = fetchInstallBuzeTimestamp()
        if installTimestamp <= 0 {
            return false
        }
        
        let date = Date(timeIntervalSince1970: TimeInterval(installTimestamp))
        let now = Date()
        let days = fabs(now.daysSince(date))
        if let tDay = config.interviewInvitePopupDay, Double(tDay) < days {
            MMKV.buz.set(true, forKey: SessionStoreKey.didPopUserInterview.rawValue)
            return true
        }
        return false
    }
    
    public func saveUidsRegisterOnThisDevice() {
        let key = SessionStoreKey.isRegisterOnThisDevice.rawValue
        var uidsArray: NSMutableArray?
        if let array = MMKV.buz.object(of: NSMutableArray.self, forKey: key) as? NSMutableArray {
            uidsArray = array
        } else {
            uidsArray = NSMutableArray()
        }
        
        if let uidsArray = uidsArray {
            let uid: NSNumber = NSNumber(value: BuzUserSession.shared.uid)
            uidsArray.add(uid)
            MMKV.buz.set(uidsArray, forKey: key)
        }
    }
    
    public func isRegisterOnThisDevice() -> Bool {
        let key2 = SessionStoreKey.isRegisterOnThisDevice.rawValue
        if let uidsArray = MMKV.buz.object(of: NSMutableArray.self, forKey: key2) as? NSMutableArray {
            let uid: NSNumber = NSNumber(value: BuzUserSession.shared.uid)
            if uidsArray.contains(uid) {
                return true
            }
        }
        return false
    }
}

// MARK: 触发代理方法
extension BuzUserSession {
    func notifyDelegatesDidLogoutSuccess() {
        self.notifyObservers { delegate in
            delegate.userSessionDidLogoutSuccess?(self)
        }
        self.publisher.didLogoutSuccess.send(self)
    }
    
    func notifyDelegatesDidLoginSuccess() {
        self.notifyObservers { delegate in
            delegate.userSessionDidLoginSuccess?(self)
        }
        self.publisher.didLoginSuccess.send(self)
    }
    
    func notifyDelegatesUserInfoChanged() {
        self.notifyObservers { delegate in
            delegate.userSessionUserInfoChanged?(self)
        }
        self.publisher.userInfoChanged.send(self)
    }
    
    func notifyDelegatesUserIdChanged() {
        self.notifyObservers { delegate in
            delegate.userSessionUserUserIdChanged?(self)
        }
        self.publisher.userIdChanged.send(self)
    }
    
    func notifyDelegatesDidSaveSessionKeyAndRefreshToken()
    {
        self.notifyObservers { delegate in
            delegate.userSessionDidSaveSessionKeyAndRefreshToken?(self)
        }
        self.publisher.didSaveSessionKeyAndRefreshToken.send(self)
    }
}


extension BuzUserSession: BuzAppConfigerObserver {
    //未登录配置
    public func appConfiger(configer: BuzAppConfiger, notLoggedConfigDidRequstSuccess config: AppGlobalConfigWithoutLogin) {
        
    }
    //登录后的配置
    public func appConfiger(configer: BuzAppConfiger, loggedConfigDidRequstSuccess config: AppGlobalConfigInLoginStatus) {
        self.alertSoundType = SpeakingVoiceType.value(config.alertSoundType)
    }
}
