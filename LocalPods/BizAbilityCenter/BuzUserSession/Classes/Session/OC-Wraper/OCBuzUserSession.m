//
//  OCSessionManager.m
//  buz
//
//  Created by lizhi on 2022/7/7.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "OCBuzUserSession.h"
#import  "BuzUserSession-Swift.h"

@implementation OCBuzUserSession

+ (int64_t)uid{
    return BuzUserSession.shared.uid;
}

+ (NSString *)singleMsgContent {
    return BuzAppConfiger.shared.appGlobalConfigWithLoginStatus.singleMsgContent;
}

+ (NSString *)groupMsgContent {
    return BuzAppConfiger.shared.appGlobalConfigWithLoginStatus.groupMsgContent;
}

@end
