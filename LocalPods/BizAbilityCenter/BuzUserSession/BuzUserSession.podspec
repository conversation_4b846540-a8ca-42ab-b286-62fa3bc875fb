#
#  Be sure to run `pod spec lint PCSocialCenter.podspec' to ensure this is a
#  valid spec and to remove all comments including this before submitting the spec.
#
#  To learn more about Podspec attributes see https://guides.cocoapods.org/syntax/podspec.html
#  To see working Podspecs in the CocoaPods repo see https://github.com/CocoaPods/Specs/
#

Pod::Spec.new do |s|
  
  s.name     = "BuzUserSession"
  s.version  = "0.0.1"
  s.summary  = "A short description of BuzUserSession."
  
  s.homepage = "https://gitlab.lizhi.fm/zhiyaPods/BuzUserSession"
  s.license  = { :type => 'Copyright', :file => 'LICENSE' }
  s.author   = { 'st.chio' => '<EMAIL>' }
  s.source   = { :git => "https://gitlab.lizhi.fm/zhiyaPods/BuzUserSession.git", :tag => s.version.to_s }
  
  s.ios.deployment_target = '10.0'
  
  s.pod_target_xcconfig = {'DEFINES_MODULE' => 'YES'}

  s.source_files  = "Classes", "Classes/*.{swift}"
  
  s.subspec 'Session' do |sp|
    sp.source_files = 'Classes/Session/**/*.{h,m,swift}'
    
    sp.dependency 'BuzIDL'
    sp.dependency 'SwiftyJSON'
    sp.dependency 'YYModel'
    sp.dependency 'BuzAppConfiger'
    sp.dependency 'BuzLog'
    sp.dependency 'BuzFoundation'
    sp.dependency 'BuzDataStore'
    sp.dependency 'BuzDataShareKit'
    sp.dependency 'BuzDatabase'
    sp.dependency 'BuzCenterKit'
  end
  
end
