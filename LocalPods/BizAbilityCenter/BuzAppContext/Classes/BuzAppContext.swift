//
//  BuzAppContext.swift
//  BuzAppContext
//
//  Created by st.chio on 2025/5/15.
//

import Combine

public class BuzAppContext {
    public static let shared = BuzAppContext()
    
    public private(set) lazy var home = Home.init()
    public private(set) lazy var vcLifeCycle = ViewControllerLifeCycle.init()
}

// MARK: - Home
public extension BuzAppContext {
    class Home {
        public private(set) lazy var publisher = HomeLifecyclePublisher.init()
        ///是否在已经在首页
        public var isOpenedHome = false
    }
    
    class HomeLifecyclePublisher {
        
        public struct LoadParams {
            public let isFirstTrigger: Bool
            
            public init(isFirstTrigger: Bool) {
                self.isFirstTrigger = isFirstTrigger
            }
        }
        
        public struct AppearParams {
            public let isFirstTrigger: Bool
            public let animated: Bool
            
            public init(isFirstTrigger: Bool, animated: Bool) {
                self.isFirstTrigger = isFirstTrigger
                self.animated = animated
            }
        }
        
        public private(set) lazy var viewDidLoad = PassthroughSubject<LoadParams, Never>()
        public private(set) lazy var viewWillAppear = PassthroughSubject<AppearParams, Never>()
        public private(set) lazy var viewDidAppear = PassthroughSubject<AppearParams, Never>()
        public private(set) lazy var viewWillDisappear = PassthroughSubject<AppearParams, Never>()
        public private(set) lazy var viewDidDisappear = PassthroughSubject<AppearParams, Never>()
    }
}

// MARK: - vcLifeCycle
public extension BuzAppContext {
        
    class ViewControllerLifeCycle {
        public private(set) lazy var publisher = ViewControllerLifeCyclePublisher.init()
    }
    
    class ViewControllerLifeCyclePublisher {
        
        public enum DisplayType{
            case push
            case present
        }
        
        public enum DisappearType {
            case pop
            case dismiss
        }

        public struct DisplayParams {
            public let viewController: UIViewController
            public let type: DisplayType
            
            public init(viewController: UIViewController, type: DisplayType) {
                self.viewController = viewController
                self.type = type
            }
        }
        
        public struct DisappearParams {
            public let viewController: UIViewController
            public let type: DisappearType
            
            public init(viewController: UIViewController, type: DisappearType) {
                self.viewController = viewController
                self.type = type
            }
        }
        
        public private(set) lazy var display = PassthroughSubject<DisplayParams, Never>()
        public private(set) lazy var disappear = PassthroughSubject<DisappearParams, Never>()
    }
}
