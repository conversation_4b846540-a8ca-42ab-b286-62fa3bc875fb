//
//  BuzIMCenterKit.swift
//  buz
//
//  Created by st.chio on 2025/2/10.
//  Copyright © 2025 lizhi. All rights reserved.
//

public let kBuzAIUserId: Int64 = 12345

// MARK: - Message Content Types
@objc
public enum BuzIMContentType: Int {
    // 解密失败的消息类型
    case decryptFailed = -2
    // 表示所有类型，不应该返回此类型
    case all = -1
    // 未知类型
    case unknown = 0
    
    // MARK: - 内容类：CT(00XX)
    // 文字消息
    case text = 1
    // 语音消息
    case count_WalkieTalkie = 2
    // 图片消息
    case image = 3
    // 文件消息
//    case file = 4
    // 视频消息
    case video = 5
    
    // 被撤回的消息
    case recalled = 99
    
    // MARK: - 通知消息 Notify(1XX)
    // 撤回消息通知
    case notify_Recall = 100
    // 已读回执
    case command_ReadReceipt = 101
    
    // 状态消息
    case status_Input = 200
    
    // MARK: - 系统命令 Command(50XX)
    case command_Begin = 5000
    // 清空未读数
    case command_ClearUnread = 5001
    // 删除会话
    case command_RemoveConversation = 5002
    // 会话信息更新（分组和扩展字段）
    case command_UpdateConversation = 5003
    // 编辑通知消息
    case command_EditMsgContent = 5004
    // 群清空未读数
    case command_GroupClearUnread = 5005
    // 消息Reaction变更
    case command_ReactionChanged = 5105
    
    case command_End = 10000
    
    // MARK: - App自定义类
    // 区间范围[10001, 100000]
    case app_Custom = 10001
    // App自定义，不在服务器计算未读数的自定义消息，区间范围[100001, 200000]
    case app_Custom_No_Unread = 100001
    case app_Custom_No_Unread_End = 200000
    case app_Custom_End
    
    // MARK: - 自定义消息类型 -------------------->
    // 命令消息
    case command = 10003
    // push to talk 对讲机消息
    case walkieTalkie = 10004
    // 语音文本消息
    case voice_Text = 10005
    // 新语音文本消息
    case new_Voice_Text = 10007
    // 语音表情消息
    case voice_Emoji = 10008
    // 本地提示消息
    case local_tip = 10009
    // 位置消息
    case location = 10010
    // 媒体文字消息
    case mediaText = 10011
    // 新动作媒体文字消息
    case mediaActionText = 100111
    
    // 音效消息
    case onairSoundBoard = 10015
    // 贴纸消息
    case onairStrickerGift = 10013
    
    // 兼容语音表情
    case compatibleVoiceEmoji = 10012
    case voicegif = 10016 //to be confirmed
    // LivePlace分享卡片
    case livePlaceShareCard = 10017
    // onair comment
    case onAir_comment = 10018
    /** 通话卡片 */
    case callCard = 10019
    /** 文件 */
    case fileAttachment = 10020
}

// MARK: - Conversation Types
//会话类型 跟VX内部的消息一致，为了以后拓展业务重新封装一层类型
@objc
public enum BuzConversationType: Int {
    case peer = 1
    case group = 3
    case chatroom = 4
    case system = 6
}

@objc
public enum BuzIMServiceStatus: Int {
    case beginLoading
    case endLoading
    case serviceDisconnected
    case serviceRecovered
    case longlinkDisconnected
    case longlinkConnected
}

// MARK: - Message Operations
//实时消息的操作 跟VX内部的IM5NotifyItemOperation 保持一致
public enum BuzIMMessageOperation: Int {
    case update = 0
    case delete = 1
    case edit = 2
    case recall = 3
    case historyUpdate = 4
    // 解密失败消息恢复解决成功
    case decryptRecover = 5
    // 所引用的消息更新
    case refMsgUpdate = 6
    // 流式更新
    case streamingUpdate = 7
    // 快速反应更新
    case reactionUpdate = 8
    // Translate编辑消息
    case translate = 9
    // 重复消息更新
    case duplicatedUpdate = 10
}

// MARK: - Error Send Message Codes
public enum BuzErrorSendMessageCode: Int {
    // 目标不存在
    case targetNotExists = 1
    // 消息内容不规范
    case invalidContent = 2
    // 对方拒收了此消息(接收方屏蔽发送方)
    case targetRejected = 3
    // 发送方已被禁言
    case bannedSender = 4
    // 滤镜下架
    case filterInfoInvalid = 101
    // 用户主动暂停失败[业务自定义]
    case pauseSendMessage = 20014
    // 取消发送
    case cancelSendMessage = 20006
}

// MARK: - Command Message Types
@objc
public enum BuzCommandMessageType: Int {
    // 默认值
    case none = 0
    // 会话刷新
    case refreshConversation = 1
    // 邀请进群消息
    case invited = 2
    // Centered message
    case centered = 3
    // 居中跳转消息
    case centeredAction = 4
    // 居中RealTimeCall
    case realTimeCall = 5
    // 居中onAir
    case onAir = 6
    // 居中LivePlace
    case livePlace = 7
}

@objc
public enum BuzPushReactionType: Int {
    case none = -1
    case quickReact_voicemoji = 1
}
