//
//  BuzSocialCenterKit.swift
//  buz
//
//  Created by st.chio on 2025/2/10.
//  Copyright © 2025 lizhi. All rights reserved.
//


// MARK: ---User---------------
// 用户状态
@objc public enum BuzUserStatus: Int32 {
    case normal = 0        //0-正常
    case temporarilyBanned = 1 //1-临时封禁
    case foreverBanned = 2 // 2-永久封禁
    case deletedAccount = 99999
}

//好友在线状态
public enum BuzFriendStatus: Int32, Codable {
    case none = -1    //未知
    case online = 1   //在线
    case offline = 2  //离线
    case quiteModeChanaged = 3
}

// 好友申请状态
@objc public enum BuzFriendApplyStatus: Int32 {
    case `default` = 0  // 待处理
    case expired = 3     // 已过期
}

// 好友关系枚举
@objc public enum BuzUserRelation: Int32 {
    case `default` = 0   // 默认空值
    case friend = 1      // 双向好友
    case iApplying = 2   // 我正在申请加他好友
    case otherApplying = 3 // 他正在申请加我好友
    case notFriend = 4   // 未加好友
    case me = 5          // 自己
}

// 用户好友类型
@objc public enum BuzUserType: Int32 {
    case `default` = 0  // 普通好友
    case bot = 1        // AI bot
    case official = 2   // 用研账号
    case officialAccount = 3 // 官方账号
}

public extension BuzUserType {
    func isOfficial() -> Bool {
        return self == .official || self == .officialAccount
    }
}


// MARK: ---User Setting---------------
// 用户 QuietMode
@objc public enum BuzUserQuietMode: Int32 {
    case unknow = 0   // 未知
    case avaliable = 1 // Available 模式
    case quiet = 2    // Quiet 模式
}

// 消息静音设置
@objc public enum BuzMuteMessagesType: Int32 {
    case unknow = 0  // 未知
    case on = 1      // 静音模式
    case off = 2     // 不静音模式
}

// 通知静音设置
@objc public enum BuzMuteNotificationType: Int32 {
    case unknow = 0  // 未知
    case on = 1      // 静音模式
    case off = 2     // 不静音模式
}

// MARK: ---Media---------------
// 语音附件类型
@objc public enum BuzVoiceAccessoryType: Int {
    case `default` = 0 // 普通语音
    case text = 1      // 文本语音
    case emoji = 2     // 表情语音
}

// MARK: ---ChatGroup---------------
// 群身份枚举
@objc public enum BuzChatGroupRole: Int32 {
    case owner = 1  // 群主
    case admin = 2  // 群管理员
    case member = 3 // 普通群成员
}

// 用户群状态枚举
@objc public enum BuzChatGroupUserStatus: Int32 {
    case joined = 1   // 已加群
    case notJoined = 2 // 未加群
    case removed = 3  // 被踢出群
}

@objc public enum BuzChatGroupStatus: Int32 {
    case disband = 0 //0 解散
    case normal = 1  //1 正常
}
@objc public enum BuzChatGroupType: Int32 {
    case normal = 1   // 普通群
    case large = 2    // 大群
}
