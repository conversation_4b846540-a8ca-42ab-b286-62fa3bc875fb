//
//  BuzUser.swift
//  buz
//
//  Created by st.chio on 2025/2/10.
//  Copyright © 2025 lizhi. All rights reserved.
//


import BuzIDL
import BuzFoundation

// MARK: ---BuzUserData---
@objcMembers
@objc(BuzUserData)
public class BuzUserData: NSObject {
    public var userId: Int64 = 0 // 用户ID
    public var base: BaseInfo = .init() // 基本信息
    public var relation: RelationInfo = .init() // 用户关系
    
    public override init() {
        super.init()
    }
    
    public init(userId: Int64) {
        self.userId = userId
        self.base = .init(userId: userId)
        self.relation = .init(userId: userId)
    }
    
    public init(userId: Int64, base: BaseInfo? = nil, relation: RelationInfo? = nil) {
        self.userId = userId
        self.base = base ?? .init(userId: userId)
        self.relation = relation  ?? .init(userId: userId)
    }
    
    public override func isEqual(_ object: Any?) -> Bo<PERSON> {
        guard let other = object as? BuzUserData else { return false }
        let result = (self.base == other.base
                      && self.relation == other.relation)
        
        return result
    }
    
    public override var hash: Int {
        var hasher = Hasher()
        hasher.combine(self.base.phone)
        hasher.combine(self.base.firstName)
        hasher.combine(self.base.lastName)
        hasher.combine(self.base.portrait)
        return hasher.finalize()
    }
}

public extension BuzUserData {
    /// IDL对象初始化
    /// - Parameter idl: idl对象
    convenience init(idl: UserRelationInfo) {
        self.init(userId: idl.userInfo.userId ?? 0,
                  base: BaseInfo.init(idl: idl.userInfo),
                  relation: RelationInfo.init(idl: idl.userRelation))
    }
    
    func deepCopy() -> BuzUserData {
        let copy = BuzUserData.init(userId: self.userId)
        copy.base = .init(deepCopy: self.base)
        copy.relation = .init(deepCopy: self.relation)
        
        return copy
    }
}

public extension BuzUserData {
    @objc var displayName: String {
        if let remark = self.relation.remark , remark.length > 0 {
            return remark
        }
        return (self.base.firstName?.length ?? 0 > 0 ? self.base.firstName : self.base.lastName) ?? "";
    }
    
    @objc var displayNameOrPhone: String {
        let name =  self.displayName
        return name.length > 0 ? name : self.base.phone ?? ""
    }
    
    //全称
    @objc var fullName: String {
        if let remark = self.relation.remark {
            return remark
        }
        return self.base.userName ?? self.base.buzId ?? ""
    }
    
    ///首页显示
    var displayNameAtHome: String {
        if let remark = self.relation.remark , remark.length > 0 {
            return remark
        }
        return (self.base.firstName?.length ?? 0 > 0 ? self.base.firstName : self.base.userName) ?? "";
    }
}

// MARK: ---BuzUserData.BaseInfo---
public extension BuzUserData {
    @objcMembers
    @objc(BuzUserDataBaseInfo)
    class BaseInfo: NSObject {
        public var userId: Int64 = 0 // 用户ID
        public var buzId: String? // 用户可自定义的唯一标识，对应 UI 中的 userName
        public var userType: BuzUserType = .default  // 用户类型 0-普通用户 1-Buz bot 2-Buz research 3-Buz official
        public var userStatus: BuzUserStatus = .normal // 用户状态 0-正常 1-临时封禁 2-永久封禁
        
        public var userName: String? // 用户昵称
        public var firstName: String? // firstName
        public var lastName: String? // lastName
        public var portrait: String? // 头像
        
        public var phone: String? // 国家码-手机号
        public var email: String? // 邮箱地址
        
        public var registerTime: Int64? // 注册时间(时间戳)
        
        //以下字段字值未存储到DB------
        public var walkieTalkieOnlineTime: Int64? // 对讲机上线时间(时间戳)
        public var quietMode: BuzUserQuietMode = .unknow // 用户quietMode状态 0-客户端忽略 1-Available 2-Quiet
        public var language: String? // 用户语言
        
        public override init() {
            super.init()
        }
        
        public init(userId: Int64) {
            self.userId = userId
        }
        
        public override func isEqual(_ object: Any?) -> Bool {
            guard let other = object as? BaseInfo else { return false }
            let result = (self.userId == other.userId
                          && self.buzId == other.buzId
                          && self.userType == other.userType
                          && self.userStatus == other.userStatus
                          && self.userName == other.userName
                          && self.firstName == other.firstName
                          && self.lastName == other.lastName
                          && self.portrait == other.portrait
                          && self.phone == other.phone
                          && self.email == other.email
                          && self.registerTime == other.registerTime)
            
            return result
        }
    }
}

public extension BuzUserData.BaseInfo {
    
    /// IDL对象初始化
    /// - Parameter idl: idl对象
    convenience init(idl: UserInfo) {
        self.init(userId: idl.userId ?? 0)
        self.buzId = idl.buzId
        self.userType = BuzUserType.init(rawValue: idl.userType) ?? .default
        self.userStatus = BuzUserStatus.init(rawValue: idl.userStatus) ?? .normal
        
        self.userName = idl.userName
        self.firstName = idl.firstName
        self.lastName = idl.lastName
        self.portrait = idl.portrait
        
        self.phone = idl.phone
        self.email = idl.email
        
        self.registerTime = idl.registerTime
        
        self.walkieTalkieOnlineTime = idl.walkieTalkieOnlineTime
        self.quietMode = BuzUserQuietMode.init(rawValue: idl.quietMode ?? 0) ?? .unknow
        self.language = idl.language
    }
    
    /// DeepCopy
    /// - Parameter copy: new object
    convenience init(deepCopy copy: BuzUserData.BaseInfo) {
        self.init(userId: copy.userId)
        self.buzId = copy.buzId
        self.userType = copy.userType
        self.userStatus = copy.userStatus
        
        self.userName = copy.userName
        self.firstName = copy.firstName
        self.lastName = copy.lastName
        self.portrait = copy.portrait
        
        self.phone = copy.phone
        self.email = copy.email
        
        self.registerTime = copy.registerTime
    }
}

public extension BuzUserData.BaseInfo {
    
    var nameFirstLetters: String {
        var twoLetter = ""
        if let first = self.firstName?.first {
            twoLetter.append(first)
        }
        if let last = self.lastName?.first {
            twoLetter.append(last)
        }
        return twoLetter
    }
    
    var displayBuzId : String {
        if self.buzId?.length ?? 0 == 0 {
            return ""
        }
        
        return "@" + (self.buzId ?? "")
    }
    
    func emailLoginNeedBindPhone() -> Bool {
        return self.email?.length ?? 0 > 0 && self.phone?.length == 0
    }
}

// MARK: ---BuzUserData.RelationInfo---
public extension BuzUserData {
    @objcMembers
    @objc(BuzUserDataRelationInfo)
    class RelationInfo: NSObject {
        public var userId: Int64 = 0  // 用户id
        public var isFriendFlag: Bool = false //是否是好友
        public var remark: String? // 备注名
        public var becomeFriendTime: Int64 = 0 // 成为好友的时间戳，非好友为0
        public var serviceMuteMessages: BuzMuteMessagesType = .unknow // 服务端 静音好友消息,1-静音，2-不静音
        public var localMuteMessages: BuzMuteMessagesType = .unknow // 本地记录 静音好友消息,1-静音，2-不静音
        public var muteNotification: BuzMuteNotificationType = .unknow // 静音好友消息通知,1-静音，2-不静音
        
        //以下字段字值未存储到DB------
        public var relation: BuzUserRelation = .default // [用户关系类型](/buz协议/枚举/公共枚举定义.html#userrelationType-用户关系)
        
        public override init() {
            super.init()
        }
        
        public init(userId: Int64) {
            self.userId = userId
        }
        
        public override func isEqual(_ object: Any?) -> Bool {
            guard let other = object as? RelationInfo else { return false }
            let result = (self.isFriendFlag == other.isFriendFlag
                          && self.remark == other.remark
                          && self.becomeFriendTime == other.becomeFriendTime
                          && self.serviceMuteMessages == other.serviceMuteMessages
                          && self.localMuteMessages == other.localMuteMessages
                          && self.muteNotification == other.muteNotification)
            
            return result
        }
    }
}

public extension BuzUserData.RelationInfo {
    
    /// IDL对象初始化
    /// - Parameter idl: idl对象
    convenience init(idl: UserRelation) {
        self.init(userId: idl.userId)
        self.relation = BuzUserRelation.init(rawValue: idl.relation) ?? .default
        self.isFriendFlag = (relation == .friend ||  relation == .me)
        
        self.remark = idl.remark
        self.becomeFriendTime = idl.becomeFriendTime
        self.serviceMuteMessages = BuzMuteMessagesType.init(rawValue: idl.muteMessages ?? 0) ?? .unknow
        self.muteNotification = BuzMuteNotificationType.init(rawValue: idl.muteNotification ?? 0) ?? .unknow
    }
    
    /// DeepCopy
    /// - Parameter copy: new object
    convenience init(deepCopy copy: BuzUserData.RelationInfo) {
        self.init(userId: copy.userId)
        self.isFriendFlag = copy.isFriendFlag
        
        self.remark = copy.remark
        self.becomeFriendTime = copy.becomeFriendTime
        self.serviceMuteMessages = copy.serviceMuteMessages
        self.localMuteMessages = copy.localMuteMessages
        self.muteNotification = copy.muteNotification
    }
}

public extension BuzUserData.RelationInfo {
    func isMuteMessages() -> Bool {
        if (self.localMuteMessages != .unknow) {
            return self.localMuteMessages == .on
        }
        return self.serviceMuteMessages == .on
    }

    func isMuteNotification() -> Bool {
        return self.muteNotification == .on
    }

}
