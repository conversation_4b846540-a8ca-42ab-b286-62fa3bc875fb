//
//  BuzIMGroupData.swift
//  buz
//
//  Created by st.chio on 2025/2/11.
//  Copyright © 2025 lizhi. All rights reserved.
//


import BuzIDL

// MARK: ---BuzIMGroupData---
@objcMembers
public class BuzIMGroupData: NSObject {
    public var groupId: Int64 // 群id
    public var base: BaseInfo // 群名称
    public var extra: ExtraInfo // 群名称
    
    public init(groupId: Int64) {
        self.groupId = groupId
        self.base = .init(groupId: groupId)
        self.extra = .init(groupId: groupId)
    }
    
    public init(groupId: Int64, base: BaseInfo? = nil, extra: ExtraInfo? = nil) {
        self.groupId = groupId
        self.base = base ?? .init(groupId: groupId)
        self.extra = extra ?? .init(groupId: groupId)
    }
    
    public override var description: String {
        return "BuzIMGroupData - groupId = \(self.groupId)"
    }
}

public extension BuzIMGroupData {
    
    /// IDL对象初始化
    /// - Parameter idl: idl对象
    convenience init(idl: GroupInfo) {
        let groupId = idl.groupBaseInfo.groupId
        self.init(groupId: groupId,
                  base: .init(idl: idl.groupBaseInfo))
        
        if let extra = idl.groupExtraInfo {
            self.extra = .init(groupId: groupId, idl: extra)
        }
    }
}

// MARK: ---BuzIMGroupData.BaseInfo---
public extension BuzIMGroupData {
    
    @objcMembers
    class BaseInfo: NSObject {
        public var groupId: Int64 = 0 // 群id
        public var displayGroupName: String? // 业务显示用的群名，可能是群成员拼装的
        public var groupName: String? // 群名称
        public var portraitUrl: String? // 群头像
        public var firstFewPortraits: [String]? // 通过firstFewMemberInfos计算出来
        public var memberNum: Int32 = 0 // 当前成员数
        public var maxMemberNum: Int32 = 0 // 最大成员数
        public var groupStatus: BuzChatGroupStatus = .normal // 0 解散 1 正常
        public var serverPortraitUrl: String? // 服务端生成的群头像
        public var groupType: BuzChatGroupType = .normal // 群组类型 1-普通群 2-大群
        
        public override init() {
            super.init()
        }
        
        public init(groupId: Int64) {
            self.groupId = groupId
        }
    }
}

public extension BuzIMGroupData.BaseInfo {
    /// IDL对象初始化
    /// - Parameter idl: idl对象
    convenience init(idl: GroupBaseInfo) {
        self.init(groupId: idl.groupId)
        self.displayGroupName = idl.groupName
        self.groupName = idl.groupName
        self.portraitUrl = idl.portraitUrl
        self.memberNum = idl.memberNum
        self.maxMemberNum = idl.maxMemberNum
        self.groupStatus = BuzChatGroupStatus.init(rawValue: idl.groupStatus) ?? .normal
        self.serverPortraitUrl = idl.serverPortraitUrl
        self.groupType = BuzChatGroupType.init(rawValue: idl.groupType) ?? .normal
    }
    
    func setFirstFewMemberInfos(members: [BuzUserData]) {
        var groupName: String = ""
        var images: [String] = []
        
        members.forEach { obj in
            let firstName = obj.relation.remark ?? (obj.base.firstName?.isEmpty == false ? obj.base.firstName : obj.base.lastName) ?? ""
            
            if !firstName.isEmpty {
                if groupName.isEmpty {
                    groupName = firstName
                } else {
                    groupName += ", \(firstName)"
                }
            }
            
            if let portrait = obj.base.portrait, !portrait.isEmpty {
                images.append(portrait)
            }
        }
        
        // 去除首尾空格
        groupName = groupName.trimmingCharacters(in: .whitespaces)
        
        // 限制最大长度 50
        if groupName.count > 50 {
            groupName = String(groupName.prefix(50))
        }
        
        if (self.displayGroupName?.isEmpty ?? true) {
            self.displayGroupName = groupName
        }
        self.firstFewPortraits = images
    }
}

// MARK: ---BuzIMGroupData.ExtraInfo---
public extension BuzIMGroupData {
    @objcMembers
    class ExtraInfo: NSObject {
        public var groupId: Int64 = 0 // 群id
        public var canInvite: Bool = false // 是否可邀请新成员
        public var canEdit: Bool = true // 是否可修改群资料
        public var userRole: BuzChatGroupRole = .member // 1 群主 2 群管理员 3 普通群成员
        public var userStatus: BuzChatGroupUserStatus = .notJoined // 1 已加群 2 未加群 3 被踢出群
        public var serviceMuteMessages: BuzMuteMessagesType = .unknow // 服务端 静音群组消息,1-静音，2-不静音
        public var localMuteMessages: BuzMuteMessagesType = .unknow /// 本地记录 静音群组消息,1-静音，2-不静音
        public var muteNotification: BuzMuteNotificationType = .unknow // 静音群组通知,1-静音，2-不静音
        public var createTime: Int64 = 0 // 当前用户入群时间
        
        public override init() {
            super.init()
        }
        
        public init(groupId: Int64) {
            self.groupId = groupId
        }
    }

}

public extension BuzIMGroupData.ExtraInfo {
    /// IDL对象初始化
    /// - Parameter idl: idl对象
    convenience init(groupId: Int64, idl: GroupExtraInfo) {
        self.init(groupId: groupId)
        self.canInvite = idl.canInvite
        self.canEdit = idl.canEdit
        self.userRole = BuzChatGroupRole.init(rawValue: idl.userRole ?? BuzChatGroupRole.member.rawValue) ?? .member
        self.userStatus = BuzChatGroupUserStatus.init(rawValue: idl.userStatus) ?? .notJoined
        self.serviceMuteMessages = BuzMuteMessagesType.init(rawValue: idl.muteMessages ?? BuzMuteMessagesType.unknow.rawValue) ?? .unknow
        self.muteNotification = BuzMuteNotificationType.init(rawValue: idl.muteNotification ?? BuzMuteNotificationType.unknow.rawValue) ?? .unknow
        self.createTime = idl.joinGroupTime ?? 0
    }
}

public extension BuzIMGroupData.ExtraInfo {
    func isMuteMessages()-> Bool {
        if self.localMuteMessages != .unknow {
            return self.localMuteMessages == .on
        }
        return self.serviceMuteMessages == .on
    }

    func isMuteNotification()-> Bool {
        return self.muteNotification == .on
    }
}

