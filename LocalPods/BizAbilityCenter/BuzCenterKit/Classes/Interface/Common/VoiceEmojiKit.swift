//
//  VoiceEmojiKit.swift
//  buz
//
//  Created by st.chio on 2025/2/10.
//  Copyright © 2025 lizhi. All rights reserved.
//



@objc
public enum VoiceEmojiType : Int32 {
    case text = 0
    case image = 1
}

@objc
public protocol VoicemojiMessageEntity : NSObjectProtocol {
    var emojiId: Int64 {get set}
    var emojiIcon: String {get set}
    var emojiVoiceUrl: String {get set}
    var emojiAnimationUrl: String {get set}
    var emojiSuperscript: String {get set}
    var emojiNewAnimationUrl: String {get set}
    var emojiNewVoiceUrl: String {get set}
    var emojiAnimationType: Int32 {get set}
    var emojiDesc: String? {get set}
}
