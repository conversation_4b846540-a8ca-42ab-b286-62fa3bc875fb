//
//  BuzUserCenterKit.swift
//  BuzCenterKit
//
//  Created by st.chio on 2025/2/12.
//
import Combine
import Localizable

public class BuzMediaCenterKit {
    
}

public enum SpeakingVoiceType : Int32 {
    case retro = 0
    case popup = 1
    case bell = 2
    
    public static func value(_ type : Int32) -> SpeakingVoiceType {
        if let val = SpeakingVoiceType.init(rawValue: type) {
            return val
        }
        
        return .retro
    }
    
    public static func options() -> [SpeakingVoiceType] {
        return [.popup, .retro, .bell]
    }
}
