#
# Be sure to run `pod lib lint PCModuleKit.podspec' to ensure this is a
# valid spec before submitting.
#
# Any lines starting with a # are optional, but their use is encouraged
# To learn more about a Podspec see https://guides.cocoapods.org/syntax/podspec.html
#

Pod::Spec.new do |s|
  s.name             = 'BuzCenterKit'
  s.version          = '0.1.0'
  s.summary          = 'A short description of BuzCenterKit.'
  s.homepage         = 'https://gitlab.lizhi.fm/zhiyaPods/BuzCenterKit.git'
  s.license          = { :type => 'Copyright', :file => 'LICENSE' }
  s.author           = { 'shiqichao' => '<EMAIL>' }
  s.source           = { :git => 'https://gitlab.lizhi.fm/zhiyaPods/BuzCenterKit.git', :tag => s.version.to_s }

  s.ios.deployment_target = '10.0'
  
  s.pod_target_xcconfig = {'DEFINES_MODULE' => 'YES'}
  s.module_name = 'BuzCenterKit'
  
  s.source_files = 'Classes/*.{swift}'
  
  s.subspec 'Interface' do |subp|
    subp.subspec 'UserCenter' do |sp|
      sp.source_files = 'Classes/Interface/UserCenter/**/*.{swift}'
    end
    
    subp.subspec 'SocialCenter' do |sp|
      sp.source_files = 'Classes/Interface/SocialCenter/**/*.{swift}'
      
      sp.dependency 'BuzIDL'
      sp.dependency 'BuzFoundation'
    end
    
    subp.subspec 'IMCenter' do |sp|
      sp.source_files = 'Classes/Interface/IMCenter/**/*.{swift}'
    end
    
    subp.subspec 'MediaCenter' do |sp|
      sp.source_files = 'Classes/Interface/MediaCenter/**/*.{swift}'
    end
    
    subp.subspec 'Common' do |sp|
      sp.source_files = 'Classes/Interface/Common/**/*.{swift}'
    end
    
    
  end

end
