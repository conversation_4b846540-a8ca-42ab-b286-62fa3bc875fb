//
//  BuzIMCenter+Extension.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/14.
//


import VoderXClient

import BuzLog
import BuzCenterKit

public protocol BuzIMCenterCommonToolable {
    func canPassMessage(message: BuzIMMessage, conversationType: BuzConversationType) -> Bool
}

public extension BuzIMCenterCommonToolable {
    func canPassMessage(message: BuzIMMessage, conversationType: BuzConversationType) -> Bool {
        if message.messageType == .command,
           let content = message.im5Message.messageContent as? BuzCommandMessageContent,
           content.uiDisplay == false
           || message.conversationType != conversationType {
            return false
        }
        
        //在线直推的消息类型（LiveChat 弹幕、贴纸）都不需要展示到消息列表所以需要把这种类型消息过滤掉
        if let content = message.im5Message.messageContent as? IM5MessageContent , content.messageContentFlag == IM5ContentFlag.ONLINE_PUSH_ONLY_CONTENT {
            return false
        }
        
        return true
    }
}
