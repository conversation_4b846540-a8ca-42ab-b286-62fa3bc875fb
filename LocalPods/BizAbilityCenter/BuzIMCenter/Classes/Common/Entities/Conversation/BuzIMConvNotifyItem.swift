//
//  IMNotifyConversationItem.swift
//  buz
//
//  Created by lizhi on 2022/7/16.
//  Copyright © 2022 lizhi. All rights reserved.
//

import UIKit
import BuzLog
import VoderXClient

///会话监听代理参数
@objcMembers
public class BuzIMConvNotifyItem: NSObject {
    
    public let convNotifyItem: IM5NotifyConversationItem
    
    public let sourceId: Int64
    
    public init(convNotifyItem: IM5NotifyConversationItem) {
        self.convNotifyItem = convNotifyItem
        self.sourceId = Int64(convNotifyItem.conversation.targetId) ?? 0
        super.init()
    }
    
    //是否正在等待消息回调处理结束
    public var isWaitingNotfify: Bool = false {
        didSet{
            if isWaitingNotfify {
            }
        }
    }
}
