//
//  BuzConvLivePlaceUpdateTime.swift
//  buz
//
//  Created by sun on 2025/1/22.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import BuzCenterKit

@objcMembers
public class BuzConvLivePlaceUpdateTime: NSObject {
    
    public var type: BuzConversationType
    public var targetId: String
    public var lastUpdateTime: Int64 //会话消息最后更新时间（单位：毫秒）,发送消息，收到新消息的情况下，会更新此字段
    
    @objc
    public init(type : BuzConversationType , targetId : String , lastUpdateTime : Int64) {
        self.type = type
        self.targetId = targetId
        self.lastUpdateTime = lastUpdateTime
        super.init()
    }
    
    public override var description: String{
        return "targetId = \(targetId) ,type = \(self.type) ,lastUpdateTime = \(self.lastUpdateTime)"
    }
}
