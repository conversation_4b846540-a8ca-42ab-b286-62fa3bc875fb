//
//  BuzIMMessage.swift
//  buz
//
//  Created by liuyufeng on 2022/7/11.
//  Copyright © 2022 lizhi. All rights reserved.
//

import VoderXClient

import BuzLog
import BuzCenterKit
import BuzUserSession

extension IM5CryptStatus: CustomStringConvertible {
    public var description: String {
        switch self {
        case .none:
            return "none"
        case .success:
            return "success"
        case .failed:
            return "failed"
        @unknown default:
            return "unknown"
        }
    }
}

@objcMembers
public class BuzIMMessage: NSObject {
    /// im5内部消息对象
    public private(set) var im5Message: IM5Message
    /// 目标对象id
    public let targetUid: String
    // 来源对象id
    public let fromUid: String
    // 来源对象id
    public var fileSize: Int64 = 0
    /// 消息类型
    public let messageType: BuzIMContentType
    /// 消息操作动作
    public let msgOpreation: BuzIMMessageOperation = .update
    // 发送群消息是需要传入的群聊信息；私聊无须理会该属性
    public var groupInfo: BuzMessageGroupInfo?
    /// 会话类型
    public let conversationType: BuzConversationType
    /// 用户关系信息，如：消息点赞
    public var reaction: BuzIMMessageReaction = BuzIMMessageReaction.init()
    /// extra
    public let extra: BuzIMMessageExtra
    /// localExtra
    public let localExtra: BuzIMMessage.LocalExtraWrapper
    
    /// 是否自己发送的消息 ；
    /// 调用 init(im5Message : IM5Message) 此初始化方法才会真正赋值
    public var isSentByMySelf: Bool = false
    
    // 发送的消息对方离线是是否发送apns推送
    public private(set) var supportApnsPush: Bool = true
    
    /// 业务处理后的时间戳：通过IM5Message消息创建的对象为createTime ; 在历史页中如果有获取到拉流时间戳的话则会修改成本地拉流的时间戳，并且历史页的消息会根据这个字段进行排序。单位：ms
    public var businessTimestamp: Int64
    
    public var isLocalMsg: Bool = false
    /// 从远端拉取的新群聊消息
    public var isNewGroupMsg: Bool = false
    ///是否推送TimeSensitive
    public var isPushTimeSensitive : Bool = false
    ///跳转preview
    public var isJumpOnAirPreview : Bool = false
    
    /// 链路追踪id
    /// 语音消息 - 录音开始时生成
    /// 文本、图片消息 - 发送消息时生成
    public var traceId: String = "" {
        didSet {
            im5Message.setMsgTraceId(traceId)
        }
    }
    
    public var referenceMsgId: Int64 = 0 {
        didSet {
            if referenceMsgId > 0 {
                im5Message.setReferenceMsgId(referenceMsgId)
            }
        }
    }
    
    public init(im5Message: IM5Message,
                targetUid: String,
                fromUid: String,
                messageType: BuzIMContentType,
                conversationType: BuzConversationType,
                businessTimestamp: Int64,
                extra: BuzIMMessageExtra,
                supportApnsPush: Bool = true) {
        self.im5Message = im5Message
        self.targetUid = targetUid
        self.fromUid = fromUid
        self.messageType = messageType
        self.conversationType = conversationType
        self.businessTimestamp = businessTimestamp
        self.extra = extra
        self.localExtra = .init(im5Message: im5Message)
        self.supportApnsPush = supportApnsPush
        super.init()
    }
    
    /// im5Message : 接收到的im5信息对象
    public init(im5Message: IM5Message) {
        let cryptStatus = im5Message.cryptStatus

        targetUid = im5Message.targetId
        fromUid = im5Message.fromId
        if im5Message.isSend {
//            BuzIMLog.info("E2EE>> Encrypt status:\(cryptStatus), convType:\(im5Message.conversationType.rawValue)")
            messageType = BuzIMMessage.messageContentType(im5Message)
        } else {
//            BuzIMLog.info("E2EE>> Decrypt status:\(cryptStatus), convType:\(im5Message.conversationType.rawValue)")
            if cryptStatus != .failed {
                messageType = BuzIMMessage.messageContentType(im5Message)
            } else {
//                BuzIMLog.error("E2EE>> Decrypt status failed, convType:\(im5Message.conversationType.rawValue)")
                if im5Message.conversationType == .peer {
                    messageType = BuzIMContentType.decryptFailed
                } else {
                    messageType = BuzIMContentType.unknown
                }
            }
        }
        self.im5Message = im5Message

        conversationType = BuzConversationType(rawValue: im5Message.conversationType.rawValue) ?? .peer
        isSentByMySelf = Int64(im5Message.fromId) == BuzUserSession.shared.uid
        
        self.localExtra = .init(im5Message: im5Message)

        if let dict: [String: Any] = self.im5Message.messageContent.extra?.dictionary() as? [String: Any] {
            extra = BuzIMMessageExtra(dict: dict)
        } else {
            extra = BuzIMMessageExtra(dict: [:])
        }
      
        if localExtra.pullStreamTimestamp > 0 {
            businessTimestamp = localExtra.pullStreamTimestamp
            BuzIMLog.debug("消息businessTimestamp来源 localExtra-拉流时间 = \(businessTimestamp)")
        } else {
            businessTimestamp = im5Message.createTime
//            BuzIMLog.debug("消息businessTimestamp来源 im5 - createtime = \(businessTimestamp)")
        }

        // 从extra拿到服务端设置的streamId , 赋值到localExtra中
        if let extra = self.im5Message.messageContent.extra, let streamId: Int64 = extra.object(forKey: "streamId") as? Int64 {
            localExtra.streamId = streamId
        }

        if let traceId = self.im5Message.msgTraceId {
            self.traceId = traceId
        }
        
        if let msgContent = im5Message.messageContent as? IM5AttachmentMessageContent {
            self.fileSize = msgContent.totalBytes
        }
        
        if let reaction = self.im5Message.reaction {
            self.reaction = BuzIMMessageReaction.init(im: reaction)
        }

        super.init()

        if conversationType == .group,
           let groupId = sourceId {
            groupInfo = BuzMessageGroupInfo()
            groupInfo?.groupId = groupId
        }
    }
    
    public override var hash: Int {
        if im5Message.serverMsgId > 0 {
            return im5Message.serverMsgId.hashValue
        }

        if im5Message.msgId > 0 {
            return im5Message.msgId.hashValue
        }

        return im5Message.hash
    }

    public override func isEqual(_ object: Any?) -> Bool {
        guard let otherMessage = object as? BuzIMMessage else {
            return false
        }
        return im5Message.identity(with: otherMessage.im5Message)
    }

    public override var description: String {
        var desc = "fromeUid = \(self.fromUid) , targetId = \(self.targetUid) , msgType = \(self.messageType.rawValue) , isGroup = \(conversationType == .group) , servermsgId = \(self.im5Message.serverMsgId) ; msgId = \(self.im5Message.msgId) ; createTime = \(self.im5Message.createTime) [\(Date(timeIntervalSince1970: TimeInterval(self.im5Message.createTime/Int64(1000.0))))] , groupInfo = \(String(describing: self.groupInfo)) , isRead = \(self.localExtra.isRead) , traceId = \(self.traceId) , IM5-traceId = \(self.im5Message.msgTraceId ?? "") , replyMsgId = \(extra.serverExtra.replyMsgId ?? "")"
        
        if im5Message.messageContent is BuzWalkieTalkieMessageContent {
            desc += "【WalkieTalkie message】"
        }
        
        var obj = self
        withUnsafePointer(to: &obj) { ptr in
            desc += "【ptr = \(ptr)】"
        }

        return desc
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

public extension BuzIMMessage {
    func update(IM5Message: IM5Message) {
        im5Message = IM5Message
    }
}
