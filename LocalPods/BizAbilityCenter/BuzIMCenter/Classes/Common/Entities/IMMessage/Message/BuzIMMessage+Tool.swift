//
//  BuzIMMessage+Tool.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/14.
//

import VoderXClient
import BuzCenterKit

public extension BuzIMMessage {
    /// 获取消息内容类型
    static func messageContentType(_ message: IM5Message) -> BuzIMContentType {
        let msgContent = message.messageContent
        let im5Type = type(of: msgContent).getContentType()
        let type = BuzIMContentType(rawValue: im5Type.rawValue) ?? .unknown
        
        return type
    }

    /// 获取语音消息时长（单位：秒）
    static func getDurationWithVoiceMessage(_ voiceMessage: IM5Message) -> Int64 {
        let type = messageContentType(voiceMessage)
        guard type == .count_WalkieTalkie,
              let voiceContent = voiceMessage.messageContent as? IM5VoiceMessageContent else {
            return 0
        }
        
        return Int64(voiceContent.duration / 1000)
    }
}

public extension BuzIMMessage {
    
    func messageContent<T>() -> T? {
        return im5Message.messageContent as? T
    }
}
