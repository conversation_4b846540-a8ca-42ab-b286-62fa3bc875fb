//
//  BuzIMLocalExtra.swift
//  buz
//
//  Created by st.chio on 2025/3/6.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import YYModel
import BuzLog

public enum TranslateTextDisplayControl: Int {
    case none = 0      // 默认态
    case show = 1      // 操作了显示
    case hide = 2      // 操作了隐藏
}

public protocol BuzIMMessageLocalExtrable {
    var isRead: Bool {get set}
    var isReply: Bool  {get set}
    var pullStreamTimestamp: Int64 {get set}
    var streamId: Int64  {get set}
    var asrText: String? {get set} /// 默认为 nil
    var isHiddenAsrText: Bool {get set}
    var textTranslateFailed: Bool {get set}
    var translateTextDisplayControl: TranslateTextDisplayControl {get set}
    var isAsrPreviewClosed: Bool {get set}
    var msgSendNtpTime: Int64 {get set}
    var locationType: String? {get set}
    var isFoward: Bool {get set}
    var isSetMsgPlayed: Bool {get set}
    
    var voiceToTextIsTranslate: Bool {get set}
    var voiceToText: String? {get set}
    
    var trackSource: String? {get set}
    var trackContent_name: String? {get set}
}

//MARK: --BuzIMMessage.LocalExtraWrapper
public extension BuzIMMessage {
    class LocalExtraWrapper {
        private(set) var im5Message: IM5Message
        private(set) var rawData: BuzIMMessage.LocalExtra
        
        public init(im5Message: IM5Message) {
            self.im5Message = im5Message
            
            if let dict: [String: Any] = self.im5Message.messageContent.localExtra?.dictionary() as? [String: Any] {
                self.rawData = .init(dict: dict)
            }else{
                self.rawData = .init()
            }
            
            addReplyStatusNotic()
        }
    }
}

private extension BuzIMMessage.LocalExtraWrapper {
    func addReplyStatusNotic() {
        NotificationCenter.default.addObserver(forName: .IMMessageReplyStatusDidUpdate , object: nil, queue: .main) { [weak self] notification in
            guard let self = self , let obj = notification.object as? BuzIMMessage.LocalExtraWrapper ,
                    obj.im5Message.msgId == self.im5Message.msgId , obj !== self else {
                return
            }
            
            let isReply = obj.isReply
            self.isReply = isReply
            BuzIMLog.debug("did received isReply status change -> isReply = \(isReply) , msgId = \(self.im5Message.msgId)")
        }
    }
}

//MARK: --BuzIMMessage.LocalExtra
extension BuzIMMessage {
    @objcMembers
    class LocalExtra: NSObject, BuzIMMessageLocalExtrable {
        
        // MARK: - BuzIMMessageLocalExtrable
        private var originalDict: [String: Any]?
        // - Properties
        public var isRead: Bool = false
        public var isReply: Bool = false
        public var pullStreamTimestamp: Int64 = 0
        public var streamId: Int64 = 0
        public var asrText: String?  /// 默认为 nil
        public var isHiddenAsrText: Bool = false
        public var textTranslateFailed: Bool = false
        public var translateTextDisplayControl: TranslateTextDisplayControl = .none
        public var isAsrPreviewClosed: Bool = false
        public var msgSendNtpTime: Int64 = 0
        public var locationType: String?
        public var isFoward: Bool = false
        public var isSetMsgPlayed: Bool = false
        
        public var voiceToTextIsTranslate: Bool = false
        public var voiceToText: String?
        
        public var trackSource: String?
        public var trackContent_name: String?
        
        override init() {
            super.init()
        }
        
        // MARK: - Init
        init(dict: [String: Any]) {
            super.init()
            self.parseData(dict: dict)
            self.originalDict = dict
        }
        
        // MARK: - YYModel
        @objc
        class func modelPropertyBlacklist() -> [String] {
            return ["originalDict"]
        }
    }
}

public enum BuzIMLocalExtra: String {
    case isRead
    case isReply
    case pullStreamTimestamp
    case streamId
    case asrText
    case isHiddenAsrText
    case textTranslateFailed
    case translateTextDisplayControl
    case isAsrPreviewClosed
    case msgSendNtpTime
    case locationType
    case isFoward
    case isSetMsgPlayed
    
    case voiceToTextIsTranslate = "VoiceToTextIsTranslate"
    case voiceToText = "VoiceToText"
    
    case trackSource
    case trackContent_name
    
    public var Key: String {
        return self.rawValue
    }
}

extension BuzIMMessage.LocalExtra {
    
    func parseData(dict: [String: Any]) {
        if let value = dict[BuzIMLocalExtra.isRead.Key] as? Bool {
            self.isRead = value
        }
        
        if let value = dict[BuzIMLocalExtra.pullStreamTimestamp.Key] as? Int64 {
            self.pullStreamTimestamp = value
        }
        
        if let value = dict[BuzIMLocalExtra.streamId.Key] as? Int64  {
            self.streamId = value
        }
        
        if let value = dict[BuzIMLocalExtra.isReply.Key] as? Bool {
            self.isReply = value
        }
        
        if let value = dict[BuzIMLocalExtra.asrText.Key] as? String {
            self.asrText = value  /// 默认为 nil
        }
        
        if let value = dict[BuzIMLocalExtra.isHiddenAsrText.Key] as? Bool  {
            self.isHiddenAsrText = value
        }
        
        if let value = dict[BuzIMLocalExtra.textTranslateFailed.Key] as? Bool {
            self.textTranslateFailed = value
        }
        
        if let value = dict[BuzIMLocalExtra.translateTextDisplayControl.Key] as? TranslateTextDisplayControl {
            self.translateTextDisplayControl = value
        }else if let value = dict[BuzIMLocalExtra.translateTextDisplayControl.Key] as? NSNumber {
            let control = value.intValue
            self.translateTextDisplayControl = TranslateTextDisplayControl(rawValue: control) ?? .none
        }
        
        if let value = dict[BuzIMLocalExtra.isAsrPreviewClosed.Key] as? Bool {
            self.isAsrPreviewClosed = value
        }
        
        if let value = dict[BuzIMLocalExtra.msgSendNtpTime.Key] as? Int64 {
            self.msgSendNtpTime = value
        }
        
        if let value = dict[BuzIMLocalExtra.locationType.Key] as? String {
            self.locationType = value
        }
        
        if let value = dict[BuzIMLocalExtra.isFoward.Key] as? Bool {
            self.isFoward = value
        }
        
        if let value = dict[BuzIMLocalExtra.isSetMsgPlayed.Key] as? Bool {
            self.isSetMsgPlayed = value
        }
        
        if let value = dict[BuzIMLocalExtra.voiceToTextIsTranslate.Key] as? Bool {
            self.voiceToTextIsTranslate = value
        }
        
        if let value = dict[BuzIMLocalExtra.voiceToText.Key] as? String {
            self.voiceToText = value
        }
        
        if let value = dict[BuzIMLocalExtra.trackSource.Key] as? String {
            self.trackSource = value
        }
        if let value = dict[BuzIMLocalExtra.trackContent_name.Key] as? String {
            self.trackContent_name = value
        }
    }
}

