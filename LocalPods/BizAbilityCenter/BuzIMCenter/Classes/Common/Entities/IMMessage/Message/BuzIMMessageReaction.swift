//
//  BuzIMMessageReaction.swift
//  buz
//
//  Created by st.chio on 2024/3/29.
//  Copyright © 2024 lizhi. All rights reserved.
//

import UIKit
import BuzUserSession
import VoderXClient
import BuzCenterKit

//MARK: ----IMMessageReaction--------------------------
@objcMembers
public class BuzIMMessageReaction: NSObject {
    
    public var lastAction: BuzIMMessageReactionAction?
    public var reactions: [BuzIMMessageReactionInfo]?
    
    public override init() {
        super.init()
    }
    
    public init(im: IM5MessageReaction) {
        super.init()
        
        if let lastAction = im.lastAction {
            self.lastAction = .init(im: lastAction)
        }
        if let reactionLists = im.reactionLists {
            var reactions = [BuzIMMessageReactionInfo].init()
            reactionLists.forEach({ info in
                reactions.append(.init(im: info))
            })
            self.reactions = reactions
        }
    }
}

public extension BuzIMMessageReaction {
    
    func hasQuickReact() ->Bool {
        guard let reactions = self.reactions else { return false }
        if reactions.filter({$0.type == BuzIMMessageReactionInfo.ReactionType.quickReact_voicemoji.rawValue}).count > 0 {
            return true
        }
        
        return false
    }
    
    func quickReactReations() ->[BuzIMMessageReactionInfo]? {
        guard let reations = self.reactions else { return nil }
        let datas = reations.filter({$0.type == BuzIMMessageReactionInfo.ReactionType.quickReact_voicemoji.rawValue})
        
        return datas
    }
    

    func quickReactForUserId(userId: String?) -> BuzIMMessageReactionInfo? {
        guard let userId = userId, let reations = self.reactions else { return nil }
        let reactionInfo = reations.filter({$0.reactionType == .quickReact_voicemoji && $0.users.filter({$0 == userId}).count > 0}).first

        return reactionInfo
    }
}


public extension BuzIMMessageReaction {
    
    func addQuickReact(info: BuzIMMessageReactionInfo) {
        if let reactions = self.reactions {
            if let existInfo = reactions.filter({$0.identifier == info.identifier}).first {
                existInfo.users += info.users
                existInfo.userCount = existInfo.users.count
            }else {
                self.reactions?.insert(info, at: 0)
            }
        }else{
            self.reactions = .init()
            self.reactions?.insert(info, at: 0)
        }
    }
    
    func replaceQuickReact(info: BuzIMMessageReactionInfo, oldInfo: BuzIMMessageReactionInfo) {
        if let reactions = self.reactions {
            if let existInfo = reactions.filter({$0.identifier == oldInfo.identifier}).first {
                existInfo.users.removeAll("\(BuzUserSession.shared.uid)")
                existInfo.userCount = existInfo.users.count
                if existInfo.userCount == 0 {
                    self.reactions?.removeAll(existInfo)
                }
            }
            
            if let existInfo = reactions.filter({$0.identifier == info.identifier}).first {
                existInfo.users += info.users
                existInfo.userCount = existInfo.users.count
            }else {
                self.reactions?.insert(info, at: 0)
            }
        }else{
            self.reactions = .init()
            self.reactions?.append(info)
        }
    }
    
    func deleteQuickReact(info: BuzIMMessageReactionInfo) {
        if let reactions = self.reactions {
            if let existInfo = reactions.filter({$0.identifier == info.identifier}).first {
                if let userId = info.users.first, let index = existInfo.users.firstIndex(of: userId) {
                    existInfo.users.remove(at: index)
                    existInfo.userCount = existInfo.users.count
                }
                if existInfo.users.count == 0, let index = self.reactions?.firstIndex(of: existInfo)  {
                    self.reactions?.remove(at: index)
                }
            }
        }
    }
}

//MARK: ----IMMessageReactionAction--------------------------
@objcMembers
public class BuzIMMessageReactionAction: NSObject {
    
    public enum OpType: Int {
        case none = 0
        case add = 1
        case delete = 2
        case replace = 3
    }
    
    //操作类型 1-添加表情回复 2-删除表情回复 3-替换表情回复
    public var opType: OpType = .none
    //操作人userId
    public var opUserId: String?
    //新reaction类型
    public var reactionType: String?
    //新reactionID
    public var reactionId: String?
    //旧reaction类型
    public var oldReactionType: String?
    //新reactionID
    public var oldReactionId: String?
    
    public init(im: IM5MessageReactionAction) {
        super.init()
        if let type = Int(im.opType), let opType = OpType.init(rawValue: type) {
            self.opType = opType
        }
        self.opUserId = im.opUserId
        self.reactionType = im.reactionType
        self.reactionId = im.reactionId
        self.oldReactionType = im.oldReactionType
        self.oldReactionId = im.oldReactionId
    }
    
    public func isQuickReaction() -> Bool {
        if let type = self.reactionType,
           let rType = BuzIMMessageReactionInfo.ReactionType.init(rawValue: type),
           rType == .quickReact_voicemoji {
            return  true
        }
        return false
    }
    
    public func opUserIsSelf() -> Bool {
        return self.opUserId == "\(BuzUserSession.shared.uid)"
    }
}

//MARK: ----IMMessageReactionInfo--------------------------
@objcMembers
public class BuzIMMessageReactionInfo: NSObject {
    
    public enum ReactionType:String {
        case none = ""
        case quickReact_voicemoji = "imMessageReactionQuickReactVoicemoji"
    }
    
    // 类型
    public var type: String = ""
    // 标志符
    public var identifier: String = ""
    // 用户列表（可能不是完整的）
    public var users: [String] = .init()
    // 完整的用户数量
    public var userCount: Int = 0
    
    public var emoji: String = ""
    
    public init(im: IM5MessageReactionInfo) {
        super.init()
        self.type = im.type
        self.identifier = im.identifier
        self.users = im.users
        self.userCount = im.userCount
    }
    
    public init(type: ReactionType, identifier: String, emoji: String? = nil) {
        super.init()
        self.type = type.rawValue
        self.identifier = identifier
        self.emoji = emoji ?? ""
        self.users = ["\(BuzUserSession.shared.uid)"]
        self.userCount = self.users.count
    }
    
    /// 当前资源关系下自己是否存在
    public func inUsersOfSelf() ->Bool {
        if let user = self.users.filter({$0 == "\(BuzUserSession.shared.uid)"}).first, !user.isEmpty {
            return true
        }
        return false
    }
}

public extension BuzIMMessageReactionInfo {
    
    var reactionType: BuzIMMessageReactionInfo.ReactionType {
        let type = BuzIMMessageReactionInfo.ReactionType.init(rawValue: self.type) ?? .none
        return type
    }
    
    var pushReactionType: BuzPushReactionType {
        let type = BuzIMMessageReactionInfo.ReactionType.init(rawValue: self.type) ?? .none
        return type == .quickReact_voicemoji ? .quickReact_voicemoji : .none
    }
}
