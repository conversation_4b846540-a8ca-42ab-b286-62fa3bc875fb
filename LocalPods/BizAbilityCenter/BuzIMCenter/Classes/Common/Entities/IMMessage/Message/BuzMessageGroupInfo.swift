//
//  BuzMessageGroupInfo.swift
//  buz
//
//  Created by liuyufeng on 2022/8/18.
//  Copyright © 2022 lizhi. All rights reserved.
//

import UIKit
import BuzIDL
import YYModel
import BuzLog
import BuzCenterKit

@objcMembers
public class BuzMessageGroupInfo: NSObject {
    public var portrait : String?
    public var groupId : Int64 = 0
    public var groupName : String?
    public var serverPortrait : String?
    public var members : [IMPushUserInfo]? {
        didSet{
            if let members = members {
                
                membersJsonArray = members.compactMap { userInfo in
                    userInfo.yy_modelToJSONObject() as? [String : AnyObject]
                }
                
                BuzIMLog.info("MessageGroupInfo of members to jsonstr = \(membersJsonArray)")
            }
            
        }
    }
    
    public static func convertFrom(info : BuzIMGroupData) -> BuzMessageGroupInfo {
        let gInfo = BuzMessageGroupInfo.init()
        gInfo.groupId = info.groupId
        gInfo.groupName = info.base.groupName
        gInfo.portrait = info.base.portraitUrl
        gInfo.serverPortrait = info.base.serverPortraitUrl
        
        return gInfo
    }
    
    public private(set) var membersJsonArray : [[String : AnyObject]]?
    
    public override var description: String {
        get {
            return "groupId = \(self.groupId) , groupName = \(String(describing: self.groupName)) , portrait = \(String(describing: self.portrait)) , members = \(String(describing: self.members))"
        }
    }
}

@objcMembers
@objc(BuzIMPushUserInfo)
//群聊 - 群成员用户信息（没有使用头像）
public class IMPushUserInfo: NSObject {
    public var userId : Int64
    public var userName : String
    
    public init(userId : Int64 , userName : String) {
        self.userId = userId
        self.userName = userName
        super.init()
    }
}
