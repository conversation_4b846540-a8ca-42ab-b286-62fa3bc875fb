//
//  BuzIMMessageNotifyItem.swift
//  buz
//
//  Created by liuyufeng on 2022/7/15.
//  Copyright © 2022 lizhi. All rights reserved.
//

import BuzCenterKit

///实时接受的消息对象
@objcMembers
public class BuzIMMessageNotifyItem: NSObject {

    public let message : BuzIMMessage
    public let operation :  BuzIMMessageOperation
    public var customPushDict : [String : Any]?    // 定制化的通知栏内容
    
    public init(message : BuzIMMessage , operation : BuzIMMessageOperation) {
        self.message = message
        self.operation = operation
        super.init()
    }
}
