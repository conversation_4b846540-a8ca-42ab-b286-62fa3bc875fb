//
//  BuzIMMessageExtra.swift
//  buz
//
//  Created by st.chio on 2025/3/6.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import YYModel

// MARK: - BuzIMMessageExtra
@objcMembers
public class BuzIMMessageExtra: NSObject {
    // MARK: - Properties
    public private(set) var originalDict: [String: Any]?
    public private(set) var serverExtra: BuzIMMessageExtra.ServerExtra
    
    // MARK: - Init
    public init(dict: [String: Any]) {
        self.originalDict = dict
        let serverExtraDict = dict["serverExtra"] as? [String: Any] ?? [:]
        self.serverExtra = BuzIMMessageExtra.ServerExtra(dict: serverExtraDict)
        super.init()
    }
    
    // MARK: - Public Methods
    public func toExtraDict() -> [String: Any] {
        guard var mDict = self.yy_modelToJSONObject() as? [String: Any] else {
            return [:]
        }
        
        // 合并原始字典中的值
        originalDict?.forEach { key, value in
            if mDict[key] == nil {
                mDict[key] = value
            }
        }
        
        // 处理 serverExtra
        if var serverExtra = mDict["serverExtra"] as? [String: Any] {
            if !((serverExtra["guidanceMsg"] as? NSNumber)?.boolValue ?? false) {
                // 不是引导消息移除多余的数据后发送
                serverExtra.removeValue(forKey: "guidanceMsg")
                if (serverExtra["guidanceStepType"] as? NSNumber)?.intValue == 0 {
                    serverExtra.removeValue(forKey: "guidanceStepType")
                }
            }
            mDict["serverExtra"] = serverExtra
        }
        
        return mDict
    }
    
    // MARK: - YYModel
    @objc
    public class func modelPropertyBlacklist() -> [String] {
        return ["originalDict"]
    }
}

// MARK: - BuzIMMessageExtra.ServerExtra
public enum GuidanceStepType: Int {
    case receivedBeginTipMsg = 1
    case receivedVEMsg = 2
    case receivedCompleteTextMsg = 3
    case receivedCompleteCardMsg = 4
    case receivedExcetionalCardMsg = 5
}



public extension BuzIMMessageExtra {
    @objcMembers
    class ServerExtra: NSObject {
        // MARK: - Properties
        public var userLanguage: String = ""
        //used in transalte bot send msg
        public var sourceLanguage: String?
        public var targetLanguage: String?
        public var replyMsgId: String?
        public var timeZoneId: String?
        public var clientVersion: String?
        public var deviceType: String?
        public var guidanceMsg: Bool = false
        public var guidanceStepType: GuidanceStepType = .receivedBeginTipMsg
        public var preDeliveryType: Int = 0
        
        private var originalDict: [String: Any]?
        
        // MARK: - Init
        public init(dict: [String: Any]) {
            super.init()
            
            userLanguage = dict.string(forKey: "userLanguage") ?? ""
            sourceLanguage = dict.string(forKey: "sourceLanguage")
            targetLanguage = dict.string(forKey: "targetLanguage")
            replyMsgId = dict.string(forKey: "replyMsgId")
            timeZoneId = dict.string(forKey: "timeZoneId")
            clientVersion = dict.string(forKey: "clientVersion")
            deviceType = dict.string(forKey: "deviceType")
            guidanceMsg = dict.number(forKey: "guidanceMsg")?.boolValue ?? false
            guidanceStepType = GuidanceStepType(rawValue: dict.number(forKey: "guidanceStepType")?.intValue ?? 1) ?? .receivedBeginTipMsg
            preDeliveryType = dict.number(forKey: "preDeliveryType")?.intValue ?? 0 
            originalDict = dict
        }
        
        // MARK: - Public Methods
        public func toExtraDict() -> [String: Any] {
            guard var mDict = self.yy_modelToJSONObject() as? [String: Any] else {
                return [:]
            }
            
            self.originalDict?.forEach { key, value in
                if mDict[key] == nil {
                    mDict[key] = value
                }
            }
            
            return mDict
        }
        
        // MARK: - YYModel
        @objc
        public class func modelPropertyBlacklist() -> [String] {
            return ["originalDict"]
        }
    }
}

// MARK: - Dictionary Extensions
private extension Dictionary where Key == String {
    func string(forKey key: String) -> String? {
        return self[key] as? String ?? nil
    }
    
    func number(forKey key: String) -> NSNumber? {
        return self[key] as? NSNumber
    }
}
