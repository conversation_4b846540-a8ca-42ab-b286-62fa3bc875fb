//
//  BuzIMMessage+Extra.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/17.
//

import VoderXClient

//MARK: --TimeZoneId
public extension BuzIMMessage {
    
    static func setUpTimeZoneIdToContentExtra(_ msgContent : IM5MessageContent) {
        let extraObj = BuzIMMessageExtra.init(dict: [:])
        extraObj.serverExtra.timeZoneId = TimeZone.current.identifier
        extraObj.serverExtra.clientVersion = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? ""
        extraObj.serverExtra.deviceType = "iOS \(UIDevice.current.systemVersion)"
        msgContent.extra = IM5Extra.init(dictionary: extraObj.toExtraDict())
    }
    
}
