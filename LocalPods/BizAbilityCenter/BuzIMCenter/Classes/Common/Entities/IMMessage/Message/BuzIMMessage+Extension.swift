//
//  BuzIMMessage+Extension.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/17.
//

import VoderXClient
import VoderXCryptClient

import BuzUserSession
import BuzStorage
import BuzTracker
import BuzLog
import BuzCenterKit
import BuzAppConfiger
import BuzConfig

public extension BuzIMMessage {
    var cryptKeyInfo: E2EEKeyInfo? {
        if let keyData = im5Message.cryptKeyData {
            let validSize = E2EEKeyInfo.IvLength + E2EEKeyInfo.KeyLength
            assert(keyData.count == validSize)
            if (keyData.count == validSize) {
                return E2EEKeyInfo(combinedData: keyData)
            } else {
                BuzIMLog.error("Invalid cryptKeyData:\(keyData.base64EncodedString())")
                
                TrackManager.reportInvalidKeyData(keyData)
                return nil
            }
        } else {
            return nil
        }
    }
}

public extension BuzIMMessage {
    /// 用于首页定位匹配场景（消息是来自哪个群，或者哪个用户的）
    var sourceId: Int64? {
        (conversationType == .group) ? Int64(im5Message.targetId) : Int64(im5Message.fromId)
    }

    // 对方ID，无论是否是自己发的还是对方发的消息， 群为群ID
    var opponentID: Int64? {
        if self.conversationType == .group {
            return Int64(self.im5Message.targetId)
        }

        let friendUid = Int64(self.im5Message.isSend ? self.im5Message.targetId : self.im5Message.fromId)
        return friendUid
    }
}

public extension BuzIMMessage {
    
    func sessionKey(type : BuzStorageUnitPriority = .sdimage) -> String {
        if self.conversationType == .group {
            //in group, we need to use self uid and group id as targetId
            return NSString.storage_sessionKey(with: "\(BuzUserSession.shared.uid)", targetId: self.targetUid,
                                               type: self.conversationType.rawValue, messageId:self.storageId(), mediaType: type.rawValue)
        } else if self.isSentByMySelf {
            return NSString.storage_sessionKey(with: "\(BuzUserSession.shared.uid)", targetId: self.targetUid,
                                               type: self.conversationType.rawValue, messageId:self.storageId(), mediaType: type.rawValue)
        } else {
            return NSString.storage_sessionKey(with: "\(BuzUserSession.shared.uid)", targetId: self.fromUid,
                                               type: self.conversationType.rawValue, messageId:self.storageId(), mediaType: type.rawValue)
        }
    }
    
    func storageId() -> Int64 {
        if self.messageType == .voicegif {
            return self.im5Message.msgId
        } else {
            return self.im5Message.serverMsgId
        }
    }
    
    func messagePrimaryKeys() -> (userId : Int64, targetId : Int64, type : BuzConversationType, msgId : Int64, servMsgId : Int64) {
        if self.conversationType == .group {
            //in group, we need to use self uid and group id as targetId
            return (userId :BuzUserSession.shared.uid, targetId :self.targetUid.nsString.longLongValue, type :self.conversationType,
                    msgId :self.im5Message.msgId, servMsgId:self.storageId())
        } else if self.isSentByMySelf {
            return (userId :BuzUserSession.shared.uid, targetId :self.targetUid.nsString.longLongValue, type :self.conversationType,
                    msgId :self.im5Message.msgId, servMsgId:self.storageId())
        } else {
            return (userId :BuzUserSession.shared.uid, targetId :self.fromUid.nsString.longLongValue, type :self.conversationType,
                    msgId :self.im5Message.msgId, servMsgId:self.storageId())
        }
    }
    
    @objc func deleteStorage() {
        var entity : BuzStorageDeletable.MessagesEntity?
        
        if self.conversationType == .group {
            //in group, we need to use self uid and group id as targetId
            entity = BuzStorageDeletable.MessagesEntity.init(userId: BuzUserSession.shared.uid,
                                                             targetId: self.targetUid.nsString.longLongValue,
                                                             conversationType: IM5ConversationType.group.rawValue,
                                                             servMsgId: self.storageId(),
                                                             msgId: self.im5Message.msgId)
        } else {
            if self.fromUid.nsString.longLongValue == BuzUserSession.shared.uid {
                entity = BuzStorageDeletable.MessagesEntity.init(userId: BuzUserSession.shared.uid,
                                                                 targetId: self.targetUid.nsString.longLongValue,
                                                                 conversationType: IM5ConversationType.peer.rawValue,
                                                                 servMsgId: self.storageId(),
                                                                 msgId: self.im5Message.msgId)
            } else {
                entity = BuzStorageDeletable.MessagesEntity.init(userId: BuzUserSession.shared.uid,
                                                                 targetId: self.fromUid.nsString.longLongValue,
                                                                 conversationType: IM5ConversationType.peer.rawValue,
                                                                 servMsgId: self.storageId(),
                                                                 msgId: self.im5Message.msgId)
            }
        }
        
        if let content = self.im5Message.messageContent as? BuzVideoMessageContent {
            let pathComponent = "\(content.orgVideoPath.lastPathComponent)"
            let coverPathComponent = "\(content.coverLocalPath)"
            
            if pathComponent.length != 0, let tempPath = BuzConfig.IMMediaTempDir?.appendingPathComponent(pathComponent),
                FileManager.default.fileExists(atPath: tempPath) {
                try? FileManager.default.removeItem(atPath: tempPath)
            }
            
            if pathComponent.length != 0, let mediaPath = BuzConfig.IMMediaDir?.appendingPathComponent(pathComponent),
               FileManager.default.fileExists(atPath: mediaPath) {
                try? FileManager.default.removeItem(atPath: mediaPath)
            }
            
            if coverPathComponent.length != 0, (FileManager.default.fileExists(atPath: coverPathComponent)) {
                try? FileManager.default.removeItem(atPath: coverPathComponent)
            }
        }
        
        if let entity = entity {
            StorageManageRegiter.service().update(entity: entity)
        }
    }
    
    func mentionUserId() -> String? {
        var mentionId : String? = nil

        if let content = self.im5Message.messageContent as? BuzTextMessageContent {
            content.mentionedInfo?.mentionedMaps.forEach({ (key: String, value: BuzMessageMentionedUser) in
                mentionId = "\(value.userId)"
            })
        }else if let content = self.im5Message.messageContent as? BuzWalkieTalkieCountMessageContent {
            content.mentionedUsers?.forEach({ value in
                mentionId = "\(value.userId)"
            })
        }

        return mentionId
    }
}
