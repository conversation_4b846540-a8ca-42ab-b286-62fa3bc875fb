//
//  BuzIMLocalExtraWrapper.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/17.
//

import BuzCenterKit
import BuzTracker

extension BuzIMMessage.LocalExtraWrapper: BuzIMMessageLocalExtrable {
    
    public var isRead: Bool {
        get {
            return rawData.isRead
        }
        set { rawData.isRead = newValue }
    }
    
    public var isReply: Bool {
        get {
            return rawData.isReply
        }
        set { rawData.isReply = newValue }
    }
    
    public var pullStreamTimestamp: Int64 {
        get {
            return rawData.pullStreamTimestamp
        }
        set { rawData.pullStreamTimestamp = newValue }
    }
    
    public var streamId: Int64 {
        get {
            return rawData.streamId
        }
        set { rawData.streamId = newValue }
    }
    
    public var asrText: String? {
        get {
            return rawData.asrText
        }
        set { rawData.asrText = newValue }
    }
    
    public var isHiddenAsrText: Bool {
        get {
            return rawData.isHiddenAsrText
        }
        set { rawData.isHiddenAsrText = newValue }
    }
    
    public var textTranslateFailed: Bool {
        get {
            return rawData.textTranslateFailed
        }
        set { rawData.textTranslateFailed = newValue }
    }
    
    public var translateTextDisplayControl: TranslateTextDisplayControl {
        get {
            return rawData.translateTextDisplayControl
        }
        set { rawData.translateTextDisplayControl = newValue }
    }
    
    public var isAsrPreviewClosed: Bool {
        get {
            return rawData.isAsrPreviewClosed
        }
        set { rawData.isAsrPreviewClosed = newValue }
    }
    
    public var msgSendNtpTime: Int64 {
        get {
            return rawData.msgSendNtpTime
        }
        set { rawData.msgSendNtpTime = newValue }
    }
    
    public var locationType: String? {
        get {
            return rawData.locationType
        }
        set { rawData.locationType = newValue }
    }
    
    public var isFoward: Bool {
        get {
            return rawData.isFoward
        }
        set { rawData.isFoward = newValue }
    }
    
    public var isSetMsgPlayed: Bool {
        get {
            return rawData.isSetMsgPlayed
        }
        set { rawData.isSetMsgPlayed = newValue }
    }
    
    public var voiceToTextIsTranslate: Bool {
        get {
            return rawData.voiceToTextIsTranslate
        }
        set { rawData.voiceToTextIsTranslate = newValue }
    }
    
    public var voiceToText: String? {
        get {
            return rawData.voiceToText
        }
        set { rawData.voiceToText = newValue }
    }
    
    public var trackSource: String? {
        get {
            return rawData.trackSource
        }
        set { rawData.trackSource = newValue }
    }
    
    public var trackContent_name: String?{
        get {
            return rawData.trackContent_name
        }
        set { rawData.trackContent_name = newValue }
    }
}

public extension BuzIMMessage.LocalExtraWrapper {
    
    func batchUpdateFields(dict: [String: Any], completion: ((Error?) -> Void)? = nil) {
        DispatchQueue.main.safeAsyncUIQueue {
            self.rawData.parseData(dict: dict)
            let convType = BuzConversationType.init(rawValue: self.im5Message.conversationType.rawValue) ?? .peer
            let msgId = self.im5Message.msgId
            
            guard msgId != 0 else {
                return
            }
            BuzIMCenter.ops.updateLocalExtra(msgId: msgId, convType: convType, extraDict: dict) { _, error in
                completion?(error)
            }
        }
    }
}

//MARK: -isRead
public extension BuzIMMessage.LocalExtraWrapper {
    /// 调用完此api后， isRead（getter）就是最新值。 syncToLocalComplete为同步到IMSDK的回调
    func updateIsRead(_ isRead: Bool, completion: ((Error?) -> Void)? = nil) {
        if isRead == self.isReply {
            completion?(nil)
            return
        }
        
        let dict = [ BuzIMLocalExtra.isRead.Key : isRead ]
        self.batchUpdateFields(dict: dict, completion: completion)

    }
}

//MARK: -isReply
public extension BuzIMMessage.LocalExtraWrapper {
    func hasBeenReply() -> Bool {
        var isReply = false
        
        if let content = self.im5Message.messageContent as? BuzVoiceTextMessageContent {
            isReply = content.isReply
        } else if let content = self.im5Message.messageContent as? BuzTextMessageContent {
            isReply = content.isReply
        } else if let content = self.im5Message.messageContent as? BuzImageMessageContent {
            isReply = content.isReply
        }
        
        return isReply || self.isReply
    }
    
    ///调用完此api后， isReply（getter）就是最新值。 syncToLocalComplete为同步到IMSDK的回调
    func updateIsReply(_ isReply : Bool , syncToLocalComplete : ((Error?) -> Void)? = nil) {
        let dict = [ BuzIMLocalExtra.isReply.Key : isReply ]
        self.batchUpdateFields(dict: dict) { error in
            NotificationCenter.default.post(name: NSNotification.Name.IMMessageReplyStatusDidUpdate, object: self, userInfo: nil)
        }
    }
}

//MARK: -pullStreamTimestamp
public extension BuzIMMessage.LocalExtraWrapper {
    
}

//MARK: -streamId
public extension BuzIMMessage.LocalExtraWrapper {
    
}

//MARK: --asrText
public extension BuzIMMessage.LocalExtraWrapper {
    func updateManualAsrText(_ asrText: String?, completion: ((Error?) -> Void)? = nil) {
        guard let _ = self.im5Message.messageContent as? BuzWalkieTalkieCountMessageContent,
              let asrText = asrText else {
            assertionFailure("非语音消息不支持ASR功能。")
            return
        }
        
        let dict = [ BuzIMLocalExtra.asrText.Key : asrText ]
        self.batchUpdateFields(dict: dict, completion: completion)
    }
}

//MARK: --isHiddenAsrText
public extension BuzIMMessage.LocalExtraWrapper {
    func updateIsHiddenAsrText(_ isHiddenAsrText: Bool, completion: ((Error?) -> Void)? = nil) {
        let dict = [ BuzIMLocalExtra.isHiddenAsrText.Key : isHiddenAsrText ]
        self.batchUpdateFields(dict: dict, completion: completion)
    }
}

//MARK: -textTranslateFailed
public extension BuzIMMessage.LocalExtraWrapper {
    func updateTextTranslateFailed(_ isFailed: Bool, completion: ((Error?) -> Void)? = nil) {
        let dict = [ BuzIMLocalExtra.textTranslateFailed.Key : isFailed ]
        self.batchUpdateFields(dict: dict, completion: completion)
        
    }
}

//MARK: -translateTextDisplayControl
public extension BuzIMMessage.LocalExtraWrapper {
    func updateHideTextTranslate(_ hideTextTranslate: Bool, completion: ((Error?) -> Void)? = nil) {
        let value = hideTextTranslate ? TranslateTextDisplayControl.hide.rawValue : TranslateTextDisplayControl.show.rawValue
        let dict = [ BuzIMLocalExtra.translateTextDisplayControl.Key : value ]
        self.batchUpdateFields(dict: dict, completion: completion)
    }
}


//MARK: --isAsrPreviewClosed
public extension BuzIMMessage.LocalExtraWrapper {
    func updateIsAsrPreviewClosed(_ completion: ((Error?) -> Void)? = nil) {
        let dict = [ BuzIMLocalExtra.isAsrPreviewClosed.Key : true ]
        self.batchUpdateFields(dict: dict, completion: completion)
    }
}

//MARK: -msgSendNtpTime
public extension BuzIMMessage.LocalExtraWrapper {
    func updateMsgSendNtpTime(_ completion: ((Error?) -> Void)? = nil) {
        let time = NTPTimeTool.nowTimestampMillisecond()
        let dict = [BuzIMLocalExtra.msgSendNtpTime.Key : time]
        self.batchUpdateFields(dict: dict, completion: completion)
    }
}

//MARK: -locationType
public extension BuzIMMessage.LocalExtraWrapper {
    
}

//MARK: -isFoward
public extension BuzIMMessage.LocalExtraWrapper {
    func updateisFoward(_ completion: ((Error?) -> Void)? = nil) {
        let dict = [BuzIMLocalExtra.isFoward.Key: NSNumber(1)]
        self.batchUpdateFields(dict: dict, completion: completion)
    }
}

//MARK: -isSetMsgPlayed
public extension BuzIMMessage.LocalExtraWrapper {
    func updateIsSetMsgPlayed(_ completion: ((Error?) -> Void)? = nil) {
        let dict = [ BuzIMLocalExtra.isSetMsgPlayed.Key : true ]
        self.batchUpdateFields(dict: dict, completion: completion)
    }
}

//MARK: -VoiceToTextIsTranslate
public extension BuzIMMessage.LocalExtraWrapper {
    func updateVoiceToTextIsTranslate(_ value: Bool, completion: ((Error?) -> Void)? = nil) {
        let dict = [ BuzIMLocalExtra.voiceToTextIsTranslate.Key : value ]
        self.batchUpdateFields(dict: dict, completion: completion)
    }
}

//MARK: -VoiceToText
public extension BuzIMMessage.LocalExtraWrapper {
    func updateVoiceToText(_ text: String, completion: ((Error?) -> Void)? = nil) {
        let dict = [ BuzIMLocalExtra.voiceToText.Key : true ]
        self.batchUpdateFields(dict: dict, completion: completion)
    }
}


//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMessage.LocalExtraWrapper {

    func updateManualAsrText(_ asrText: String?) -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.updateManualAsrText(asrText) { error in
                promise(.success((error)))
            }
        }
        .eraseToAnyPublisher()
    }

    func updateIsHiddenAsrText(_ isHiddenAsrText: Bool) -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.updateIsHiddenAsrText(isHiddenAsrText) { error in
                promise(.success((error)))
            }
        }
        .eraseToAnyPublisher()
    }

    func updateTextTranslateFailed(_ isFailed: Bool) -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.updateTextTranslateFailed(isFailed) { error in
                promise(.success((error)))
            }
        }
        .eraseToAnyPublisher()
    }

    func updateHideTextTranslate(_ hideTextTranslate: Bool) -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.updateHideTextTranslate(hideTextTranslate) { error in
                promise(.success((error)))
            }
        }
        .eraseToAnyPublisher()
    }

    func updateIsAsrPreviewClosed() -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.updateIsAsrPreviewClosed { error in
                promise(.success((error)))
            }
        }
        .eraseToAnyPublisher()
    }

    func updateMsgSendNtpTime() -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.updateMsgSendNtpTime { error in
                promise(.success((error)))
            }
        }
        .eraseToAnyPublisher()
    }

    func updateIsFoward() -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.updateisFoward { error in
                promise(.success((error)))
            }
        }
        .eraseToAnyPublisher()
    }

    func updateIsSetMsgPlayed() -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.updateIsSetMsgPlayed { error in
                promise(.success((error)))
            }
        }
        .eraseToAnyPublisher()
    }

    func updateVoiceToTextIsTranslate(_ value: Bool) -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.updateVoiceToTextIsTranslate(value) { error in
                promise(.success((error)))
            }
        }
        .eraseToAnyPublisher()
    }

    func updateVoiceToText(_ text: String) -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.updateVoiceToText(text) { error in
                promise(.success((error)))
            }
        }
        .eraseToAnyPublisher()
    }
}
