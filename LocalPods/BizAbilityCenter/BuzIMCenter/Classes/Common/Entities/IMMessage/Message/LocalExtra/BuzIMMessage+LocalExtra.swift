//
//  BuzIMMessage+LocalExtra.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/17.
//

import VoderXClient

//MARK: --asrText
public extension BuzIMMessage {
    var asrText: String? {
        if let content: BuzWalkieTalkieCountMessageContent = self.messageContent() {
            return content.asrText ?? localExtra.asrText
        }
        return localExtra.asrText
    }
}

//MARK: --isHiddenAsrText
public extension BuzIMMessage {
    var shouldShowAsrText: Bool {
        if let _ = self.asrText {
            return self.localExtra.isHiddenAsrText == false
        }
        return false
    }
}
