//
//  BuzCallCardMessageContent.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/4/8.
//

import VoderXClient
import BuzFoundation
import Localizable
import BuzLocalizable
import Buz<PERSON>Kit
import BuzCenterKit

public class BuzCallCardMessageContent: IM5MessageContent {
    // MARK: - Properties
    public var channelId: Int64 = 0
    public var channelType: Int64 = 0
    public var callType: Int64 = 0
    public var duration: Int64 = 0
    public var status: Int64 = 0
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.callCard.rawValue) ?? .unknown
        return type
    }
    
    public override func digest() -> String {
        return "Call Card"
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let dict = super.encodeToJsonObject()

        dict["channelId"] = NSNumber(value: channelId)
        dict["channelType"] = NSNumber(value: channelType)
        dict["callType"] = NSNumber(value: callType)
        dict["duration"] = NSNumber(value: duration)
        dict["status"] = NSNumber(value: status)

        return dict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)

        if let channelId = dict["channelId"] as? NSNumber {
            self.channelId = channelId.int64Value
        }
        if let channelType = dict["channelType"] as? NSNumber {
            self.channelType = channelType.int64Value
        }
        if let callType = dict["callType"] as? NSNumber {
            self.callType = callType.int64Value
        }
        if let duration = dict["duration"] as? NSNumber {
            self.duration = duration.int64Value
        }
        if let status = dict["status"] as? NSNumber {
            self.status = status.int64Value
        }

        return dict
    }
}
