//
//  BuzVoicegifMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import VoderXClient
import Localizable
import BuzLocalizable
import BuzCenterKit

public class BuzVoicegifMessageContent: BuzWalkieTalkieCountMessageContent {
    // MARK: - Properties
    public var voicegifId: String = ""
    public var animationUrl: String?
    public var thumbnailUrl: String?
    public var smallThumbnailUrl: String?
    public var videoUrl: String?
    public var remoteUrl: String?
    public var width: Int64 = 0
    public var height: Int64 = 0
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.voicegif.rawValue) ?? .unknown
        return type
    }
    
    public override func digest() -> String {
        return Localizable.voice_gif_tag
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let dict = super.encodeToJsonObject()
        
        dict["id"] = voicegifId
        dict["animationUrl"] = animationUrl
        dict["thumbnailUrl"] = thumbnailUrl
        dict["smallThumbnailUrl"] = smallThumbnailUrl
        dict["videoUrl"] = videoUrl
        dict["width"] = NSNumber(value: width)
        dict["height"] = NSNumber(value: height)
        dict["remoteUrl"] = remoteUrl
        
        return dict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        voicegifId = dict["id"] as? String ?? ""
        animationUrl = dict["animationUrl"] as? String
        thumbnailUrl = dict["thumbnailUrl"] as? String
        smallThumbnailUrl = dict["smallThumbnailUrl"] as? String
        videoUrl = dict["videoUrl"] as? String
        width = (dict["width"] as? NSNumber)?.int64Value ?? 0
        height = (dict["height"] as? NSNumber)?.int64Value ?? 0
        
        if let remoteUrlString = dict["remoteUrl"] as? String {
            remoteURL = remoteUrlString
            remoteUrl = remoteURL
        }
        
        // Set default dimensions if width or height is 0
        if width == 0 || height == 0 {
            width = 200
            height = 200
        }
        
        return dict
    }
}
