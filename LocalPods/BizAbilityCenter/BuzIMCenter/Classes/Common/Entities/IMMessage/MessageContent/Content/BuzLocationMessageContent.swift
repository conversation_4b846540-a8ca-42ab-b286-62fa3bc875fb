//
//  BuzLocationMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import Localizable
import BuzLocalizable
import BuzCenterKit

public class BuzLocationMessageContent: IM5MessageContent {
    // MARK: - Properties
    public var longitude: Double = 0.0
    public var latitude: Double = 0.0
    public var locationName: String = ""
    public var locationAddress: String = ""
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.location.rawValue) ?? .unknown
        return type
    }
    
    public override func digest() -> String {
        return Localizable.location
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let dict = super.encodeToJsonObject()
        
        dict["longitude"] = String(format: "%f", self.longitude)
        dict["latitude"] = String(format: "%f", self.latitude)
        dict["locationName"] = self.locationName
        dict["locationAddress"] = self.locationAddress
        
        return dict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        // Handle longitude (may be String or Double)
        if let stringValue = dict["longitude"] as? String {
            self.longitude = Double(stringValue) ?? 0.0
        } else if let doubleValue = dict["longitude"] as? Double {
            self.longitude = doubleValue
        }
        
        // Handle latitude (may be String or Double)
        if let stringValue = dict["latitude"] as? String {
            self.latitude = Double(stringValue) ?? 0.0
        } else if let doubleValue = dict["latitude"] as? Double {
            self.latitude = doubleValue
        }
        
        self.locationName = dict["locationName"] as? String ?? ""
        self.locationAddress = dict["locationAddress"] as? String ?? ""
        
        return dict
    }
}
