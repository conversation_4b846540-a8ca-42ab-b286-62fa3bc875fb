//
//  BuzVoicemojiMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import VoderXClient
import Localizable
import BuzLocalizable
import BuzCenterKit

// MARK: - BuzVoicemojiMessageContent
public class BuzVoicemojiMessageContent: BuzWalkieTalkieCountMessageContent, VoicemojiMessageEntity {
    // MARK: - Properties
    /// 语音表情id
    public var emojiId: Int64 = 0
    /// 表情图标，Unicode或后续用户上传的表情
    public  var emojiIcon: String = ""
    /// 动效文件地址
    public var emojiAnimationUrl: String = ""
    /// 音效文件地址
    public var emojiVoiceUrl: String = ""
    /// 角标文案
    public var emojiSuperscript: String = ""
    /// 类别名字
    public var emojiCategory: String = ""
    /// 类别
    public var emojiCategoryType: Int64 = 0
    /// 新 Lottie 动效地址
    public var emojiNewAnimationUrl: String = ""
    /// 新音效文件地址
    public var emojiNewVoiceUrl: String = ""
    /// 动画类型 1 - 全局动画 2 - 局部居中动画
    public var emojiAnimationType: Int32 = 0
    /// 0 or nil : normal emoji  - 1 :picture emoji
    public var emojiType: VoiceEmojiType = .text
    /// if emojiType == 1, show emojiDesc outside of the app, because of the inconvenience to show the image
    public var emojiDesc: String?
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.voice_Emoji.rawValue) ?? .unknown
        return type
    }
    
    public override class func getContentFlag() -> IM5ContentFlag {
        return .PERSISTED_AND_COUNTED
    }
    
    public override func digest() -> String {
        if emojiType == .text {
            return "[\(Localizable.ve_coicemoji_updated): \(emojiIcon)]"
        } else {
            return Localizable.ve_voiceemoji_tip_updated
        }
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let dict = super.encodeToJsonObject()
        
        dict["emojiId"] = String(emojiId)
        dict["emojiAnimationUrl"] = emojiAnimationUrl
        dict["emojiIcon"] = emojiIcon
        
        if let emojiDesc = emojiDesc {
            dict["emojiDescription"] = emojiDesc
        }
        
        dict["emojiVoiceUrl"] = emojiVoiceUrl
        dict["emojiSuperscript"] = emojiSuperscript
        dict["emojiCategory"] = emojiCategory
        dict["emojiCategoryType"] = String(emojiCategoryType)
        dict["emojiNewAnimationUrl"] = emojiNewAnimationUrl
        dict["emojiNewVoiceUrl"] = emojiNewVoiceUrl
        dict["emojiAnimationType"] = String(emojiAnimationType)
        dict["emojiType"] = NSNumber(value: emojiType.rawValue)
        
        return dict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        emojiId = (dict["emojiId"] as? NSString)?.longLongValue ?? 0
        emojiIcon = dict["emojiIcon"] as? String ?? ""
        emojiDesc = dict["emojiDescription"] as? String
        emojiAnimationUrl = dict["emojiAnimationUrl"] as? String ?? ""
        emojiVoiceUrl = dict["emojiVoiceUrl"] as? String ?? ""
        emojiSuperscript = dict["emojiSuperscript"] as? String ?? ""
        emojiCategory = dict["emojiCategory"] as? String ?? ""
        emojiCategoryType = (dict["emojiCategoryType"] as? NSString)?.longLongValue ?? 0
        emojiNewAnimationUrl = dict["emojiNewAnimationUrl"] as? String ?? ""
        emojiNewVoiceUrl = dict["emojiNewVoiceUrl"] as? String ?? ""
        emojiAnimationType = Int32((dict["emojiAnimationType"] as? NSString)?.intValue ?? 0)
        emojiType = .text
        
        if let emojiTypeNumber = dict["emojiType"] as? NSNumber,
           emojiTypeNumber.int64Value == VoiceEmojiType.image.rawValue {
            emojiType = .image
        }
        
        return dict
    }
}

// MARK: - BuzCompatibleVoicemojiMesasgeContent
public class BuzCompatibleVoicemojiMesasgeContent: BuzVoicemojiMessageContent {
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.compatibleVoiceEmoji.rawValue) ?? .unknown
        return type
    }
    
    public override class func getContentFlag() -> IM5ContentFlag {
        return .PERSISTED_AND_COUNTED
    }
}
