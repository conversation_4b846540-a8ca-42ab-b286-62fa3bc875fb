//
//  BuzVideoMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import Localizable
import BuzLocalizable
import BuzCenterKit

public class BuzVideoMessageContent: IM5VideoMessageContent {
    // MARK: - Properties
    public var conversationType: BuzConversationType = .peer
    public var isReply: Bool = false
    public var progress: CGFloat = 0.0
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.video.rawValue) ?? .unknown
        return type
    }
    
    public override func digest() -> String {
        return Localizable.notification_tile_video
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        let extra = dict["extra"]
        
        var sExtra: [String: Any]?
        
        if let extra = extra as? [String: Any] {
            sExtra = extra["serverExtra"] as? [String: Any]
        } else {
            let extra = self.extra.object(forKey: "serverExtra")
            if let extra = extra as? [String: Any] {
                sExtra = extra
            }else if let extraString = extra as? String {
                sExtra = extraString.toDict()
            }
        }
        
        if let sExtra = sExtra, !sExtra.isEmpty {
            self.isReply = (sExtra["isReply"] as? Bool) ?? false
        }
        
        return dict
    }
}
