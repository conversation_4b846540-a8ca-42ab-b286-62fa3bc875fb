//
//  BuzTextMessageMentionedsDisplayInfo.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import BuzUserSession

public class BuzTextMessageMentionedsDisplayInfo: NSObject {
    // MARK: - Properties
    public var textContent: String = ""
    public var hightlightInfosArray: [BuzMessageHightLightInfo] = []
}

extension BuzTextMessageMentionedsDisplayInfo {
    
    public static func transformMentionedInfo(toTextMessageDisplay mentionInfo: BuzMessageMentionedInfo?) -> BuzTextMessageMentionedsDisplayInfo? {
        guard let mentionInfo = mentionInfo,
              !mentionInfo.textMentioned.isEmpty,
              !mentionInfo.mentionedMaps.isEmpty else {
            return nil
        }
        
        let currentUserId = "\(BuzUserSession.shared.uid)"
        let replaceContent = NSMutableString(string: mentionInfo.textMentioned)
        var hightlightInfoArray: [BuzMessageHightLightInfo] = []
        
        for (key, obj) in mentionInfo.mentionedMaps {
            var memtionedString = "@\(obj.userName)"
            var isMentionedMe = false
            
            // Support mention all @All
            if obj.isMentioningAll {
                memtionedString = "@All"
            } else {
                let userInfo = BuzIMCenter.center.provider?.queryUserInfo(userId: obj.userIdNum)
                
                // get updated username from db
                if let userName = userInfo?.base.userName, !userName.isEmpty {
                    memtionedString = "@\(userName)"
                }
                
                // get remark from db
                if let remark = userInfo?.relation.remark, !remark.isEmpty {
                    memtionedString = "@\(remark)"
                }
                
                isMentionedMe = (obj.userId == currentUserId)
                if isMentionedMe, let myName = BuzUserSession.shared.userInfo?.base.userName {
                    memtionedString = "@\(myName)"
                }
            }
            
            var resultRange = NSRange(location: NSNotFound, length: 0)
            
            func checkResultRange() -> NSRange {
                resultRange = replaceContent.range(of: key)
                return resultRange
            }
            
            while checkResultRange().location != NSNotFound {
                if resultRange.location != NSNotFound {
                    replaceContent.replaceCharacters(in: resultRange, with: memtionedString)
                    resultRange.length = (memtionedString as NSString).length
                    
                    for obj in hightlightInfoArray {
                        if resultRange.location < obj.range.location {
                            //修改的文字在当前已处理文字的前面
                            let deltaIndex = (key as NSString).length - (memtionedString as NSString).length
                            let origLocation = obj.range.location
                            let origLen = obj.range.length
                            obj.range = NSRange(location: origLocation - deltaIndex, length: origLen)
                        }
                    }
                    
                    let routerDict: [String: Any] = [
                        "scheme": "personal/profile",
                        "extraData": [
                            "userId": obj.userIdNum,
                            "botReportSource": "group_@_msg",
                            "isDisplayBotSettingUI": false
                        ]
                    ]
                    
                    let info = BuzMessageHightLightInfo(text: memtionedString, range: resultRange, routerDict: routerDict, isMentionedMe: isMentionedMe)
                    if obj.isMentioningAll {
                        info.isMentioningAll = true
                    }
                    hightlightInfoArray.append(info)
                }
            }
        }
        
        let displayInfo = BuzTextMessageMentionedsDisplayInfo()
        displayInfo.textContent = replaceContent as String
        displayInfo.hightlightInfosArray = hightlightInfoArray
        return displayInfo
    }
}
