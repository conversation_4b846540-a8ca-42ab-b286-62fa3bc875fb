//
//  BuzMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import VoderXClient
import BuzCenterKit

@objc
public protocol BuzMessageContentProtocol: IM5MessageContentProtocol {
    func getEditType() -> BuzMessageContentEditType
    func getTranslateInfo() -> BuzTextTranslateInfo?
}

public extension BuzMessageContentProtocol {
    func getEditType() -> BuzMessageContentEditType {
        return .none
    }
    
    func getTranslateInfo() -> BuzTextTranslateInfo? {
        return nil
    }
}

//// MARK: - BuzMessageContent
extension IM5MessageContent: BuzMessageContentProtocol {
    public func getEditType() -> BuzMessageContentEditType {
        return .none
    }
    
    public func getTranslateInfo() -> BuzTextTranslateInfo? {
        return nil
    }
}
