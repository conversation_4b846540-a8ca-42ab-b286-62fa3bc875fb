//
//  BuzFileMessageContent.swift
//  Pods
//
//  Created by Ha D on 2025/6/4.
//

import VoderXClient
import BuzCenterKit
import Localizable

public class BuzFileMessageContent: IM5AttachmentMessageContent {
    public var extensionFileName: String {
        return self.contentExtensionName ?? ""
    }
    
    public var fileName : String {
        return self.contentFileName ?? "UNKNOW"
    }
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.fileAttachment.rawValue) ?? .unknown
        return type
    }
    
    public override func digest() -> String {
        return String.init(format: Localizable.chat_pop_msg_file_tag, fileName)
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let dict = super.encodeToJsonObject()
        
        ///file attribute
        
        return dict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        return dict
    }
}
