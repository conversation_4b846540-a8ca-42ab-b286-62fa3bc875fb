//
//  BuzMessageMentionedInfo.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import YYModel
import VoderXClient

@objc
@objcMembers
public class BuzMessageMentionedInfo: NSObject, NSCoding {
    // MARK: - Properties
    public var textMentioned: String = ""
    public var mentionedMaps: [String: BuzMessageMentionedUser] = [:]
    
    // MARK: - Init
    public init(dict: [String: Any]) {
        super.init()
        
        textMentioned = dict.string(forKey: "textMentioned")
        
        if let mentionedDict = dict.dictionary(forKey: "mentionedMaps") {
            var tempMentionedMaps: [String: BuzMessageMentionedUser] = [:]
            
            mentionedDict.forEach { key, value in
                if let userDict = value as? [String: Any],
                   let user = BuzMessageMentionedUser.yy_model(with: userDict) {
                    user.key = key
                    tempMentionedMaps[key] = user
                }
            }
            
            mentionedMaps = tempMentionedMaps
        }
    }
    
    public override init() {
        super.init()
    }
    
    // MARK: - NSCoding
    public required init?(coder: NSCoder) {
        super.init()
        yy_modelInit(with: coder)
    }
    
    public func encode(with coder: NSCoder) {
        yy_modelEncode(with: coder)
    }
}

public extension BuzMessageMentionedInfo {
    
    var replaceContent: String? {
        if !textMentioned.isEmpty && !self.mentionedMaps.isEmpty {
            var replaceContent = textMentioned
            
            for (key, obj) in mentionedMaps {
                let mentionedString = "@\(obj.userName)"
                while let range = replaceContent.range(of: key) {
                    replaceContent.replaceSubrange(range, with: mentionedString)
                }
            }

            return replaceContent
        }
        
        return nil
    }
    
    func mentionUsers() -> [String] {
        var userIds: [String] = []
        for mentionedUser in self.mentionedMaps.values {
            if mentionedUser.isMentioningAll {
                userIds.append(IM5MentionedAllFlag)
            } else if !mentionedUser.userId.isEmpty {
                userIds.append(mentionedUser.userId)
            }
        }

        return userIds
    }
}

// MARK: - Dictionary Extensions
private extension Dictionary {
    func string(forKey key: Key) -> String where Value == Any {
        return (self[key] as? String) ?? ""
    }
    
    func dictionary(forKey key: Key) -> [String: Any]? where Value == Any {
        return self[key] as? [String: Any]
    }
}
