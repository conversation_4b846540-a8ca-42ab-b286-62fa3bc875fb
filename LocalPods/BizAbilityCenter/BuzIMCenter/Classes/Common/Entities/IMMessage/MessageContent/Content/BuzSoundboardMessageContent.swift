//
//  BuzSoundboardMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import Localizable
import BuzLocalizable
import BuzCenterKit

public class BuzSoundboardMessageContent: IM5MessageContent {
    // MARK: - Properties
    public var displayValue: String = ""
    public var displayType: Int32 = 0
    public var superscript: String = ""
    public var voiceUrl: String = ""
    public var soundBoardId: Int64 = 0
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.onairSoundBoard.rawValue) ?? .unknown
        return type
    }
    
    public override class func getContentFlag() -> IM5ContentFlag {
        return .ONLINE_PUSH_ONLY_CONTENT
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let dict = super.encodeToJsonObject()
        
        dict["displayValue"] = displayValue
        dict["displayType"] = NSNumber(value: displayType)
        dict["superscript"] = superscript
        dict["voiceUrl"] = voiceUrl
        dict["soundBoardId"] = NSNumber(value: soundBoardId)
        
        return dict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        displayValue = (dict["displayValue"] as? String) ?? ""
        displayType = (dict["displayType"] as? NSNumber)?.int32Value ?? 0
        superscript = (dict["superscript"] as? String) ?? ""
        voiceUrl = (dict["voiceUrl"] as? String) ?? ""
        soundBoardId = (dict["soundBoardId"] as? NSNumber)?.int64Value ?? 0
        
        return dict
    }
}
