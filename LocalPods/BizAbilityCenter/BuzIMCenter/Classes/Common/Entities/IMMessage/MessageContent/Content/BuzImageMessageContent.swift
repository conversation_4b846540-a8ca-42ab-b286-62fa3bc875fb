//
//  BuzImageMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/4.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import BuzFoundation
import Localizable
import BuzLocalizable
import BuzUIKit
import Buz<PERSON><PERSON><PERSON>it

private let kIM5ImageOrientationKey = "orientation"

public class BuzImageMessageContent: IM5ImageMessageContent {
    public var conversationType: BuzConversationType = .peer
    // MARK: - Properties
    public var orientation: UIImage.Orientation = .up
    public var cgOrientation: CGImagePropertyOrientation = .up
    public var isReply: Bool = false
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.image.rawValue) ?? .unknown
        return type
    }
    
    public override func digest() -> String {
        return Localizable.notification_tile_photo
    }
    
    /*
               up(1)
     right(6)          left(8)
              down(3)
     */
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let localExtra = super.encodeToJsonObject()
        localExtra[kIM5ImageOrientationKey] = self.cgOrientation.rawValue
        return localExtra
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        let orientationValue = dict[kIM5ImageOrientationKey] as? UInt32

        if let orientationValue = orientationValue {
            self.orientation = UIImage.imageOrientationForCGImagePropertyOrientation(cgOrientation: orientationValue)
            self.cgOrientation = CGImagePropertyOrientation(rawValue: orientationValue) ?? .up
        } else {
            self.orientation = .up
            self.cgOrientation = .up
        }
        
        let extra = dict["extra"]
        var sExtra: [String: Any]?
        
        if let extra = extra as? [AnyHashable: Any] {
            sExtra = extra["serverExtra"] as? [String: Any]
        } else {
            let extra = self.extra.object(forKey: "serverExtra")
            if let extra = extra as? [String: Any] {
                sExtra = extra
            } else if let extra = extra as? String {
                sExtra = extra.toDict()
            }
        }
        if let sExtra = sExtra, !sExtra.isEmpty {
            self.isReply = (sExtra["isReply"] as? Bool) ?? false
        }
        
        return dict
    }
}
