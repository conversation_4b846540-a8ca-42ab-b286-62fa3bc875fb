//
//  BuzCommandMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import YYModel
import Localizable
import BuzLocalizable
import BuzCenterKit
import BuzUserSession

// MARK: - BuzCommandCenteredMsgType
public enum BuzCommandCenteredMsgType: Int {
    // modify group name
    case modifyGroupName = 1
    // invite enter group
    case groupInvite = 2
    case createGroupInvite = 3
    case aiRobotIntroduce = 4
    case realTimeCallStart = 5
    case realTimeCallEnd = 6
    case realTimeCallMissed = 7
    case realTimeCallLineBusy = 8
    case onAirStart = 9
    case onAirEnd = 10
    case onAirMissed = 11
    case onAirLineBusy = 12
    case livePlaceStart = 13
    case livePlaceEnd = 14
    case livePlaceMissed = 15
    case livePlaceLineBusy = 16
    case livePlaceUpdate = 17
    case livePlaceCreated = 18
    case livePlaceKnockGuest = 19
    case livePlaceKnockMaster = 20
}

fileprivate let buzCommmandTranslateKey = "isTranslate"
// MARK: - BuzCommandMessageContent
@objcMembers
public class BuzCommandMessageContent: IM5MessageContent {
    // MARK: - Properties
    /// 邀请人信息
    public var inviterInfo: BuzUserData?
    public var commandType: BuzCommandMessageType = .none
    public var extraDict: [String: Any] = [:]
    public var uiDisplay: Bool = false
    public var bgDisplay: Bool = false
    public var isTranslate: Bool = false
    public var subBusinessType: BuzCommandCenteredMsgType = .modifyGroupName
    /// center action 类型字段
    public var text: String = ""
    public var actionText: String = ""
    public var action: [String: Any] = [:]
    public var digestText: String = ""
    ///text action hightlight info
    public var hightlightInfoArray: [BuzMessageHightLightInfo] = []
    
    ///对于本地信息填充
    public var fillData: [[String: Any]]? = []

    //用于查找高亮文案映射
    //{
    //    "[English]": {
    //        "displayText": "English",
    //        "action": {
    //            "scheme": "ai/translatorLangSetting",
    //            "extraData": {
    //                "robotId": 123456,
    //                "type": 0
    //            }
    //        }
    //    }
    //}
    public var actionMaps: [String: Any] = [:] {
        didSet {
            self.wrapActionMapsInfo()
        }
    }
    
    private var originalText: String?
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.command.rawValue) ?? .unknown
        return type
    }
    
    public override class func getContentFlag() -> IM5ContentFlag {
        return .PERSISTED
    }
    
    public override func digest() -> String {
        switch commandType {
        case .invited:
            return "You were invited to group by \(inviterInfo?.base.userName ?? "")"
        case .centered:
            switch subBusinessType {
            case .modifyGroupName, .groupInvite:
                return ""
            default:
                return ""
            }
        case .centeredAction:
            return digestText
        default:
            return ""
        }
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        self.extra.setObject([buzCommmandTranslateKey: true], forKey: kContentStyleExtra)
        let superDict = super.encodeToJsonObject()
        
        guard var dict = yy_modelToJSONObject() as? [String: Any] else {
            return superDict
        }
        
        dict["businessType"] = dict["commandType"]
        dict["digest"] = dict["digestText"]
        dict["text"] = originalText
        dict["isTranslate"] = nil
        dict["extra"] = nil
        dict["subBusinessType"] = subBusinessType.rawValue
        dict.removeValue(forKey: "commandType")
        dict.removeValue(forKey: "digestText")
        
        superDict.addEntries(from: dict)
        return superDict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        extraDict = dict as? [String: Any] ?? extraDict
        let baseInfo = BuzUserData.BaseInfo.yy_model(with: dict)
        inviterInfo = .init(userId: baseInfo?.userId ?? 0, base: baseInfo)
        commandType = BuzCommandMessageType(rawValue: (dict["businessType"] as? Int) ?? 0) ?? .none
        uiDisplay = (dict["uiDisplay"] as? NSNumber)?.boolValue ?? false
        bgDisplay = (dict["bgDisplay"] as? NSNumber)?.boolValue ?? false
        
        if let contentStyleExtra = self.extra.object(forKey: kContentStyleExtra) as? [String: Any],
           let isTranslateValue = contentStyleExtra[buzCommmandTranslateKey] as? NSNumber {
            isTranslate = isTranslateValue.boolValue
        }
        
        subBusinessType = BuzCommandCenteredMsgType(rawValue: (dict["subBusinessType"] as? Int) ?? 0) ?? .modifyGroupName
        text = dict["text"] as? String ?? ""
        actionText = dict["actionText"] as? String ?? ""
        action = dict["action"] as? [String: Any] ?? [:]
        digestText = dict["digest"] as? String ?? ""
        actionMaps = dict["actionMaps"] as? [String: Any] ?? [:]
        fillData = dict["fillData"] as? [[String: Any]] ?? []
        
        return dict
    }
    
    public override var description: String {
        return "This is invite messageContent -> \(digest())"
    }
}

private extension BuzCommandMessageContent {
    
    func wrapActionMapsInfo() {
        guard let actionMaps = actionMaps as? [String: [String: Any]] else { return }
        
        var hightlightInfoArray: [BuzMessageHightLightInfo] = []
        let mutableText = NSMutableString(string: text)
        
        for (key, obj) in actionMaps {
            guard let displayText = obj["displayText"] as? String else { continue }
            
            let routerDict = obj["action"] as? [String: Any] ?? .init()
            
            let range = mutableText.range(of: key)
            guard range.length > 0 else { continue }
            
            mutableText.replaceCharacters(in: range, with: displayText)
            var newRange = range
            newRange.length = displayText.count
            
            // Update existing ranges
            for info in hightlightInfoArray {
                if range.location < info.range.location {
                    let deltaIndex = key.count - displayText.count
                    info.range = NSRange(location: info.range.location - deltaIndex, length: info.range.length)
                }
            }
            
            let info = BuzMessageHightLightInfo(text: displayText, range: newRange, routerDict: routerDict, isMentionedMe: false)
            hightlightInfoArray.append(info)
        }
        
        if originalText == nil {
            originalText = text
        }
        self.hightlightInfoArray = hightlightInfoArray
        self.text = mutableText as String
    }
}

public extension BuzCommandMessageContent {
    
    func pushcontent() -> String {
        switch commandType {
        case .onAir:
            switch subBusinessType {
            case .onAirStart:
                return Localizable.air_somebody_started_onair
            case .onAirEnd:
                return Localizable.air_air_end
            case .onAirMissed:
                return Localizable.air_miss_air_invited
            case .onAirLineBusy:
                return Localizable.line_busy
            default:
                return ""
            }
        case .livePlace:
            switch subBusinessType {
            case .livePlaceMissed:
                return Localizable.liveplace_missed_group
            case .livePlaceLineBusy:
                return Localizable.line_busy
            default:
                return ""
            }
        default:
            return ""
        }
    }
    
    ///是否RTC消息
    func isRTCMessage() -> Bool {
        if commandType == .realTimeCall {
            return [.realTimeCallStart, .realTimeCallEnd,
                    .realTimeCallMissed, .realTimeCallLineBusy].contains(subBusinessType)
        }
        return false
    }
    ///是否OnAir消息
    func isOnAirMessage() -> Bool {
        if commandType == .onAir {
            return [.onAirStart, .onAirEnd,
                    .onAirMissed, .onAirLineBusy].contains(subBusinessType)
        }
        return false
    }
    
    ///是否LivePlace消息
    func isLivePlaceMessage() -> Bool {
        guard commandType == .livePlace else { return false }
        
        switch subBusinessType {
        case .livePlaceStart,
             .livePlaceEnd,
             .livePlaceMissed,
             .livePlaceLineBusy,
             .livePlaceUpdate,
             .livePlaceCreated,
             .livePlaceKnockGuest,
             .livePlaceKnockMaster:
            return true
        default:
            return false
        }
    }
}
