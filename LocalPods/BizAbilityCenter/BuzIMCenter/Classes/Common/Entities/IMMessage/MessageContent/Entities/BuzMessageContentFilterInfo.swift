//
//  BuzMessageContentFilterInfo.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import YYModel

// MARK: - BuzMessageContentFilterState
@objc
public enum BuzMessageContentFilterState: Int {
    case normal = 0
    case processing = 1     // 变声中
    case succeed = 2        // 变声成功
    case failed = 3         // 变声失败
}

// MARK: - BuzMessageContentFilterInfo
@objcMembers
public class BuzMessageContentFilterInfo: NSObject, NSCoding {
    // MARK: - Properties
    /// 滤镜id
    public var filterId: Int64 = 0
    /// 语音变声的原消息url
    public var originalUrl: String?
    /// 变声状态  1 变声中、2 变声成功 、3 变声失败
    public var filterState: BuzMessageContentFilterState = .normal
    
    /// 气泡样式类型
    public var bubbleStyle: Int32 = 0
    /// 配套颜色 1，backgroundColor
    public var suitColor1: String?
    /// 配套颜色 2，microphoneColor
    public var suitColor2: String?
    /// 模板视频链接
    public var videoTemplateUrl: String?
    /// 模板文件 MD5
    public var videoTemplateMd5: String?
    
    // MARK: - Init
    public override init() {
        super.init()
    }
    
    // MARK: - NSCoding
    public required init?(coder: NSCoder) {
        super.init()
        yy_modelInit(with: coder)
    }
    
    public func encode(with coder: NSCoder) {
        yy_modelEncode(with: coder)
    }
}
