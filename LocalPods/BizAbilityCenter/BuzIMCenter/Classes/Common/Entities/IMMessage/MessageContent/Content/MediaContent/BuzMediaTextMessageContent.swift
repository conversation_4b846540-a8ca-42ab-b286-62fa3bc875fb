//
//  BuzMediaTextMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import BuzConfig
import YYModel
import BuzFoundation
import BuzCenterKit

// MARK: - BuzMediaTextMediaContent
@objcMembers
public class BuzMediaTextMediaContent: NSObject {
    /// 视频长度（毫秒）
    public var duration: Int32 = 0
    /// 视频画布尺寸
    public var width: Int32 = 0
    /// 视频画布尺寸
    public var height: Int32 = 0
    /// 高清封面URL
    public var hdCoverUrl: String = ""
    /// 封面格式
    public var type: BuzMediaType = .none
    /// 封面的 URL 地址（接收方）
    public var remoteUrl: String?
}

// MARK: - BuzMediaTextMessageContent
public class BuzMediaTextMessageContent: IM5MessageContent {
    // MARK: - Properties
    /// 媒体内容
    public var media: BuzMediaTextMediaContent?
    /// 大标题
    public var title: String?
    /// we use html to display office description, it can make it abundant
    public var htmlDescription: String?
    /// on official account iteration, the link display after description
    public var link: String?
    /// link name
    public var linkName: String?
    /// button name
    public var buttonName: String?
    /// button left side unicode font
    public var buttonIconFont: String?
    /// link name
    public var buttonFontColor: String?
    /// button name
    public var buttonBackgroundColor: String?
    /// routerActionInfo
    public var actionInfo: [String: Any]?
    /// api action with scenarioType and callbackParam
    public var apiAction: [String: Any]?
    /// action type
    public var actionType: Int = 0
    /// is official account
    public var officialAccount: Bool = false
    /// should enable button
    public var isButtonDisabled: Bool = false
    /// is button loadingview on
    public var isButtonLoading: Bool = false
    /// linkType
    public var linkType: Int = 0
    /// card businessType
    public var businessType: Int = 0
    
    // MARK: - Private Properties
    private var extraDict: [String: Any] = [:]
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.mediaText.rawValue) ?? .unknown
        return type
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        return NSMutableDictionary(dictionary: self.extraDict)
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        self.extraDict = dict as? [String: Any] ?? .init()
        
        self.title = dict["title"] as? String
        self.htmlDescription = dict["htmlDescription"] as? String
        self.link = dict["link"] as? String
        self.linkName = dict["linkName"] as? String
        self.buttonName = dict["buttonName"] as? String
        self.buttonFontColor = dict["buttonFontColor"] as? String
        self.buttonBackgroundColor = dict["buttonBackgroundColor"] as? String
        self.actionInfo = dict["actionInfo"] as? [String: Any]
        self.officialAccount = (dict["officialAccount"] as? NSNumber)?.boolValue ?? false
        self.linkType = (dict["linkType"] as? NSNumber)?.intValue ?? 0
        self.businessType = (dict["businessType"] as? NSNumber)?.intValue ?? 0
        self.isButtonLoading = (dict["isButtonLoading"] as? NSNumber)?.boolValue ?? false
        self.isButtonDisabled = (dict["isButtonDisabled"] as? NSNumber)?.boolValue ?? false
        self.buttonIconFont = dict["buttonIconFont"] as? String
        self.apiAction = dict["apiAction"] as? [String: Any]
        self.actionType = (dict["actionType"] as? NSNumber)?.intValue ?? 0
        
        if let mediaItem = dict["mediaItem"] as? [String: Any] {
            self.media = BuzMediaTextMediaContent.yy_model(with: mediaItem)
        }
        
        return self.extraDict
    }
    
    public override func digest() -> String {
        return self.title ?? ""
    }
}
