//
//  BuzMessageMentionedUser.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import YYModel

@objc
@objcMembers
public class BuzMessageMentionedUser: NSObject, NSCoding {
    // MARK: - Properties
    public var invalid: Bool = false
    public var isBot: Bool = false
    public var notNeedLocationAfter: Bool = false
    public var userId: String = ""
    public var userName: String = ""
    public var remarksName: String?
    public var key: String?
    
    private var _userIdNum: Int64 = 0
    public var userIdNum: Int64 {
        get {
            if _userIdNum == 0 {
                _userIdNum = Int64(userId) ?? 0
            }
            return _userIdNum
        }
    }
    public var isMentioningAll: Bool = false
    
    // MARK: - Init
    public init(userId: String, userName: String) {
        super.init()
        self.userId = userId
        self.userName = userName
    }
    
    public override init() {
        super.init()
    }
    
    // MARK: - NSCoding
    public required init?(coder: NSCoder) {
        super.init()
        yy_modelInit(with: coder)
    }
    
    public func encode(with coder: NSCoder) {
        yy_modelEncode(with: coder)
    }
    
    // MARK: - YYModel
    @objc
    public class func modelPropertyBlacklist() -> [String] {
        return ["remarksName", "key", "userIdNum"]
    }
}
