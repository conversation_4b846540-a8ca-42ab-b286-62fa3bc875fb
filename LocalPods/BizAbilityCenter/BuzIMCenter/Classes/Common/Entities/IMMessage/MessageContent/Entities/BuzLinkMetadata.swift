//
//  BuzLinkMetadata.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation

@objcMembers
public class BuzLinkMetadata: NSObject {
    // MARK: - Properties
    public var linkUrl: String?
    public var linkSiteName: String?
    public var linkTitle: String?
    public var linkDesc: String?
    public var linkImagePath: String?
    public var linkLogoPath: String?
    
    // MARK: - Public Methods
    public func isAllMetadataNull() -> Bool {
        // Case 1: Only linkUrl exists
        if linkUrl != nil &&
           linkSiteName == nil &&
           linkTitle == nil &&
           linkDesc == nil &&
           linkLogoPath == nil &&
           linkImagePath == nil {
            return true
        }
        
        // Case 2: All properties are nil
        if linkUrl == nil &&
           linkSiteName == nil &&
           linkTitle == nil &&
           linkDesc == nil &&
           linkLogoPath == nil &&
           linkImagePath == nil {
            return true
        }
        
        return false
    }
}
