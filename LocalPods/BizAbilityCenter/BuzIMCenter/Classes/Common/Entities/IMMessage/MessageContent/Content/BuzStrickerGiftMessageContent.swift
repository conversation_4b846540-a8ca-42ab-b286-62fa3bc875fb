//
//  BuzStrickerGiftMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import Localizable
import BuzLocalizable
import YYModel
import BuzCenterKit

// MARK: - BuzStrickerGiftMessage
@objcMembers
public class BuzStrickerGiftMessage: NSObject {
    // MARK: - Properties
    /// 礼物id
    public var giftId: Int64 = 0
    /// 礼物矩形左上角在触摸区域的x百分比
    public var xPercent: CGFloat = 0.0
    /// 礼物矩形左上角在触摸区域的y百分比
    public var yPercent: CGFloat = 0.0
    /// 礼物动效文件地址
    public var icoUrl: String = ""
    /// 资源大小相对屏幕宽百分比
    public var widthRatio: CGFloat = 0.0
    /// 资源宽高百分比
    public var whRatio: CGFloat = 0.0
    /// 旋转角度。角度制
    public var ratation: CGFloat = 0.0
    /// 默认动效文件地址
    public var categoryIcoUrl: String = ""
}

// MARK: - BuzStrickerGiftMessageContent
public class BuzStrickerGiftMessageContent: IM5MessageContent {
    // MARK: - Properties
    public var giftList: [BuzStrickerGiftMessage] = []
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.onairStrickerGift.rawValue) ?? .unknown
        return type
    }
    
    public override class func getContentFlag() -> IM5ContentFlag {
        return .ONLINE_PUSH_ONLY_CONTENT
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let dict = super.encodeToJsonObject()
        
        let dictionaryArray = giftList.compactMap { gift -> [String: Any]? in
            return gift.yy_modelToJSONObject() as? [String: Any]
        }
        
        dict["giftList"] = dictionaryArray
        
        return dict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        if let dictionaryArray = dict["giftList"] {
            if let value = NSArray.yy_modelArray(with: BuzStrickerGiftMessage.self, json: dictionaryArray) as? [BuzStrickerGiftMessage] {
                self.giftList = value
            }
        }
        
        return dict
    }
}
