//
//  BuzTextTranslateInfo.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import YYModel
import BuzUserSession
import BuzCenterKit

// MARK: - BuzMessageContentEditType
@objc
public enum BuzMessageContentEditType: Int {
    case none = 0
    case asr = 1
    case translate = 2
    //返回对应的语言（编辑消息）
    case detectLanguage = 3
}

// MARK: - BuzMessageContentTranslateCode
@objc
public enum BuzMessageContentTranslateCode: Int {
    case normalResult = 1  // 需要展示翻译结果
    case sameLanguage = 2  // 文本源语言与目标语言一致，不需要展示翻译结果 翻译内容不返回
}

@objcMembers
public class BuzTextTranslateInfo: NSObject, NSCoding {
    // MARK: - Properties
    public var translateText: String?  // 翻译内容
    public var targetLanguage: String? // 翻译语言
    public var sourceLanguage: String? // 源语言
    public var replaceholderList: [String]?
    public var mentionIds: [Int64]?
    public var translateCode: BuzMessageContentTranslateCode = .normalResult  // 1:需要展示翻译结果  2：文本源语言与目标语言一致，不需要展示翻译结果 翻译内容不返回
    
    // MARK: - Init
    public override init() {
        super.init()
    }
    
    // MARK: - NSCoding
    public required init?(coder: NSCoder) {
        super.init()
        yy_modelInit(with: coder)
    }
    
    public func encode(with coder: NSCoder) {
        yy_modelEncode(with: coder)
    }
}

public extension BuzTextTranslateInfo {
    
    var replaceContent: String? {
        if let replaceholders = self.replaceholderList,
           let mentionIds = self.mentionIds,
           (!replaceholders.isEmpty || !mentionIds.isEmpty),
           var translatedText = self.translateText {
            
            let currentUserId = BuzUserSession.shared.userInfo?.base.userId ?? 0
            for (idx, mentionString) in replaceholders.enumerated() {
                if idx < mentionIds.count {
                    let userId = mentionIds[idx]
                    let userIdStr = userId
                    let userInfo = BuzIMCenter.center.provider?.queryUserInfo(userId: userId)
                    let remark = userInfo?.relation.remark ?? ""
                    let userName = userInfo?.base.userName ?? ""
                    var replacement = remark.isEmpty ? "@\(userName)" : "@\(remark)"
                    
                    if userIdStr == currentUserId, let userName = BuzUserSession.shared.userInfo?.base.userName {
                        replacement = "@\(userName)"
                    }
                    
                    translatedText = translatedText.replacingOccurrences(of: mentionString, with: replacement)
                }
            }
            
            return translatedText
        }
        
        return nil
    }
}
