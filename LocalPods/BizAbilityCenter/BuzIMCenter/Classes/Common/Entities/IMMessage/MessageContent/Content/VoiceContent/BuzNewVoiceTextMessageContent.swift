//
//  BuzNewVoiceTextMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import BuzCenterKit

public class BuzNewVoiceTextMessageContent: BuzVoiceTextMessageContent {
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.new_Voice_Text.rawValue) ?? .unknown
        return type
    }
}
