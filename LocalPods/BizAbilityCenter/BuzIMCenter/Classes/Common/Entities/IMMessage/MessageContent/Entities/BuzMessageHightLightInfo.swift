//
//  BuzMessageHightLightInfo.swift
//  buz
//
//  Created by liuyufeng on 2023/11/1.
//  Copyright © 2023 lizhi. All rights reserved.
//

import UIKit

@objcMembers
public class BuzMessageHightLightInfo: NSObject {
    
    public var range : NSRange
    public var text : String
    public var routerDict : [String : Any]
    public var isMentionedMe: Bool
    public var isMentioningAll: Bool = false

    init(text : String , range : NSRange , routerDict : [String : Any], isMentionedMe: Bool = false)
    {
        self.range = range
        self.text = text
        self.routerDict = routerDict
        self.isMentionedMe = isMentionedMe
        super.init()
    }
    
    public override var description: String {
        return "text = \(self.text) ; range = [loc : \(self.range.location) ; len : \(self.range.length)] , routerDict = \(self.routerDict)"
    }
}
