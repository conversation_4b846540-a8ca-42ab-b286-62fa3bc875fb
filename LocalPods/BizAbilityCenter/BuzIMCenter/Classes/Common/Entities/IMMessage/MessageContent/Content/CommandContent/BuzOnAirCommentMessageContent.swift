//
//  BuzOnAirCommentMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import Localizable
import BuzLocalizable
import BuzCenterKit

public class BuzOnAirCommentMessageContent: IM5MessageContent {
    // MARK: - Properties
    /// subType:
    /// 1: 用户拒绝导致无法On Air邀请
    /// 2: 用户超时拒绝导致无法On Air邀请
    /// 3：被呼叫用户忙线
    /// 4：成员达到上限
    public var subType: Int = 0
    
    /// 一个json结构 用于配合使用subtype
    public var placeHoldJson: String = ""
    
    /// 仅用于向前兼容字段。
    /// 如果旧版本收到一个不支持subType那么判断defaultContent是否为空，
    /// 如果defaultContent为空旧版显示升级版本，无点击交互
    /// 如果defaultContent不为空旧版显示defaultContent文案
    /// 服务端下发的默认文案，可能为空
    public var defaultContent: String = ""
    
    /// 频道 Id
    public var channelId: Int64 = 0
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.onAir_comment.rawValue) ?? .unknown
        return type
    }
    
    public override class func getContentFlag() -> IM5ContentFlag {
        return .ONLINE_PUSH_ONLY_CONTENT
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let dict = super.encodeToJsonObject()
        
        dict["subType"] = NSNumber(value: subType)
        dict["placeHoldJson"] = placeHoldJson
        dict["defaultContent"] = defaultContent
        dict["channelId"] = NSNumber(value: channelId)
        
        return dict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        subType = (dict["subType"] as? NSNumber)?.intValue ?? 0
        placeHoldJson = (dict["placeHoldJson"] as? String) ?? ""
        defaultContent = (dict["defaultContent"] as? String) ?? ""
        channelId = (dict["channelId"] as? NSNumber)?.int64Value ?? 0
        
        return dict
    }
}
