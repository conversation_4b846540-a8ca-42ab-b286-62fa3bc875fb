//
//  BuzWalkieTalkieMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import Localizable
import BuzLocalizable
import BuzCenterKit

public class BuzWalkieTalkieMessageContent: BuzWalkieTalkieCountMessageContent {
    
    // MARK: - Override Methods
    public override func digest() -> String {
        return Localizable.imsendaprivatevoiceleavemessage()
    }
    
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.walkieTalkie.rawValue) ?? .unknown
        return type
    }
    
    public override class func getContentFlag() -> IM5ContentFlag {
        return .PERSISTED
    }
    
}
