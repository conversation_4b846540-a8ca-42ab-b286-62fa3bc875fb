//
//  BuzLocalTipMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import Localizable
import BuzLocalizable
import BuzCenterKit

// MARK: - BuzLocalTipMessageType
public enum BuzLocalTipMessageType: Int {
    case voicemojiTimeout = 1
}
public class BuzLocalTipMessageContent: IM5MessageContent {
    // MARK: - Properties
    public var tipType: BuzLocalTipMessageType = .voicemojiTimeout
    public var tipText: String = ""
    
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.local_tip.rawValue) ?? .unknown
        return type
    }
    
    public override class func getContentFlag() -> IM5ContentFlag {
        return .DONT_UPDATE_CONVERSATION_CONTENT
    }
    
    public override func digest() -> String {
        return "[localTip: \(tipType.rawValue)]"
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let dict = super.encodeToJsonObject()
        
        dict["tipType"] = String(tipType.rawValue)
        dict["tipText"] = tipText
        
        let currentLanguage = Localizable.currentLanguage
        self.extra.setObject(currentLanguage, forKey: "userLanguage")
        
        if let extraString = self.extra.encodeToJsonString() {
            dict[IM5ContentExtraKey] = extraString
        }
        
        return dict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        if let tipTypeValue = dict["tipType"] as? String,
           let tipTypeInt = Int(tipTypeValue),
           let type = BuzLocalTipMessageType(rawValue: tipTypeInt) {
            self.tipType = type
        }
        
        self.tipText = dict["tipText"] as? String ?? ""
        
        return dict
    }
    
}
