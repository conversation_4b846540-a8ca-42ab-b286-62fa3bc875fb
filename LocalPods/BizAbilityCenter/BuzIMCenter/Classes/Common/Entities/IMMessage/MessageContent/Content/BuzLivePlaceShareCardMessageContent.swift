//
//  BuzLivePlaceShareCardMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/6.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import Localizable
import BuzLocalizable
import VoderXClient
import BuzCenterKit

public class BuzLivePlaceShareCardMessageContent: IM5MessageContent {
    // MARK: - Properties
    public var channelId: Int64 = 0
    public var ownerId: Int64 = 0
    public var groupId: Int64 = 0
    public var channelType: Int64 = 0
    public var name: String = ""
    public var startTime: Int64 = 0  /// 开播时间戳，毫秒，客户端在本地基于ntp时间计算开播时长
    public var topic: String = ""
    public var portraitDomain: String = ""
    public var bgImgUrl: String = ""
    public var portraitUrls: [String] = []
    public var channelStatus: Int32 = 0
    public var memberNum: Int64 = 0
    public var bgImgColor: String = ""
    
    // MARK: - Override Methods
    public override func digest() -> String {
        return Localizable.live_place
    }
    
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.livePlaceShareCard.rawValue) ?? .unknown
        return type
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let dict = super.encodeToJsonObject()
        
        dict["channelId"] = NSNumber(value: channelId)
        dict["ownerId"] = NSNumber(value: ownerId)
        dict["groupId"] = NSNumber(value: groupId)
        dict["channelType"] = NSNumber(value: channelType)
        dict["name"] = name
        dict["startTime"] = NSNumber(value: startTime)
        dict["topic"] = topic
        dict["portraitDomain"] = portraitDomain
        dict["portraitUrls"] = portraitUrls
        dict["channelStatus"] = NSNumber(value: channelStatus)
        dict["bgImgUrl"] = bgImgUrl
        dict["memberNum"] = NSNumber(value: memberNum)
        dict["bgImgColor"] = bgImgColor
        return dict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        if let dict = dict as? [String: Any] {
            channelId = (dict["channelId"] as? NSNumber)?.int64Value ?? 0
            ownerId = (dict["ownerId"] as? NSNumber)?.int64Value ?? 0
            groupId = (dict["groupId"] as? NSNumber)?.int64Value ?? 0
            channelType = (dict["channelType"] as? NSNumber)?.int64Value ?? 0
            name = dict["name"] as? String ?? ""
            startTime = (dict["startTime"] as? NSNumber)?.int64Value ?? 0
            topic = dict["topic"] as? String ?? ""
            portraitDomain = dict["portraitDomain"] as? String ?? ""
            portraitUrls = dict["portraitUrls"] as? [String] ?? []
            channelStatus = (dict["channelStatus"] as? NSNumber)?.int32Value ?? 0
            bgImgUrl = dict["bgImgUrl"] as? String ?? ""
            memberNum = (dict["memberNum"] as? NSNumber)?.int64Value ?? 0
            bgImgColor = dict["bgImgColor"] as? String ?? ""
        }
        
        return dict
    }
}
