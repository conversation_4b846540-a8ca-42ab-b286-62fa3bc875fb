//
//  BuzTextMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import BuzFoundation
import VoderXClient
import YYModel
import Localizable
import BuzLocalizable
import BuzCenterKit
import BuzUserSession

// MARK: - Constants
public let kExtraTopicKey = "isTopic"
public let kIsSendToBot = "isSendToBot"
public let kContentStyleExtra = "contentStyleExtra"
public let kHyperlinkMetadataExtra = "hyperlinkMetadataExtra"

public class BuzTextMessageContent: IM5TextMessageContent {
    // MARK: - Properties
    public var conversationType: BuzConversationType = .peer
    public var isReply: Bool = false
    public var isTopic: Bool = false
    public var isMentionedMe: Bool = false
    public var mentionedInfo: BuzMessageMentionedInfo? {
        didSet {
            mentionedDisplayInfo = BuzTextMessageMentionedsDisplayInfo.transformMentionedInfo(toTextMessageDisplay: mentionedInfo)
        }
    }
    public var mentionedDisplayInfo: BuzTextMessageMentionedsDisplayInfo?
    public var firstLinkMetaData: BuzLinkMetadata?
    public var linkString: String?
    public var detectLanguageCode: String?
    public var translateInfo: BuzTextTranslateInfo?
    public var editType: BuzMessageContentEditType = .none
    /// 滤镜信息
    public var filterInfo: BuzMessageContentFilterInfo?
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.text.rawValue) ?? .unknown
        return type
    }
    
    public override func digest() -> String {
        var text = self.text
        // Replace mention keys with usernames
        if var processedText = self.mentionedInfo?.textMentioned, let mentionedMaps = self.mentionedInfo?.mentionedMaps {
            for (key, obj) in mentionedMaps {
                let mentionedString = "@\(obj.userName)"
                processedText = processedText.replacingOccurrences(of: key, with: mentionedString)
            }
            text = processedText
        }
        if self.linkString != nil || self.firstLinkMetaData != nil {
            return String(format: Localizable.chat_pop_msg_link_tag, text ?? "")
        }else if let text = text {
            return text
        }
        
        return super.digest()
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let dict = super.encodeToJsonObject()
        
        // Set current language
        let currentLanguage = Localizable.currentLanguage
        self.extra.setObject(currentLanguage, forKey: "userLanguage")
        
        // Handle mentioned info
        if let mentionedInfo = mentionedInfo,
           !mentionedInfo.mentionedMaps.isEmpty,
           let mentionInfoDict = mentionedInfo.yy_modelToJSONObject() as? [String: Any] {
            mentionInfoDict.forEach { dict[$0] = $1 }
        }
        
        if let replaceContent = self.mentionedInfo?.replaceContent {
            self.text = replaceContent
        }
        
        // Handle topic
        if isTopic {
            let isTopicNumber = NSNumber(value: isTopic)
            self.extra.setObject(isTopicNumber, forKey: kExtraTopicKey)
            
            let extra = self.extra.object(forKey: "serverExtra")
            var sExtra: [String: Any]?
            if let extra = extra as? [String: Any] {
                sExtra = extra
            }else if let extraString = extra as? String {
                sExtra = extraString.toDict()
            }
            
            var sExtraM = sExtra ?? .init()
            sExtraM[kExtraTopicKey] = isTopicNumber
            self.extra.setObject(sExtraM, forKey: "serverExtra")
            
            if let extraString = self.extra.encodeToJsonString() {
                dict[IM5ContentExtraKey] = extraString
            }
        }
        
        // Handle link metadata
        if let firstLinkMetaData = firstLinkMetaData,
           let metaData = firstLinkMetaData.yy_modelToJSONObject() {
            dict["extra"] = [kContentStyleExtra: [kHyperlinkMetadataExtra: metaData]]
        }
        
        // Handle translate info
        if let translateInfo = translateInfo {
            dict["translateInfo"] = translateInfo.yy_modelToJSONObject()
        }
        
        if let detectLanguageCode = detectLanguageCode {
            dict["detectLanguageCode"] = detectLanguageCode
        }
        
        // Handle edit type
        if editType != .none {
            dict["editType"] = NSNumber(value: editType.rawValue)
        }
        
        // Handle filter info
        if let filterInfo = filterInfo {
            dict["filterInfo"] = filterInfo.yy_modelToJSONObject()
        }
        
        
        return dict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        // Handle extra and server extra
        let extra = dict["extra"]
        
        var sExtra: [String: Any]?
        if let extra = extra as? [String: Any] {
            sExtra = extra["serverExtra"] as? [String: Any]
        } else {
            let extra = self.extra.object(forKey: "serverExtra")
            if let extra = extra as? [String: Any] {
                sExtra = extra
            }else if let extraString = extra as? String {
                sExtra = extraString.toDict()
            }
        }
        
        var contentStyle: [String: Any]?
        if let extra = extra as? [String: Any] {
            contentStyle = extra[kContentStyleExtra] as? [String: Any]
        } else {
            let contentStyleExtra = self.extra.object(forKey: kContentStyleExtra)
            if let extra = contentStyleExtra as? [String: Any] {
                contentStyle = extra
            }else if let extra = contentStyleExtra as? String {
                contentStyle = extra.toDict()
            }
        }
        
        // Handle reply status
        if let sExtra = sExtra, !sExtra.isEmpty {
            if let value = sExtra["isReply"] as? Bool {
                isReply = value
            }
        }
        
        // Handle hyperlink metadata
        if let contentStyle = contentStyle,
           let hyperlinkMetadata = contentStyle[kHyperlinkMetadataExtra] as? [String: Any] {
            firstLinkMetaData = BuzLinkMetadata.yy_model(with: hyperlinkMetadata)
        }
        
        // Handle mentioned info
        if let dict = dict as? [String: Any] {
            let mentionInfo = BuzMessageMentionedInfo(dict: dict)
            if !mentionInfo.mentionedMaps.isEmpty {
                self.mentionedInfo = mentionInfo
                let currentUserId = "\(BuzUserSession.shared.uid)"
                for user in mentionInfo.mentionedMaps.values {
                    if user.userId == currentUserId || user.isMentioningAll {
                        self.isMentionedMe = true
                        break
                    }
                }
            }
        }
        
        // Handle translate info
        if let translateInfoDict = dict["translateInfo"] as? [String: Any] {
            translateInfo = BuzTextTranslateInfo.yy_model(with: translateInfoDict)
            if let text = translateInfo?.replaceContent {
                translateInfo?.translateText = text
            }
        }
        
        if let languageCode = dict["detectLanguageCode"] as? String {
            detectLanguageCode = languageCode
        }
        
        // Handle edit type
        if let editTypeNumber = dict["editType"] as? NSNumber {
            editType = BuzMessageContentEditType(rawValue: editTypeNumber.intValue) ?? .none
        } else {
            editType = .none
        }
        
        // Handle filter info
        if let filterInfoDict = dict["filterInfo"] as? [String: Any] {
            filterInfo = BuzMessageContentFilterInfo.yy_model(with: filterInfoDict)
        }
        
        return dict
    }
    
    // MARK: - Public Methods
    public func cannotDisplayMetadata() -> Bool {
        guard let metaData = firstLinkMetaData else { return true }
        
        return metaData.linkSiteName?.isEmpty ?? true &&
               metaData.linkTitle?.isEmpty ?? true &&
               metaData.linkDesc?.isEmpty ?? true &&
               metaData.linkImagePath?.isEmpty ?? true
    }
    
    public override func getEditType() -> BuzMessageContentEditType {
        return self.editType
    }
    
    public override func getTranslateInfo() -> BuzTextTranslateInfo? {
        return self.translateInfo
    }
    
    public override func mentionUsers() -> [String]? {
        var userIds: [String] = []
        if let mentionedMaps = self.mentionedInfo?.mentionedMaps {
            for mentionedUser in mentionedMaps.values {
                if mentionedUser.isMentioningAll {
                    userIds.append(IM5MentionedAllFlag)
                } else if !mentionedUser.userId.isEmpty {
                    userIds.append(mentionedUser.userId)
                }
            }
        }
        return userIds
    }
}
