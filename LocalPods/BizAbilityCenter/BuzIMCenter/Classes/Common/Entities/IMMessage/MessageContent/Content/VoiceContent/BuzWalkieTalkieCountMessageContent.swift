//
//  BuzWalkieTalkieCountMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import Localizable
import BuzLocalizable
import YYModel
import BuzCenterKit

public class BuzWalkieTalkieCountMessageContent: IM5VoiceMessageContent {
    // MARK: - Properties
    public var conversationType: BuzConversationType = .peer
    public var userLanguage: String = ""
    public var serverSourceCode: String?
    public var serverTargetCode: String?
    public var isSendToBot: NSNumber?
    public var mentionedUsers: [BuzMessageMentionedUser]?
    public var asrText: String?
    public var editType: BuzMessageContentEditType = .none
    //当前目标语言种类
    public var detectLanguageCode: String?
    public var translateInfo: BuzTextTranslateInfo?
    /// 滤镜信息
    public var filterInfo: BuzMessageContentFilterInfo?
    
    // MARK: - Override Methods
    public override class func getContentFlag() -> IM5ContentFlag {
        return .PERSISTED_AND_COUNTED
    }
    
    public override func digest() -> String {
        var digest = ""
        
        if conversationType == .peer {
            digest = Localizable.imsendaprivatevoiceleavemessage()
        } else if conversationType == .group {
            digest = Localizable.imsendaprivatevoiceleavemessage()
        }
        
        if digest.isEmpty {
            digest = Localizable.imsendaprivatevoiceleavemessage()
        }
        
        // 对于voice filter消息的处理
        if filterInfo != nil {
            digest = Localizable.send_voice_with_vf_tip
        }
        
        return digest
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let dict = super.encodeToJsonObject()
        let currentLanguage = Localizable.currentLanguage
        self.extra.setObject(currentLanguage, forKey: "userLanguage")
        if let mentionedUsers = mentionedUsers {
            dict["mentionedUsers"] = (mentionedUsers as NSArray).yy_modelToJSONObject()
        }
        var sExtra: [String: Any]?
        let serverExtra = self.extra.object(forKey: "serverExtra")
        if let serverExtra = serverExtra as? [String: Any] {
            sExtra = serverExtra
        }else if let extraString = serverExtra as? String {
            sExtra = extraString.toDict()
        }
        
        var sExtraM = sExtra ?? .init()
        sExtraM["userLanguage"] = currentLanguage

        if let isSendToBot = isSendToBot {
            self.extra.setObject([kIsSendToBot: isSendToBot], forKey: kContentStyleExtra)
        }
        
        self.extra.setObject(sExtraM, forKey: "serverExtra")
        
        if let extraString = extra.encodeToJsonString() {
            dict[IM5ContentExtraKey] = extraString
        }
        
        if let asrText = asrText {
            dict["asrText"] = asrText
        }
        
        if let translateInfo = translateInfo {
            dict["translateInfo"] = translateInfo.yy_modelToJSONObject()
        }
        
        if let detectLanguageCode = detectLanguageCode {
            dict["detectLanguageCode"] = detectLanguageCode
        }

        
        if editType != .none {
            dict["editType"] = NSNumber(value: editType.rawValue)
        }
        
        if let filterInfo = filterInfo {
            dict["filterInfo"] = filterInfo.yy_modelToJSONObject()
        }
        
        return dict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        if let msgAsrText = dict["asrText"] as? String {
            asrText = msgAsrText
        }
        
        if let msgTranslateInfo = dict["translateInfo"] as? [String: Any] {
            translateInfo = BuzTextTranslateInfo.yy_model(with: msgTranslateInfo)
        }
        
        if let languageCode = dict["detectLanguageCode"] as? String {
            detectLanguageCode = languageCode
        }

        if let editTypeNumber = dict["editType"] as? NSNumber {
            editType = BuzMessageContentEditType(rawValue: editTypeNumber.intValue) ?? .none
        } else {
            editType = .none
        }
        let extra = dict["extra"]
        var sExtra: [String: Any]?
        if let extra = extra as? [String: Any] {
            sExtra = extra["serverExtra"] as? [String: Any]
        } else {
            let serverExtra = self.extra.object(forKey: "serverExtra")
            if let extra = serverExtra as? [String: Any] {
                sExtra = extra
            }else if let extraString = serverExtra as? String {
                sExtra = extraString.toDict()
            }
        }
        
        if let sExtra = sExtra, !sExtra.isEmpty {
            userLanguage = sExtra["userLanguage"] as? String ?? ""
            
            if let sourceLanguage = sExtra["sourceLanguage"] as? String,
               let targetLanguage = sExtra["targetLanguage"] as? String {
                serverSourceCode = sourceLanguage
                serverTargetCode = targetLanguage
            }
        }
        
        var contentStyleExtra: [String: Any]?
        if let styleExtra = self.extra.object(forKey: kContentStyleExtra) as? [String: Any] {
            contentStyleExtra = styleExtra
        } else if let styleExtraString = self.extra.object(forKey: kContentStyleExtra) as? String {
            contentStyleExtra = NSDictionary.yy_dictionary(withJSON: styleExtraString) as? [String: Any]
        }
        
        if let botValue = contentStyleExtra?[kIsSendToBot] as? NSNumber {
            isSendToBot = botValue
        }
        
        mentionedUsers = NSArray.yy_modelArray(with: BuzMessageMentionedUser.self, json: dict["mentionedUsers"] as Any) as? [BuzMessageMentionedUser]
        
        if let msgFilterInfo = dict["filterInfo"] as? [String: Any] {
            filterInfo = BuzMessageContentFilterInfo.yy_model(with: msgFilterInfo)
        }
        
        return dict
    }
    
    public override func getEditType() -> BuzMessageContentEditType {
        return self.editType
    }
    
    public override func getTranslateInfo() -> BuzTextTranslateInfo? {
        return self.translateInfo
    }

}
