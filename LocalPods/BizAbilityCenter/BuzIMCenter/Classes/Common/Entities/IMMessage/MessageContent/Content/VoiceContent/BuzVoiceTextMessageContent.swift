//
//  BuzVoiceTextMessageContent.swift
//  buz
//
//  Created by st.chio on 2025/3/5.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import VoderXClient
import YYModel
import BuzCenterKit

// MARK: - Constants
public let kVoiceTextEmptyKey = "[empty]"
public let kVoiceTextErrorKey = "[error]"

public class BuzVoiceTextMessageContent: BuzWalkieTalkieCountMessageContent {
    // MARK: - Properties
    public var autoPlay: Bool = false
    public var text: String = ""
    public var replyMsgId: String?
    public var isReply: Bool = false
    
    // MARK: - Override Methods
    public override class func getContentType() -> IM5ContentType {
        let type = IM5ContentType(rawValue: BuzIMContentType.voice_Text.rawValue) ?? .unknown
        return type
    }
    
    public override class func getContentFlag() -> IM5ContentFlag {
        return .PERSISTED_AND_COUNTED
    }
    
    public override func encodeToJsonObject() -> NSMutableDictionary {
        let dict = super.encodeToJsonObject()
        
        if autoPlay {
            dict["autoPlay"] = NSNumber(value: autoPlay)
        }
        
        return dict
    }
    
    public override func decode(from object: Any) -> [AnyHashable : Any] {
        let dict = super.decode(from: object)
        
        autoPlay = (dict["autoPlay"] as? NSNumber)?.boolValue ?? false
        text = dict["text"] as? String ?? ""
        
        // Handle serverExtra
        let extra = dict["extra"]
        var sExtra: [String: Any]?
        if let extra = extra as? [String: Any] {
            sExtra = extra["serverExtra"] as? [String: Any]
        } else {
            let extra = self.extra.object(forKey: "serverExtra")
            if let extra = extra as? [String: Any] {
                sExtra = extra
            }else if let extraString = extra as? String {
                sExtra = extraString.toDict()
            }
        }
        
        // Handle contentStyleExtra
        var contentStyleExtra: [String: Any]?
        if let styleExtra = self.extra.object(forKey: kContentStyleExtra) as? [String: Any] {
            contentStyleExtra = styleExtra
        } else if let styleExtraString = self.extra.object(forKey: kContentStyleExtra) as? String {
            contentStyleExtra = NSDictionary.yy_dictionary(withJSON: styleExtraString) as? [String: Any]
        }
        
        if let botValue = contentStyleExtra?[kIsSendToBot] as? NSNumber {
            isSendToBot = botValue
        }
        
        if let sExtra = sExtra, !sExtra.isEmpty {
            if let value = sExtra["isReply"] as? Bool {
                isReply = value
            }
            replyMsgId = sExtra["replyMsgId"] as? String
        }
        
        return dict
    }
    
    // MARK: - Public Methods
    public func isASRSuccess() -> Bool {
        if !text.isEmpty {
            return !text.contains(kVoiceTextEmptyKey) && !text.contains(kVoiceTextErrorKey)
        }
        return false
    }
}
