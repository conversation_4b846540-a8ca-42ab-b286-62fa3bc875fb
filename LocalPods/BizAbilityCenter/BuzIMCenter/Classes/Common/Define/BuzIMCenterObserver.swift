//
//  BuzIMCenterObserver.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/14.
//

import BuzCenterKit

@objc public protocol BuzIMCenterObserver: AnyObject {}

// MARK: - BuzIMCenterAuthObserver
@objc
public protocol BuzIMCenterAuthObserver: BuzIMCenterObserver {
    @objc optional func imLoginSucceeded(center: BuzIMCenter)
    @objc optional func imLoginKickedOut(center: BuzIMCenter)
    @objc optional func imLoginNeedToRefreshTokenAndSessionKey(center: BuzIMCenter)
}

// MARK: - BuzIMCenterConversationObserver
@objc
public protocol BuzIMCenterConversationObserver: BuzIMCenterObserver {
    @objc optional func imConversationEntered(center: BuzIMCenter, targetId: String)
    @objc optional func imConversationLeaved(center: BuzIMCenter, targetId: String)
    @objc optional func imConversationUnreadCleared(center: BuzIMCenter, targetId: String)
    
    /// 会话列表变化通知回调
    @objc optional func imConversationsUpdated(center: BuzIMCenter, items: [BuzIMConvNotifyItem])
}

// MARK: - BuzIMCenterStateObserver
@objc
public protocol BuzIMCenterStateObserver: BuzIMCenterObserver {
    @objc optional func imServerStatusChanged(center: BuzIMCenter, status: BuzIMServiceStatus)
}

// MARK: - BuzIMCenterSendObserver
@objc
public protocol BuzIMCenterSendObserver: BuzIMCenterObserver {
    /// 将要发送的消息保存成功回调，与发送消息api中save回调功能一样
    @objc optional func imMessageToBeSentSaved(center: BuzIMCenter, message: BuzIMMessage)
    /// 发送消息完成（成功/失败）
    @objc optional func imMessageSendCompleted(center: BuzIMCenter, message: BuzIMMessage, error: Error?)
    /// 更新发送文件进度
    @objc optional func imAttachmentProgressUpdated(center: BuzIMCenter, message: BuzIMMessage, progress: Float)
    /// 发送状态变更
    @objc optional func imMessageSendStateChanged(center: BuzIMCenter, message: BuzIMMessage, sendState: BuzIMMessageSendState, isResend: Bool, error: Error?)
}

// MARK: - BuzIMCenterMessageObserver
@objc
public protocol BuzIMCenterMessageObserver: BuzIMCenterObserver {
    /// 实时消息（除了对讲机消息以外的所有消息）收到后的回调
    @objc optional func imMessageReceived(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem)
    /// 对讲机消息实时消息，收到后的回调
    @objc optional func imWalkieTalkieMessageReceived(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem)
    /// 收到Reference更新消息
    @objc optional func imReferenceMessageUpdated(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem)
    /// 收到Reaction消息
    @objc optional func imReactionMessageReceived(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem)
    /// reactions
    @objc optional func imReactionMessageChanged(center: BuzIMCenter, message: BuzIMMessage, type: BuzMessageReactionChangeType, error: Error?)
    /// 收到ASR编辑通知消息
    @objc optional func imASREditMessageReceived(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem)
    /// 收到Text翻译编辑通知消息
    @objc optional func imTextTranslateEditMessageReceived(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem)
    /// 收到Tex对应的目标语言编辑通知消息
    @objc optional func imTextDetectLanguageEditMessageReceived(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem)
    /// 接收到音效、礼物
    @objc optional func imInteractionMessageReceived(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem)
    /// OnAir 接收到comment
    @objc optional func imOnAirCommentMessageReceived(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem)
}

// MARK: - BuzIMCenterReporterObserver
@objc
public protocol BuzIMCenterReporterObserver: BuzIMCenterObserver {
    @objc optional func imReporterEvent(center: BuzIMCenter, eventKey: String, object: [AnyHashable: Any])
}


//MARK: -BuzCombineKit==================================
import Combine

// MARK: - Auth 事件
public extension BuzIMCenter {
    class AuthPublisher {
        /// IM 登录成功
        public let imLoginSucceeded = PassthroughSubject<BuzIMCenter, Never>()
        /// IM 登录失败
        public let imLoginFailed = PassthroughSubject<BuzIMCenter, Never>()
        /// IM 被踢出
        public let imLoginKickedOut = PassthroughSubject<BuzIMCenter, Never>()
    }
}

// MARK: - Conversation 事件
public extension BuzIMCenter {
    class ConvPublisher {
        /// 进入会话
        public let imConversationEntered = PassthroughSubject<(center: BuzIMCenter, targetId: String), Never>()
        /// 离开会话
        public let imConversationLeaved = PassthroughSubject<(center: BuzIMCenter, targetId: String), Never>()
        /// 清除会话未读数
        public let imConversationUnreadCleared = PassthroughSubject<(center: BuzIMCenter, targetId: String), Never>()
        /// 会话列表变化通知回调
        public let imConversationsUpdated = PassthroughSubject<(center: BuzIMCenter, items: [BuzIMConvNotifyItem]), Never>()
    }
}

// MARK: - State 事件
public extension BuzIMCenter {
    class StatePublisher {
        /// 服务器状态变更
        public let imServerStatusChanged = PassthroughSubject<(center: BuzIMCenter, status: BuzIMServiceStatus), Never>()
        public let imLoginStateChanged = PassthroughSubject<(BuzIMCenter), Never>()
    }
}

// MARK: - 发送相关事件
public extension BuzIMCenter {
    class SendPublisher {
        /// 将要发送的消息保存成功回调，与发送消息 API 中 save 回调功能一样
        public let imMessageToBeSentSaved = PassthroughSubject<(center: BuzIMCenter, message: BuzIMMessage), Never>()
        /// 发送消息完成（成功/失败）
        public let imMessageSendCompleted = PassthroughSubject<(center: BuzIMCenter, message: BuzIMMessage, error: Error?), Never>()
        /// 更新发送文件进度
        public let imAttachmentProgressUpdated = PassthroughSubject<(center: BuzIMCenter, message: BuzIMMessage, progress: Float), Never>()
        /// 发送状态变更
        public let imMessageSendStateChanged = PassthroughSubject<(center: BuzIMCenter, message: BuzIMMessage, sendState: BuzIMMessageSendState, isResend: Bool, error: Error?), Never>()
    }
}

// MARK: - 消息相关事件
public extension BuzIMCenter {
    class MessagePublisher {
        /// 实时消息（除了对讲机消息以外的所有消息）收到后的回调
        public let imMessageReceived = PassthroughSubject<(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem), Never>()
        /// 对讲机消息实时消息，收到后的回调
        public let imWalkieTalkieMessageReceived = PassthroughSubject<(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem), Never>()
        /// 收到 Reference 更新消息
        public let imReferenceMessageUpdated = PassthroughSubject<(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem), Never>()
        /// 收到 Reaction 消息
        public let imReactionMessageReceived = PassthroughSubject<(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem), Never>()
        /// reactions 变更
        public let imReactionMessageChanged = PassthroughSubject<(center: BuzIMCenter, message: BuzIMMessage, type: BuzMessageReactionChangeType, error: Error?), Never>()
        /// 收到 ASR 编辑通知消息
        public let imASREditMessageReceived = PassthroughSubject<(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem), Never>()
        /// 收到 Text 翻译编辑通知消息
        public let imTextTranslateEditMessageReceived = PassthroughSubject<(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem), Never>()
        /// 收到Tex对应的目标语言编辑通知消息
        public let imTextDetectLanguageEditMessageReceived = PassthroughSubject<(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem), Never>()
        /// 接收到音效、礼物
        public let imInteractionMessageReceived = PassthroughSubject<(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem), Never>()
        /// OnAir 接收到 comment
        public let imOnAirCommentMessageReceived = PassthroughSubject<(center: BuzIMCenter, notifyItem: BuzIMMessageNotifyItem), Never>()
    }
}

// MARK: - Reporter 事件
public extension BuzIMCenter {
    class ReporterPublisher {
        /// 事件上报
        public let imReporterEvent = PassthroughSubject<(center: BuzIMCenter, eventKey: String, object: [AnyHashable: Any]), Never>()
    }
}
