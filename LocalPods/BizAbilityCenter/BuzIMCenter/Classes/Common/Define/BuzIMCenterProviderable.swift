//
//  BuzIMCenterProviderable.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/11.
//

import Foundation
import BuzCenterKit

public protocol BuzIMCenterProviderable {
    //MARK: --pushContent & pushPayload---
    func pushContent(message: BuzIMMessage) -> String
    func pushPayload(message: BuzIMMessage) -> String
    
    func pushContent(reaction: BuzIMMessageReactionInfo) -> String
    func pushPayload(message: BuzIMMessage, pushContent: String, reactionInfo: BuzIMMessageReactionInfo) -> String
    
    //MARK: --Bot info---
    func isBotAtId(_ botId: String?) -> Bool
    //MARK: --UserInfo---
    func queryUserInfo(userId: Int64) -> BuzUserData?
    
    //MARK: --report----
    func trackReceivedMsgResult(message: <PERSON>uzIMMessage)
}
