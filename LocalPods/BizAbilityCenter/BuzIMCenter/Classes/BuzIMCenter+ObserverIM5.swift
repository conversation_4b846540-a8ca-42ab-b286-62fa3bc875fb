//
//  BuzIMCenter+ObserverIM5.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/14.
//

import VoderXClient

// MARK: - IM5AuthStatusObserver
extension BuzIMCenter: IM5AuthStatusObserver {
    public func im5AuthStatusChanged(_ authStatus: IM5AuthStatus, for loginInfo: IM5LoginInfo) {
        self.auth.im5AuthStatusChanged(authStatus, for: loginInfo)
    }
}

// MARK: - IM5ServiceStatusObserver
extension BuzIMCenter: IM5ServiceStatusObserver {
    public func im5ServiceStatusChanged(_ status: IM5ServiceStatus) {
        self.state.im5ServiceStatusChanged(status)
    }
}

// MARK: - IM5ConversationObserver
extension BuzIMCenter: IM5ConversationObserver {
    public func im5ConversationNotify(_ itemList: [IM5NotifyConversationItem]) {
        self.conv.im5ConversationNotify(itemList)
    }
    
    public func im5ConversationInitializedNotify(_ itemList: [IM5NotifyConversationItem]) {
        self.conv.im5ConversationInitializedNotify(itemList)
    }
}

// MARK: - IM5ReporterObserver
extension BuzIMCenter: IM5ReporterObserver {
    public func im5ReporterEvent(_ eventKey: String!, object: [AnyHashable : Any]!) {
        self.receiver.im5ReporterEvent(eventKey, object: object)
    }
}

// MARK: - IM5SendMessageObserver
//发送消息全局状态 , 全局不受重试开关影响 , isResend = false , 说明是第一次发送的回调, isResend = true , 说明是重试发送的回调
extension BuzIMCenter: IM5SendMessageObserver {
    /// 发送消息全局状态，全局不受重试开关影响
    /// isResend = false，说明是第一次发送的回调
    /// isResend = true，说明是重试发送的回调
    
    /// 只针对视频消息需要重新准备 (只有重试的时候会来,并且是init状态)
    public func im5SendMessagePrepareVideo(_ videoMessage: IM5Message, isResend: Bool) {
        self.sender.im5SendMessagePrepareVideo(videoMessage, isResend: isResend)
    }
    
    /// 发消息已经保存，或者视频消息状态已经更新
    public func im5SendMessageUpdated(_ message: IM5Message, isResend: Bool) {
        self.sender.im5SendMessageUpdated(message, isResend: isResend)
    }
    
    /// （附件消息）视频、图片、语音发送进度 (如果是发送同一种图片, 会很快跳过进度到complete状态)
    public func im5SendMessageProgress(_ attachmentMessage: IM5Message, completionBytes: Int64, totalBytes: Int64) {
        self.sender.im5SendMessageProgress(attachmentMessage, completionBytes: completionBytes, totalBytes: totalBytes)
    }
    
    /// 发送成功
    public func im5SendMessageSuccess(_ message: IM5Message, isResend: Bool) {
        self.sender.im5SendMessageSuccess(message, isResend: isResend)
    }
    
    /// 第一次发生失败的时候来到这里、手动压缩暂停、手动暂停上传、手动暂停取消发送，服务端正常失败，都会以失败来到这个回调
    public func im5SendMessageFailed(_ message: IM5Message, error: Error, isResend: Bool) {
        self.sender.im5SendMessageFailed(message, error: error, isResend: isResend)
    }
}

// MARK: - IM5MessageObserver
extension BuzIMCenter: IM5MessageObserver {
    public func im5MessageBatchNotify(_ items: [IM5NotifyMessageItem]) {
        self.receiver.im5MessageBatchNotify(items)
    }
}
