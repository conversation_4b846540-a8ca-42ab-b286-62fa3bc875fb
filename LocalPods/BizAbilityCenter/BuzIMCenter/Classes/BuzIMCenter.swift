//
//  BuzIMCenter.swift
//  buz
//
//  Created by st.chio on 2025/2/28.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import VoderXIdempotent

import BuzLog
import BuzDataShareKit
import BuzCenterKit

// MARK: ---BuzIMEventPublisher--------
public protocol BuzIMEventPublisher {
    associatedtype Observer: AnyObject
    
    func addObserver(_ observer: Observer)
    func removeObserver(_ observer: Observer)
}

protocol BuzIMEventPublisherInternal: BuzIMEventPublisher {
    var observers: NSHashTable<Observer> { get }
    func notifyObservers<T>(of type: T.Type, _ event: (T) -> Void)
}

extension BuzIMEventPublisherInternal {
    public func addObserver(_ observer: Observer) {
        observers.add(observer)
    }
    
    public func removeObserver(_ observer: Observer) {
        observers.remove(observer)
    }
    
    func notifyObservers<T>(of type: T.Type, _ event: (T) -> Void) {
        let observers = observers.allObjects.compactMap { $0 as? T }
        for observer in observers {
            event(observer)
        }
    }
}

// MARK: ---BuzIMControllerable--------
protocol BuzIMControllerable: BuzIMCenterCommonToolable {
    associatedtype IMCenter: AnyObject
    var center: IMCenter? { get }
    
    init(center: IMCenter?)
}

// MARK: ---BuzIMCenter
public class BuzIMCenter: NSObject, BuzIMEventPublisher, BuzIMEventPublisherInternal, BuzIMCenterCommonToolable {
    public typealias Observer = BuzIMCenterObserver
    internal let observers = NSHashTable<BuzIMCenterObserver>.weakObjects()
    
    public static let center = BuzIMCenter()
    
    private(set) lazy var auth: BuzIMAuthCtrl = .init(center: self)
    private(set) lazy var state: BuzIMStateCtrl = .init(center: self)
    private(set) lazy var conv: BuzIMConvCtrl = .init(center: self)
    private(set) lazy var query: BuzIMMsgQueryCtrl = .init(center: self)
    private(set) lazy var sender: BuzIMMsgSendCtrl = .init(center: self)
    private(set) lazy var ops: BuzIMMsgOpsCtrl = .init(center: self)
    private(set) lazy var readed: BuzIMMsgReadedCtrl = .init(center: self)
    private(set) lazy var channel: BuzIMChannelCtrl = .init(center: self)
    private(set) lazy var startup: BuzIMStartupCtrl = .init(center: self)
    private(set) lazy var receiver: BuzIMMsgReceiveCtrl = .init(center: self)
    
    public private(set) lazy var authPublisher = BuzIMCenter.AuthPublisher.init()
    public private(set) lazy var convPublisher = BuzIMCenter.ConvPublisher.init()
    public private(set) lazy var statePublisher = BuzIMCenter.StatePublisher.init()
    public private(set) lazy var sendPublisher = BuzIMCenter.SendPublisher.init()
    public private(set) lazy var messagePublisher = BuzIMCenter.MessagePublisher.init()
    public private(set) lazy var reportPublisher = BuzIMCenter.ReporterPublisher.init()
    
    // MARK: - provider
    public var provider: BuzIMCenterProviderable?
    
    // MARK: - Properties
    public private(set) var im5Client: IM5Client = IM5Client.instance()
    public private(set) var messageIdempotent: MessageIdempotent = .init(config: .init(userId: "", appGroups: NSEShareConfig.appGroups))
    
    private override init() {
        super.init()
        self.start()
    }
}

extension BuzIMCenter {
    public static var auth: BuzIMAuthCtrl { BuzIMCenter.center.auth }
    public static var state: BuzIMStateCtrl { BuzIMCenter.center.state }
    public static var conv: BuzIMConvCtrl { BuzIMCenter.center.conv }
    public static var query: BuzIMMsgQueryCtrl { BuzIMCenter.center.query }
    public static var sender: BuzIMMsgSendCtrl { BuzIMCenter.center.sender }
    public static var ops: BuzIMMsgOpsCtrl { BuzIMCenter.center.ops }
    public static var readed: BuzIMMsgReadedCtrl { BuzIMCenter.center.readed }
    public static var channel: BuzIMChannelCtrl { BuzIMCenter.center.channel }
}

private extension BuzIMCenter {
    func start() {
        // 添加观察者
        self.im5Client.add(self as IM5AuthStatusObserver)
        im5Client.add(self as IM5ServiceStatusObserver)
        im5Client.sendMessageObserver = self
        im5Client.add(self as IM5MessageObserver)
        im5Client.add(self as IM5ConversationObserver)
        // 启动
        self.startup.start()
        im5Client.setGlobalReporterObserver(self)
        self.state.setup()
    }
}

extension BuzIMCenter {
    
    func setMessageIdempotent(_ idempotent: MessageIdempotent) {
        self.messageIdempotent = idempotent
    }
}
