//
//  BuzIMChannelCtrl.swift
//  buz
//
//  Created by st.chio on 2025/3/6.
//  Copyright © 2025 lizhi. All rights reserved.
//

import VoderXClient
import BuzLocalizable
import BuzLog


public typealias BuzSendTemporaryMessageCompletion = (_ message: BuzIMMessage?, _ error: Error?) -> Void

public class BuzIMChannelCtrl: NSObject, BuzIMControllerable {
    typealias IMCenter = BuzIMCenter
    weak var center: BuzIMCenter?
    
    required init(center: BuzIMCenter?) {
        self.center = center
    }
}
public extension BuzIMChannelCtrl {
    /// 进入消息频道
    /// - Parameters:
    ///   - channelId: 频道 ID
    ///   - interval: 心跳频率，单位：秒
    /// - Discussion: 生命周期内只能进入一个频道
    func enterChannel(_ channelId: String, heatbeat interval: TimeInterval) {
        BuzIMLog.info("channelId = \(channelId), interval = \(interval)")
        self.center?.im5Client.enterChannel(channelId, heatbeat: interval)
    }
    
    /// 离开消息频道
    /// - Parameter channelId: 频道 ID
    func leaveChannel(_ channelId: String) {
        BuzIMLog.info("channelId = \(channelId)")
        self.center?.im5Client.leaveChannel(channelId)
    }
    
    /// 发送在线直推消息给频道
    /// - Parameters:
    ///   - content: 消息内容
    ///   - channelId: 频道 ID
    ///   - completionBlock: 消息已经完成发送的回调
    /// - Discussion: 在线直推消息不支持加密，不支持附件，不更新会话，不更新未读数，不保存本地历史
    func sendOnlinePushMessage(_ content: IM5MessageContentProtocol,
                               toChannel channelId: String,
                               completion completionBlock: BuzSendTemporaryMessageCompletion? = nil) {
        self.center?.im5Client.sendOnlinePushMessage(content, toChannel: channelId) { error in
            completionBlock?(nil, error)
        }
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMChannelCtrl {

    // MARK: - 进入消息频道
    func enterChannel(_ channelId: String, heatbeat interval: TimeInterval) -> AnyPublisher<Void, Never> {
        return Just(self.object.enterChannel(channelId, heatbeat: interval))
            .eraseToAnyPublisher()
    }

    // MARK: - 离开消息频道
    func leaveChannel(_ channelId: String) -> AnyPublisher<Void, Never> {
        return Just(self.object.leaveChannel(channelId))
            .eraseToAnyPublisher()
    }

    // MARK: - 发送在线直推消息
    func sendOnlinePushMessage(_ content: IM5MessageContentProtocol, toChannel channelId: String) -> AnyPublisher<(BuzIMMessage?, Error?), Never> {
        return Future { promise in
            self.object.sendOnlinePushMessage(content, toChannel: channelId, completion: { message, error in
                promise(.success((message, error)))
            })
        }
        .eraseToAnyPublisher()
    }
}
