//
//  BuzIMAuthCtrl+Observer.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog
import BuzAppConfiger
import BuzUIKit
import Localizable
import BuzLocalizable
import BuzTracker

// MARK: - IM5AuthStatusObserver
extension BuzIMAuthCtrl {
    
    public func im5AuthStatusChanged(_ authStatus: IM5AuthStatus, for loginInfo: IM5LoginInfo) {
        // 使用静态字典存储状态描述
        let statusNameDict: [Int: String] = [
            0: "IM5_INVALID",
            100: "IM5_AUTH_LOGINED - 已经登录",
            101: "IM5_AUTH_UNLOGIN - 未登录",
            102: "IM5_AUTH_LOGINING - 正在登录",
            103: "IM5_AUTH_KICKOUT - 被踢下线",
            104: "IM5_AUTH_LOGOUTED - 已经登出",
            201: "IM5_AUTH_LOGIN_ERROR - 登录出错",
            202: "IM5_AUTH_LOGOUT_ERROR - 登出出错",
            203: "IM5_AUTH_FROZEN - 账号被冻结",
            204: "IM5_AUTH_MAINTENANCING - 系统正在维护",
            205: "IM5_AUTH_TOKEN_ERROR - Token错误，App需要刷新Token并重新登录",
            206: "IM5_AUTH_SESSION_INVALID - 会话无效，App需要重新登录",
            207: "IM5_AUTH_SESSION_TIMEOUT - 会话过期，App 刷新 session",
            501: "IM5_AUTH_NETWORK_BROKEN - 网络连接状态断开，网络连接状态断开，不影响登录状态，APP需要关注此情况才需要更新UI",
            502: "IM5_AUTH_NETWORK_RECOVERED - 网络连接状态恢复"
        ]
        
        BuzIMLog.info("IM received changed authStatus : \(statusNameDict[Int(authStatus.rawValue)] ?? "Unknown")(\(authStatus.rawValue))")
        
        switch authStatus {
        case .AUTH_TOKEN_ERROR:
            self.loginExecuter?.handleIMStatusChangeToAuthTokenError(loginInfo: loginInfo)
            
        case .AUTH_SESSION_INVALID:
            
            self.loginExecuter?.handleIMStatusChangeToAuthSessionInvaid(loginInfo: loginInfo)
            
        case .AUTH_SESSION_TIMEOUT:
            self.loginExecuter?.handleIMStatusChangeToSessionTimeout(loginInfo: loginInfo)
            
        case .AUTH_KICKOUT:
            // 这里处理登出
            handleKickout()
            
        case .AUTH_FROZEN, .AUTH_MAINTENANCING:
            // 冻结或维护状态，暂不处理
            break
            
        case .AUTH_NETWORK_BROKEN:
            // 断网了就通知断开连接了，不改变原有的登录状态
            break
            
        case .AUTH_NETWORK_RECOVERED:
            // 网络恢复之后，用原来的登录状态通知上层
            break
        default:
            break
        }
    }
}

extension BuzIMAuthCtrl {
    private func handleKickout() {
        BuzIMLog.info("IM 执行被踢出逻辑")
        
        self.center?.notifyObservers(of: BuzIMCenterAuthObserver.self) { observer in
            observer.imLoginKickedOut?(center: self.center!)
        }
        self.center?.authPublisher.imLoginKickedOut.send(BuzIMCenter.center)
        
        AlertViewController.showOK(
            title: Localizable.login_account_logged_tip,
            message: Localizable.login_account_verify_tip,
            confirmTitle: Localizable.okay(),
            confirmStyle: .hightlight,
            confirmClickBlock: nil
        )
        
        TrackManager.reportViewScreen(propertiesDict: [
            "exclusive_id": "VS2023072002",
            "$title": "旧设备登录自动登出",
            "page_type": "device_quit"
        ])
    }
}
