//
//  File.swift
//  buz
//
//  Created by <PERSON><PERSON> on 2/14/25.
//  Copyright © 2025 lizhi. All rights reserved.
//

import Foundation
import BuzAppConfiger
import BuzTracker
import Localizable
import BuzCenterKit
import BuzLog
import Combine

public class BuzLoginSuccessTimeInfo {
    var validNTP: Bool
    var loginTime: Int64
    var loginUptime: Int64
    var loginLoading: Bool
    var loginLoadingEndTime: Int64
    var serviceStatusObserver: AnyCancellable?
    
    public var showLocalPushHandler: ((String) -> Void)?
    
    public init() {
        let timestamp = NTPTimeTool.nowTimestampMillisecondTuple()
        validNTP = timestamp.isNtp
        loginTime = timestamp.timestamp
        if !validNTP {
            loginUptime = Int64(ProcessInfo.processInfo.systemUptime * 1000)
        } else {
            loginUptime = 0
        }
        if BuzIMCenter.state.isStartupLoading() {
            loginLoading = true
            loginLoadingEndTime = Int64.max
            BuzLog.debug("isOldMessageTime, loading")
            serviceStatusObserver = BuzIMCenter.center.statePublisher.imLoginStateChanged
                .receive(on: DispatchQueue.main)
                .sink(receiveValue: { [weak self] _ in
                    guard let self else { return }
                    if !BuzIMCenter.state.isStartupLoading() {
                        loginLoadingEndTime = NTPTimeTool.nowTimestampMillisecond()
                        loginLoading = false
                        // loading 结束后，我们就不再需要监听这个状态变化了
                        self.serviceStatusObserver?.cancel()
                        self.serviceStatusObserver = nil
                        
                        BuzLog.debug("isOldMessageTime, loading end:\(loginLoadingEndTime)")
                        checkStartupUnread()
                    }
                })
        } else {
            loginLoading = false
            loginLoadingEndTime = NTPTimeTool.nowTimestampMillisecond()
            BuzLog.debug("isOldMessageTime, loading end:\(loginLoadingEndTime)")
        }
    }

    func updateNTPIfNeeded() {
        if !validNTP {
            let ntpTimestamp = NTPTimeTool.nowOnlyNTPMillisecond()
            if ntpTimestamp > 0 {
                let nowUptime = Int64(ProcessInfo.processInfo.systemUptime * 1000)
                let diff = nowUptime - loginUptime
                if diff > 0 {
                    let adjustLoginTime = ntpTimestamp - diff
                    if adjustLoginTime > 0 {
                        loginTime = adjustLoginTime
                        validNTP = true
                        BuzLog.debug("adjust login time to \(adjustLoginTime)")
                    }
                }
            }
        }
    }

    func isFarBackMessage(_ msgTime: Int64, convType: BuzConversationType) -> Bool {
        updateNTPIfNeeded()
        if msgTime < loginLoadingEndTime {
            let loadingTolerate = 1000 * Int64(MessageAutoPlayTimeStoreCache.messageAutoPlayTime)
            BuzLog.debug("isFarBackMessage, loading, msg: \(msgTime)")
            return msgTime < (loginTime - loadingTolerate)
        } else {
            // 其他时候，单聊都播放
            if convType == .peer {
                BuzLog.debug("isFarBackMessage, peer not loading")
                return false
            } else if convType == .group {
                // 群聊是当前时间 5 分钟内都自动播
                let currentGroupTolerate = 1000 * Int64(MessageAutoPlayTimeStoreCache.groupMessageAutoPlayTime)
                let now = NTPTimeTool.nowTimestampMillisecond()
                BuzLog.debug("isFarBackMessage, group not loading, msg: \(msgTime), now:\(now)")
                return msgTime < (now - currentGroupTolerate)
            } else {
                // 其他会话类型，默认按登录前处理
                return true
            }
        }
    }
    
    func isFarBackNotifyMessage(_ msgTime: Int64) -> Bool {
        isLoadingOldMessage(msgTime)
    }

    func isFarBackTranslateMessage(_ msgTime: Int64) -> Bool {
        isLoadingOldMessage(msgTime)
    }
    
    private func isLoadingOldMessage(_ msgTime: Int64) -> Bool {
        if msgTime < loginLoadingEndTime {
            let loadingTolerate = 1000 * Int64(MessageAutoPlayTimeStoreCache.messageAutoPlayTime)
            BuzLog.debug("isFarBackMessage translate, loading, msg: \(msgTime)")
            return msgTime < (loginTime - loadingTolerate)
        } else {
            //
            return false
        }
    }
    
    func checkStartupUnread() {
        guard UIApplication.shared.applicationState == .background else {
            return
        }
        BuzIMCenter.conv.getConversationsTotalUnreadCount { unreadCount in
            if unreadCount > 0 {
                let message = if unreadCount == 1 {
                    Localizable.have_one_unread_message
                } else {
                    String.init(format: Localizable.have_x_unread_messages, "\(unreadCount)")
                }
                self.showLocalPushHandler?(message)
            }
        }
    }
}

// 拓展增加判定“旧消息“ API
public extension BuzIMAuthCtrl {
    // 检查是否为“旧消息”，“旧消息“的产品规则参考：https://vocalbeats.sg.larksuite.com/wiki/YcTBwxXIWiNtXmkpXQ0lYIlognh
    func checkIsFarBackMessage(_ msg: BuzIMMessage) -> Bool {
        guard let loginTime = self.loginSuccessTimeInfo else {
            // 还没有登录？按登录前消息处理了
            return true
        }
        return loginTime.isFarBackMessage(msg.im5Message.createTime, convType: msg.conversationType)
    }
    
    // 检查是否为需要本地通知“旧消息”
    func checkIsFarBackNotifyMessage(_ msg: BuzIMMessage) -> Bool {
        guard let loginTime = self.loginSuccessTimeInfo else {
            return true
        }
        return loginTime.isFarBackNotifyMessage(msg.im5Message.createTime);
    }

    // 检查是否需要翻译“旧消息”
    func checkIsFarBackTranslateMessage(_ msg: BuzIMMessage) -> Bool {
        guard let loginTime = self.loginSuccessTimeInfo else {
            return true
        }
        return loginTime.isFarBackTranslateMessage(msg.im5Message.createTime);
    }

}
