//
//  IMLoginActionInfo.swift
//  buz
//
//  Created by liuyufeng on 2022/8/10.
//  Copyright © 2022 lizhi. All rights reserved.
//

import UIKit

@objcMembers
public class BuzIMLoginActionInfo: NSObject {

    //当前是否是登录成功状态
    public var isInLoginSuccessStatus : Bool = false {
        didSet{
            self.isRequestingLogin = false;
        }
    }
    
    ///当前是否正在请求登录
    public var isRequestingLogin : Bool = false
    public var loginError: Bool = false
    //是否有调用login进行登录,  会在logout中把该值置为false
    public var isExecutedLogin: Bool = false
    public func reset()
    {
        self.isInLoginSuccessStatus = false
        self.isRequestingLogin = false
        self.isExecutedLogin = false
    }
    
}
