//
//  IMNetwork.swift
//  buz
//
//  Created by liuyufeng on 2022/7/7.
//  Copyright © 2022 lizhi. All rights reserved.
//

import UIKit
import Buz<PERSON>L
import ITNetLibrary
import BuzLog

@objcMembers
class IMNetwork: NSObject {

    @discardableResult
    static func requestIMToken(complete : ((Bool , String?) -> Void)?) -> ITFuture
    {
        let requestServiceClient : BuzNetCommonServiceClient = BuzNetCommonServiceClient.init()
        
        return requestServiceClient.getImToken(request: RequestGetImToken.init()) { result in
            
            switch result {
            case .success(let responseInfo):
    
                guard responseInfo.code == 0 else{
                    BuzIMLog.error("IMToken请求失败！！code = \(responseInfo.code) ； msg = \(String(describing: responseInfo.msg))")
                    complete?(false , responseInfo.data?.token )
                    return
                }
                BuzIMLog.info("IMToken请求成功 token = \(String(describing: responseInfo.data?.token))")
                complete?(true , responseInfo.data?.token)
                
            case .failure(let error):
                
                BuzIMLog.error("IMToken请求失败！！error = \(error)")
                complete?(false , nil)
            }
        }
    }
    
}
