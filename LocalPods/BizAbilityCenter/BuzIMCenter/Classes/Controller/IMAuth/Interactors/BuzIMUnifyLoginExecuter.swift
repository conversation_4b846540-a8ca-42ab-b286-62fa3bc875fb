//
//  BuzIMUnifyLoginExecuter.swift
//  BuzIMCenter
//
//  Created by liuyufeng on 2025/4/25.
//

import Foundation
import VoderXClient
import VoderXCryptClient
import VoderXE2EE
import VoderXIdempotent
import BuzLog
import BuzUserSession

class BuzIMUnifyLoginExecuter: NSObject{
    private(set) var loginInfo: BuzIMLoginActionInfo = BuzIMLoginActionInfo()
    private weak var authCtrl: BuzIMAuthCtrl?
    private(set) var type: IMLoginType = .unify(IMUnifyLoginInfo.init(accid: "", uin: 0, session: ""))
    required init(authCtrl: BuzIMAuthCtrl){
        super.init()
        self.authCtrl = authCtrl
        BuzUserSession.shared.addObserver(self)
    }
    
    deinit{
        BuzIMLog.info("")
    }
}

extension BuzIMUnifyLoginExecuter: BuzAuthLoginable{
    func login(type: IMLoginType){
        
        guard case .unify(let info) = type else{
            BuzIMLog.error("loginType error!!!")
            return
        }
        DispatchQueue.main.safeAsyncUIQueue {
            assert(info.accid.length > 0 && info.uin > 0 && info.session.length > 0, "Unify登录传入的参数有问题，需要检查一下！！！")
            self.type = type
            self.loginInfo.isRequestingLogin = true
            self.loginInfo.isExecutedLogin = true
            self.authCtrl?.loginIMStateChange()
            let loginInfo = IM5BizLoginInfo(accid: info.accid, uin: info.uin, session: info.session)
            self.authCtrl?.center?.im5Client.onBizLogin(with: loginInfo, completion: { [weak self] in
                guard let self = self else {return}
                self.loginInfo.isRequestingLogin = false
                self.loginInfo.isInLoginSuccessStatus = true
                self.authCtrl?.loginIMStateChange()
                self.authCtrl?.executeLoginCompleteTask()
                if let center = self.authCtrl?.center {
                    self.authCtrl?.center?.notifyObservers(of: BuzIMCenterAuthObserver.self) { observer in
                        observer.imLoginSucceeded?(center: center)
                    }
                }
                self.authCtrl?.center?.authPublisher.imLoginSucceeded.send(BuzIMCenter.center)

                BuzIMLog.info("login complete.")
            })
        }
    }
    
    func logout() {
        BuzIMLog.info("begin logout[unify]")
        self.loginInfo.reset()
        self.authCtrl?.center?.im5Client.onBizLogouted(completion: {
            BuzIMLog.info("logout complete.")
        })
    }
}

extension BuzIMUnifyLoginExecuter: BuzIMStatusChangedHandleable
{
    func handleIMStatusChangeToAuthTokenError(loginInfo: IM5LoginInfo) {
        //business 401
        self.handleIMStatusChangeToAuthSessionInvaid(loginInfo: loginInfo)
    }
    
    func handleIMStatusChangeToAuthSessionInvaid(loginInfo: IM5LoginInfo) {
        //business 401
        guard let center = self.authCtrl?.center else {
            return
        }
        center.notifyObservers(of: BuzIMCenterAuthObserver.self) { observer in
            observer.imLoginKickedOut?(center: center)
        }
    }
    
    func handleIMStatusChangeToSessionTimeout(loginInfo: IM5LoginInfo) {
        //business 409
        guard let center = self.authCtrl?.center else {
            return
        }
        
        guard loginInfo.session == BuzUserSession.shared.sessionKey else {
            BuzIMLog.info("timeout session key is not userSession.sessionKey， IM5LoginInfo.sessionKey = \(loginInfo.session) ， BuzUserSession.shared.sessionKey = \(BuzUserSession.shared.sessionKey ?? "")")
            return
        }
        
        center.notifyObservers(of: BuzIMCenterAuthObserver.self) { observer in
            observer.imLoginNeedToRefreshTokenAndSessionKey?(center: center)
        }
    }
}


extension BuzIMUnifyLoginExecuter: BuzUserSessionObserver {
    func userSessionDidSaveSessionKeyAndRefreshToken(_ mgr: BuzUserSession) {
        guard let sessionKey = mgr.sessionKey else {
            BuzIMLog.info("sessionKey = nil , return ")
            return
        }
        let accid = "\(mgr.uid)"
        BuzIMLog.info("refresh im unify login, accid = \(accid), uin = \(mgr.imUin), sessionKey = \(sessionKey)")
        let loginInfo = IM5BizLoginInfo(accid: accid, uin: mgr.imUin, session: sessionKey)
        self.authCtrl?.center?.im5Client.onSessionRefrehed(loginInfo)
    }
}
