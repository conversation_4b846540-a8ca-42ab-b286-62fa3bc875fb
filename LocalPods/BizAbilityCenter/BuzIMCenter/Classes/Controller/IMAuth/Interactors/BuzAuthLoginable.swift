//
//  BuzAuthLoginable.swift
//  BuzIMCenter
//
//  Created by liuyufeng on 2025/4/27.
//

import Foundation
import VoderXClient

protocol BuzAuthLoginable {
    init(authCtrl: BuzIMAuthCtrl)
    var loginInfo: BuzIMLoginActionInfo{get}
    var type: IMLoginType{get}
    
    func login(type: IMLoginType)
    func logout()
}

protocol BuzIMStatusChangedHandleable
{
    //205、244 - Unify（401）、Token登录需要处理
    func handleIMStatusChangeToAuthTokenError(loginInfo: IM5LoginInfo)
    //206 - Token、Unify登录需要处理
    func handleIMStatusChangeToAuthSessionInvaid(loginInfo: IM5LoginInfo)
    //207 - Unify登录需要处理
    func handleIMStatusChangeToSessionTimeout(loginInfo: IM5LoginInfo)
}

typealias BuzIMLoginExecuter = BuzAuthLoginable&BuzIMStatusChangedHandleable


public struct IMUnifyLoginInfo{
    //uid
    public let accid: String
    //userInfo = uin
    public let uin: Int64
    //sessionKey
    public let session: String
}

public enum IMLoginType: Equatable{
    case token
    case unify(IMUnifyLoginInfo)
    
    public static func == (lhs: IMLoginType, rhs: IMLoginType) -> Bool {
        switch (lhs, rhs) {
        case (.token, .token):
            return true
        case (.unify, .unify):
            return true
        default:
            return false
        }
    }
    
    func createLoginExecuter(authCtrl: BuzIMAuthCtrl) -> BuzIMLoginExecuter
    {
        switch self {
        case .token:
            return BuzIMTokenLoginExecuter.init(authCtrl: authCtrl)
        case .unify(_):
            return BuzIMUnifyLoginExecuter.init(authCtrl: authCtrl)
        }
    }
    
    var description: String {
        switch self {
        case .token:
            return "token"
        case .unify(let info):
            return "unify [accid = \(info.accid), uin = \(info.uin), session = \(info.session)]"
        }
    }
}

