//
//  BuzIMTokenLoginImp.swift
//  BuzIMCenter
//
//  Created by liuyufeng on 2025/4/25.
//

import VoderXClient
import VoderXCryptClient
import VoderXE2EE
import VoderXIdempotent

import BuzDataStore
import BuzAppInfo
import BuzMagicBox
import BuzLog
import BuzUserSession
import BuzNetworker
import BuzDataShareKit
import LZDeviceUtil
import BuzUIKit
import ITNetLibrary
import BuzDataStore

/// 调用IM-SDK登录后的结果回调
enum BuzVoderXLoginResult: Int {
    /// 登录成功
    case success = 0
    /// 进入Token请求，成功后自动调用登录
    case requestingTokenAndAutoReteyLogin = 1
    /// 调用IM登录接口后返回失败，并且根据错误码的情况需要重试登录
    /// IM5ErrorServerInterError 、IM5ErrorServerUnknown 、 IM5ErrorServerBusy
    case failNeetToRetry = 2
    /// 用户ID为空
    case errorWithoutUserId = 3
}

fileprivate let IMTOKEN_LOCAL_STORE_KEY = "IMTokenKey_\(BuzUserSession.shared.uid)"

class BuzIMTokenLoginExecuter: NSObject{
    
    // 登录行为信息
    var loginInfo: BuzIMLoginActionInfo = .init()// Token 相关
    var token: String? {
        didSet {
            self.setToken()
        }
    }
    
    deinit{
        BuzIMLog.info("")
    }

    // MARK: - Private Properties---
    private var requestTokenTask: IDLTask?
    // MARK: - 重试任务
    private weak var retryRefreshTokenAndAutoLoginTask: FibIntervalRetryTask?
    private(set) weak var retryLoginTask: FibIntervalRetryTask?
    private(set) var type: IMLoginType = .token
    private weak var authCtrl: BuzIMAuthCtrl?
    required init(authCtrl: BuzIMAuthCtrl) {
        self.authCtrl = authCtrl
        self.token = MMKV.buz.string(forKey: IMTOKEN_LOCAL_STORE_KEY)
        super.init()
        NotificationCenter.default.addObserver(self, selector: #selector(didReceivedNetworkReachable), name: .netReachablePrompt, object: nil)
        BuzIMLog.info("local store token is \(self.token ?? "")")
    }
    
    @objc func didReceivedNetworkReachable() {
        loginIfNeeded()
    }
    
    private func loginIfNeeded() {
        // 前台的情况都需要登录  self.retryLoginTask == nil时 一般还在登录页面，还没有主动执行登录。
        if !loginInfo.isInLoginSuccessStatus &&
            !loginInfo.isRequestingLogin &&
            retryLoginTask != nil &&
            authCtrl?.center?.state.isDisableReceiveMsg == false {
            BuzIMLog.info("loginIfNeeded -> need execute login")
            login()
        } else {
            BuzIMLog.info("current is login success or never do login ; do not need execute login")
        }
    }
    
    private func setToken() {
        if let token = token, !token.isEmpty {
            let isStoreIMTokenSuccess = MMKV.buz.set(token, forKey: IMTOKEN_LOCAL_STORE_KEY)
            BuzIMLog.info("setToken isStoreIMTokenSuccess = \(isStoreIMTokenSuccess)")
        } else {
            MMKV.buz.removeValue(forKey: IMTOKEN_LOCAL_STORE_KEY)
        }
    }
    
    
    private func refeshToken(complete: @escaping (Bool) -> Void) {
        BuzIMLog.info("begin requestToken")
        weak var weakSelf = self
        self.requestTokenTask = IMNetwork.requestIMToken { isSuccess, token in
            guard let self = weakSelf else { return }
            self.requestTokenTask = nil
            self.token = token
            BuzIMLog.info("request IM token : isSuccess = \(isSuccess), token = \(String(describing: token))")
            complete(isSuccess)
        }
    }
    
    
    private func login(withResult resultCallback: ((BuzVoderXLoginResult) -> Void)?) {
        BuzIMLog.info("applicationState = \(UIApplication.shared.applicationState.rawValue)")
        self.loginInfo.loginError = false
        self.authCtrl?.loginIMStateChange()
        let currentUid = BuzUserSession.shared.uid
        
        // uid handle
        if currentUid == 0 {
            BuzIMLog.info("im login did return uid = 0")
            resultCallback?(.errorWithoutUserId)
            return
        }
        
        // token request handle
        if token?.isEmpty ?? true {
            refreshTokenAndAutoLogin()
            resultCallback?(.requestingTokenAndAutoReteyLogin)
            return
        }
        self.doLoginUser(currentUid, resultCallback: resultCallback)
    }
    
    private func doLoginUser(_ userId: Int64, resultCallback: ((BuzVoderXLoginResult) -> Void)?) {
        // login im handle
        let loginInfo = IM5LoginInfo()
        loginInfo.accid = String(userId)
        if let token = self.token {
            loginInfo.token = token
        }
        
        BuzIMLog.info("begin login VX, token = \(loginInfo.token)")
        self.authCtrl?.center?.im5Client.login(loginInfo, success: { [weak self] loginInfo in
            guard let self = self else { return }
            
            self.loginInfo.isInLoginSuccessStatus = true
            self.authCtrl?.loginIMStateChange()
            self.authCtrl?.executeLoginCompleteTask()
            
            resultCallback?(.success)
            
            self.authCtrl?.center?.authPublisher.imLoginSucceeded.send(BuzIMCenter.center)
            
        }, fail: { [weak self] loginInfo, error in
            guard let self = self else { return }
            
            self.loginInfo.isInLoginSuccessStatus = false
            self.authCtrl?.loginIMStateChange()
            self.authCtrl?.executeLoginCompleteTask()
            
            BuzIMLog.error("IM login fail ❌!!! error = \(error)")
            BuzProgressHUD.showDebugToast(withText: "error : IM Login Failure")
            
            var isNeedRetry = false
            let errorCode = IM5ErrorCommonResultCode.init(rawValue: (error as NSError).code)
            
            switch errorCode {
            case .frozen:
                BuzIMLog.error("IM登录失败 : 账号被封禁 ; 建议提示用户账号被封禁。error = \(error)")
                
            case .paramError:
                BuzIMLog.error("IM登录失败 : 参数错误; 一般是AppKey不对，开发应该检查客户端使用的AppKey是否跟业务服务器使用的AppKey一致。error = \(error)")
                
            case .serverInterError:
                BuzIMLog.error("IM登录失败 : 服务器内部错误; 开发期间找VoderX开发组反馈问题；线上情况应该进行定时重连 error = \(error)")
                isNeedRetry = true
                
            case .serverUnknown:
                BuzIMLog.error("IM登录失败 : 服务器位置错误 ; 开发期间找VoderX开发组反馈问题；线上情况应该进行定时重连 error = \(error)")
                isNeedRetry = true
                
            case .serverBusy:
                BuzIMLog.error("IM登录失败 : 系统繁忙 ; 一般是遇到服务器流控，应该定时重连。error = \(error)")
                isNeedRetry = true
                
            default:
                BuzIMLog.error("IM登录失败 : 未知错误 error = \(error)")
            }
            
            if isNeedRetry {
                resultCallback?(.failNeetToRetry)
            } else {
                self.loginInfo.loginError = true
                self.authCtrl?.center?.authPublisher.imLoginFailed.send(BuzIMCenter.center)
            }
            
        }, tokenError: { [weak self] loginInfo in
            guard let self = self else { return }
            
            BuzIMLog.error("IM login token error ❌")
            self.refreshTokenAndAutoLogin()
            resultCallback?(.requestingTokenAndAutoReteyLogin)
        })
    }
}


extension BuzIMTokenLoginExecuter: BuzAuthLoginable{
    /// 刷新Token并自动登录
    private func refreshTokenAndAutoLogin() {
        if let task = self.retryRefreshTokenAndAutoLoginTask {
            task.cancel()
        }
        
        BuzIMLog.info("begin create task of refreshTokenAndAutoLogin")
        let task = FibIntervalRetryTask(name: "com.buz.retryRefreshTokenAndAutoLogin")
        self.retryRefreshTokenAndAutoLoginTask = task
        
        weak var weakSelf = self
        task.start {
            guard let self = weakSelf else { return }
            
            self.refeshToken { isSuccess in
                if isSuccess {
                    // 请求成功
                    self.login()
                    self.retryRefreshTokenAndAutoLoginTask?.complete()
                } else {
                    if BuzNetworker.shared.netMonitor.currentReachabilityStatus != .notReachable {
                        // 有网络继续重试
                        _ = self.retryRefreshTokenAndAutoLoginTask?.retry()
                    } else {
                        // 没网络取消重试
                        self.retryRefreshTokenAndAutoLoginTask?.cancel()
                        self.loginInfo.reset()
                    }
                }
            }
        }
        
        RetryTaskManager.shared.addRetryTask(task)
    }
    
    func login(type: IMLoginType = .token){
        guard case .token = type else{
            BuzIMLog.error("loginType error!!!")
            return
        }
        
        DispatchQueue.main.safeAsyncUIQueue { [weak self] in
            guard let self = self else { return }
            
            if self.loginInfo.isRequestingLogin {
                BuzIMLog.info("isRequestingLogin == YES , do not multi login")
                return
            }
            
            self.loginInfo.isRequestingLogin = true
            self.loginInfo.isExecutedLogin = true
            self.retryLoginTask?.cancel()
            
            if self.token?.isEmpty ?? true {
                self.token = MMKV.buz.string(forKey: IMTOKEN_LOCAL_STORE_KEY)
            }
            
            BuzIMLog.info("begin create task of refreshTokenAndAutoLogin")
            let task = FibIntervalRetryTask(name: "com.buz.retryLoginTask.loginVX")
            self.retryLoginTask = task
            self.retryLoginTask?.start { [weak self] in
                guard let self = self else { return }
                
                self.login { result in
                    self.loginInfo.isRequestingLogin = false
                    
                    switch result {
                    case .success:
                        self.retryLoginTask?.complete()
                        if let center = self.authCtrl?.center {
                            self.authCtrl?.center?.notifyObservers(of: BuzIMCenterAuthObserver.self) { observer in
                                observer.imLoginSucceeded?(center: center)
                            }
                        }
                        self.authCtrl?.center?.authPublisher.imLoginSucceeded.send(BuzIMCenter.center)
                        
                    case .requestingTokenAndAutoReteyLogin: // 进入Token请求，成功后自动调用登录
                        self.retryLoginTask?.cancel()
                        
                    case .failNeetToRetry: // 调用IM登录接口后返回失败，并且根据错误码的情况需要重试登录 IM5ErrorServerInterError 、IM5ErrorServerUnknown 、 IM5ErrorServerBusy
                        if BuzNetworker.shared.netMonitor.currentReachabilityStatus != .notReachable {
                            // 有网络继续重试
                            _ = self.retryLoginTask?.retry()
                        } else {
                            // 没网络取消重试
                            self.retryLoginTask?.cancel()
                            self.loginInfo.reset()
                        }
                        
                    case .errorWithoutUserId:
                        break
                    }
                }
            }
            if let task = self.retryLoginTask {
                RetryTaskManager.shared.addRetryTask(task)
            }
        }
    }
    
    func logout() {
        BuzIMLog.info("begin logout[token]")
        self.requestTokenTask?.cancel()
        self.retryRefreshTokenAndAutoLoginTask?.cancel()
        self.token = nil
        self.loginInfo.reset()
        self.authCtrl?.center?.im5Client.logout { loginInfo, error in
            BuzIMLog.info("IM logout -> error = \(String(describing: error))")
        }
    }
}

extension BuzIMTokenLoginExecuter: BuzIMStatusChangedHandleable
{
    func handleIMStatusChangeToAuthTokenError(loginInfo: IM5LoginInfo) {
        self.handleIMStatusChangeToAuthSessionInvaid(loginInfo: loginInfo)
    }
    
    func handleIMStatusChangeToAuthSessionInvaid(loginInfo: IM5LoginInfo) {
        guard self.loginInfo.isExecutedLogin == true else {
            BuzIMLog.error("do not execute login or did logout, don't refreshTokenAndAutoLogin")
            return
        }
        self.refreshTokenAndAutoLogin()
    }
    
    func handleIMStatusChangeToSessionTimeout(loginInfo: IM5LoginInfo) {
        //空实现
    }
}
