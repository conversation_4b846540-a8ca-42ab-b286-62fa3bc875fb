//
//  BuzIMAuthCtrl.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/12.
//

import VoderXClient
import VoderXCryptClient
import VoderXE2EE
import VoderXIdempotent

import BuzDataStore
import BuzAppInfo
import BuzMagicBox
import BuzLog
import BuzUserSession
import BuzNetworker
import BuzDataShareKit
import LZDeviceUtil
import BuzUIKit
import ITNetLibrary

public class BuzIMAuthCtrl: NSObject, BuzIMControllerable {
    typealias IMCenter = BuzIMCenter
    weak var center: BuzIMCenter?
    
    // MARK: - Properties---
    public lazy var deviceInfo: BuzIMDeviceInfo = {
        let info = self.center?.im5Client.getDeviceInfo()
        return BuzIMDeviceInfo.init(deviceType: info?.deviceType ?? "", deviceId: info?.deviceId ?? "", sdkVersionCode: info?.sdkVersionCode ?? 0)
    }()
    
    // 可为空的属性
    public var loginSuccessTimeInfo: BuzLoginSuccessTimeInfo?
    
    private(set) var loginExecuter: BuzIMLoginExecuter?
      // 登录后的待执行任务数组，登录结果可能成功也可能失败
    var exectueTaskArrayInLoginComplete: [() -> Void] = []
    
    // E2EE 相关
    private var e2eeNeedDeactivate: Bool = false
    
    // MARK: - Public Properties---
    // 可读写属性
    public var peerSendWithE2EE: Bool = false
    
    
    required init(center: BuzIMCenter?) {
        self.center = center
        BuzIMLog.info("IM did start, appKey: \(VoderXInfo.appKey)")
    }
}

extension BuzIMAuthCtrl {
    
    func loginIMStateChange() {
        BuzIMLog.info("""
            当前IM初始化状态：
            --- isLogining: \(isLoginingIM())
            --- isServiceDisconnected: \(String(describing: self.center?.state.isServiceDisconnected()))
            --- isloading: \(String(describing: self.center?.state.isLoading()))
            --- isNetworkUnreadable: \(String(describing: self.center?.state.isNetworkUnreadable()))
            --- isLoginError: \(isLoginError())
            """)
        
        self.center?.statePublisher.imLoginStateChanged.send((BuzIMCenter.center))
    }
}

public extension BuzIMAuthCtrl {
    var isLoginSuccess: Bool {
        guard let loginExecuter = self.loginExecuter else{
            return false
        }
        return loginExecuter.loginInfo.isInLoginSuccessStatus
    }
    
    func isLoginingIM() -> Bool {
        guard let loginExecuter = self.loginExecuter else{
            return true
        }
        return loginExecuter.loginInfo.isInLoginSuccessStatus == false
    }
    
    func isLoginError() -> Bool {
        guard let loginExecuter = self.loginExecuter else{
            return false
        }
        return loginExecuter.loginInfo.loginError
    }
}

public extension BuzIMAuthCtrl {
    func settingLoginSuccessTimeInfo() {
        self.loginSuccessTimeInfo = BuzLoginSuccessTimeInfo()
    }
}

public extension BuzIMAuthCtrl {
    func addLoginCompleteTask(_ task: @escaping () -> Void) {
        DispatchQueue.main.async {
            if let loginExecuter = self.loginExecuter{
                // 正在请求登录 || 还没执行登录操作  -> 先把操作保存起来
                let needStoreAction = loginExecuter.loginInfo.isRequestingLogin || loginExecuter.loginInfo.isInLoginSuccessStatus == false
                if needStoreAction {
                    self.exectueTaskArrayInLoginComplete.append(task)
                    BuzIMLog.debug("add task into exectueTaskArrayInLoginComplete")
                } else {
                    task()
                }
            }else{
                self.exectueTaskArrayInLoginComplete.append(task)
                BuzIMLog.debug("add task into exectueTaskArrayInLoginComplete")
            }
        }
    }
    
    func executeLoginCompleteTask()
    {
        BuzIMLog.info("IM login success ✅  , exectue task count = \(self.exectueTaskArrayInLoginComplete.count)")
        self.exectueTaskArrayInLoginComplete.forEach { task in
            task()
        }
        self.exectueTaskArrayInLoginComplete.removeAll()
    }
}

public extension BuzIMAuthCtrl {
    func login() {
        let sessionMgr = BuzUserSession.shared
        let currentUid = sessionMgr.uid
        // uid handle
        if currentUid == 0 {
            BuzIMLog.error("im login did return uid = 0")
            return
        }
        let userId = String(currentUid)
        // 消息去重处理器
        let miconfig = MessageIdempotentConfig(userId: userId, appGroups: NSEShareConfig.appGroups)
        let messageIdempotent = MessageIdempotent(config: miconfig)
        self.center?.setMessageIdempotent(messageIdempotent)
        
        NSEShareConfig.saveSharedUserId(userId)
        var type: IMLoginType = .token
        if sessionMgr.useUnifyLogin {
            guard let sessionKey = sessionMgr.sessionKey else{
                BuzIMLog.error("im login did return sessionKey = nil")
                return
            }
            let info = IMUnifyLoginInfo.init(accid: userId, uin: sessionMgr.imUin, session: sessionKey)
            type = .unify(info)
        }
        
        let loginExecuter = self.reloadLoginExecuter(type: type)
        BuzIMLog.info("begin execute login with type = \(type)")
        
        if !VoderXInfo.isDisableE2EEInit() {
            e2eeNeedDeactivate = false
            NSEShareConfig.saveSharedDeviceId(AppInfos.deviceId)
            peerSendWithE2EE = VoderXInfo.isEnableE2EEPeerSend()
            
            let cryptClient = E2EEAesCryptClient.instance
            cryptClient.start(with: e2eeConfig()) { [weak self] result in
                guard let self = self else { return }
                self.center?.im5Client.setCrypt(cryptClient)
                BuzIMLog.info("E2EE start \(currentUid)")
                loginExecuter.login(type: type)
            }
        } else {
            e2eeNeedDeactivate = true
            peerSendWithE2EE = false
            self.center?.im5Client.setCrypt(nil)
            loginExecuter.login(type: type)
            checkE2EEDeactivation()
        }
    }
    
    private func reloadLoginExecuter(type: IMLoginType) -> BuzIMLoginExecuter
    {
        guard let loginExecuter = self.loginExecuter else {
            let loginExecuter = type.createLoginExecuter(authCtrl: self)
            self.loginExecuter = loginExecuter
            return loginExecuter
        }
        
        if loginExecuter.type != type {
            loginExecuter.logout()
            self.loginExecuter = nil
            let loginExecuter = type.createLoginExecuter(authCtrl: self)
            self.loginExecuter = loginExecuter
            return loginExecuter
        }
        
        return loginExecuter
    }
    
    func logout() {
        self.exectueTaskArrayInLoginComplete.removeAll()
        self.loginExecuter?.logout()
        self.loginExecuter = nil
        self.center?.state.logout()
        self.center?.query.logout()
    }
    
    private func e2eeConfig() -> E2EEConfig {
        let currentUid = BuzUserSession.shared.uid
        let userId = String(currentUid)
        let e2eeAppKey = VoderXInfo.e2eeAppKey
        NSEShareConfig.saveSharedE2EEAppKey(e2eeAppKey)
        
        let e2eeConfig = E2EEConfig(userId: userId,
                                    appKey: e2eeAppKey,
                                    env: .eastUS,
                                    deviceId: LZDeviceMgr.deviceId(),
                                    appGroups: VoderXInfo.appGroups)
        
        e2eeConfig.disableGroup = true
        e2eeConfig.enableSyncSenderKey = false
        return e2eeConfig
    }
}

extension BuzIMAuthCtrl {
    func setupE2EEOption(_ message: BuzIMMessage) {
        if message.conversationType == .peer {
            let isBotAtId = self.center?.provider?.isBotAtId(message.targetUid) ?? false
            if peerSendWithE2EE && !isBotAtId {
                BuzIMLog.info("E2EE>> enable peer encrypt")
                message.im5Message.setEnableEncryption()
            }
        }
    }
    
    func checkE2EEDeactivation() {
        // E2EE 下线
    }
}


//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMAuthCtrl {

    func addLoginCompleteTask() -> AnyPublisher<Void, Never> {
          return Future { promise in
              self.object.addLoginCompleteTask {
                  promise(.success(()))
              }
          }
          .eraseToAnyPublisher()
    }
}
