//
//  BuzIMMsgSendCtrl+Video.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//
import VoderXClient

import BuzCenterKit
import BuzLog

// MARK: - Video Message Handling
public extension BuzIMMsgSendCtrl {
    /// 准备视频消息
    func prepareVideoMessage(_ message: BuzIMMessage, save saveBlock: ((BuzIMMessage, Error?) -> Void)? = nil, completion: ((BuzIMMessage, Error?) -> Void)? = nil) {
        var bMsg = message
        sendMessagePrepare(message)
        
        self.center?.im5Client.prepareVideoMessage(message.im5Message,
                                                   toTarget: message.targetUid) { [weak self] attachmentMessage, error in
            guard let self = self else { return }
            
            BuzIMLog.error("prepareVideoMessage result = \(error == nil), error = \(String(describing: error))")
            
            if let attachmentMessage = attachmentMessage {
                bMsg = BuzIMMessage(im5Message: attachmentMessage)
                completion?(bMsg, error)
                
                self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                    observer.imMessageToBeSentSaved?(center: self.center!, message: bMsg)
                }
                self.center?.sendPublisher.imMessageToBeSentSaved.send((BuzIMCenter.center, bMsg))
            }
        }
    }
    
    /// 重新准备视频消息
    func reprepareVideoMessage(msgId: Int64, convType: BuzConversationType, completion: ((BuzIMMessage, Error?) -> Void)? = nil) {
        self.center?.im5Client.reprepareVideoMessage(msgId,
                                                     convType: IM5ConversationType(rawValue: convType.rawValue) ?? .peer) { message, error in
            BuzIMLog.error("reprepareVideoMessage result = \(error == nil), error = \(String(describing: error))")
            
            if let message = message {
                let bMsg = BuzIMMessage(im5Message: message)
                completion?(bMsg, error)
            }
        }
    }
    
    /// 设置准备失败
    func setPrepareFailed(msgId: Int64, convType: BuzConversationType, reason: String, completion: ((BuzIMMessage?, Error?) -> Void)? = nil) {
        self.center?.im5Client.setPrepareFailed(msgId,
                                                convType: IM5ConversationType(rawValue: convType.rawValue) ?? .peer,
                                                reason: reason) { attachmentMessage, error in
            BuzIMLog.error("setPrepareFailedWithMsgId result = \(error == nil), error = \(String(describing: error))")
            
            if let attachmentMessage = attachmentMessage {
                let bMsg = BuzIMMessage(im5Message: attachmentMessage)
                completion?(bMsg, error)
            } else {
                completion?(nil, error)
            }
        }
    }
}

// MARK: - Video Message Sending
extension BuzIMMsgSendCtrl {
    /// 上传视频
    func sendIM5VideoMessage(_ message: BuzIMMessage,
                             save saveBlock: ((BuzIMMessage, Error?) -> Void)? = nil,
                             progress progressBlock: BuzSendMessageProgress? = nil,
                             completion: BuzSendMessageCompletion? = nil) {
        let im5Message = message.im5Message
        
        // 处理图片消息内容
        if let imageContent = im5Message.messageContent as? IM5ImageMessageContent {
            // 如果是真正的png图片的话，在下载的时候也不会有影响缩略图
            imageContent.contentType = "image/jpeg" // "image/png"
        }
        
        // 处理附件消息内容
        if let attachmentContent = im5Message.messageContent as? IM5AttachmentMessageContent {
            attachmentContent.enableEmbeddedMessage = true
        }
        
        var bMsg = message
        self.center?.im5Client.sendPreparedVideoMessage(message.im5Message.msgId,
                                                        convType: message.im5Message.conversationType,
                                                        updated: { [weak self] attachmentMessage in
            guard let self = self else { return }
            
            if let content = attachmentMessage?.messageContent as? IM5VideoMessageContent {
                BuzIMLog.error("BuzLog_MediaHandling saveBlock: sendStatus = \(String(describing: attachmentMessage?.sendStatus)), uploadState = \(content.uploadState), uploadedBytes = \(content.uploadedBytes)")
            } else {
                BuzIMLog.error("BuzLog_MediaHandling saveBlock = \(String(describing: attachmentMessage?.sendStatus))")
            }
            
            if let attachmentMessage = attachmentMessage {
                bMsg = BuzIMMessage(im5Message: attachmentMessage)
                bMsg.localExtra.updateMsgSendNtpTime()
                
                saveBlock?(bMsg, nil)
                
                self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                    observer.imMessageToBeSentSaved?(center: self.center!, message: bMsg)
                }
                self.center?.sendPublisher.imMessageToBeSentSaved.send((BuzIMCenter.center, bMsg))
            }
        }, progress: { attachmentMessage, completionBytes, totalBytes in
            // Progress handling code commented out in original
        }, completion: { [weak self] attachmentMessage, error in
            guard let self = self else { return }
            
            let resultMessage: BuzIMMessage
            if let attachmentMessage = attachmentMessage {
                resultMessage = BuzIMMessage(im5Message: attachmentMessage)
            } else {
                resultMessage = message
            }
            
            completion?(resultMessage, error)
            
            self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                observer.imMessageSendCompleted?(center: self.center!, message: resultMessage, error: error)
            }
            self.center?.sendPublisher.imMessageSendCompleted.send((BuzIMCenter.center, bMsg, error))
            
            BuzIMLog.error("BuzLog_MediaHandling finish error = \(String(describing: error)); message = \(message); sendStatus = \(String(describing: attachmentMessage?.sendStatus))")
        })
    }
}

// MARK: - Video Message Sending
public extension BuzIMMsgSendCtrl {
    
    /// 发送和恢复上传视频
    func sendVideoMessage(_ message: BuzIMMessage,
                          save saveBlock: BuzSendMessageCompletion? = nil,
                          progress progressBlock: BuzSendMessageProgress? = nil,
                          completion: BuzSendMessageCompletion? = nil) {
        // assert(message.traceId.count > 0, "traceId should not be null")
        BuzIMLog.info("begin send video message -> \(message)")
        sendIM5VideoMessage(message,
                            save: saveBlock,
                            progress: progressBlock,
                            completion: completion)
    }
    
    /// 暂停视频和图片上传
    func pauseSending(msgId: Int64, convType: BuzConversationType, completion: BuzPauseMessageCompletion? = nil) {
        self.center?.im5Client.pauseSendingAttachmentMessage(msgId,
                                                             convType: IM5ConversationType(rawValue: convType.rawValue) ?? .peer) { error in
            BuzIMLog.error("BuzLog_MediaHandling pauseSendingMsgId result = \(error == nil), error = \(String(describing: error))")
            completion?(error)
        }
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgSendCtrl {

    /// 准备视频消息
    func prepareVideoMessage(_ message: BuzIMMessage) -> AnyPublisher<IMMsgSendResult, Never> {
        let subject = PassthroughSubject<IMMsgSendResult, Never>()
        self.object.prepareVideoMessage(message, save: { msg, error in
            subject.send(.save(msg, error))
        }, completion: { msg, error in
            subject.send(.completion(msg, error))
            subject.send(completion: .finished)
        })
        
        return subject.eraseToAnyPublisher()
    }
    
    /// 重新准备视频消息
    func reprepareVideoMessage(msgId: Int64, convType: BuzConversationType) -> AnyPublisher<(BuzIMMessage?, Error?), Never> {
        return Future { promise in
            self.object.reprepareVideoMessage(msgId: msgId, convType: convType, completion: { msg, error in
                promise(.success((msg, error)))
            })
        }
        .eraseToAnyPublisher()
    }
    
    /// 设置准备失败
    func setPrepareFailed(msgId: Int64, convType: BuzConversationType, reason: String) -> AnyPublisher<(BuzIMMessage?, Error?), Never> {
        return Future { promise in
            self.object.setPrepareFailed(msgId: msgId, convType: convType, reason: reason, completion: { msg, error in
                promise(.success((msg, error)))
            })
        }
        .eraseToAnyPublisher()
    }

    /// 发送和恢复上传视频
    func sendVideoMessage(_ message: BuzIMMessage) -> AnyPublisher<IMMsgSendResult, Never> {
        let subject = PassthroughSubject<IMMsgSendResult, Never>()

        self.object.sendVideoMessage(message, save: { msg, error in
            subject.send(.save(msg, error))
        }, progress: { msg, progress in
            subject.send(.progress(msg, progress))  // 发送进度信息
        }, completion: { msg, error in
            subject.send(.completion(msg, error))
            subject.send(completion: .finished)
        })

        return subject.eraseToAnyPublisher()
    }
    /// 暂停视频和图片上传
    func pauseSending(msgId: Int64, convType: BuzConversationType) -> AnyPublisher< Error?, Never> {
        return Future { promise in
            self.object.pauseSending(msgId: msgId, convType: convType, completion: { error in
                if let error = error {
                    promise(.success(error))
                } else {
                    promise(.success(nil))
                }
            })
        }
        .eraseToAnyPublisher()
    }
}
