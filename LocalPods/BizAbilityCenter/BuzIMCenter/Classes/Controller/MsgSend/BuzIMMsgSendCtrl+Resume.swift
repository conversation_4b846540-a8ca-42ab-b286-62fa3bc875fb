//
//  BuzIMMsgSendCtrl+Resume.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog

// MARK: - Typealias
public typealias CancelSendingAttachmentMessageCompletion = (Error?) -> Void

public extension BuzIMMsgSendCtrl {
    
    /// 重发消息
    func resendMessage(messageId: Int64,
                       convType: BuzConversationType,
                       save savedBlock: ((BuzIMMessage?) -> Void)? = nil,
                       completion completeBlock: ((BuzIMMessage?, Error?) -> Void)? = nil) {
        self.center?.im5Client.resendMessage(messageId,
                                             convType: convType == .peer ? .peer : .group,
                                             updated: { [weak self] message in
            guard let self = self else { return }
            
            var bMsg: BuzIMMessage?
            if let message = message {
                bMsg = BuzIMMessage(im5Message: message)
                bMsg?.localExtra.updateMsgSendNtpTime()
                savedBlock?(bMsg)
            } else {
                savedBlock?(nil)
            }
            
            if let bMsg = bMsg {
                self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                    observer.imMessageToBeSentSaved?(center: self.center!, message: bMsg)
                }
                self.center?.sendPublisher.imMessageToBeSentSaved.send((BuzIMCenter.center, bMsg))
            }
            
            BuzIMLog.info("resendMessage.save finish  message = \(String(describing: message))")
        }, progress: { _, _, _ in
            // Progress handling
        }, completion: { [weak self] message, error in
            guard let self = self else { return }
            
            var bMsg: BuzIMMessage?
            if let message = message {
                bMsg = BuzIMMessage(im5Message: message)
                completeBlock?(bMsg, error)
            } else {
                completeBlock?(nil, error)
            }
            
            if let bMsg = bMsg {
                self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                    observer.imMessageSendCompleted?(center: self.center!, message: bMsg, error: error)
                }
                self.center?.sendPublisher.imMessageSendCompleted.send((BuzIMCenter.center, bMsg, error))
            }
            
            BuzIMLog.info("forwardMessage.resendMessage finish error = \(String(describing: error)); message = \(String(describing: message)); sendStatus = \(String(describing: message?.sendStatus))")
        })
    }
}


public extension BuzIMMsgSendCtrl {
    /// 恢复图片发送消息
    func resumeSending(msgId: Int64,
                       convType: BuzConversationType,
                       save saveBlock: BuzSendMessageCompletion? = nil,
                       completion: BuzPauseMessageCompletion? = nil) {
        self.center?.im5Client.resumeSendAttachmentMessage(msgId,
                                                           convType: IM5ConversationType(rawValue: convType.rawValue) ?? .peer,
                                                           updated: { message in
            guard let message = message else {
                return
            }
            let bMsg = BuzIMMessage(im5Message: message)
            
            if let content = message.messageContent as? IM5ImageMessageContent {
                BuzIMLog.error("BuzLog_MediaHandling updatedBlock: sendStatus = \(message.sendStatus), uploadState = \(content.uploadState), uploadedBytes = \(content.uploadedBytes)")
            } else {
                BuzIMLog.error("BuzLog_MediaHandling updatedBlock = \(message.sendStatus)")
            }
            
            saveBlock?(bMsg, nil)
            
        }, progress: { attachmentMessage, completionBytes, totalBytes in
            // Progress handling code commented out in original
            /*
             let bMsg = BuzIMMessage(im5Message: attachmentMessage)
             
             var progress = Int(100.0 * (Double(completionBytes) / Double(totalBytes)))
             if completionBytes == totalBytes {
             // 修正浮点偏差
             progress = 100
             }
             
             var fileName = ""
             var remoteURL = ""
             if let msgContent = attachmentMessage.messageContent as? IM5AttachmentMessageContent {
             fileName = msgContent.localPath.lastPathComponent
             remoteURL = msgContent.remoteURL
             }
             
             self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
             observer.imAttachmentProgressUpdated?(center: self.center!, message: bMsg, progress: Float(progress))
             }
             
             BuzIMLog.info("resumeSendAttachment progress:(\(completionBytes),\(totalBytes)) -> \(progress)% fileName: \(fileName), remoteURL: \(remoteURL)")
             */
            
        }, completion: { [weak self] attachmentMessage, error in
            guard let self = self else { return }
            
            BuzIMLog.error("resumeSendAttachment resumeSendingMsgId: result = \(error == nil), error = \(String(describing: error))")
            completion?(error)
            
            if let attachmentMessage = attachmentMessage {
                let bMsg = BuzIMMessage(im5Message: attachmentMessage)
                self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                    observer.imMessageSendCompleted?(center: self.center!, message: bMsg, error: error)
                }
                self.center?.sendPublisher.imMessageSendCompleted.send((BuzIMCenter.center, bMsg, error))
            }
        })
    }
    
    /// 取消发送消息
    func cancelSending(msgId: Int64, convType: BuzConversationType, completion: CancelSendingAttachmentMessageCompletion? = nil) {
        self.center?.im5Client.cancelSendingAttachmentMessage(msgId, convType: IM5ConversationType(rawValue: convType.rawValue) ?? .peer) { error in
            completion?(error)
        }
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgSendCtrl {
    
    // 重发消息
    func resendMessage(messageId: Int64, convType: BuzConversationType) -> AnyPublisher<IMMsgSendResult, Never> {
        let subject = PassthroughSubject<IMMsgSendResult, Never>()
        
        self.object.resendMessage(messageId: messageId, convType: convType, save: { msg in
            subject.send(.save(msg, nil))
        }, completion: { msg, error in
            subject.send(.completion(msg, error))
            subject.send(completion: .finished)
        })
        
        return subject.eraseToAnyPublisher()
    }
    // 恢复图片发送消息
    func resumeSending(msgId: Int64, convType: BuzConversationType) -> AnyPublisher<IMMsgSendResult, Never> {
        let subject = PassthroughSubject<IMMsgSendResult, Never>()
        
        self.object.resumeSending(msgId: msgId, convType: convType, save: { msg, error in
            subject.send(.save(msg, error))
        }, completion: { error in
            subject.send(.pause(error))
            subject.send(completion: .finished)
        })
        
        return subject.eraseToAnyPublisher()
    }
    
    // 取消发送消息
    func cancelSending(msgId: Int64, convType: BuzConversationType) -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.cancelSending(msgId: msgId, convType: convType, completion: { error in
                if let error = error {
                    promise(.success(error))
                } else {
                    promise(.success(nil))
                }
            })
        }
        .eraseToAnyPublisher()
    }
}
