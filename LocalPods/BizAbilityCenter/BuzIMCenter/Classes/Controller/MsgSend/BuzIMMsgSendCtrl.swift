//
//  BuzIMMsgSendCtrl.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/12.
//

import VoderXClient

import BuzCenterKit
import BuzLog

// MARK: - Typealias
public typealias BuzSendMessageCompletion = (BuzIMMessage, Error?) -> Void
public typealias BuzSendMessageProgress = (BuzIMMessage, Float) -> Void
public typealias BuzPauseMessageCompletion = (Error?) -> Void

public class BuzIMMsgSendCtrl: NSObject, BuzIMControllerable {
    typealias IMCenter = BuzIMCenter
    weak var center: BuzIMCenter?
    
    required init(center: BuzIMCenter?) {
        self.center = center
    }
}

// MARK: - Send Message
public extension BuzIMMsgSendCtrl {
    /// 公共的发送入口
    func sendMessage(_ message: BuzIMMessage, save saveBlock: ((BuzIMMessage, Error?) -> Void)? = nil, completion: BuzSendMessageCompletion? = nil) {
        // assert(message.traceId.count > 0, "traceId should not be null")
        BuzIMLog.info("begin send message -> \(message)")
        
        sendMessagePrepare(message)
        
        if message.isLocalMsg {
            insertLocalMessage(message, completion: completion)
        } else {
            switch BuzIMMessage.messageContentType(message.im5Message) {
            case .count_WalkieTalkie,
                    .walkieTalkie,
                    .voice_Text,
                    .fileAttachment,
                    .new_Voice_Text:
                sendIM5AttachmentMessage(message, save: saveBlock, completion: completion)
                
            case .voice_Emoji,
                    .compatibleVoiceEmoji:
                sendIM5Message(message, save: saveBlock, completion: completion)
                
            case .text,
                    .location,
                    .voicegif:
                sendIM5Message(message, save: saveBlock, completion: completion)
                
            case .image:
                sendIM5AttachmentMessage(message, save: saveBlock, completion: completion)
                
            case .video:
                sendIM5VideoMessage(message, save: saveBlock, progress: nil, completion: completion)
                
            default:
                break
            }
        }
    }
}

public extension BuzIMMsgSendCtrl {
    
    /// 插入一条本地消息
    /// - Parameters:
    ///   - message: 消息体
    ///   - completion: 处理结果回调
    /// - Note: 此函数的time的单位是毫秒！
    func insertLocalMessage(_ message: BuzIMMessage, completion: ((BuzIMMessage, Error?) -> Void)?) {
        self.center?.im5Client.insertLocalMessage(message.im5Message,
                                                  fromId: message.fromUid,
                                                  targetId: message.targetUid,
                                                  status: .sendOK,
                                                  createTime: message.businessTimestamp,
                                                  localMsgUID: nil) { [weak self] im5Message, error in
            guard let self = self, let im5Message = im5Message else { return }
            
            let bMsg = BuzIMMessage(im5Message: im5Message)
            
            completion?(bMsg, error)
            
            let notifyItem = BuzIMMessageNotifyItem(message: bMsg,operation: .update)
            self.center?.notifyObservers(of: BuzIMCenterMessageObserver.self) { observer in
                observer.imMessageReceived?(center: self.center!, notifyItem: notifyItem)
            }
            self.center?.messagePublisher.imMessageReceived.send((BuzIMCenter.center, notifyItem))
        }
    }
}

extension BuzIMMsgSendCtrl {
    
    /// 所有的消息发送业务数据准备
    func sendMessagePrepare(_ message: BuzIMMessage) {
        let im5Message = message.im5Message
        im5Message.messageContent.extra = IM5Extra(dictionary: message.extra.toExtraDict())
        
        // if message.extra.serverExtra.sourceLanguage.count > 0 || message.extra.serverExtra.targetLanguage.count > 0 {
        //     // im5message Re execute encodeToJsonObject
        //     im5Message = IM5Message(content: im5Message.messageContent,
        //                            conversationType: im5Message.conversationType,
        //                            userInfo: im5Message.userInfo)
        //     message.update(with: im5Message)
        // }
        
        message.isSentByMySelf = true
        
        self.center?.auth.setupE2EEOption(message)
        self.center?.auth.checkE2EEDeactivation()
        
        if message.conversationType == .group {
            assert(message.groupInfo != nil, "发送群对讲机消息一定要带上群信息,否则ptt消息逻辑将会出错 , 或者推送信息显示错误")
        }
        
        if message.supportApnsPush {
            let pushContent = self.center?.provider?.pushContent(message: message) ?? ""
            let pushPayload: String = self.center?.provider?.pushPayload(message: message) ?? ""
            im5Message.addPushContent(pushContent, pushPayload: pushPayload)
        }
    }
}

extension BuzIMMsgSendCtrl {
    
    /// 普通消息: 文本、表情语音、地图
    func sendIM5Message(_ message: BuzIMMessage, save saveBlock: ((BuzIMMessage, Error?) -> Void)?, completion: BuzSendMessageCompletion?) {
        var bMsg = message
        
        self.center?.im5Client.send(message.im5Message,
                                    toTarget: message.targetUid,
                                    saved: { [weak self] im5Message, error in
            guard let self = self else { return }
            
            bMsg = BuzIMMessage(im5Message: im5Message)
            saveBlock?(bMsg, error)
            
            self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                observer.imMessageToBeSentSaved?(center: self.center!, message: bMsg)
            }
            self.center?.sendPublisher.imMessageToBeSentSaved.send((BuzIMCenter.center, bMsg))
        }, completion: { [weak self] im5Message, error in
            guard let self = self, let im5Message = im5Message else { return }
            
            bMsg = BuzIMMessage(im5Message: im5Message)
            BuzIMLog.error("sendIM5Message result = \(error == nil), error = \(String(describing: error))")
            
            completion?(bMsg, error)
            
            self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                observer.imMessageSendCompleted?(center: self.center!, message: bMsg, error: error)
            }
            self.center?.sendPublisher.imMessageSendCompleted.send((BuzIMCenter.center, bMsg, error))
        })
    }
}

extension BuzIMMsgSendCtrl {
    
    /// 发送图片、普通语音、文本语音
    func sendIM5AttachmentMessage(_ message: BuzIMMessage, save saveBlock: ((BuzIMMessage, Error?) -> Void)?, completion: BuzSendMessageCompletion?) {
        let im5Message = message.im5Message
        
        if let imageContent = im5Message.messageContent as? IM5ImageMessageContent {
            // 如果是真正的png图片的话，在下载的时候也不会有影响缩略图
            imageContent.contentType = "image/jpeg" // "image/png"
        }
        
        if let attachmentContent = im5Message.messageContent as? IM5AttachmentMessageContent {
            attachmentContent.enableEmbeddedMessage = !(attachmentContent is BuzFileMessageContent)
        }
        
        var bMsg = message
        
        self.center?.im5Client.sendAttachmentMessage(im5Message, toTarget: message.targetUid, save: { [weak self] im5Message, error in
            guard let self = self else { return }
            BuzIMLog.info("✅sendIM5AttachmentMessage save callback error = \(String(describing: error)) ; message = \(message)")
            bMsg = BuzIMMessage(im5Message: im5Message)
            
            saveBlock?(bMsg, error)
            
            self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                observer.imMessageToBeSentSaved?(center: self.center!, message: bMsg)
            }
            self.center?.sendPublisher.imMessageToBeSentSaved.send((BuzIMCenter.center, bMsg))
        }, timeout: { attachmentMessage, completionOffsetBytes, completionBytes, totalBytes in
            BuzIMLog.error("❌im5 file upload timeout")
            completion?(bMsg, NSError(domain: "com.buz.vx",
                                      code: IM5ErrorTypeUploadCode.timeout.rawValue,
                                      userInfo: nil))
        }, progress: { attachmentMessage, completionBytes, totalBytes in
            //            let uploadState = (attachmentMessage.messageContent as? BuzImageMessageContent)?.uploadState
            //
            //            print("fangyukui333-\(uploadState)")
            //            var progress = Int(100.0 * (Double(completionBytes) / Double(totalBytes)))
            //            if completionBytes == totalBytes {
            //                // 修正浮点偏差
            //                progress = 100
            //            }
            //
            //            var fileName = ""
            //            var remoteURL = ""
            //            if let msgContent = attachmentMessage.messageContent as? IM5AttachmentMessageContent {
            //                fileName = msgContent.localPath.lastPathComponent
            //                remoteURL = msgContent.remoteURL
            //            }
            //
            //            self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
            //                observer.imAttachmentProgressUpdated?(center: self.center!, message: bMsg, progress: Float(progress))
            //            }
            //
            //            BuzIMLog.info("sendIM5AttachmentMessage progress:(\(completionBytes),\(totalBytes)) -> \(progress)% fileName: \(fileName), remoteURL = \(remoteURL)")
            
        }, completion: { [weak self] im5Message, error in
            guard let self = self, let im5Message = im5Message else { return }
            bMsg = BuzIMMessage(im5Message: im5Message)
            
            completion?(bMsg, error)
            
            self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                observer.imMessageSendCompleted?(center: self.center!, message: bMsg, error: error)
            }
            self.center?.sendPublisher.imMessageSendCompleted.send((BuzIMCenter.center, bMsg, error))
            BuzIMLog.error("sendIM5AttachmentMessage finish error = \(String(describing: error)) ; message = \(String(describing: message))")
        })
    }
}


//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgSendCtrl {
    enum IMMsgSendResult {
        case save(BuzIMMessage?, Error?)
        case progress(BuzIMMessage, Float)
        case pause(Error?)
        case completion(BuzIMMessage?, Error?)
    }
    
    // 发送消息
    func sendMessage(_ message: BuzIMMessage) -> AnyPublisher<IMMsgSendResult, Never> {
        let subject = PassthroughSubject<IMMsgSendResult, Never>()
        self.object.sendMessage(message, save: { msg, error in
            subject.send(.save(msg, error))
        }, completion: { msg, error in
            subject.send(.completion(msg, error))
            subject.send(completion: .finished)
        })
        
        return subject.eraseToAnyPublisher()
    }
    
    // 插入本地消息
    func insertLocalMessage(_ message: BuzIMMessage) -> AnyPublisher<(BuzIMMessage?, Error?), Never> {
        return Future { promise in
            self.object.insertLocalMessage(message) { msg, error in
                promise(.success((msg, error)))
            }
        }
        .eraseToAnyPublisher()
    }
}
