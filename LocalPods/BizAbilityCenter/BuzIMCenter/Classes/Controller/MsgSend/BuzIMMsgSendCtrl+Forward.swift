//
//  BuzIMMsgSendCtrl+Forward.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog

public extension BuzIMMsgSendCtrl {
    /// 转发消息
    func forwardMessage(messageId: Int64,
                        toTarget: String,
                        conversationType convType: BuzConversationType,
                        targetConvType: BuzConversationType,
                        pushPayload: String,
                        pushContent: String,
                        userInfo: IM5UserInfo? = nil,
                        traceId: String,
                        save savedBlock: ((BuzIMMessage?, Error?) -> Void)? = nil,
                        completion completeBlock: ((BuzIMMessage?, Error?) -> Void)? = nil) {
        BuzIMLog.info("forwardMessageId: \(messageId), toTarget = \(toTarget), conversationType = \(convType.rawValue), targetConvType = \(targetConvType.rawValue)")
        
        self.center?.im5Client.forwardMessage(messageId,
                                 convType: convType == .peer ? .peer : .group,
                                 toTarget: toTarget,
                                 targetConvType: targetConvType == .peer ? .peer : .group,
                                 msgTraceId: traceId,
                                 pushContent: pushContent,
                                 pushPayload: pushPayload,
                                 userInfo: userInfo,
                                 saved: { [weak self] message, error in
            guard let self = self else { return }
            
            BuzIMLog.info("forwardMessage save.result = \(error == nil), error = \(String(describing: error))")
            
            let bMsg: BuzIMMessage = BuzIMMessage(im5Message: message)
            bMsg.localExtra.updateMsgSendNtpTime()
            bMsg.localExtra.updateisFoward()
            
            savedBlock?(bMsg, error)
            
            self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                observer.imMessageToBeSentSaved?(center: self.center!, message: bMsg)
            }
            self.center?.sendPublisher.imMessageToBeSentSaved.send((BuzIMCenter.center, bMsg))
        }, completion: { [weak self] message, error in
            guard let self = self else { return }
            
            // 自动重发不用返回结果
            if message?.willAutoResend == true && error != nil {
                return
            }
            
            var bMsg: BuzIMMessage?
            if let message = message {
                bMsg = BuzIMMessage(im5Message: message)
                completeBlock?(bMsg, error)
            } else {
                completeBlock?(nil, error)
            }
            
            if let bMsg = bMsg {
                self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                    observer.imMessageSendCompleted?(center: self.center!, message: bMsg, error: error)
                }
                self.center?.sendPublisher.imMessageSendCompleted.send((BuzIMCenter.center, bMsg, error))
            }
            
            BuzIMLog.info("forwardMessage finish error = \(String(describing: error)); message = \(String(describing: message)); sendStatus = \(String(describing: message?.sendStatus))")
        })
    }
}


//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgSendCtrl {
    // - 转发消息
    func forwardMessage(messageId: Int64,
                        toTarget: String,
                        conversationType convType: BuzConversationType,
                        targetConvType: BuzConversationType,
                        pushPayload: String,
                        pushContent: String,
                        userInfo: IM5UserInfo? = nil,
                        traceId: String) -> AnyPublisher<IMMsgSendResult, Never> {
        let subject = PassthroughSubject<IMMsgSendResult, Never>()
        
        self.object.forwardMessage(messageId: messageId,
                                   toTarget: toTarget,
                                   conversationType: convType,
                                   targetConvType: targetConvType,
                                   pushPayload: pushPayload,
                                   pushContent: pushContent,
                                   userInfo: userInfo,
                                   traceId: traceId,
                                   save: { msg, error in
            subject.send(.save(msg, error))
        }, completion: { msg, error in
            subject.send(.completion(msg, error))
            subject.send(completion: .finished)
        })
        
        return subject.eraseToAnyPublisher()
    }
}
