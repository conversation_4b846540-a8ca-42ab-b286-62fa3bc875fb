//
//  BuzIMMsgSendCtrl+Observer.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog

@objc
public enum BuzIMMessageSendState: Int32 {
    case prepareVideo // 视频消息需要重新准备
    case updated      // 发消息已经保存，或者视频消息状态已经更新
    case progress     //（附件消息）发送进度
    case succeed      // 发送成功
    case failed       // 发送失败
}

// MARK: - IM5SendMessageObserver
//发送消息全局状态 , 全局不受重试开关影响 , isResend = false , 说明是第一次发送的回调, isResend = true , 说明是重试发送的回调
extension BuzIMMsgSendCtrl {
    /// 发送消息全局状态，全局不受重试开关影响
    /// isResend = false，说明是第一次发送的回调
    /// isResend = true，说明是重试发送的回调
    
    /// 只针对视频消息需要重新准备 (只有重试的时候会来,并且是init状态)
    public func im5SendMessagePrepareVideo(_ videoMessage: IM5Message, isResend: Bool) {
        let msg = BuzIMMessage(im5Message: videoMessage)
        sendMessageChanged(msg, sendState: .prepareVideo, isResend: isResend, error: nil)
    }
    
    /// 发消息已经保存，或者视频消息状态已经更新
    public func im5SendMessageUpdated(_ message: IM5Message, isResend: Bool) {
        let msg = BuzIMMessage(im5Message: message)
        sendMessageChanged(msg, sendState: .updated, isResend: isResend, error: nil)
    }
    
    /// （附件消息）视频、图片、语音发送进度 (如果是发送同一种图片, 会很快跳过进度到complete状态)
    public func im5SendMessageProgress(_ attachmentMessage: IM5Message, completionBytes: Int64, totalBytes: Int64) {
        var progress = Int(100.0 * (Double(completionBytes) / Double(totalBytes)))
        if completionBytes == totalBytes {
            // 修正浮点偏差
            progress = 100
        }
        
        var fileName = ""
        var remoteURL = ""
        if let msgContent = attachmentMessage.messageContent as? IM5AttachmentMessageContent {
            fileName = msgContent.localPath?.lastPathComponent ?? ""
            remoteURL = msgContent.remoteURL ?? ""
        }
        
        let bMsg = BuzIMMessage(im5Message: attachmentMessage)
        
        self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
            observer.imAttachmentProgressUpdated?(center: self.center!, message: bMsg, progress: Float(progress))
        }
        self.center?.sendPublisher.imAttachmentProgressUpdated.send((BuzIMCenter.center, bMsg, Float(progress)))
        
        if let content = attachmentMessage.messageContent as? IM5VideoMessageContent {
            BuzIMLog.info("""
                BuzLog_MediaHandling progress:(\(completionBytes),\(totalBytes)) -> \(progress)% \
                fileName: \(fileName), remoteURL: \(remoteURL), content= \(content.uploadState), \
                sendStatus = \(attachmentMessage.sendStatus), msgId = \(attachmentMessage.msgId)
                """)
        }
        
        // sendMessageChanged(attachmentMessage)
    }
    
    /// 发送成功
    public func im5SendMessageSuccess(_ message: IM5Message, isResend: Bool) {
        BuzIMLog.info("BuzLog_ResendMessage: 全局成功回调 \(message.sendStatus)")
        
        let msg = BuzIMMessage(im5Message: message)
        sendMessageChanged(msg, sendState: .succeed, isResend: isResend, error: nil)
        
        self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
            observer.imMessageSendCompleted?(center: self.center!, message: msg, error: nil)
        }
        self.center?.sendPublisher.imMessageSendCompleted.send((BuzIMCenter.center, msg, nil))
    }
    
    /// 第一次发生失败的时候来到这里、手动压缩暂停、手动暂停上传、手动暂停取消发送，服务端正常失败，都会以失败来到这个回调
    public func im5SendMessageFailed(_ message: IM5Message, error: Error, isResend: Bool) {
        BuzIMLog.info("BuzLog_ResendMessage: 全局失败回调 \(message.sendStatus)")
        
        let msg = BuzIMMessage(im5Message: message)
        sendMessageChanged(msg, sendState: .failed, isResend: isResend, error: error)
        
        // 过滤自动重试的失败
        guard !message.willAutoResend else { return }
        
        BuzIMLog.info("BuzLog_ResendMessage: 手动压缩暂停、手动暂停上传、手动暂停取消发送, 服务端正常失败, 都会以失败来到这个回调sendStatus:\(message.sendStatus)")
        self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
            observer.imMessageSendCompleted?(center: self.center!, message: msg, error: error)
        }
        self.center?.sendPublisher.imMessageSendCompleted.send((BuzIMCenter.center, msg, error))
    }
}

extension BuzIMMsgSendCtrl {
    
    /// 发送消息状态变更
    private func sendMessageChanged(_ message: BuzIMMessage, sendState: BuzIMMessageSendState, isResend: Bool, error: Error?) {
        self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
            observer.imMessageSendStateChanged?(center: self.center!, message: message, sendState: sendState, isResend: isResend, error: error)
        }
        self.center?.sendPublisher.imMessageSendStateChanged.send((BuzIMCenter.center, message, sendState, isResend, error))
    }
}
