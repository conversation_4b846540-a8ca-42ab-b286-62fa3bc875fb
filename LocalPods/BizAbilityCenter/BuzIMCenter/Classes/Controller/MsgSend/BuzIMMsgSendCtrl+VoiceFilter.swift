//
//  BuzIMMsgSendCtrl+VoiceFilter.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog

@objc
public enum BuzVoiceFilterPreprocessErrorType: Int {
    case waitTimeout = 50001
    case userCancel = 50002
}

// MARK: - Send VoiceFilter Message
public extension BuzIMMsgSendCtrl {
    /// 预览语音滤镜消息
    @discardableResult
    func previewVoiceFilterMessage(_ message: BuzIMMessage, completionBlock: ((BuzIMMessage?, Error?) -> Void)? = nil) -> String {
        sendMessagePrepare(message)
        
        if let content = message.im5Message.messageContent as? IM5AttachmentMessageContent {
            content.enableEmbeddedMessage = true
        }
        
        let toTarget = message.targetUid
        let previewId = self.center?.im5Client.previewVoiceFilterMessage(message.im5Message,
                                                                         toTarget: toTarget,
                                                                         progress: { _, _, _ in
            // Progress handling
        }, processing: { _ in
            // Processing handling
        }, completion: { message, error in
            if let message = message {
                completionBlock?(BuzIMMessage(im5Message: message), error)
            } else {
                completionBlock?(nil, error)
            }
            BuzIMLog.info("VoiceFilter.previewVoiceFilterMessage.completion msgId = \(message?.msgId ?? 0) toTarget = \(toTarget) error = \(String(describing: error))")
        })
        
        BuzIMLog.info("VoiceFilter.previewVoiceFilterMessage msgId = \(message.im5Message.msgId) toTarget = \(toTarget), previewId = \(previewId)")
        return previewId ?? ""
    }
    
    /// 取消预览语音滤镜
    func cancelPreviewVoiceFilter(_ previewId: String) {
        BuzIMLog.info("VoiceFilter.cancelPreviewVoiceFilter previewId = \(previewId)")
        self.center?.im5Client.cancelPreviewVoiceFilter(previewId)
    }
    
    /// 发送预览的语音滤镜
    func sendPreviewedVoiceFilter(_ previewId: String,
                                  savedBlock: ((BuzIMMessage?, Error?) -> Void)? = nil,
                                  completionBlock: ((BuzIMMessage?, Error?) -> Void)? = nil) {
        BuzIMLog.info("VoiceFilter.sendPreviewedVoiceFilter previewId = \(previewId)")
        
        self.center?.im5Client.sendPreviewedVoiceFilter(previewId, saved: { [weak self] message, error in
            guard let self = self else { return }
            
            var bMsg: BuzIMMessage?
            if error == nil {
                bMsg = BuzIMMessage(im5Message: message)
            }
            
            savedBlock?(bMsg, error)
            
            if let bMsg = bMsg {
                self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                    observer.imMessageToBeSentSaved?(center: self.center!, message: bMsg)
                }
                self.center?.sendPublisher.imMessageToBeSentSaved.send((BuzIMCenter.center, bMsg))
            }
            
            BuzIMLog.info("VoiceFilter.sendPreviewedVoiceFilter.saved msgId = \(message.msgId) previewId = \(previewId) error = \(String(describing: error))")
        }, progress: { _, _, _ in
            // Progress handling
        }, processing: { _ in
            // Processing handling
        }, completion: { [weak self] message, error in
            guard let self = self else { return }
            
            var bMsg: BuzIMMessage?
            if error == nil, let message = message {
                bMsg = BuzIMMessage(im5Message: message)
            }
            
            completionBlock?(bMsg, error)
            
            if let bMsg = bMsg {
                self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                    observer.imMessageSendCompleted?(center: self.center!, message: bMsg, error: error)
                }
                self.center?.sendPublisher.imMessageSendCompleted.send((BuzIMCenter.center, bMsg, error))
            }
            
            BuzIMLog.info("VoiceFilter.sendPreviewedVoiceFilter.completion msgId = \(message?.msgId ?? 0) previewId = \(previewId) error = \(String(describing: error))")
        })
    }
    
    /// 发送预处理消息
    func sendPreprocessMessage(_ message: BuzIMMessage,
                               savedBlock: ((BuzIMMessage?, Error?) -> Void)? = nil,
                               completionBlock: ((BuzIMMessage?, Error?) -> Void)? = nil) {
        let toTarget = message.targetUid
        sendMessagePrepare(message)
        
        if let content = message.im5Message.messageContent as? IM5AttachmentMessageContent {
            content.enableEmbeddedMessage = true
        }
        
        BuzIMLog.info("VoiceFilter.sendPreprocessMessage msgId = \(message.im5Message.msgId) toTarget = \(toTarget)")
        
        self.center?.im5Client.sendPreprocessMessage(message.im5Message,
                                                     toTarget: toTarget,
                                                     saved: { [weak self] message, error in
            guard let self = self else { return }
            
            var bMsg: BuzIMMessage?
            if error == nil {
                bMsg = BuzIMMessage(im5Message: message)
            }
            
            savedBlock?(bMsg, error)
            
            if let bMsg = bMsg {
                self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                    observer.imMessageToBeSentSaved?(center: self.center!, message: bMsg)
                }
                self.center?.sendPublisher.imMessageToBeSentSaved.send((BuzIMCenter.center, bMsg))
            }
            
            BuzIMLog.info("VoiceFilter.sendVoiceFilterMessage.saved msgId = \(message.msgId) toTarget = \(toTarget) error = \(String(describing: error))")
        }, progress: { _, _, _ in
            // Progress handling
        }, processing: { _ in
            // Processing handling
        }, completion: { [weak self] message, error in
            guard let self = self else { return }
            
            var bMsg: BuzIMMessage?
            if error == nil, let message = message {
                bMsg = BuzIMMessage(im5Message: message)
            }
            
            completionBlock?(bMsg, error)
            
            if let bMsg = bMsg {
                self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                    observer.imMessageSendCompleted?(center: self.center!, message: bMsg, error: error)
                }
                self.center?.sendPublisher.imMessageSendCompleted.send((BuzIMCenter.center, bMsg, error))
            }
            
            BuzIMLog.info("VoiceFilter.sendVoiceFilterMessage.completion msgId = \(message?.msgId ?? 0) toTarget = \(toTarget) error = \(String(describing: error))")
        })
    }
    
    /// 发送语音滤镜消息
    func sendVoiceFilterMessage(_ message: BuzIMMessage,
                                savedBlock: ((BuzIMMessage?, Error?) -> Void)? = nil,
                                completionBlock: ((BuzIMMessage?, Error?) -> Void)? = nil) {
        let toTarget = message.targetUid
        sendMessagePrepare(message)
        
        if let content = message.im5Message.messageContent as? IM5AttachmentMessageContent {
            content.enableEmbeddedMessage = true
        }
        
        BuzIMLog.info("VoiceFilter.sendVoiceFilterMessage msgId = \(message.im5Message.msgId) toTarget = \(toTarget)")
        
        self.center?.im5Client.sendVoiceFilterMessage(message.im5Message,
                                                      toTarget: toTarget,
                                                      saved: { [weak self] message, error in
            guard let self = self else { return }
            
            var bMsg: BuzIMMessage?
            if error == nil {
                bMsg = BuzIMMessage(im5Message: message)
            }
            
            savedBlock?(bMsg, error)
            
            if let bMsg = bMsg {
                self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                    observer.imMessageToBeSentSaved?(center: self.center!, message: bMsg)
                }
                self.center?.sendPublisher.imMessageToBeSentSaved.send((BuzIMCenter.center, bMsg))
            }
            
            BuzIMLog.info("VoiceFilter.sendVoiceFilterMessage.saved msgId = \(message.msgId) toTarget = \(toTarget) error = \(String(describing: error))")
        }, progress: { _, _, _ in
            // Progress handling
        }, processing: { _ in
            // Processing handling
        }, completion: { [weak self] message, error in
            guard let self = self else { return }
            
            var bMsg: BuzIMMessage?
            if error == nil, let message = message {
                bMsg = BuzIMMessage(im5Message: message)
            }
            
            completionBlock?(bMsg, error)
            
            if let bMsg = bMsg {
                self.center?.notifyObservers(of: BuzIMCenterSendObserver.self) { observer in
                    observer.imMessageSendCompleted?(center: self.center!, message: bMsg, error: error)
                }
                self.center?.sendPublisher.imMessageSendCompleted.send((BuzIMCenter.center, bMsg, error))
            }
            
            BuzIMLog.info("VoiceFilter.sendVoiceFilterMessage.completion msgId = \(message?.msgId ?? 0) toTarget = \(toTarget) error = \(String(describing: error))")
        })
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgSendCtrl {
    /// 预览语音滤镜消息
    func previewVoiceFilterMessage(_ message: BuzIMMessage) -> AnyPublisher<(String?, BuzIMMessage?, Error?), Never> {
        let subject = PassthroughSubject<(String?, BuzIMMessage?, Error?), Never>()
        var previewId = ""
        // 调用原始API，获取 previewId（同步）
        previewId = self.object.previewVoiceFilterMessage(message) { msg, error in
            if let error = error {
                subject.send((nil, nil, error))
            } else {
                subject.send((previewId, msg, nil))
            }
            subject.send(completion: .finished)
        }
        subject.send((previewId, nil, nil))
        
        return subject.eraseToAnyPublisher()
    }
    
    /// 取消预览语音滤镜
    func cancelPreviewVoiceFilter(_ previewId: String) -> AnyPublisher<Void, Never> {
        Just(self.object.cancelPreviewVoiceFilter(previewId))
            .eraseToAnyPublisher()
    }
    
    /// 发送预览的语音滤镜
    func sendPreviewedVoiceFilter(_ previewId: String) -> AnyPublisher<IMMsgSendResult, Never> {
        let subject = PassthroughSubject<IMMsgSendResult, Never>()
        
        self.object.sendPreviewedVoiceFilter(previewId, savedBlock: { msg, error in
            subject.send(.save(msg, error))
        }, completionBlock: { msg, error in
            subject.send(.completion(msg, error))
            subject.send(completion: .finished)
        })
        
        return subject.eraseToAnyPublisher()
    }
    
    /// 发送预处理消息
    func sendPreprocessMessage(_ message: BuzIMMessage) -> AnyPublisher<IMMsgSendResult, Never> {
        let subject = PassthroughSubject<IMMsgSendResult, Never>()
        
        self.object.sendPreprocessMessage(message, savedBlock: { msg, error in
            subject.send(.save(msg, error))
        }, completionBlock: { msg, error in
            subject.send(.completion(msg, error))
            subject.send(completion: .finished)
        })
        
        return subject.eraseToAnyPublisher()
    }
    
    /// 发送语音滤镜消息
    func sendVoiceFilterMessage(_ message: BuzIMMessage) -> AnyPublisher<IMMsgSendResult, Never> {
        let subject = PassthroughSubject<IMMsgSendResult, Never>()
        
        self.object.sendVoiceFilterMessage(message, savedBlock: { msg, error in
            subject.send(.save(msg, error))
        }, completionBlock: { msg, error in
            subject.send(.completion(msg, error))
            subject.send(completion: .finished)
        })
        
        return subject.eraseToAnyPublisher()
    }
}
