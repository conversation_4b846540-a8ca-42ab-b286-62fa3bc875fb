//
//  BuzIMStartupCtrl.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/12.
//

import VoderXClient

import BuzAppInfo
import BuzAppConfiger
import BuzDataStore
import BuzLog

class BuzIMStartupCtrl: NSObject, BuzIMControllerable {
    typealias IMCenter = BuzIMCenter
    weak var center: BuzIMCenter?
    
    // 客户端版本
    private var _clientVersion: UInt32 = 0
    private var clientVersion: UInt32 {
        return self.getClientVersion()
    }
    
    required init(center: BuzIMCenter?) {
        self.center = center
    }
}

extension BuzIMStartupCtrl {
    
    func start() {
        // 注册消息内容类
        self.registerMessageContentClass()
        // 缓冲读取
        BuzAppConfiger.shared.fetchAppGlobalConfigWithoutLogin(isForceRefresh: false) { [weak self] info in
            guard let self = self else { return }
            
            let config = IM5Config()
            let appKey = VoderXInfo.appKey
            config.appKey = appKey
            
            if VoderXInfo.isProductionEnv() {
                config.serverEnvironment = .eastUS
            } else {
                config.serverEnvironment = .buzDev
            }
            
#if DEBUG
            config.showConsoleLog = true
            config.logLevel = .debug
#else
            config.showConsoleLog = false
            config.logLevel = .info
#endif
            
            config.writeLog = true
            config.clientVersion = self.clientVersion
            config.disableGroupService = false
            config.appHost = VoderXInfo.appHost
            
            // 调大一点上传超时时间
            config.uploadTimeout = 60 * 10 // 10分钟
            config.videoUploadTimeout = 60 * 10 // 10分钟
            config.enableEmbeddedMessage = true
            config.enableReportSampling = true
            
            // 这个打开就是开启自动发送功能
            if let info = info {
                config.enableAutoResend = info.enableAutoResend
                config.resendPeriod = info.resendPeriod.int64Value
            } else {
                config.enableAutoResend = true
            }
            
            config.historyContainsRecalledMessage = true
            
            self.center?.im5Client.start(with: config)
        }
    }
}

extension BuzIMStartupCtrl {
    
    func registerMessageContentClass() {
        self.center?.im5Client.registerMessageType(BuzWalkieTalkieCountMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzCommandMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzWalkieTalkieMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzTextMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzImageMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzFileMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzVoiceTextMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzVoicemojiMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzNewVoiceTextMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzVideoMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzLocalTipMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzLocationMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzMediaTextMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzMediaActionTextMessageContent.self)
        
        self.center?.im5Client.registerMessageType(BuzStrickerGiftMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzSoundboardMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzOnAirCommentMessageContent.self)
        
        self.center?.im5Client.registerMessageType(BuzCompatibleVoicemojiMesasgeContent.self)
        self.center?.im5Client.registerMessageType(BuzLivePlaceShareCardMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzVoicegifMessageContent.self)
        self.center?.im5Client.registerMessageType(BuzCallCardMessageContent.self)
    }
    
}

extension BuzIMStartupCtrl {
    
    private func getClientVersion() -> UInt32 {
        if _clientVersion == 0 {
            // 计算版本号，版本号每一个节取两位。
            if let versionStr = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String {
                let versionParts = versionStr.components(separatedBy: ".")
                var clientVersion: UInt32 = 0
                let shift: UInt32 = 100
                
                for part in versionParts {
                    assert(part.count <= 2, "版本号每个节最大数为99")
                    if let partValue = UInt32(part) {
                        clientVersion = clientVersion * shift + partValue
                    }
                }
                _clientVersion = clientVersion
            }
        }
        return _clientVersion
    }
}
