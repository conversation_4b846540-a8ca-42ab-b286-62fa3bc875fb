//
//  BuzIMMsgReadedCtrl.swift
//  buz
//
//  Created by liuyufeng on 2022/7/19.
//  Copyright © 2022 lizhi. All rights reserved.
//
import VoderXClient

import BuzCenterKit
import BuzLog
import BuzDataStore
import BuzUserSession

//MARK: -ReadedIMMessage
public struct ReadedIMMessage : Codable {
    // 注意：群聊是 serverMsgId，私聊是 localMsgId
    public var msgId : Int64
    public var createTime : Int64
    
    init(msgId: Int64 = 0 ,  createTime: Int64 = 0) {
        self.msgId = msgId
        self.createTime = createTime
    }
}

//MARK: -BuzIMMsgReadedCtrl
///标记 + 读取 已读消息记录的工具
public class BuzIMMsgReadedCtrl: NSObject, BuzIMControllerable {
    typealias IMCenter = BuzIMCenter
    weak var center: BuzIMCenter?
    
    ///[targetId : (msgId : createTime)]
    private var readedMessageMemoryCache : [String : ReadedIMMessage] = [:]
    
    required init(center: BuzIMCenter?) {
        self.center = center
    }
}

extension BuzIMMsgReadedCtrl {
    
    private func lastReadedMessageKey(targetId : String , conversationType : BuzConversationType) -> String {
        let myselfUid = BuzUserSession.shared.uid
        return "\(myselfUid)" + "_" + targetId + "_" + "\(conversationType.rawValue)_lastReadedMessage"
    }
}

public extension BuzIMMsgReadedCtrl {
    ///标识某个用户的某条消息为已读状态
    @discardableResult
    func storeNewestReaded(msgId : Int64 ,  createTime : Int64 , targetId : String , conversationType : BuzConversationType) -> Bool {
        let key = self.lastReadedMessageKey(targetId: targetId , conversationType: conversationType)
        
        var currentReadMessage : ReadedIMMessage = ReadedIMMessage.init()
        
        //获取当前本地已读消息
        if let currentReadMsg = self.readedMessageMemoryCache[key] {
            currentReadMessage = currentReadMsg
        }else{
            let jsonData = MMKV.buz.data(forKey: key)
            if let jsonData = jsonData , let cMessage = ReadedIMMessage.init(from: jsonData) {
                currentReadMessage = cMessage
                self.readedMessageMemoryCache[key] = cMessage
            }
        }
        
        //时间戳比本地记录大才需要更新本地的未读消息
        guard createTime > currentReadMessage.createTime else {
            BuzIMLog.info("Store readed msgId failure , because have newer msgId \(currentReadMessage.msgId) , createTime = \(currentReadMessage.createTime)")
            return false
        }
        let newestMsg = ReadedIMMessage.init(msgId: msgId, createTime: createTime)
        self.readedMessageMemoryCache[key] = newestMsg
        
        var isSuccess = false
        
        do {
            let jsonData = try JSONEncoder().encode(newestMsg)
            isSuccess = MMKV.buz.set(jsonData, forKey: key)
            BuzIMLog.info("Store readed msgId result , msgId \(newestMsg.msgId) , createTime = \(newestMsg.createTime)")
            
        }catch{
            BuzIMLog.info("Encode ReadedIMMessage error \(error)")
        }
        return isSuccess
    }
    
    func removeReadedRecord(targetId : String , conversationType : BuzConversationType) {
        let key = self.lastReadedMessageKey(targetId: targetId , conversationType: conversationType)
        self.readedMessageMemoryCache.removeValue(forKey: key)
        MMKV.buz.removeValue(forKey: key)
    }
    
    //获取该用户上一次已读的消息的时间戳
    func lastReadedMsg(targetId : String , conversationType : BuzConversationType) -> ReadedIMMessage {
        let key = self.lastReadedMessageKey(targetId: targetId , conversationType:conversationType)
        var currentMsg : ReadedIMMessage?
        if let cMsg = self.readedMessageMemoryCache[key] {
            currentMsg = cMsg
        }else{
            if let jsonData : Data = MMKV.buz.data(forKey: key) {
                currentMsg = ReadedIMMessage.init(from: jsonData)
            }
        }
        
        guard let readedMsg = currentMsg else{
            return ReadedIMMessage.init(msgId: 0, createTime: 0)
        }
        
        return readedMsg
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgReadedCtrl {

    func storeNewestReaded(msgId: Int64, createTime: Int64, targetId: String, conversationType: BuzConversationType) -> AnyPublisher<Bool, Never> {
        return Just(self.object.storeNewestReaded(msgId: msgId, createTime: createTime, targetId: targetId, conversationType: conversationType))
            .eraseToAnyPublisher()
    }

    func removeReadedRecord(targetId: String, conversationType: BuzConversationType) -> AnyPublisher<Void, Never> {
        return Just(self.object.removeReadedRecord(targetId: targetId, conversationType: conversationType))
            .eraseToAnyPublisher()
    }

    func lastReadedMsg(targetId: String, conversationType: BuzConversationType) -> AnyPublisher<ReadedIMMessage, Never> {
        return Just(self.object.lastReadedMsg(targetId: targetId, conversationType: conversationType))
            .eraseToAnyPublisher()
    }
}
