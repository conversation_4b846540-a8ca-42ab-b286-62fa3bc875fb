//
//  BuzIMMsgQueryCtrl.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/12.
//

import VoderXClient

import BuzCenterKit
import BuzLog

// MARK: - Typealias
public typealias BuzQueryMessageListBlock = ([BuzIMMessage]?, Bool) -> Void
public typealias BuzQueryMessageListAndErrorBlock = ([BuzIMMessage]?, Error?) -> Void

public class BuzIMMsgQueryCtrl: NSObject, BuzIMControllerable {
    
    static let kPageSize = 50
    static let kGroupChatHistoryMessageMaxCount = 30
    
    typealias IMCenter = BuzIMCenter
    weak var center: BuzIMCenter?
    
    // 进入私聊/群聊拉取消息期间接收到的消息回调结果
    var messageNotifyTempDict: [String: [BuzUniqueMessagesHolder]] = [:]
    
    required init(center: BuzIMCenter?) {
        self.center = center
    }
}

extension BuzIMMsgQueryCtrl {
    func logout() {
        self.messageNotifyTempDict.removeAll()
    }
    
    // MARK: - Helper Functions
    func loadingHistoryReceivedMsgContainerKey(_ convType: BuzConversationType, _ targetId: String) -> String {
        let suffixStr = convType == .peer ? "privateChat" : "groupChat"
        return "\(targetId)_\(suffixStr)"
    }
}

extension BuzIMMsgQueryCtrl {
    func traceIfNewMessage(_ message: IM5GroupHistoryItem, buzMessage: BuzIMMessage) {
        guard message.isNewMessage() else { return }
        
        let messageTypes: [BuzIMContentType] = [
            .text,
            .image,
            .count_WalkieTalkie,
            .voice_Emoji,
            .compatibleVoiceEmoji,
            .voicegif,
            .video
        ]
        
        if messageTypes.contains(buzMessage.messageType) {
            self.center?.provider?.trackReceivedMsgResult(message: buzMessage)
        }
    }
}

public extension BuzIMMsgQueryCtrl {
    // MARK: - Query Methods
    func queryMessage(_ msgId: Int64, convType: BuzConversationType, result handler: @escaping (BuzIMMessage) -> Void) {
        
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            self.center?.im5Client.queryMessage(msgId, convType: IM5ConversationType(rawValue: convType.rawValue) ?? .peer) { msg in
                handler(BuzIMMessage(im5Message: msg))
            }
        }
    }
    
    func queryMessage(serverMsgId: Int64, conversationType convType: BuzConversationType, result handler: @escaping (BuzIMMessage?) -> Void) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            self.center?.im5Client.queryMessage(bySvrMsgId: serverMsgId, convType: IM5ConversationType(rawValue: convType.rawValue) ?? .peer) { msg in
                guard let im5Message = msg else { return }
                let bMsg = BuzIMMessage(im5Message: im5Message)
                handler(bMsg)
                BuzIMLog.info("result msg = \(String(describing: msg))")
            }
        }
    }
}

public extension BuzIMMsgQueryCtrl {
    /// 查询上一次的已读消息
    func queryLastReadMessage(targetId: String, conversationType: BuzConversationType, completion: @escaping (BuzIMMessage?, Error?) -> Void) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            
            BuzIMLog.info("begin queryLastReadMessageOfTarget targetId = \(targetId), conversationType = \(conversationType)")
            
            self.center?.im5Client.queryLastReadMessage(ofTarget: targetId,
                                                        conversationType: IM5ConversationType(rawValue: conversationType.rawValue) ?? .peer) { message, error in
                let msg = message.map { BuzIMMessage(im5Message: $0) }
                completion(msg, error)
                
                BuzIMLog.info("finish queryLastReadMessageOfTarget targetId = \(targetId), conversationType = \(conversationType), msg = \(String(describing: message)), error = \(String(describing: error))")
            }
        }
    }
    
    /// 获取范围历史
    func getRangeHistory(targetId: String,
                         conversationType: BuzConversationType,
                         boundary svrMsgId: Int64,
                         before beforeCount: Int,
                         after afterCount: Int,
                         success: @escaping ([BuzIMMessage]) -> Void,
                         failed: @escaping (Error?) -> Void) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            
            BuzIMLog.info("begin getRangeHistoryOfTarget target = \(targetId), conversationType = \(conversationType), svrMsgId = \(svrMsgId), beforeCount = \(beforeCount), afterCount = \(afterCount)")
            
            self.center?.im5Client.getRangeHistory(ofTarget: targetId,
                                                   conversationType: IM5ConversationType(rawValue: conversationType.rawValue) ?? .peer,
                                                   boundary: svrMsgId,
                                                   before: beforeCount,
                                                   after: afterCount,
                                                   success: { msgList in
                let messagesArray = msgList?.compactMap { msg -> BuzIMMessage? in
                    let bMessage = BuzIMMessage(im5Message: msg)
                    return self.canPassMessage(message: bMessage, conversationType: conversationType) ? bMessage : nil
                } ?? .init()
                success(messagesArray)
            }, failed: { error in
                BuzIMLog.error("❌ getRangeHistoryOfTarget fail: error = \(String(describing: error)); target = \(targetId), conversationType = \(conversationType), svrMsgId = \(svrMsgId), beforeCount = \(beforeCount), afterCount = \(afterCount)")
                failed(error)
            })
        }
    }
}

public extension BuzIMMsgQueryCtrl {
    
    /// 获取本地媒体消息历史
    /// get media message from voderx, currently 100 before bundlemsgid, 100 after bundlemsgid
    func getChatLocalMedias(targetId: String,
                            conversationType type: BuzConversationType,
                            boundaryMsgId: Int64,
                            pageSize: Int,
                            completion: @escaping ([BuzIMMessage]) -> Void) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            
            let results = NSMutableArray()
            
            let handleMsg: ([IM5Message]?) -> Void = { msgs in
                msgs?.forEach { msg in
                    let bMessage = BuzIMMessage(im5Message: msg)
                    results.add(bMessage)
                }
                completion(results as? [BuzIMMessage] ?? [])
            }
            
            let mediaTypes: [NSNumber] = [
                NSNumber(value: BuzIMContentType.image.rawValue),
                NSNumber(value: BuzIMContentType.video.rawValue),
                NSNumber(value: BuzIMContentType.mediaText.rawValue),
                NSNumber(value: BuzIMContentType.mediaActionText.rawValue)
            ]
            
            guard let type = IM5ConversationType.init(rawValue: type.rawValue) else { return }
            self.center?.im5Client.getLocalHistory(ofTargetId: targetId,
                                                   conversationType: type,
                                                   boundary: boundaryMsgId,
                                                   msgTypes: mediaTypes,
                                                   before: true,
                                                   limit: pageSize) { [weak self] beforeMsgList, haveMore in
                guard let self = self else { return }
                
                self.center?.im5Client.getLocalHistory(ofTargetId: targetId,
                                                       conversationType: type,
                                                       boundary: beforeMsgList?.last?.msgId ?? 0,
                                                       msgTypes: mediaTypes,
                                                       before: false,
                                                       limit: pageSize) { afterMsgList, haveMore in
                    let combinedList = (beforeMsgList ?? []) + (afterMsgList ?? [])
                    handleMsg(combinedList)
                }
            }
        }
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgQueryCtrl {
    // 查询单条消息
    func queryMessage(_ msgId: Int64, convType: BuzConversationType) -> AnyPublisher<BuzIMMessage, Never> {
        return Future { promise in
            self.object.queryMessage(msgId, convType: convType, result: { message in
                promise(.success(message))
            })
        }
        .eraseToAnyPublisher()
    }
    
    // 查询服务器消息ID
    func queryMessage(serverMsgId: Int64, convType: BuzConversationType) -> AnyPublisher<BuzIMMessage?, Never> {
        return Future { promise in
            self.object.queryMessage(serverMsgId: serverMsgId, conversationType: convType, result: { message in
                promise(.success(message))
            })
        }
        .eraseToAnyPublisher()
    }
    
    // 查询上一次已读消息
    func queryLastReadMessage(targetId: String, conversationType: BuzConversationType) -> AnyPublisher<(BuzIMMessage?, Error?), Never> {
        return Future { promise in
            self.object.queryLastReadMessage(targetId: targetId, conversationType: conversationType, completion: { message, error in
                promise(.success((message, error)))
            })
        }
        .eraseToAnyPublisher()
    }
    
    // 获取范围历史
    func getRangeHistory(targetId: String,
                         conversationType: BuzConversationType,
                         boundary svrMsgId: Int64,
                         before beforeCount: Int,
                         after afterCount: Int) -> AnyPublisher<([BuzIMMessage]?, Error?), Never> {
        return Future { promise in
            self.object.getRangeHistory(targetId: targetId, conversationType: conversationType, boundary: svrMsgId, before: beforeCount, after: afterCount, success: { messages in
                promise(.success((messages, nil)))
            }, failed: { error in
                promise(.success((nil, error)))
            })
        }
        .eraseToAnyPublisher()
    }
    
    // 获取本地媒体消息历史
    func getChatLocalMedias(targetId: String,
                            conversationType type: BuzConversationType,
                            boundaryMsgId: Int64,
                            pageSize: Int) -> AnyPublisher<[BuzIMMessage], Never> {
        return Future { promise in
            self.object.getChatLocalMedias(targetId: targetId, conversationType: type, boundaryMsgId: boundaryMsgId, pageSize: pageSize, completion: { messages in
                promise(.success(messages))
            })
        }
        .eraseToAnyPublisher()
    }
}
