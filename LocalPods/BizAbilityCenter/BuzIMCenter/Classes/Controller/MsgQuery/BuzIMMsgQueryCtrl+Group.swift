//
//  BuzIMMsgQueryCtrl.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/12.
//

import VoderXClient

import BuzCenterKit
import BuzLog

// MARK: - Group Chat History
public extension BuzIMMsgQueryCtrl {
    /// 群聊 获取历史消息
    /// - Parameters:
    ///   - targetId: 目标ID
    ///   - boundaryMsgId: 边界消息ID
    ///   - isBefore: 是否在边界之前
    ///   - pageSize: 页面大小
    ///   - localDataCompletion: 本地数据完成回调
    ///   - remoteDataCompletion: 远程数据完成回调
    func groupHistoryMessages(targetId: String,
                              boundaryMsgId: Int64,
                              isBefore: Bool,
                              pageSize: Int,
                              localDataCompletion: @escaping ([BuzIMMessage]) -> Void,
                              remoteDataCompletion: @escaping ([BuzIMMessage], Bool, Bool, Error?) -> Void) {
        
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            
            // 记录获取消息期间收到的消息回调
            let uuidString = UUID().uuidString
            let msgHolder = BuzUniqueMessagesHolder(uuidString: uuidString)
            let containerKey = self.loadingHistoryReceivedMsgContainerKey(.group, targetId)
            
            // 添加到容器中持有holder
            if var holderArray = self.messageNotifyTempDict[containerKey] {
                holderArray.append(msgHolder)
                self.messageNotifyTempDict[containerKey] = holderArray
            } else {
                self.messageNotifyTempDict[containerKey] = [msgHolder]
            }
            
            BuzIMLog.info("begin get group chat msg history targetId = \(targetId), boundaryMsgId = \(boundaryMsgId), isBefore = \(isBefore), pageSize = \(pageSize)")
            
            self.center?.im5Client.getGroupHistory(ofTarget: targetId,
                                                   boundary: boundaryMsgId,
                                                   before: isBefore,
                                                   limit: pageSize) { msgList in
                // 处理本地数据
                let messagesArray = msgList?.compactMap { msg -> BuzIMMessage? in
                    let bMessage = BuzIMMessage(im5Message: msg)
                    return self.canPassMessage(message: bMessage, conversationType: bMessage.conversationType) ? bMessage : nil
                } ?? .init()
                
                let firstMsgId = messagesArray.first?.im5Message.msgId ?? -1
                let lastMsgId = messagesArray.last?.im5Message.msgId ?? -2
                
                BuzIMLog.info("1.✅finish load group chat local msg history targetId = \(targetId), boundaryMsgId = \(boundaryMsgId), isBefore = \(isBefore), pageSize = \(pageSize), count = \(messagesArray.count), firstMsgId = \(firstMsgId), lastMsgId = \(lastMsgId)")
                
                localDataCompletion(messagesArray)
                
            } serverResult: { [weak self] msgList, error in
                guard let self = self else { return }
                
                let haveMore = (msgList?.count ?? 0) >= pageSize
                var messagesArray: [BuzIMMessage] = []
                var batchNotifyMsgs: [IM5NotifyMessageItem] = []
                var onlineMessages = Set<NSNumber>()
                
                // 处理服务器返回的消息
                if let msgList = msgList {
                    for item in msgList {
                        let obj = item.message
                        if item.isOnlineMessage() {
                            onlineMessages.insert(NSNumber(value: obj.serverMsgId))
                        }
                        
                        let bMessage = BuzIMMessage(im5Message: obj)
                        if item.isNewMessage() {
                            bMessage.isNewGroupMsg = true
                        }
                        
                        if self.canPassMessage(message: bMessage, conversationType: bMessage.conversationType) {
                            messagesArray.append(bMessage)
                        }
                        
                        self.traceIfNewMessage(item, buzMessage: bMessage)
                    }
                }
                
                let firstMsgId = messagesArray.first?.im5Message.msgId ?? -1
                let lastMsgId = messagesArray.last?.im5Message.msgId ?? -2
                
                BuzIMLog.info("2.✅finish load group chat remote msg history targetId = \(targetId), boundaryMsgId = \(boundaryMsgId), isBefore = \(isBefore), pageSize = \(pageSize), count = \(messagesArray.count), firstMsgId = \(firstMsgId), lastMsgId = \(lastMsgId)")
                
                var isNeedInsertRTMessage = false
                
                // 处理期间接收到的消息回调数据
                if var array = self.messageNotifyTempDict[containerKey],
                   let targetHolder = array.first(where: { $0.uuidString == uuidString }) {
                    
                    if (isBefore && boundaryMsgId == 0) || (!isBefore && !haveMore) {
                        // 同一次历史记录获取的逻辑，需要对消息进行去重 + 排序
                        let notifyMessageArray = targetHolder.msgArray
                        var historyMsgSet = Set(messagesArray)
                        historyMsgSet.formUnion(notifyMessageArray)
                        
                        // 排序
                        messagesArray = Array(historyMsgSet).sorted { $0.im5Message.compare($1.im5Message) == .orderedAscending }
                        isNeedInsertRTMessage = true
                    }
                    
                    // 清理临时存储
                    array.removeAll { $0.uuidString == uuidString }
                    if array.isEmpty {
                        self.messageNotifyTempDict.removeValue(forKey: containerKey)
                    } else {
                        self.messageNotifyTempDict[containerKey] = array
                    }
                }
                
                // 处理在线消息通知
                for message in messagesArray where onlineMessages.contains(NSNumber(value: message.im5Message.serverMsgId)) {
                    batchNotifyMsgs.append(IM5NotifyMessageItem(message: message.im5Message))
                }
                
                self.center?.receiver.im5MessageBatchNotify(batchNotifyMsgs)
                remoteDataCompletion(messagesArray, haveMore, isNeedInsertRTMessage, error)
            }
        }
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgQueryCtrl {

    func groupHistoryMessages(targetId: String,
                              boundaryMsgId: Int64,
                              isBefore: Bool,
                              pageSize: Int) -> AnyPublisher<([BuzIMMessage], Bool, Bool, Error?), Never> {
        let subject = PassthroughSubject<([BuzIMMessage], Bool, Bool, Error?), Never>()

        self.object.groupHistoryMessages(targetId: targetId,
                                         boundaryMsgId: boundaryMsgId,
                                         isBefore: isBefore,
                                         pageSize: pageSize,
                                         localDataCompletion: { localMessages in
                                             subject.send((localMessages, false, false, nil))
                                         },
                                         remoteDataCompletion: { remoteMessages, haveMore, isLastPage, error in
                                            subject.send((remoteMessages, haveMore, isLastPage, error))
                                            subject.send(completion: .finished)
                                         })

        return subject.eraseToAnyPublisher()
    }
}
