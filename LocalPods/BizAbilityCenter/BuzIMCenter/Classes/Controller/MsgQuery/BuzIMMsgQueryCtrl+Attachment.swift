//
//  BuzIMMsgQueryCtrl+Attachment.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog

// MARK: - Attachment Handling
public extension BuzIMMsgQueryCtrl {
    func reloadAttachment(message: BuzIMMessage, isForce: Bool, complete: @escaping (Bool, BuzIMMessage) -> Void) {
        let im5Msg = message.im5Message
        guard let attachment = im5Msg.messageContent as? IM5AttachmentMessageContent else {
            complete(false, message)
            return
        }
        
        let urlType = attachment.checkPerferredUrl()
        
        let handleReloadCompletion: (IM5Message?, Error?) -> Void = { reloadedMessage, error in
            if let reloadedMessage = reloadedMessage {
                BuzIMLog.error("[reload msg]serverMsgId : \(reloadedMessage.serverMsgId) ; reload success✅")
                complete(true, BuzIMMessage(im5Message: reloadedMessage))
            } else {
                // 这个消息确实存在系统错误，无法恢复，可以提示用户"消息已过期"
                BuzIMLog.error("[reload msg]serverMsgId : \(im5Msg.serverMsgId) ; error = \(String(describing: error))")
                complete(false, message)
            }
        }
        
        if isForce {
            self.center?.im5Client.reloadAttachmentMessage(im5Msg.msgId, convType: im5Msg.conversationType, completion: handleReloadCompletion)
        } else {
            switch urlType {
            case .local:
                complete(false, message)
                // use attachment.localPath
                
            case .remote:
                complete(false, message)
                // use attachment.remoteURL
                
            case .reload:
                self.center?.im5Client.reloadAttachmentMessage(im5Msg.msgId, convType: im5Msg.conversationType, completion: handleReloadCompletion)
            default:
                break
            }
        }
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgQueryCtrl {

    func reloadAttachment(message: BuzIMMessage, isForce: Bool) -> AnyPublisher<(Bool, BuzIMMessage?, Error?), Never> {
        return Future { promise in
            self.object.reloadAttachment(message: message, isForce: isForce, complete: { success, updatedMessage in
                if success {
                    promise(.success((success, updatedMessage, nil)))
                } else {
                    let error = NSError(domain: "ReloadAttachmentError", code: -1, userInfo: [NSLocalizedDescriptionKey: "Failed to reload attachment"])
                    promise(.success((false, nil, error)))
                }
            })
        }
        .eraseToAnyPublisher()
    }
}
