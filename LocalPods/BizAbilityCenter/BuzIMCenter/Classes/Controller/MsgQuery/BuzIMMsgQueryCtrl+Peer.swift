//
//  BuzIMMsgQueryCtrl.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/12.
//

import VoderXClient

import BuzCenterKit
import BuzLog

// MARK: - Peer Chat History
public extension BuzIMMsgQueryCtrl {
    
    /// 获取私聊本地历史消息
    /// 需要监听实时消息回调的时机
    /// 1、isBefore = YES && boundaryMsgId = 0 需要监听
    /// 2、isBefore = NO  && haveMore = NO 需要监听
    func peerHistoryMessages(targetId: String,
                             boundaryMsgId: Int64,
                             isBefore: Bool,
                             pageSize: Int,
                             completion: @escaping ([BuzIMMessage], Bool, Bool) -> Void) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            
            // 记录获取消息期间收到的消息回调
            let uuidString = UUID().uuidString
            let containerKey = self.loadingHistoryReceivedMsgContainerKey(.peer, targetId)
            let msgHolder = BuzUniqueMessagesHolder(uuidString: uuidString)
            
            // 添加到容器中持有holder
            if var holderArray = self.messageNotifyTempDict[containerKey] {
                holderArray.append(msgHolder)
                self.messageNotifyTempDict[containerKey] = holderArray
            } else {
                self.messageNotifyTempDict[containerKey] = [msgHolder]
            }
            
            BuzIMLog.info("begin get private chat msg history targetId = \(targetId), boundaryMsgId = \(boundaryMsgId), isBefore = \(isBefore), pageSize = \(pageSize)")
            
            self.center?.im5Client.getLocalHistory(ofTargetId: targetId,
                                                   conversationType: .peer,
                                                   boundary: boundaryMsgId,
                                                   before: isBefore,
                                                   limit: pageSize) { [weak self] msgList, haveMore in
                guard let self = self else { return }
                
                var messagesArray = msgList?.compactMap { msg -> BuzIMMessage? in
                    let bMessage = BuzIMMessage(im5Message: msg)
                    return self.canPassMessage(message: bMessage, conversationType: bMessage.conversationType) ? bMessage : nil
                } ?? .init()
                
                BuzIMLog.info("finish load private chat msg history targetId = \(targetId), boundaryMsgId = \(boundaryMsgId), isBefore = \(isBefore), pageSize = \(pageSize), count = \(messagesArray.count)")
                
                var isNeedInsertRTMessage = false
                
                // 处理期间接收到的消息回调数据
                if var array =  self.messageNotifyTempDict[containerKey],
                   let targetHolder = array.first(where: { $0.uuidString == uuidString }) {
                    
                    if (isBefore && boundaryMsgId == 0) || (!isBefore && !haveMore) {
                        // 同一次历史记录获取的逻辑，需要对消息进行去重 + 排序
                        let notifyMessageArray = targetHolder.msgArray
                        var historyMsgSet = Set(messagesArray)
                        historyMsgSet.formUnion(notifyMessageArray)
                        
                        // 排序
                        messagesArray = Array(historyMsgSet).sorted { $0.im5Message.compare($1.im5Message) == .orderedAscending }
                        isNeedInsertRTMessage = true
                    }
                    
                    // 清理临时存储
                    array.removeAll { $0.uuidString == uuidString }
                    if array.isEmpty {
                        self.messageNotifyTempDict.removeValue(forKey: containerKey)
                    } else {
                        self.messageNotifyTempDict[containerKey] = array
                    }
                }
                
                completion(messagesArray, haveMore, isNeedInsertRTMessage)
            }
        }
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgQueryCtrl {
    
    func peerHistoryMessages(targetId: String,
                             boundaryMsgId: Int64,
                             isBefore: Bool,
                             pageSize: Int) -> AnyPublisher<([BuzIMMessage], Bool, Bool), Never> {
        let subject = PassthroughSubject<([BuzIMMessage], Bool, Bool), Never>()
        
        self.object.peerHistoryMessages(targetId: targetId,
                                        boundaryMsgId: boundaryMsgId,
                                        isBefore: isBefore,
                                        pageSize: pageSize,
                                        completion: { messages, haveMore, isLastPage in
            subject.send((messages, haveMore, isLastPage))
            subject.send(completion: .finished)
        })
        
        return subject.eraseToAnyPublisher()
    }
}
