//
//  BuzIMStateCtrl.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog
import BuzNetworker
import Buz<PERSON>agicBox
import BuzUserSession

public class BuzIMStateCtrl: NSObject, BuzIMControllerable {
    typealias IMCenter = BuzIMCenter
    weak var center: BuzIMCenter?
    
    var isNetworkDisable: Bool = false
    
    // MARK: - 重试任务
    private weak var retryDisableReceiveMessageTask: FibIntervalRetryTask?
    private weak var retryEnableReceiveMessageTask: FibIntervalRetryTask?
    
    private(set) var isDisableReceiveMsg: Bool = false
    
    required init(center: BuzIMCenter?) {
        super.init()
        self.center = center
    }
    
    func setup() {
        // 初始化属性
        self.isNetworkDisable = BuzNetworker.shared.netMonitor.currentReachabilityStatus == .notReachable
        // 添加通知观察者
        NotificationCenter.default.addObserver(self, selector: #selector(appDidEnterBackground), name: UIApplication.didEnterBackgroundNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(appWillEnterForeground), name: UIApplication.willEnterForegroundNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(didReceivedNetworkReachable), name: .netReachablePrompt, object: nil)
        NotificationCenter.default.addObserver( self, selector: #selector(didReceivedNetwrokNotReachable), name: .netNotReachPrompt, object: nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}


// MARK: - IM Service Status
public extension BuzIMStateCtrl {
    func isLoading() -> Bool {
        return self.center?.im5Client.isLoading ?? false
    }
    
    func isStartupLoading() -> Bool {
        return self.center?.im5Client.isStartupLoading ?? false
    }
    
    func isServiceDisconnected() -> Bool {
        return self.center?.im5Client.isServiceDisconnected ?? false
    }
    
    func isLonglinkDisconnected() -> Bool {
        return self.center?.im5Client.isLonglinkDisconnected ?? false
    }
    
    func isNetworkUnreadable() -> Bool {
        return isNetworkDisable
    }
}

// MARK: - IM5ServiceStatusObserver
extension BuzIMStateCtrl {
    public func im5ServiceStatusChanged(_ status: IM5ServiceStatus) {
        // 使用静态字典存储状态名称
        let statusNameDict: [IM5ServiceStatus: String] = [
            .beginLoading: "BeginLoading",
            .endLoading: "EndLoading",
            .serviceDisconnected: "ServiceDisconnected",
            .serviceRecovered: "ServiceRecovered",
            .longlinkDisconnected: "LonglinkDisconnected",
            .longlinkConnected: "LonglinkConnected"
        ]
        
        BuzIMLog.info("当前IM状态: 【\(status.rawValue) - \(statusNameDict[status] ?? "")】 --- isLogining: \(self.center?.auth.isLoginingIM()) --- isServiceDisconnected: \(isServiceDisconnected()) --- isloading: \(isLoading()) --- isLonglinkDisconnected：\(isLonglinkDisconnected())")
        
        self.center?.statePublisher.imLoginStateChanged.send((BuzIMCenter.center))
        
        let state = BuzIMServiceStatus(rawValue: status.rawValue) ?? .beginLoading
        self.center?.notifyObservers(of: BuzIMCenterStateObserver.self) { observer in
            observer.imServerStatusChanged?(center: self.center!, status: state)
        }
        self.center?.statePublisher.imServerStatusChanged.send((BuzIMCenter.center, state))
    }
}

extension BuzIMStateCtrl {
    func logout() {
        self.isDisableReceiveMsg = false
    }
}

private extension BuzIMStateCtrl {
    /// 进入后台断开长连接触发后台远程消息的发送
    @objc func appDidEnterBackground() {
        BuzIMLog.info("")
    }
    
    @objc func appWillEnterForeground() {
        guard let auth = self.center?.auth  else { return }
        BuzIMLog.info("isLoginSuccess = \(auth.isLoginSuccess), isDisableReceiveMsg = \(isDisableReceiveMsg)")
        
        if !auth.isLoginSuccess {
            auth.login()
        } else if isDisableReceiveMsg {
            enableReceiveMessage()
        }
    }
    
    @objc func didReceivedNetworkReachable() {
        self.isNetworkDisable = false
        self.center?.auth.loginIMStateChange()
        BuzIMLog.info("current network reachable")
    }
    
    @objc func didReceivedNetwrokNotReachable() {
        self.isNetworkDisable = true
        self.center?.auth.loginIMStateChange()
        BuzIMLog.info("current network not reachable")
    }
}

// MARK: - Long Link Control
private extension BuzIMStateCtrl {
    
    func disableReceiveMessage() {
        cancelAllLongLinkRetryTask()
        
        BuzIMLog.info("（IM Long Link）begin disableReceiveMessage")
        let task = FibIntervalRetryTask(name: "com.buz.retryDisableReceiveMessageTask")
        retryDisableReceiveMessageTask = task
        
        task.start { [weak self] in
            guard let self = self else { return }
            self.isDisableReceiveMsg = true
            self.center?.im5Client.disableReceiveMessage { error in
                let isSuccess = error == nil
                BuzIMLog.info("（IM Long Link）disableReceiveMessage result : isSuccess = \(isSuccess), error = \(String(describing: error))")
                
                if isSuccess {
                    self.retryDisableReceiveMessageTask?.complete()
                } else {
                    _ = self.retryDisableReceiveMessageTask?.retry()
                }
            }
        }
        
        RetryTaskManager.shared.addRetryTask(task)
    }
    
    func enableReceiveMessage() {
        cancelAllLongLinkRetryTask()
        
        BuzIMLog.info("（IM Long Link）begin enableReceiveMessage")
        let task = FibIntervalRetryTask(name: "com.buz.retryEnableReceiveMessageTask")
        retryEnableReceiveMessageTask = task
        
        task.start { [weak self] in
            guard let self = self else { return }
            self.center?.im5Client.enableReceiveMessage { error in
                let isSuccess = error == nil
                BuzIMLog.info("（IM Long Link）enableReceiveMessage result : isSuccess = \(isSuccess), error = \(String(describing: error))")
                
                if isSuccess {
                    self.isDisableReceiveMsg = false
                    self.retryEnableReceiveMessageTask?.complete()
                } else {
                    _ = self.retryEnableReceiveMessageTask?.retry()
                }
            }
        }
        
        RetryTaskManager.shared.addRetryTask(task)
    }
    
    private func cancelAllLongLinkRetryTask() {
        retryDisableReceiveMessageTask?.cancel()
        retryEnableReceiveMessageTask?.cancel()
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMStateCtrl {
    
}
