//
//  BuzIMConvCtrl+Observer.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog

// MARK: - IM5ConversationObserver
extension BuzIMConvCtrl {
    func im5ConversationNotify(_ itemList: [IM5NotifyConversationItem]) {
        let list = itemList.map { item -> BuzIMConvNotifyItem in
            let noticItem = BuzIMConvNotifyItem(convNotifyItem: item)
            BuzIMLog.info("IM5ConversationNotify \(item) operation:\(item.operation), notPlayedCount = \(item.conversation.notPlayedCount), unreadCount = \(item.conversation.unReadCount)")
            return noticItem
        }
        
        self.center?.notifyObservers(of: BuzIMCenterConversationObserver.self) { observer in
            observer.imConversationsUpdated?(center: self.center!, items: list)
        }
        self.center?.convPublisher.imConversationsUpdated.send((BuzIMCenter.center, list))
    }
    
    func im5ConversationInitializedNotify(_ itemList: [IM5NotifyConversationItem]) {
        // 解决本地重装不拉取私聊IM消息，但IM会话会有未读计数展示不一致问题
        // 首次安装app的时候清空所有私聊计数
        for object in itemList {
            BuzIMLog.info("IM5ConversationInitializedNotify \(object) operation:\(object.operation), notPlayedCount = \(object.conversation.notPlayedCount), unreadCount = \(object.conversation.unReadCount)")
            
            if object.conversation.unReadCount > 0 && object.conversation.conversationType == .peer {
                clearConversationUnread(targetId: object.conversation.targetId, conversationType: .peer) { _, _ in }
            }
        }
        
        BuzIMLog.info("IM5ConversationInitializedNotify call IM5ConversationNotify begin count:\(itemList.count)")
        im5ConversationNotify(itemList)
        BuzIMLog.info("IM5ConversationInitializedNotify call IM5ConversationNotify end")
    }
}
