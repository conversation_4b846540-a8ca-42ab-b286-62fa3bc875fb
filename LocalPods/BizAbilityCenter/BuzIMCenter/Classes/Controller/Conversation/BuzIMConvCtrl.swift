//
//  BuzIMConvCtrl.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/12.
//

import VoderXClient

import BuzCenterKit
import BuzLog

public class BuzIMConvCtrl: NSObject, BuzIMControllerable {
    typealias IMCenter = BuzIMCenter
    weak var center: BuzIMCenter?
    
    //当前进入的会话Id
    public private(set) var currentConvId: String?
    
    required init(center: BuzIMCenter?) {
        self.center = center
    }
}

// MARK: - Conversation List
public extension BuzIMConvCtrl {
    /// 获取会话列表
    func getConversations(before convId: Int64,
                          conversationTypes convTypes: [NSNumber],
                          group: Int64,
                          limit count: Int,
                          result handler: IM5ConversationListEventBlock?) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            self.center?.im5Client.getConversationsBefore(convId,
                                                          conversationTypes: convTypes,
                                                          group: group,
                                                          limit: count) { conversationList in
                handler?(conversationList)
                // BuzIMLog.debug("conversationList -> \(String(describing: conversationList))")
            }
        }
    }
    
    func getConversation(targetId: String,
                         conversationType: BuzConversationType,
                         result handler: ((IM5Conversation?) -> Void)?) {
        self.center?.im5Client.getConversationOfTarget(targetId, conversationType: IM5ConversationType(rawValue: conversationType.rawValue) ?? .peer) { conversation in
            BuzIMLog.info("getConversationOfTarget = \(targetId) finish")
            handler?(conversation)
        }
    }
}

// MARK: - Conversation enter&leave
public extension BuzIMConvCtrl {
    func enterConversation(targetId: String, conversationType: BuzConversationType, clearUnread: Bool) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            self.currentConvId = targetId
            
            self.center?.notifyObservers(of: BuzIMCenterConversationObserver.self) { observer in
                observer.imConversationEntered?(center: self.center!, targetId: targetId)
            }
            self.center?.convPublisher.imConversationEntered.send((BuzIMCenter.center, targetId))
            
            self.center?.im5Client.enterConversation(ofTargetId: targetId,
                                                     conversationType: IM5ConversationType(rawValue: conversationType.rawValue) ?? .peer,
                                                     clearUnread: clearUnread)
            BuzIMLog.info("enterConversationOfTargetId:\(targetId) ; conversationType:\(conversationType), clearUnread:\(clearUnread)")
        }
    }
    
    func leaveConversation(targetId: String, conversationType: BuzConversationType, clearUnread: Bool) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            
            if let currentTargetId = self.currentConvId {
                self.center?.notifyObservers(of: BuzIMCenterConversationObserver.self) { observer in
                    observer.imConversationLeaved?(center: self.center!, targetId: currentTargetId)
                }
                self.center?.convPublisher.imConversationLeaved.send((BuzIMCenter.center, currentTargetId))
            }
            self.currentConvId = nil
            self.center?.im5Client.leaveConversation(ofTargetId: targetId,
                                                     conversationType: IM5ConversationType(rawValue: conversationType.rawValue) ?? .peer,
                                                     clearUnread: clearUnread)
            BuzIMLog.info("leaveConversation targetId = \(targetId), conversationType = \(conversationType) clearUnread = \(clearUnread)")
        }
    }
}



//MARK: -ConversationUnread
public extension BuzIMConvCtrl {
    func getConversationsTotalUnreadCount(_ completion: @escaping (Int) -> Void) {
        self.center?.im5Client.getConversationsTotalUnreadCount(completion)
    }
    
    /// 获得指定会话未读数
    /// - Parameters:
    ///   - targetId: 会话用户target
    ///   - conversationType: 会话类型
    ///   - completion: 结果回调
    func getConversationUnreadCount(targetId: String, conversationType: BuzConversationType, completion: @escaping IM5QueryConversationBlock) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            self.center?.im5Client.getConversationUnreadCount(ofTargetId: targetId,
                                                              conversationType: IM5ConversationType(rawValue: conversationType.rawValue) ?? .peer) { conversation in
                completion(conversation)
            }
        }
    }
    
    func getConversationAllUnreadCount(targetId: String, conversationType: BuzConversationType, completion: @escaping (IM5Conversation?, Int) -> Void) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            self.center?.im5Client.getConversationUnreadCount(ofTargetId: targetId,
                                                              conversationType: IM5ConversationType(rawValue: conversationType.rawValue) ?? .peer) { conversation in
                completion(conversation, conversation?.notPlayedCount ?? 0)
            }
        }
    }
    
    /// 获取所有会话消息未播放总数
    /// - Parameter completion: 结果回调
    func getConversationsTotalNotPlayedCount(_ completion: @escaping (Int) -> Void) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            self.center?.im5Client.getConversationsTotalNotPlayedCount(completion)
        }
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMConvCtrl {
    // MARK: - 获取会话列表
    func getConversations(before convId: Int64,
                          conversationTypes convTypes: [NSNumber],
                          group: Int64,
                          limit count: Int) -> AnyPublisher<[IM5Conversation]?, Never> {
        return Future { promise in
            self.object.getConversations(before: convId, conversationTypes: convTypes, group: group, limit: count, result: { conversations in
                promise(.success(conversations))
            })
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 获取指定会话
    func getConversation(targetId: String, conversationType: BuzConversationType) -> AnyPublisher<IM5Conversation?, Never> {
        return Future { promise in
            self.object.getConversation(targetId: targetId, conversationType: conversationType, result: { conversation in
                promise(.success(conversation))
            })
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 进入会话
    func enterConversation(targetId: String, conversationType: BuzConversationType, clearUnread: Bool) -> AnyPublisher<Void, Never> {
        return Just(self.object.enterConversation(targetId: targetId, conversationType: conversationType, clearUnread: clearUnread))
            .eraseToAnyPublisher()
    }

    // MARK: - 离开会话
    func leaveConversation(targetId: String, conversationType: BuzConversationType, clearUnread: Bool) -> AnyPublisher<Void, Never> {
        return Just(self.object.leaveConversation(targetId: targetId, conversationType: conversationType, clearUnread: clearUnread))
            .eraseToAnyPublisher()
    }

    // MARK: - 获取会话总未读数
    func getConversationsTotalUnreadCount() -> AnyPublisher<Int, Never> {
        return Future { promise in
            self.object.getConversationsTotalUnreadCount { count in
                promise(.success(count))
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 获取指定会话未读数
    func getConversationUnreadCount(targetId: String, conversationType: BuzConversationType) -> AnyPublisher<IM5Conversation?, Never> {
        return Future { promise in
            self.object.getConversationUnreadCount(targetId: targetId, conversationType: conversationType) { conversation in
                promise(.success(conversation))
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 获取指定会话总未读数
    func getConversationAllUnreadCount(targetId: String, conversationType: BuzConversationType) -> AnyPublisher<(IM5Conversation?, Int), Never> {
        return Future { promise in
            self.object.getConversationAllUnreadCount(targetId: targetId, conversationType: conversationType) { conversation, count in
                promise(.success((conversation, count)))
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 获取所有会话未播放总数
    func getConversationsTotalNotPlayedCount() -> AnyPublisher<Int, Never> {
        return Future { promise in
            self.object.getConversationsTotalNotPlayedCount { count in
                promise(.success(count))
            }
        }
        .eraseToAnyPublisher()
    }
}
