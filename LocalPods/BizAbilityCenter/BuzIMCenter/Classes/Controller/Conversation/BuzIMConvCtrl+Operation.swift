//
//  BuzIMConvCtrl+Operation.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog
import BuzAppConfiger

//MARK: -Conversation
public extension BuzIMConvCtrl {
    func deleteConversation(targetId: String, conversationType: BuzConversationType, completion: @escaping (Error?) -> Void) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            BuzModuleLogType.BuzConversationList.log("deleteConversation targetId = \(targetId), conversationType = \(conversationType)")
            self.center?.im5Client.deleteConversation(ofTargetId: targetId,
                                                      conversationType: IM5ConversationType(rawValue: conversationType.rawValue) ?? .peer) { error in
                if let error = error {
                    BuzModuleLogType.BuzConversationList.error("deleteConversation targetId = \(targetId), conversationType = \(conversationType), error: \(error)")
                }
                completion(error)
            }
        }
    }
}

//MARK: -ConversationUnread
public extension BuzIMConvCtrl {
    func clearConversationUnread(targetId: String, conversationType: BuzConversationType, completion: ((IM5Conversation?, Error?) -> Void)?) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            self.center?.im5Client.clearConversationUnread(ofTargetId: targetId,
                                                           conversationType: IM5ConversationType(rawValue: conversationType.rawValue) ?? .peer) { conversation, error in
                if let error = error {
                    BuzIMLog.error("clearConversationUnreadOfTargetId:\(targetId) ; error:\(error)")
                    return
                }
                
                BuzIMLog.info("clearConversationUnreadOfTargetId:\(targetId) ;success")
                
                self.center?.notifyObservers(of: BuzIMCenterConversationObserver.self) { observer in
                    observer.imConversationUnreadCleared?(center: self.center!, targetId: targetId)
                }
                self.center?.convPublisher.imConversationUnreadCleared.send((BuzIMCenter.center, targetId))
                
                self.center?.im5Client.getConversationOfTarget(targetId,
                                                               conversationType: IM5ConversationType(rawValue: conversationType.rawValue) ?? .peer) { conversation in
                    guard let conversation = conversation else { return }
                    let item = IM5NotifyConversationItem(conversation: conversation)
                    self.im5ConversationNotify([item])
                }
            }
        }
    }
}

// MARK: - Local Extra
public extension BuzIMConvCtrl {
    func updateConversation(targetId: String,
                            conversationType: BuzConversationType,
                            extra extraDict: [String: Any]?,
                            completion: ((IM5Conversation?, Error?) -> Void)?) {
        BuzIMLog.info("begin update conversation extra = \(String(describing: extraDict))")
        guard let extraDict = extraDict else { return }
        let extra = IM5Extra(dictionary: extraDict)
        
        self.center?.im5Client.updateConversation(targetId,
                                                  conversationType: IM5ConversationType(rawValue: conversationType.rawValue) ?? .peer,
                                                  group: IM5ConversationGroup.unset.rawValue,
                                                  andExtra: extra) { [weak self] conversation, error in
            guard let self = self else { return }
            
            BuzIMLog.info("finish update conversation.extra = \(String(describing: conversation?.extra)); extraDict = \(String(describing: extraDict)); error = \(String(describing: error))")
            
            completion?(conversation, error)
        }
    }
}

//MARK: -ConversationTime
public extension BuzIMConvCtrl {
    /// 修改会话时间
    /// - Parameter convInfos: 会话信息的数组
    func modifyConversationUpdateTime(_ convInfos: [BuzConvLivePlaceUpdateTime]) {
        var infos = [IM5ConversationInfo].init()
        BuzIMLog.info("modifyConversationUpdateTime = \(convInfos)")
        
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            
            for item in convInfos {
                let info = IM5ConversationInfo(target: item.targetId, type: .init(rawValue: item.type.rawValue) ?? .peer, updateTime: item.lastUpdateTime)
                infos.append(info)
            }
            
            if !infos.isEmpty {
                self.center?.im5Client.modifyConversationUpdateTime(infos)
            }
        }
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMConvCtrl {
    // MARK: - 删除会话
    func deleteConversation(targetId: String, conversationType: BuzConversationType) -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.deleteConversation(targetId: targetId, conversationType: conversationType) { error in
                promise(.success((error)))
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 清除会话未读数
    func clearConversationUnread(targetId: String, conversationType: BuzConversationType) -> AnyPublisher<(IM5Conversation?, Error?), Never> {
        return Future { promise in
            self.object.clearConversationUnread(targetId: targetId, conversationType: conversationType) { conversation, error in
                promise(.success((conversation, error)))
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 更新会话
    func updateConversation(targetId: String, conversationType: BuzConversationType, extra extraDict: [String: Any]?) -> AnyPublisher<(IM5Conversation?, Error?), Never> {
        return Future { promise in
            self.object.updateConversation(targetId: targetId, conversationType: conversationType, extra: extraDict) { conversation, error in
                promise(.success((conversation, error)))
            }
        }
        .eraseToAnyPublisher()
    }

    // MARK: - 修改会话时间
    func modifyConversationUpdateTime(_ convInfos: [BuzConvLivePlaceUpdateTime]) -> AnyPublisher<Void, Never> {
        return Just(self.object.modifyConversationUpdateTime(convInfos))
            .eraseToAnyPublisher()
    }
}
