//
//  BuzIMMsgOpsCtrl.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog
import Localizable
import BuzLocalizable

// MARK: - Typealias
public typealias BuzEventCompletionBlock = (Error?) -> Void

public class BuzIMMsgOpsCtrl: NSObject, BuzIMControllerable {
    typealias IMCenter = BuzIMCenter
    weak var center: BuzIMCenter?
    
    var setPlayedMsgIdSet: Set<NSNumber> = .init()
    
    required init(center: BuzIMCenter?) {
        self.center = center
    }
}

// MARK: - Delete Messages
public extension BuzIMMsgOpsCtrl {
    /// 删除消息
    func deleteMessage(_ msgId: Int64,
                       convType: BuzConversationType,
                       deleteRemote: Bool,
                       localResult: BuzEventCompletionBlock? = nil,
                       remoteResult: BuzEventCompletionBlock? = nil) {
        self.center?.im5Client.deleteMessage(msgId, convType: IM5ConversationType(rawValue: convType.rawValue) ?? .peer, deleteRemote: true) { error in
            BuzIMLog.info("delete local message result = \(error == nil), error = \(String(describing: error))")
            localResult?(error)
        } remoteResult: { error in
            BuzIMLog.info("delete remote message result = \(error == nil), error = \(String(describing: error))")
            remoteResult?(error)
        }
    }
    
    /// 删除本地消息
    func deleteMessage(_ msgId: Int64,
                       target targetId: String,
                       convType: BuzConversationType,
                       completion: BuzEventCompletionBlock? = nil) {
        self.center?.im5Client.deleteMessage(msgId, target: targetId, convType: IM5ConversationType(rawValue: convType.rawValue) ?? .peer) { error in
            BuzIMLog.info("deleteLocalMessage isSuccess = \(error == nil), error = \(String(describing: error))")
            completion?(error)
        }
    }
    
    /// 删除远程消息
    func deleteRemoteMessage(_ msgId: Int64,
                             target targetId: String,
                             convType: BuzConversationType,
                             completion: BuzEventCompletionBlock? = nil) {
        self.center?.im5Client.deleteRemoteMessage(msgId, target: targetId, convType: IM5ConversationType(rawValue: convType.rawValue) ?? .peer) { error in
            BuzIMLog.info("deleteRemoteMessage isSuccess = \(error == nil), error = \(String(describing: error))")
            completion?(error)
        }
    }
    
    /// 清除指定时间之前的消息
    func clearMessages(targetId: String,
                       conversationType: BuzConversationType,
                       before timestamp: TimeInterval,
                       deleteRemote: Bool,
                       localResult: BuzEventCompletionBlock? = nil,
                       remoteResult: BuzEventCompletionBlock? = nil) {
        self.center?.im5Client.clearMessages(ofTargetId: targetId,
                                             conversationType: IM5ConversationType(rawValue: conversationType.rawValue) ?? .peer,
                                             before: timestamp,
                                             deleteRemote: deleteRemote) { error in
            BuzIMLog.info("clear local message isSuccess = \(error == nil), error = \(String(describing: error))")
            localResult?(error)
        } remoteResult: { error in
            BuzIMLog.info("clear remote message isSuccess = \(error == nil), error = \(String(describing: error))")
            remoteResult?(error)
        }
    }
}

// MARK: - Message Operations
public extension BuzIMMsgOpsCtrl {
    /// 撤回消息
    func recallMessage(messageId: Int64,
                       message: BuzIMMessage,
                       svrMsgId: Int64,
                       conversationType: BuzConversationType,
                       completion: @escaping (Error?, BuzIMMessage?) -> Void) {
        BuzIMLog.info("recallMessageId message -> \(messageId)")
        let pushPayload: String = self.center?.provider?.pushPayload(message: message) ?? ""
        
        self.center?.im5Client.recallMessage(messageId,
                                             convType: conversationType == .peer ? .peer : .group,
                                             pushContent: Localizable.message_recalled,
                                             pushPayload: pushPayload,
                                             keepOriginalContent: false,
                                             success: { message in
            completion(nil, message.map { BuzIMMessage(im5Message: $0) })
        }, failed: { error in
            completion(error, nil)
        })
    }
}


//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgOpsCtrl {
    enum IMMsgDeleteResult {
        case remote(Bool)
        case local(Bool)
        case error(Error)
    }
    // MARK: - 删除消息
    func deleteMessage(_ msgId: Int64,
                       convType: BuzConversationType,
                       deleteRemote: Bool) -> AnyPublisher<IMMsgDeleteResult, Never> {
        let subject = PassthroughSubject<IMMsgDeleteResult, Never>()
        
        self.object.deleteMessage(msgId, convType: convType, deleteRemote: deleteRemote, localResult: { _ in
            subject.send(.local(true))
        }, remoteResult: { error in
            if let error = error {
                subject.send(.error(error))
            } else {
                subject.send(.remote(true))
            }
            subject.send(completion: .finished)
        })
        
        return subject.eraseToAnyPublisher()
    }
    
    func deleteLocalMessage(_ msgId: Int64,
                            target targetId: String,
                            convType: BuzConversationType) -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.deleteMessage(msgId, target: targetId, convType: convType, completion: { error in
                if let error = error {
                    promise(.success(error))
                } else {
                    promise(.success(nil))
                }
            })
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - 删除远程消息
    func deleteRemoteMessage(_ msgId: Int64,
                             target targetId: String,
                             convType: BuzConversationType) -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.deleteRemoteMessage(msgId, target: targetId, convType: convType, completion: { error in
                if let error = error {
                    promise(.success(error))
                } else {
                    promise(.success(nil))
                }
            })
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - 清除指定时间之前的消息
    func clearMessages(targetId: String,
                       conversationType: BuzConversationType,
                       before timestamp: TimeInterval,
                       deleteRemote: Bool) -> AnyPublisher<Error?, Never> {
        return Future { promise in
            self.object.clearMessages(targetId: targetId, conversationType: conversationType, before: timestamp, deleteRemote: deleteRemote, localResult: { _ in
                promise(.success(nil))
            }, remoteResult: { error in
                promise(.success(error))
            })
        }
        .eraseToAnyPublisher()
    }
    
    // MARK: - 撤回消息
    func recallMessage(messageId: Int64,
                       message: BuzIMMessage,
                       svrMsgId: Int64,
                       conversationType: BuzConversationType) -> AnyPublisher<(BuzIMMessage?, Error?), Never> {
        return Future { promise in
            self.object.recallMessage(messageId: messageId, message: message, svrMsgId: svrMsgId, conversationType: conversationType, completion: { error, recalledMessage in
                promise(.success((recalledMessage, error)))
            })
        }
        .eraseToAnyPublisher()
    }
}
