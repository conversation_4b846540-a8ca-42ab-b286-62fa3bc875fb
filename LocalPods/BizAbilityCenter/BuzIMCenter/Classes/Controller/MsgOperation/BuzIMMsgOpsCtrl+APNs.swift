//
//  BuzIMMsgOpsCtrl+APNs.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog


public extension BuzIMMsgOpsCtrl {
    
    func handleReceivedRemoteNotification(withServerMsgId serverMsgId: Int64) {
        BuzIMLog.info("begin sync notification serverMsgId = \(serverMsgId)")
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            BuzIMLog.info("exec sync notification serverMsgId = \(serverMsgId)")
            self.center?.im5Client.onReceivedRemoteNotification(serverMsgId)
        }
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgOpsCtrl {
    func handleReceivedRemoteNotification(withServerMsgId serverMsgId: Int64) -> AnyPublisher<Void, Never>{
        return Just(self.object.handleReceivedRemoteNotification(withServerMsgId: serverMsgId))
            .eraseToAnyPublisher()
    }
}
