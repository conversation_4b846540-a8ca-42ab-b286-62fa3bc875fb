//
//  BuzIMMsgOpsCtrl+SetPlayed.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog

// MARK: - Other
public extension BuzIMMsgOpsCtrl {
    func setPlayedMessage(targetId: String, conversationType: BuzConversationType, svrMsgId: Int64) {
        self.center?.auth.addLoginCompleteTask { [weak self] in
            guard let self = self else { return }
            
#if DEBUG
            // Watch 对点击自己发送的消息时候，有时候会没有 svrMsgId 的数据
            if svrMsgId == 0 {
                BuzIMLog.error("setPlayedMessageOfTargetId no svrMsgId")
                return
            }
            
            self.center?.query.queryMessage(serverMsgId: svrMsgId, conversationType: conversationType) { msg in
                BuzIMLog.error("setPlayedMessageOfTargetId messageType error -> svrMsgId = \(svrMsgId), targetId = \(targetId), conversationType = \(String(describing: msg?.conversationType)) messageType: \(String(describing: msg?.messageType))")
                
                let validMessageTypes: Set<BuzIMContentType> = [
                    .walkieTalkie, .count_WalkieTalkie, .voice_Text, .new_Voice_Text,
                    .voice_Emoji, .compatibleVoiceEmoji, .video, .image, .location,
                    .mediaText, .mediaActionText, .text, .voicegif, .fileAttachment
                ]
                
                assert(msg.map { validMessageTypes.contains($0.messageType) } ?? false,
                       "传入了错误类型，这里应该只有WalkieTalkie")
            }
#endif
            
            if self.setPlayedMsgIdSet.contains(NSNumber(value: svrMsgId)) {
                BuzIMLog.error("重复设置已播消息将会出现错误 svrMsgId = \(svrMsgId), targetId = \(targetId), conversationType = \(conversationType)")
                return
            }
            
            self.setPlayedMsgIdSet.insert(NSNumber(value: svrMsgId))
            self.center?.im5Client.setPlayedMessageOfTargetId(targetId,
                                                      conversationType: IM5ConversationType(rawValue: conversationType.rawValue) ?? .peer,
                                                      svrMsgId: svrMsgId)
            BuzIMLog.info("setPlayedMessageOfTargetId -> svrMsgId = \(svrMsgId), targetId = \(targetId), conversationType = \(conversationType)")
        }
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgOpsCtrl {
    func setPlayedMessage(targetId: String, conversationType: BuzConversationType, svrMsgId: Int64) -> AnyPublisher<Void, Never>{
        return Just(self.object.setPlayedMessage(targetId: targetId, conversationType: conversationType, svrMsgId: svrMsgId))
            .eraseToAnyPublisher()
    }
}
