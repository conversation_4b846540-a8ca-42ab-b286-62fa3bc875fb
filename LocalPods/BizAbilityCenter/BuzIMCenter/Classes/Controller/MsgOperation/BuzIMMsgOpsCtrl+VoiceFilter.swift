//
//  BuzIMMsgOpsCtrl+VoiceFilter.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog

// MARK: - Voice Filter Operations
public extension BuzIMMsgOpsCtrl {
    /// 删除预览语音滤镜本地文件
    func deletePreviewVoiceFilterLocalVoiceFile(_ localPath: String) {
        if !localPath.isEmpty && FileManager.default.fileExists(atPath: localPath) {
            do {
                try FileManager.default.removeItem(atPath: localPath)
                BuzIMLog.info("VoiceFilter.deletePreviewVoiceFilterLocalVoiceFile.fileExistsAtPath = \(localPath), result = true")
            } catch {
                BuzIMLog.info("VoiceFilter.deletePreviewVoiceFilterLocalVoiceFile.fileExistsAtPath = \(localPath), result = false")
            }
        } else {
            BuzIMLog.info("VoiceFilter.deletePreviewVoiceFilterLocalVoiceFile")
        }
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgOpsCtrl {
    // 删除预览语音滤镜本地文件
    func deletePreviewVoiceFilterLocalVoiceFile(_ localPath: String) -> AnyPublisher<Void, Never>{
        return Just(self.object.deletePreviewVoiceFilterLocalVoiceFile(localPath))
            .eraseToAnyPublisher()
    }
}
