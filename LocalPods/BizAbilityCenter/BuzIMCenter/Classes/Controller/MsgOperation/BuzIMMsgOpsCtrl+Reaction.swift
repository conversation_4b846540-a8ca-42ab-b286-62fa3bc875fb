//
//  BuzIMMsgOpsCtrl+Reaction.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog

// MARK: - Enums
@objc
public enum BuzMessageReactionChangeType: Int {
    case add = 0      // 添加
    case replace = 1  // 更新
    case delete = 2   // 删除
}

// MARK: - Quick Reactions
public extension BuzIMMsgOpsCtrl {
    /// 添加快速响应
    func addReaction(message: BuzIMMessage,
                     reaction: BuzIMMessageReactionInfo,
                     success: ((BuzIMMessage) -> Void)? = nil,
                     failed: ((BuzIMMessage, Error?) -> Void)? = nil) {
        let pushContent: String = self.center?.provider?.pushContent(reaction: reaction) ?? ""
        let pushPayload: String = self.center?.provider?.pushPayload(message: message, pushContent: pushContent, reactionInfo: reaction) ?? ""
        self.center?.im5Client.addReactionType(reaction.type,
                                               andIdentifier: reaction.identifier,
                                               forMsg: message.im5Message.msgId,
                                               convType: message.im5Message.conversationType,
                                               pushContent: pushContent,
                                               pushPayload: pushPayload) {[weak self] message in
            BuzIMLog.info("addReaction message isSuccess = yes")
            guard let self = self else { return }
            guard let message = message else { return }
            
            let bMsg = BuzIMMessage(im5Message: message)
            success?(bMsg)
            
            self.center?.notifyObservers(of: BuzIMCenterMessageObserver.self) { observer in
                observer.imReactionMessageChanged?(center: self.center!, message: bMsg, type: .add, error: nil)
            }
            self.center?.messagePublisher.imReactionMessageChanged.send((BuzIMCenter.center, bMsg, .add, nil))
        } failed: { [weak self] error in
            BuzIMLog.info("addReaction message isSuccess = failed, error = \(String(describing: error))")
            self?.center?.query.queryMessage(message.im5Message.msgId, convType: message.conversationType) { msg in
                failed?(msg, error)
            }
        }
    }
    
    /// 删除快速响应
    func deleteReaction(message: BuzIMMessage,
                        reaction: BuzIMMessageReactionInfo,
                        success: ((BuzIMMessage) -> Void)? = nil,
                        failed: ((BuzIMMessage, Error?) -> Void)? = nil) {
        self.center?.im5Client.deleteReactionType(reaction.type,
                                                  andIdentifier: reaction.identifier,
                                                  forMsg: message.im5Message.msgId,
                                                  convType: message.im5Message.conversationType,
                                                  pushContent: nil,
                                                  pushPayload: nil) {[weak self] message in
            BuzIMLog.info("deleteReaction message isSuccess = yes")
            guard let self = self else { return }
            guard let message = message else { return }
            
            let bMsg = BuzIMMessage(im5Message: message)
            success?(bMsg)
            
            self.center?.notifyObservers(of: BuzIMCenterMessageObserver.self) { observer in
                observer.imReactionMessageChanged?(center: self.center!, message: bMsg, type: .delete, error: nil)
            }
            self.center?.messagePublisher.imReactionMessageChanged.send((BuzIMCenter.center, bMsg, .delete, nil))
        } failed: { [weak self] error in
            BuzIMLog.info("deleteReaction message isSuccess = failed, error = \(String(describing: error))")
            self?.center?.query.queryMessage(message.im5Message.msgId, convType: message.conversationType) { msg in
                failed?(msg, error)
            }
        }
    }
    
    /// 替换快速响应
    func replaceReaction(message: BuzIMMessage,
                         reaction: BuzIMMessageReactionInfo,
                         oldReaction: BuzIMMessageReactionInfo,
                         success: ((BuzIMMessage) -> Void)? = nil,
                         failed: ((BuzIMMessage, Error?) -> Void)? = nil) {
        let pushContent: String = self.center?.provider?.pushContent(reaction: reaction) ?? ""
        let pushPayload: String = self.center?.provider?.pushPayload(message: message, pushContent: pushContent, reactionInfo: reaction) ?? ""
        
        self.center?.im5Client.replaceReactionOldType(oldReaction.type,
                                                      andOldIdentifier: oldReaction.identifier,
                                                      withNewType: reaction.type,
                                                      andNewIdentifier: reaction.identifier,
                                                      forMsg: message.im5Message.msgId,
                                                      convType: message.im5Message.conversationType,
                                                      pushContent: pushContent,
                                                      pushPayload: pushPayload) { [weak self] message in
            BuzIMLog.info("replaceReaction message isSuccess = yes")
            guard let self = self else { return }
            guard let message = message else { return }
            
            let bMsg = BuzIMMessage(im5Message: message)
            success?(bMsg)
            
            self.center?.notifyObservers(of: BuzIMCenterMessageObserver.self) { observer in
                observer.imReactionMessageChanged?(center: self.center!, message: bMsg, type: .replace, error: nil)
            }
            self.center?.messagePublisher.imReactionMessageChanged.send((BuzIMCenter.center, bMsg, .replace, nil))
        } failed: { [weak self] error in
            BuzIMLog.info("replaceReaction message isSuccess = failed, error = \(String(describing: error))")
            self?.center?.query.queryMessage(message.im5Message.msgId, convType: message.conversationType) { msg in
                failed?(msg, error)
            }
        }
    }
}

//MARK: -BuzCombineKit==================================
import Combine
import BuzFoundation
public extension BuzCombineKit where Base: BuzIMMsgOpsCtrl {
    // 添加快速响应
    func addReaction(message: BuzIMMessage, reaction: BuzIMMessageReactionInfo) -> AnyPublisher<(BuzIMMessage?, Error?), Never> {
        return Future { promise in
            self.object.addReaction(message: message, reaction: reaction, success: { msg in
                promise(.success((msg, nil)))
            }, failed: { msg, error in
                promise(.success((msg, error)))
            })
        }
        .eraseToAnyPublisher()
    }

    // 删除快速响应
    func deleteReaction(message: BuzIMMessage, reaction: BuzIMMessageReactionInfo) -> AnyPublisher<(BuzIMMessage?, Error?), Never> {
        return Future { promise in
            self.object.deleteReaction(message: message, reaction: reaction, success: { msg in
                promise(.success((msg, nil)))
            }, failed: { msg, error in
                promise(.success((msg, error)))
            })
        }
        .eraseToAnyPublisher()
    }

    // 替换快速响应
    func replaceReaction(message: BuzIMMessage, reaction: BuzIMMessageReactionInfo, oldReaction: BuzIMMessageReactionInfo) -> AnyPublisher<(BuzIMMessage?, Error?), Never> {
        return Future { promise in
            self.object.replaceReaction(message: message, reaction: reaction, oldReaction: oldReaction, success: { msg in
                promise(.success((msg, nil)))
            }, failed: { msg, error in
                promise(.success((msg, error)))
            })
        }
        .eraseToAnyPublisher()
    }
}
