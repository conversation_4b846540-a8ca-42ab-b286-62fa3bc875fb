//
//  BuzIMMsgOpsCtrl+LocalExtra.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog

// MARK: - Local Extra
extension BuzIMMsgOpsCtrl {
    //不对center外开放，可在BuzIMLocalExtraWrapper新增相关扩展字段的更新逻辑
    func updateLocalExtra(msgId: Int64,
                          convType: BuzConversationType,
                          extraDict: [String: Any]?,
                          completion: ((Int64, Error?) -> Void)? = nil) {
        guard let extraDict = extraDict else { return }
        self.center?.im5Client.updateMessage(msgId,
                                             convType: IM5ConversationType(rawValue: convType.rawValue) ?? .peer,
                                             with: IM5Extra(dictionary: extraDict)) { msgId, error in
            BuzIMLog.info("msgID = \(msgId), error = \(String(describing: error))")
            completion?(msgId, error)
        }
    }
}
