//
//  BuzIMMsgReceiveCtrl+Reporter.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/13.
//

import VoderXClient

import BuzCenterKit
import BuzLog

// MARK: - IM5ReporterObserver
extension BuzIMMsgReceiveCtrl {
    public func im5ReporterEvent(_ eventKey: String!, object: [AnyHashable : Any]!) {
        BuzIMLog.error("IM5ReporterEvent eventKey = \(String(describing: eventKey)), object = \(String(describing: object))")
        
        self.center?.notifyObservers(of: BuzIMCenterReporterObserver.self) { observer in
            observer.imReporterEvent?(center: self.center!, eventKey: eventKey, object: object)
        }
        self.center?.reportPublisher.imReporterEvent.send((BuzIMCenter.center, eventKey, object))
    }
}
