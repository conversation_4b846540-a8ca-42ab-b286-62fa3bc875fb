//
//  BuzIMMsgReceiveCtrl.swift
//  BuzIMCenter
//
//  Created by st.chio on 2025/3/12.
//

import VoderXClient

import BuzCenterKit
import BuzLog
import BuzUserSession

public class BuzIMMsgReceiveCtrl: NSObject, BuzIMControllerable {
    typealias IMCenter = BuzIMCenter
    weak var center: BuzIMCenter?
    
    required init(center: BuzIMCenter?) {
        self.center = center
    }
}

// MARK: - IM5MessageObserver
extension BuzIMMsgReceiveCtrl {
    func im5MessageBatchNotify(_ items: [IM5NotifyMessageItem]) {
        for item in items {
            let im5Message = item.message
            let message = BuzIMMessage(im5Message: im5Message)
            
            // 接收到带asr结果的消息需要设置自动显示ASR
            // if let voiceContent = message.im5Message.messageContent as? BuzWalkieTalkieCountMessageContent,
            //    item.operation == .update,
            //    voiceContent.asrText != nil {
            //    message.updateIsShowAsrText(true, syncToLocalComplete: nil)
            // }
            
            receiveMessageHandle(message, im5Message: im5Message, item: item, operationType: item.operation)
        }
    }
}

extension BuzIMMsgReceiveCtrl {
    private func receiveMessageHandle(_ message: BuzIMMessage, im5Message: IM5Message, item: IM5NotifyMessageItem, operationType: IM5NotifyItemOperation) {
        // 添加到容器中
        // 群聊key -> targetId
        // 私聊key -> fromId
        let id = message.conversationType == .peer ? im5Message.fromId : im5Message.targetId
        let key = self.center?.query.loadingHistoryReceivedMsgContainerKey(message.conversationType, id) ?? ""
        
        if let holderArray = self.center?.query.messageNotifyTempDict[key] {
            for holder in holderArray {
                if self.canPassMessage(message: message, conversationType: message.conversationType) {
                    holder.msgArray.append(message)
                    BuzIMLog.info("IM5MessageBatchNotify add im message to container holder \(message)")
                }
            }
        }
        
        BuzIMLog.info("GROUP REPLACE MSGID replace -> \(item.operation) \(message.im5Message.serverMsgId)")
        
        let noticItem = BuzIMMessageNotifyItem(message: message, operation: BuzIMMessageOperation(rawValue: operationType.rawValue) ?? .update)
        
        let operationNameDict: [Int: String] = [
            0: "IM5NotifyItemOperationUpdate",
            1: "IM5NotifyItemOperationDelete",
            2: "IM5NotifyItemOperationEdit",
            3: "IM5NotifyItemOperationRecall",
            4: "IM5NotifyItemOperationHistoryUpdate",
            5: "IM5NotifyItemOperationDecryptRecover",
            6: "IM5NotifyItemOperationRefMsgUpdate",
            7: "IM5NotifyItemOperationStreamingUpdate",
            8: "IM5NotifyItemOperationReactionUpdate",
            9: "IM5NotifyItemOperationASREdit",
            10: "IM5NotifyItemOperationDuplicatedUpdate"
        ]
        
        BuzIMLog.info("IM5MessageBatchNotify received im message \(message) 【operationType = \(operationNameDict[operationType.rawValue] ?? "unknown")】")
        
        let msgContentType = message.messageType
        
        // Handle OnAir messages
        if msgContentType == .onairStrickerGift || msgContentType == .onairSoundBoard {
            if msgContentType == .onairStrickerGift {
                BuzIMLog.info("[OnAir IM5MessageBatchNotify received]: gift -> \(message) 【operationType = \(operationNameDict[operationType.rawValue] ?? "unknown")】")
            } else {
                BuzIMLog.info("[OnAir IM5MessageBatchNotify received]: soundBoard -> \(message) 【operationType = \(operationNameDict[operationType.rawValue] ?? "unknown")】")
            }
            
            self.center?.notifyObservers(of: BuzIMCenterMessageObserver.self) { observer in
                observer.imInteractionMessageReceived?(center: self.center!, notifyItem: noticItem)
            }
            self.center?.messagePublisher.imInteractionMessageReceived.send((BuzIMCenter.center, noticItem))
            return
        }
        
        if msgContentType == .onAir_comment {
            self.center?.notifyObservers(of: BuzIMCenterMessageObserver.self) { observer in
                observer.imOnAirCommentMessageReceived?(center: self.center!, notifyItem: noticItem)
            }
            self.center?.messagePublisher.imOnAirCommentMessageReceived.send((BuzIMCenter.center, noticItem))
            return
        }
        
        var isWalkieTalkitMessage = [
            .walkieTalkie,
            .count_WalkieTalkie,
            .voice_Emoji,
            .compatibleVoiceEmoji,
            .voicegif
        ].contains(msgContentType)
        
        if msgContentType == .voice_Text || msgContentType == .new_Voice_Text,
           let content = item.message.messageContent as? BuzVoiceTextMessageContent,
           content.autoPlay {
            if !(content.isSendToBot?.boolValue == true && operationType == .edit) {
                isWalkieTalkitMessage = true
            }
        }
        
        let isMyselfSendMsg = im5Message.userInfo.userId == "\(BuzUserSession.shared.uid)"
        if isMyselfSendMsg {
            BuzIMLog.info("Recevice walkie talkie message isMyselfSendMsg: \(message)")
        }
        
        let isReaction = operationType == .reactionUpdate
        let isTranslateEdit = operationType.rawValue == BuzIMMessageOperation.translate.rawValue
        let isReferenceUpdate = operationType == .refMsgUpdate
        
        if isTranslateEdit, let content = item.message.messageContent as? BuzMessageContentProtocol {
            let editType = content.getEditType()
            BuzIMLog.debug("recevice translateEdit:\(String(describing: editType)), message:\(message)")
            //收到语音带有目标语言的编辑消息，他走的是asr的路径，因此要注意ASREdit这个函数
            switch editType {
            case .asr:
                self.center?.notifyObservers(of: BuzIMCenterMessageObserver.self) { observer in
                    observer.imASREditMessageReceived?(center: self.center!, notifyItem: noticItem)
                }
                self.center?.messagePublisher.imASREditMessageReceived.send((BuzIMCenter.center, noticItem))
            case .translate:
                self.center?.notifyObservers(of: BuzIMCenterMessageObserver.self) { observer in
                    observer.imTextTranslateEditMessageReceived?(center: self.center!, notifyItem: noticItem)
                }
                self.center?.messagePublisher.imTextTranslateEditMessageReceived.send((BuzIMCenter.center, noticItem))
            case .detectLanguage:
                self.center?.notifyObservers(of: BuzIMCenterMessageObserver.self) { observer in
                    observer.imTextDetectLanguageEditMessageReceived?(center: self.center!, notifyItem: noticItem)
                }
                self.center?.messagePublisher.imTextDetectLanguageEditMessageReceived.send((BuzIMCenter.center, noticItem))
            default:
                break
            }
        } else if isReaction {
            self.center?.notifyObservers(of: BuzIMCenterMessageObserver.self) { observer in
                observer.imReactionMessageReceived?(center: self.center!, notifyItem: noticItem)
            }
            self.center?.messagePublisher.imReactionMessageReceived.send((BuzIMCenter.center, noticItem))
        } else if isReferenceUpdate {
            self.center?.notifyObservers(of: BuzIMCenterMessageObserver.self) { observer in
                observer.imReferenceMessageUpdated?(center: self.center!, notifyItem: noticItem)
            }
            self.center?.messagePublisher.imReferenceMessageUpdated.send((BuzIMCenter.center, noticItem))
        } else if isWalkieTalkitMessage {
            if !isMyselfSendMsg {
                self.center?.notifyObservers(of: BuzIMCenterMessageObserver.self) { observer in
                    observer.imWalkieTalkieMessageReceived?(center: self.center!, notifyItem: noticItem)
                }
                self.center?.messagePublisher.imWalkieTalkieMessageReceived.send((BuzIMCenter.center, noticItem))
            }
        } else {
            self.center?.notifyObservers(of: BuzIMCenterMessageObserver.self) { observer in
                observer.imMessageReceived?(center: self.center!, notifyItem: noticItem)
            }
            self.center?.messagePublisher.imMessageReceived.send((BuzIMCenter.center, noticItem))
        }
    }
}
