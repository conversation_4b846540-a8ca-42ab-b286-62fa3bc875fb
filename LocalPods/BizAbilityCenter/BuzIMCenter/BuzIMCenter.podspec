#
#  Be sure to run `pod spec lint PCSocialCenter.podspec' to ensure this is a
#  valid spec and to remove all comments including this before submitting the spec.
#
#  To learn more about Podspec attributes see https://guides.cocoapods.org/syntax/podspec.html
#  To see working Podspecs in the CocoaPods repo see https://github.com/CocoaPods/Specs/
#

Pod::Spec.new do |s|
  
  s.name     = "BuzIMCenter"
  s.version  = "0.0.1"
  s.summary  = "A short description of BuzIMCenter."
  
  s.homepage = "https://gitlab.lizhi.fm/zhiyaPods/BuzIMCenter"
  s.license  = { :type => 'Copyright', :file => 'LICENSE' }
  s.author   = { 'shiqichao' => '<EMAIL>' }
  s.source   = { :git => "https://gitlab.lizhi.fm/zhiyaPods/BuzIMCenter.git", :tag => s.version.to_s }
  
  s.ios.deployment_target = '10.0'
  
  s.pod_target_xcconfig = {'DEFINES_MODULE' => 'YES'}
  
  s.source_files  = "Classes", "Classes/**/*.{h,m,swift}"
  
  s.dependency 'VoderXClient'
  s.dependency 'VoderXCryptClient'
  s.dependency 'VoderXE2EE'
  s.dependency 'VoderXIdempotent'
  
  s.dependency 'BuzIDL'
  s.dependency 'BuzLocalizable'
  s.dependency 'BuzAppConfiger'
  s.dependency 'LZDeviceUtil'
  s.dependency 'BuzMagicBox'
  s.dependency 'BuzNetworker'
  s.dependency 'BuzAppInfo'
  s.dependency 'BuzDataShareKit'
  s.dependency 'BuzUserSession'
  s.dependency 'BuzTracker'
  s.dependency 'ITNetLibrary'
  s.dependency 'BuzDataStore'
  s.dependency 'BuzLog'
  s.dependency 'LZDeviceUtil'
  s.dependency 'BuzUIKit'
  
end
