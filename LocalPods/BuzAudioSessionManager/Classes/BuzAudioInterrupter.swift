//
//  BuzAudioInterrupter.swift
//  BuzMediaPlayer
//
//  Created by lizhifm on 2/20/25.
//

import Foundation
import BuzAppConfiger
import AVFoundation
import BuzFoundation

public enum BuzMediaInterruptType : Int32 {
    case RouteChangeNewDeviceAvailable
    case RouteChangeOldDeviceUnavailable
    case RouteChangeOldDeviceUnavailableAndRecover
    case RouteChangeOverride
    case interruptBegin
    case interruptEndAndResume
}

open class BuzAudioInterrupter : NSObject {
    public var isInterrupting : Bool = false {
        didSet {
            if self.isInterrupting == false {
                self.previousIsPlayingBeforeInterrupt = false
                self.isInterruptByThirdParty = false
            }
            
//            if self.isInterrupting == oldValue {
//                return
//            }
//            
//            self.interruptStateChange()
        }
    }
    var isInterruptByThirdParty : Bool = false
    var previousIsPlayingBeforeInterrupt : Bool = false
    open var isPlaying = false
    public var routeChangeNeedResume = false
    
    open func enableHandleRouter() -> Bool {
        return false
    }
    
    
    private var interruptTimer : BuzTimer?
    private func addInterrupterTimer() {
        DispatchQueue.main.async {
            self.removeInterrupterTimer()
            BuzModuleLogType.BuzAudioSessionManager.log("BuzAudioInterrupter start timer.")
            self.interruptTimer = BuzTimer.scheduledTimer(timeInterval: 1.0, repeats: true, block: { [weak self] timer in
                guard let self = self else { return }
                
                if self.isInterruptByThirdParty && self.previousIsPlayingBeforeInterrupt {
                    self.recoverInterruptIfNeed()
                } else {
                    self.removeInterrupterTimer()
                }
            })
        }
    }
    
    private func recoverInterruptIfNeed() {
        BuzAudioSessionManager.checkIsPhoneCalling { isCalling in
//            BuzModuleLogType.BuzAudioSessionManager.debug("BuzAudioInterrupter recoverInterruptIfNeed \(isCalling) \(AVAudioSession.sharedInstance().secondaryAudioShouldBeSilencedHint)")
            if isCalling == false {
                if self.isInterruptByThirdParty && self.previousIsPlayingBeforeInterrupt {
                    self.endInterrupt()
                }
                BuzModuleLogType.BuzAudioSessionManager.log("BuzAudioInterrupter resume from timer.")
                self.removeInterrupterTimer()
            }
        }
    }
    
    private func removeInterrupterTimer() {
        self.interruptTimer?.invalidate()
        self.interruptTimer = nil
    }
    
    public override init() {
        super.init()
        // 注册播放打断通知
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(interruptionTypeChanged(_:)),
                                               name: AVAudioSession.interruptionNotification,
                                               object: nil)
        
        if self.enableHandleRouter() {
            ///路由变更
            NotificationCenter.default.addObserver(self,
                                                   selector: #selector(handleRouteChange),
                                                   name: AVAudioSession.routeChangeNotification,
                                                   object: nil)
        }
        
//        NotificationCenter.default.addObserver(self,
//                                               selector: #selector(handleSecondaryAudioHint(_:)),
//                                               name: AVAudioSession.silenceSecondaryAudioHintNotification,
//                                               object: nil)
//        NotificationCenter.default.addObserver(self,
//                                               selector: #selector(becomeActive(_:)),
//                                               name: UIApplication.willEnterForegroundNotification,
//                                               object: nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
//    open func interruptStateChange() {
//        
//    }
//    @objc
//    private func becomeActive(_ notification: NSNotification) {
//        if self.isInterruptByThirdParty && self.previousIsPlayingBeforeInterrupt {
//            try? LZAudioSession.sharedInstance()?.setActive(true)
//        }
//    }
    public func addInterruptTimerIfNeed() {
        if self.previousIsPlayingBeforeInterrupt && self.isInterruptByThirdParty {
            self.addInterrupterTimer()
        }
    }
    
    open func endInterrupt() {
        if self.isInterruptByThirdParty && self.previousIsPlayingBeforeInterrupt {
            self.isInterruptByThirdParty = false
            self.previousIsPlayingBeforeInterrupt = false
            
            self.interrupt(isAnotherAudioSuspend: false, type: .ended, resume:true)
            BuzAudioSessionManager.playOtherAppMusicIfNeed()
        }
    }
    
//    @objc
//    private func handleSecondaryAudioHint(_ notification: NSNotification) {
//        guard let userInfo = notification.userInfo, let type = userInfo[AVAudioSessionSilenceSecondaryAudioHintTypeKey] as? UInt else { return }
//        
//        if (type == AVAudioSession.SilenceSecondaryAudioHintType.end.rawValue) {
//            self.endInterrupt()
//        }
//    }
    
    public func clearInterruptState() {
        if self.isInterruptByThirdParty && self.previousIsPlayingBeforeInterrupt {
            self.isInterruptByThirdParty = false
            self.previousIsPlayingBeforeInterrupt = false
        }
        
        self.isInterrupting = false
    }
    
    @objc
    private func interruptionTypeChanged(_ notification: NSNotification) {
        guard let userInfo = notification.userInfo, let reasonValue = userInfo[AVAudioSessionInterruptionTypeKey] as? UInt else { return }
        switch reasonValue {
        case AVAudioSession.InterruptionType.began.rawValue:
            var isAnotherAudioSuspend = false /// 是否是被其他音频会话打断
            if #available(iOS 14.5, *) { /// iOS 14.5之后使用InterruptionReasonKey
                if let reasonKey = userInfo[AVAudioSessionInterruptionReasonKey] as? UInt {
                    switch reasonKey {
                    case AVAudioSession.InterruptionReason.default.rawValue: /// 因为另一个会话被激活,音频中断[闹钟、CallKit]
                        isAnotherAudioSuspend = true
                        break
                    case AVAudioSession.InterruptionReason.appWasSuspended.rawValue: /// 由于APP被系统挂起，音频中断。
                        break
                    case AVAudioSession.InterruptionReason.builtInMicMuted.rawValue: /// 音频因内置麦克风静音而中断(例如iPad智能关闭套iPad's Smart Folio关闭)
                        break
                    default:
                        break
                    }
                }
            } else {/// iOS 10.3-14.5，InterruptionWasSuspendedKey为true表示中断是由于系统挂起，false是被另一音频打断
                if let suspendedNumber: NSNumber = userInfo[AVAudioSessionInterruptionWasSuspendedKey] as? NSNumber {
                    isAnotherAudioSuspend = !suspendedNumber.boolValue
                }
            }
            
            self.previousIsPlayingBeforeInterrupt = self.isPlaying || self.routeChangeNeedResume
            self.isInterruptByThirdParty = true
            self.interrupt(isAnotherAudioSuspend: isAnotherAudioSuspend, type: .began)
            BuzModuleLogType.BuzAudioSessionManager.log("interrupt isAnotherAudioSuspend:\(isAnotherAudioSuspend) began.")
            
            if UIApplication.shared.applicationState == .background {
                DispatchQueue.main.asyncAfter(delay: 0.5) {
                    self.recoverInterruptIfNeed()
                }
            }
            
            break
        case AVAudioSession.InterruptionType.ended.rawValue:
            _ = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt
            if self.isInterruptByThirdParty && self.previousIsPlayingBeforeInterrupt {
                var resume = false
                self.isInterruptByThirdParty = false
                self.previousIsPlayingBeforeInterrupt = false
                // 检查是否可以恢复
                if let optionsValue = userInfo[AVAudioSessionInterruptionOptionKey] as? UInt {
                    let options = AVAudioSession.InterruptionOptions(rawValue: optionsValue)
                    if options.contains(.shouldResume) {
                        // 恢复播放
                        resume = true
                    }
                }
                
                self.removeInterrupterTimer()
                self.interrupt(isAnotherAudioSuspend: false, type: .ended, resume:resume)
                BuzModuleLogType.BuzAudioSessionManager.log("interrupt end.")
                BuzAudioSessionManager.playOtherAppMusicIfNeed()
            }
            break
        default:
            break
        }
    }
    
    
    @objc private func handleRouteChange(notification: Notification) {
        if let routeChangeReason = notification.userInfo?[AVAudioSessionRouteChangeReasonKey] as? UInt ,
        let reason = AVAudioSession.RouteChangeReason.init(rawValue: routeChangeReason) {
            switch reason {
                case .newDeviceAvailable: //蓝牙 + 有线耳机 连接
                    self.audioRouteChange(type: .RouteChangeNewDeviceAvailable)
                case .oldDeviceUnavailable: //有线耳机断开
                    if self.routeChangeNeedResume || self.isPlaying {
                        self.audioRouteChange(type: .RouteChangeOldDeviceUnavailableAndRecover)
                    } else {
                        self.audioRouteChange(type: .RouteChangeOldDeviceUnavailable)
                    }
                case .override: //蓝牙耳机断开
                    self.audioRouteChange(type: .RouteChangeOverride)
                default:
                    break
            }
        }
    }
    
    open func audioRouteChange(type : BuzMediaInterruptType) { }
    open func interrupt(isAnotherAudioSuspend : Bool, type : AVAudioSession.InterruptionType, resume : Bool = false) { }
}
