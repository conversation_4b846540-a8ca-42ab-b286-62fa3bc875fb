import Foundation
import LZAudioSessionManager
import BuzAppConfiger
import BuzRTCEngine
import BuzFoundation
import Call<PERSON><PERSON>

@objc public enum SessionSceneType : Int {
    //play
    case none = 0
    case pttGuide = 1
    case previewVideo = 2
    case playVideo = 3
    case frontAutoPlayVoice = 4
    case bgAutoPlayVoice = 5
    case manualPlayVoice = 6
    case bgKeepAlive = 7
    case manualGetVolume = 8
    case ringtong = 9
    case bgPttGuide = 10
    case bgRingtong = 11
    
    
    //record
    case voiceRecord = 101 //including history, home ptt
    case screenBlueRecord = 102
    case recordVideo = 103
    
    
    //interrupt
    case ptxInterrupt = 1003
    
    func scentTypeString() -> String {
        switch self {
        case .none:
            return "none"
        case .pttGuide:
            return "pttGuide"
        case .bgPttGuide:
            return "bgPttGuide"
        case .previewVideo:
            return "previewVideo"
        case .playVideo:
            return "playVideo"
        case .frontAutoPlayVoice:
            return "frontAutoPlayVoice"
        case .bgAutoPlayVoice:
            return "bgAutoPlayVoice"
        case .bgKeepAlive:
            return "bgKeepAlive"
        case .manualPlayVoice:
            return "manualPlayVoice"
        case .ringtong:
            return "ringtong"
        case .bgRingtong:
            return "bgRingtong"
        case .voiceRecord:
            return "voiceRecord"
        case .screenBlueRecord:
            return "screenBlueRecord"
        case .recordVideo:
            return "recordVideo"
        case .manualGetVolume:
            return "manualGetVolume"
        case .ptxInterrupt:
            return "ptxInterrupt"
        }
    }
}

public class BuzAudioSessionManager : NSObject {
    fileprivate static let shared : BuzAudioSessionManager = BuzAudioSessionManager.init()
    private static var audioScene : SessionSceneType = .none
    private static var activeCount : Int64 = 0
    private var deactiveDatas : [SessionSceneType] = []
    public static var canDeactiveAfterInterrupt : (() -> Bool)? = nil
    public static var isInterupting : (() -> Bool)? = nil
    public static var isDisableActive = false
    public static var isInChannel = false
    
//    override init() {
//        LZAudioSession.sharedInstance()?.disableActiveCounter()
//    }
    
    public static func setUpAudioSessionCategory(category : AVAudioSession.Category, options : AVAudioSession.CategoryOptions = []) {
        if #available(iOS 16.1, *), Self.isInChannel {
            if let audioSession = LZAudioSession.sharedInstance() {
                audioSession.lockForConfiguration()
                
                for _ in 0 ..< 3 {
                    do {
                        try audioSession.setCategory(category.rawValue, with: options)
                        BuzModuleLogType.BuzAudioSessionManager.log("finish play silence auido reset AVAudioSession.category = \(AVAudioSession.sharedInstance().category) ✅")
                        break
                    } catch  {
                        BuzModuleLogType.BuzAudioSessionManager.error("finish play silence auido reset AVAudioSession.category error = \(error) ❌")
                    }
                }
                
                audioSession.unlockForConfiguration()
            }
        }
    }
       
    @objc private func deactiveScenes() {
        self.deactiveDatas.forEach { type in
            do {
                BuzModuleLogType.BuzAudioSessionManager.log("actually dea_ctiveScene \(type)")
                try LZAudioSession.sharedInstance()?.deactiveScene(type.scentTypeString())
            } catch {
                BuzModuleLogType.BuzAudioSessionManager.log("deactiveScene \(type) failed.")
            }
        }
        
        self.deactiveDatas.removeAll()
        if BuzAudioSessionManager.canDeactiveAfterInterrupt?() ?? false && !(Self.isInterupting?() ?? false) {
            Self.setUpAudioSessionCategory(category: .playAndRecord,
                                           options: [.mixWithOthers, .allowBluetooth, .defaultToSpeaker])
        }
    }
    
    @objc public static func deactiveScene(_ type : SessionSceneType) {
        if type == .none || self.isDisableActive {
            return
        }
        
        let callback = {
            BuzAudioSessionManager.shared.deactiveDatas.append(type)
            NSObject.cancelPreviousPerformRequests(withTarget: BuzAudioSessionManager.shared)
            BuzAudioSessionManager.shared.perform(#selector(deactiveScenes), with: nil, afterDelay: 0.5)
        }
        
        Self.activeCount = Self.activeCount - 1
        
        if Self.activeCount == 0 {
            self.audioScene = .none
        }
//        || type == .recordVideo
        if type == .bgKeepAlive {
            if Thread.isMainThread {
                BuzAudioSessionManager.shared.deactiveDatas.append(type)
                NSObject.cancelPreviousPerformRequests(withTarget: BuzAudioSessionManager.shared)
                BuzAudioSessionManager.shared.deactiveScenes()
            } else {
                DispatchQueue.main.async {
                    BuzAudioSessionManager.shared.deactiveDatas.append(type)
                    NSObject.cancelPreviousPerformRequests(withTarget: BuzAudioSessionManager.shared)
                    BuzAudioSessionManager.shared.deactiveScenes()
                }
            }
        } else if Thread.isMainThread {
            callback()
        } else {
            DispatchQueue.main.async {
                callback()
            }
        }
    }
    
    static func checkIsPhoneCalling(complete : @escaping  (Bool) -> Void) {
        QueueManager.shared.ioQueue.async {
            let callObserver = CXCallObserver()
            let calls = callObserver.calls
            for call in calls {
                if !call.hasEnded{
                    DispatchQueue.main.async {
                        complete(true)
                    }
                    return
                }
            }
            DispatchQueue.main.async {
                complete(false)
            }
        }
    }
    
    public static func setup() {
        BuzRTCEngine.sharedInstance().weakDelegates.add(BuzAudioSessionManager.shared)
    }
    
    @objc public static func currentActiveScene() -> SessionSceneType {
        return Self.audioScene
    }
    
    @objc public static func playOtherAppMusicIfNeed() {
        if Self.activeCount == 0 && BuzAudioSessionManager.canDeactiveAfterInterrupt?() ?? false {
            // Todo: lby
            BuzAudioSessionManager.activeScene(.manualGetVolume)
            BuzAudioSessionManager.deactiveScene(.manualGetVolume)
        }
    }
    
    @discardableResult
    @objc public static func activeScene(_ type : SessionSceneType) -> Bool {
        if type == .none || self.isDisableActive {
            return false
        }
        
        var funcType = FTPlayback
        var mixType = MTNone
        var btType = BTHighQualityPlay
        var defaultToSpeaker = true
        var lowPriority = false
        
        switch type {
        case .none:
            break
        case .previewVideo, .playVideo, .ringtong:
            funcType = FTPlaybackExt
//            mixType = MTDuckOthers
            break
        case .pttGuide, .frontAutoPlayVoice, .manualPlayVoice:
            funcType = FTPlaybackExt
            mixType = MTDuckOthers
        case .ptxInterrupt:
            funcType = FTPlaybackExtAndRecord
            mixType = MTBackgroundNormal
            btType = BTRecordOrPlay
            defaultToSpeaker = false
        case .bgAutoPlayVoice, .bgRingtong://, .pttGuide, .frontAutoPlayVoice, .manualPlayVoice, .ringtong, .previewVideo, .playVideo:
            funcType = FTPlaybackExt
            mixType = MTDuckOthers
        case .bgPttGuide://, .pttGuide, .frontAutoPlayVoice, .manualPlayVoice, .ringtong, .previewVideo, .playVideo:
            funcType = FTPlaybackExt
            mixType = MTNormal
        case .bgKeepAlive:
            funcType = FTPlaybackExt
            mixType = MTNormal
            lowPriority = true
        case .voiceRecord, .recordVideo:
            funcType = FTPlaybackExtAndRecord
            btType = BTRecordOrPlay
            defaultToSpeaker = false
        case .screenBlueRecord:
            funcType = FTPlaybackExtAndRecord
            btType = BTRecordOrPlay
            mixType = MTDuckOthers
            defaultToSpeaker = false
        case .manualGetVolume:
            funcType = FTPlayback
            mixType = MTNormal
        }
        
        let config = LZAudioSessionSceneConfig.init()
        config.funcType = funcType
        config.mixType = mixType
        config.btType = btType
        config.defaultToSpeaker = defaultToSpeaker
        config.lowPriority = lowPriority
        config.setByForce = true
        Self.audioScene = type
    
        if let audioSession = LZAudioSession.sharedInstance() {
            do {
                try audioSession.activeScene(type.scentTypeString(), config: config)
                BuzModuleLogType.BuzAudioSessionManager.log("activeScene \(type)")
            } catch {
                BuzModuleLogType.BuzAudioSessionManager.error("catch activeScene fail.")
                
                audioSession.lockForConfiguration()
                
                if !audioSession.isInUsed() {
                    do {
                        try audioSession.setActivePlain(false)
                        BuzModuleLogType.BuzAudioSessionManager.log("catch setActivePlain success")
                    } catch  {
                        BuzModuleLogType.BuzAudioSessionManager.error("catch setActivePlain error = \(error) ❌")
                    }
                }
                
                audioSession.unlockForConfiguration()
                
                do {
                    try audioSession.activeScene(type.scentTypeString(), config: config)
                    BuzModuleLogType.BuzAudioSessionManager.log("catch activeScene \(type)")
                } catch {
                    BuzModuleLogType.BuzAudioSessionManager.log("catch Setting category to AVAudioSessionCategoryPlayback failed.")
                    return false
                }
            }
            
            Self.activeCount = Self.activeCount + 1
            
//            if defaultToSpeaker {
//                audioSession.lockForConfiguration()
//                do {
//                    try audioSession.overrideOutputAudioPort(.speaker)
//                }catch {
//                    BuzModuleLogType.BuzAudioSessionManager.log("overrideOutputAudioPort failed.")
//                }
//                audioSession.unlockForConfiguration()
//            }
        }
        
        return true
    }
}

extension BuzAudioSessionManager : BuzRTCEngineDelegate {
    public func rtcEngine(_ engine: BuzRTCEngine, beginExecJoinChannel channel: RTCChannelModel, isMuteMic: Bool) {
        Self.audioScene = .none
        Self.activeCount = 0
        if Thread.isMainThread {
            NSObject.cancelPreviousPerformRequests(withTarget: BuzAudioSessionManager.shared)
            BuzAudioSessionManager.shared.deactiveDatas.removeAll()
        } else {
            DispatchQueue.main.async {
                NSObject.cancelPreviousPerformRequests(withTarget: BuzAudioSessionManager.shared)
                BuzAudioSessionManager.shared.deactiveDatas.removeAll()
            }
        }
        
        Self.isDisableActive = true
        LZAudioSession.sharedInstance()?.disableActiveCounter()
        BuzModuleLogType.BuzAudioSessionManager.log("disableActiveCounter")
    }
    
    public func rtcEngine(_ engine: BuzRTCEngine, didLeaveChannel channel: RTCChannelModel) {
        LZAudioSession.sharedInstance()?.enableActiveCounter()
        Self.isDisableActive = false
        BuzModuleLogType.BuzAudioSessionManager.log("enableActiveCounter")
    }
}
