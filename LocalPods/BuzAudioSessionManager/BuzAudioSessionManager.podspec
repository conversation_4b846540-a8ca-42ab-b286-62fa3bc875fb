Pod::Spec.new do |s|
  s.name         = 'BuzAudioSessionManager'
  s.summary      = 'High performance model framework for iOS/OSX.'
  s.version      = '1.0.0'
  s.license      = { :type => 'MIT', :file => 'LICENSE' }
  s.authors      = { 'ibireme' => '<EMAIL>' }
  s.social_media_url = 'http://blog.ibireme.com'
  s.homepage     = 'https://github.com/ibireme/YYModel'

  s.ios.deployment_target = '6.0'
  s.osx.deployment_target = '10.7'
  s.watchos.deployment_target = '2.0'
  s.tvos.deployment_target = '9.0'

  s.source       = { :git => 'https://github.com/ibireme/YYModel.git', :tag => s.version.to_s }
  
  s.requires_arc = true
  s.source_files = 'Classes/*.{swift}'

  s.dependency 'LZAudioSessionManager'
  s.dependency 'BuzAppConfiger'
  s.dependency 'BuzRTCEngine'
end
