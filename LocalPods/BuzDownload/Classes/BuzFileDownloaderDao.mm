//
//  BuzFileDownloaderDat.m
//  BuzDownload
//
//  Created by Ha D on 2025/6/3.
//

#import "BuzFileDownloaderDao.h"
#import "BuzFileDownloaderEntity+WCTTableCoding.h"
#import "WCDB.h"
#import "BuzFileDownloaderDao+Bridge.h"

@interface BuzFileDownloaderDao ()
@property (nonatomic, strong) WCTDatabase *database;
@end

@implementation BuzFileDownloaderDao

+ (NSString *)tableName {
    return @"FileDownloader";
}

+ (instancetype)sharedInstance {
    static BuzFileDownloaderDao *instance = nil;
    if (instance == nil) {
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            instance = [[self alloc] init];
            instance.database = [[WCTDatabase alloc] initWithPath:BuzFileDownloaderDao.dbPath];
            [instance.database createTableAndIndexesOfName:BuzFileDownloaderDao.tableName
                                                 withClass:BuzFileDownloaderEntity.class];
        });
    }
    return instance;
}

/** 更新群数据**/
- (void)addOrUpdate:(NSArray <BuzFileDownloaderEntity *>*)entities
           complete:(nullable void (^)(BOOL))complete {
    if (entities.count == 0) {
        dispatch_async(dispatch_get_main_queue(), ^{
            if (complete) {
                complete(true);
            }
        });
    } else {
        dispatch_async(BuzFileDownloaderDao.dbQueue, ^{
            BOOL isUpdateSuccess = [self.database runTransaction:^BOOL{
                return [self.database insertOrReplaceObjects:entities
                                                        into:BuzFileDownloaderDao.tableName];
            }];
            
            dispatch_async(dispatch_get_main_queue(), ^{
                if (complete) {
                    complete(isUpdateSuccess);
                }
            });
        });
    }
}

/** get all entities according to the userId, limit day information**/
- (void)allEntitiesWithComplete:(nullable void (^)(NSArray<BuzFileDownloaderEntity *> *))complete {
    dispatch_async(BuzFileDownloaderDao.dbQueue, ^{
        NSArray<BuzFileDownloaderEntity *> *results = [self.database getAllObjectsOfClass:BuzFileDownloaderEntity.class
                                                                                fromTable:BuzFileDownloaderDao.tableName];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (complete) {
                complete(results);
            }
        });
    });
}

- (void)allEntitiesWithUserId:(int64_t)userId
                     Complete:(nullable void (^)(NSArray<BuzFileDownloaderEntity *> *))complete {
    dispatch_async(BuzFileDownloaderDao.dbQueue, ^{
        NSArray<BuzFileDownloaderEntity *> *results = [self.database getObjectsOfClass:BuzFileDownloaderEntity.class
                                                                             fromTable:BuzFileDownloaderDao.tableName
                                                                                 where:BuzFileDownloaderEntity.userId == userId];
        
        dispatch_async(dispatch_get_main_queue(), ^{
            if (complete) {
                complete(results);
            }
        });
    });
}

@end
