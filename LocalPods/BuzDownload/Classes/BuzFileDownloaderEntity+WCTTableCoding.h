//
//  BuzFileDownloaderEntity+WCTTableCoding.h
//  buz
//
//  Created by Ha <PERSON> on 2025/6/3.
//  Copyright © 2025 lizhi. All rights reserved.
//

#import "BuzFileDownloaderEntity.h"
#import "WCDB.h"

NS_ASSUME_NONNULL_BEGIN

@interface BuzFileDownloaderEntity (WCTTableCoding)<WCTTableCoding>

WCDB_PROPERTY(totalLength)
WCDB_PROPERTY(remoteUrl)
WCDB_PROPERTY(filename)
WCDB_PROPERTY(md5)
WCDB_PROPERTY(startTime)
WCDB_PROPERTY(updateTime)
WCDB_PROPERTY(userId)
WCDB_PROPERTY(targetId)
WCDB_PROPERTY(source)
WCDB_PROPERTY(status)
WCDB_PROPERTY(priority)

@end

NS_ASSUME_NONNULL_END
