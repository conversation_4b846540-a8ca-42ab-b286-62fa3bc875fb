//
//  BuzFileDownloaderEntity.h
//  buz
//
//  Created by Ha <PERSON> on 2025/6/3.
//  Copyright © 2025 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN
///don't modify the value, just add status, value had been saved into db
typedef NS_ENUM(NSUInteger, BuzFileDownloaderStatus) {
    BuzFileDownloaderStatusIDLE = 0,
    BuzFileDownloaderStatusPENDING, // added to queue, hasn't started
    BuzFileDownloaderStatusSTARTED, // added to queue and started
    BuzFileDownloaderStatusPAUSED, // added to queue and pause
    BuzFileDownloaderStatusCANCEL,
    BuzFileDownloaderStatusFAILURE,
    BuzFileDownloaderStatusSUCCESS
};

@interface BuzFileDownloaderEntity : NSObject

@property (nonatomic, assign) int64_t totalLength;
@property (nonatomic, assign) int64_t startTime;
@property (nonatomic, assign) int64_t updateTime;
@property (nonatomic, assign) int64_t userId;
@property (nonatomic, assign) int64_t targetId;
@property (nonatomic, strong) NSString *remoteUrl;
@property (nonatomic, strong) NSString *filename;
@property (nonatomic, strong) NSString *md5;
@property (nonatomic, strong) NSString *source;

@property (nonatomic, assign) int64_t priority;
@property (nonatomic, assign) BuzFileDownloaderStatus status;

@end

NS_ASSUME_NONNULL_END
