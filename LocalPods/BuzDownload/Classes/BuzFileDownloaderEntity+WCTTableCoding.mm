//
//  BuzFileDownloaderEntity+WCTTableCoding.m
//  buz
//
//  Created by Ha <PERSON> on 2025/6/3.
//  Copyright © 2025 lizhi. All rights reserved.
//

#import "BuzFileDownloaderEntity+WCTTableCoding.h"

@implementation BuzFileDownloaderEntity (WCTTableCoding)
WCDB_IMPLEMENTATION(BuzFileDownloaderEntity)

WCDB_SYNTHESIZE(BuzFileDownloaderEntity, totalLength)
WCDB_SYNTHESIZE(BuzFileDownloaderEntity, remoteUrl)
WCDB_SYNTHESIZE(BuzFileDownloaderEntity, filename)
WCDB_SYNTHESIZE(BuzFileDownloaderEntity, md5)
WCDB_SYNTHESIZE(BuzFileDownloaderEntity, startTime)
WCDB_SYNTHESIZE(BuzFileDownloaderEntity, updateTime)
WCDB_SYNTHESIZE(BuzFileDownloaderEntity, userId)
WCDB_SYNTHESIZE(BuzFileDownloaderEntity, targetId)
WCDB_SYNTHESIZE(BuzFileDownloaderEntity, source)
WCDB_SYNTHESIZE(BuzFileDownloaderEntity, status)
WCDB_SYNTHESIZE(BuzFileDownloaderEntity, priority)

WCDB_PRIMARY(BuzFileDownloaderEntity, remoteUrl)

@end
