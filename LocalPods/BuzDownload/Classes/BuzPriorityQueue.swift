//
//  PriorityQueue.swift
//  buz
//
//  Created by Ha D on 2025/6/3.
//  Copyright © 2025 lizhi. All rights reserved.
//

struct BuzPriorityQueue<Element: Comparable> {
    private var elements: [Element] = []
    private let comparator: (Element, Element) -> Bool
    
    // 初始化方法（默认最大堆）
    init(sort: @escaping (Element, Element) -> Bool = { $0 > $1 }) {
        self.comparator = sort
    }
    
    // 判断队列是否为空
    var isEmpty: Bool {
        return elements.isEmpty
    }
    
    // 获取队列元素数量
    var count: Int {
        return elements.count
    }
    
    // 查看队首元素（优先级最高）
    func peek() -> Element? {
        return elements.first
    }
    
    // 插入元素
    mutating func enqueue(_ element: Element) {
        elements.append(element)
        siftUp(from: elements.count - 1)
    }
    
    // 移除队首元素
    mutating func dequeue() -> Element? {
        guard !isEmpty else { return nil }
        guard count > 1 else { return elements.removeLast() }
        
        elements.swapAt(0, count - 1)
        let element = elements.removeLast()
        siftDown(from: 0)
        return element
    }
    
    // 上浮操作（维护堆性质）
    private mutating func siftUp(from index: Int) {
        var child = index
        var parent = parentIndex(of: child)
        
        while child > 0 && comparator(elements[child], elements[parent]) {
            elements.swapAt(child, parent)
            child = parent
            parent = parentIndex(of: child)
        }
    }
    
    // 下沉操作（维护堆性质）
    private mutating func siftDown(from index: Int) {
        var parent = index
        
        while true {
            let left = leftChildIndex(of: parent)
            let right = rightChildIndex(of: parent)
            var candidate = parent
            
            // 找出最大/最小的子节点
            if left < count && comparator(elements[left], elements[candidate]) {
                candidate = left
            }
            if right < count && comparator(elements[right], elements[candidate]) {
                candidate = right
            }
            
            if candidate == parent {
                return  // 已满足堆性质
            }
            
            elements.swapAt(parent, candidate)
            parent = candidate
        }
    }
    
    // 辅助方法：计算父节点索引
    private func parentIndex(of index: Int) -> Int {
        return (index - 1) / 2
    }
    
    // 辅助方法：计算左子节点索引
    private func leftChildIndex(of index: Int) -> Int {
        return 2 * index + 1
    }
    
    // 辅助方法：计算右子节点索引
    private func rightChildIndex(of index: Int) -> Int {
        return 2 * index + 2
    }
}
