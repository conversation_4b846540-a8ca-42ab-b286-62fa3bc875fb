//
//  BuzFileDownloaderDat.h
//  BuzDownload
//
//  Created by Ha D on 2025/6/3.
//

#import <Foundation/Foundation.h>
#import "BuzFileDownloaderEntity.h"

NS_ASSUME_NONNULL_BEGIN

@interface BuzFileDownloaderDao : NSObject

+ (instancetype)sharedInstance;

/** 更新群数据**/
- (void)addOrUpdate:(NSArray <BuzFileDownloaderEntity *>*)entities
           complete:(nullable void (^)(BOOL))complete;

/** get all entities**/
- (void)allEntitiesWithComplete:(nullable void (^)(NSArray<BuzFileDownloaderEntity *> *))complete;

/** get all entities according to the userId**/
- (void)allEntitiesWithUserId:(int64_t)userId
                     Complete:(nullable void (^)(NSArray<BuzFileDownloaderEntity *> *))complete;
@end

NS_ASSUME_NONNULL_END
