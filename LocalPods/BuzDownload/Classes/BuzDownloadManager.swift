//
//  BuzDownloadManager.swift
//  buz
//
//  Created by <PERSON> <PERSON> on 2025/6/3.
//  Copyright © 2025 lizhi. All rights reserved.
//
import Combine
import LZFileDownload
import BuzConfig
import BuzAppConfiger

public class BuzFileUtil : NSObject {
    fileprivate class func filePath(url : String, filename : String) -> String {
        return Self.directory(url : url) + "/" + filename
    }
    
    fileprivate class func directory(url : String) -> String {
        return BuzConfig.fileCacheDir + "/" + url.md5()
    }
}

public class BuzFileDownloader : NSObject, Comparable {
    var onStatusChange : ((BuzFileDownloaderStatus, BuzFileDownloader) -> Void)?
    
    public static func < (lhs: BuzFileDownloader, rhs: BuzFileDownloader) -> Bool {
        return lhs.entity.priority < rhs.entity.priority
    }
    
    public var status : BuzFileDownloaderStatus {
        didSet {
            self.entity.status = self.status
            BuzModuleLogType.BuzDownload.log(" \(self.entity.remoteUrl) switch status from \(oldValue) to \(self.status)")
            self.onStatusChange?(oldValue, self)
        }
    }
    
    public let entity : BuzFileDownloaderEntity
    
    func checkForUpdate( progress : ((UInt64, UInt64, CGFloat) -> Void)?, callback : ((String?) -> Void)? ) {
        self.status = .STARTED
        let dir = BuzConfig.fileCacheDir as NSString
        
        LZURLSessionMgr.instance().downloadFile(withURL: self.entity.remoteUrl,
                                                directory: dir.replacingOccurrences(of: NSHomeDirectory(), with: ""), //get relative directory
                                                filename: self.entity.remoteUrl.lastPathComponent) { receivedSize, expectedSize, progressVal in
            progress?(receivedSize, expectedSize, progressVal)
        } completion: { data, url, toFilePath, error, isPause in
            self.status = error != nil ? (isPause ? .PAUSED : .FAILURE) : .SUCCESS
            callback?(toFilePath)
        }
    }
    
    func resume() {
        self.status = .STARTED
        LZURLSessionMgr.instance().resumeDownloadTask(withURL: self.entity.remoteUrl)
    }
    
    func canResuem() -> Bool {
        return LZURLSessionMgr.instance().canResume(self.entity.remoteUrl)
    }
    
    func cancel() {
        if self.status == .STARTED {
            LZURLSessionMgr.instance().cancelDownloadTask(withURL: self.entity.remoteUrl)
        }
        
        self.status = .CANCEL
    }
    
    func pause() {
        if self.status == .STARTED {
            LZURLSessionMgr.instance().pauseDownloadTask(withURL: self.entity.remoteUrl)
        }
        
        self.status = .PAUSED
    }
    
    func pending() {
        if self.status == .STARTED {
            LZURLSessionMgr.instance().pauseDownloadTask(withURL: self.entity.remoteUrl)
        }
        
        self.status = .PENDING
    }
    
    init(entity: BuzFileDownloaderEntity) {
        self.entity = entity
        self.status = entity.status
    }
}

// MARK: - Combine Publisher
extension BuzFileDownloadManager {
    public class Publisher {
        public var statusDidChange = PassthroughSubject<(BuzFileDownloaderStatus, BuzFileDownloader), Never>()
        public var progressDidChange = PassthroughSubject<(BuzFileDownloader, CGFloat), Never>()
    }
}

public class BuzFileDownloadManager : NSObject {
    private static let invalidId : Int64 = -1
    private static let maxDownloadings = 5
    private var downloadMap : [String : BuzFileDownloader] = [:]
    private var entityMap : [String : BuzFileDownloaderEntity] = [:]
    private var userId : Int64 = BuzFileDownloadManager.invalidId
    private var cancellables = Set<AnyCancellable>()
    private var callbacks : [String : [((BuzFileDownloader) -> Void?)]] = [:]
    public static let shared : BuzFileDownloadManager = BuzFileDownloadManager()
    public let publisher : Publisher = BuzFileDownloadManager.Publisher()
    
    private func nowTs() -> Int64 {
        return Int64(Date().timeIntervalSince1970 * 1000)
    }
    
    public class func downloadUrlEncode(_ url : String,
                                 filename : String? = nil) -> String {
        if url.contains("?") {
            return url + "&fileId=" + (filename?.md5() ?? "default")
        } else {
            return url + "?fileId=" + (filename?.md5() ?? "default")
        }
    }
    
    public func progress(_ url : String) -> CGFloat {
        if let entity = self.entityMap[url], entity.totalLength != 0 {
           let progress = min(1.0, CGFloat(LZURLSessionMgr.instance().filesize(for: url, filename: url.lastPathComponent)) / CGFloat(entity.totalLength))
//            NSLog("ASKDJHALKSJHDKLAJHDLKAHSJDLKA DHALSD  \(url.lastPathComponent) \(progress) \(entity.totalLength)")
            return progress
        }
        
        return 0.0
    }
    
    public func filePath(_ url : String) -> String? {
        let dir = BuzFileUtil.directory(url: url)
        let childFiles = try? FileManager.default.contentsOfDirectory(atPath: dir)
        
        if let filename = childFiles?.first {
            return dir.appendingPathComponent(filename)
        }
        
        return nil
    }
    
    public func login(userId : Int64) {
        self.userId = userId
        
        self.publisher.statusDidChange.sink { [weak self] (preStatus, downloader) in
            guard let self = self else {return}
            ///delay 0.2 to avoid other process are using the enqueue function
            DispatchQueue.main.delay(0.2) {
                if preStatus == .STARTED &&
                    (downloader.entity.status == .PAUSED || downloader.entity.status == .CANCEL || downloader.entity.status == .SUCCESS || downloader.entity.status == .FAILURE) {
                    if self.downloadMap.filter({ (key: String, value: BuzFileDownloader) in
                        return value.entity.status == .STARTED
                    }).count < Self.maxDownloadings {
                        self.downloadMaximunPriority()
                    }
                }
            }
        }.store(in: &cancellables)
    }
    
    public func setupDownload(entities: [BuzFileDownloaderEntity], allEntities : [BuzFileDownloaderEntity]) {
        allEntities.forEach { entity in
            self.entityMap[entity.remoteUrl] = entity
        }
        
        entities.forEach { entity in
            BuzFileDownloadManager.shared.addDownloaderWithEntity(entity)
        }
        
        let downloadAbleCount = Self.maxDownloadings - self.downloadMap.count(where: { (key: String, value: BuzFileDownloader) in
            return value.entity.status == .STARTED
        })
        
        if downloadAbleCount > 0 {
            for _ in 0..<downloadAbleCount {
                BuzFileDownloadManager.shared.downloadMaximunPriority()
            }
        }
        
        let dir = LZURLSessionUtil.sessionTempDownloadPath() ?? ""
        let childFiles = try? FileManager.default.contentsOfDirectory(atPath: dir)
        
        // 递归删除每个子项
        if let contents = childFiles {
            let now = Date()
            
            for filename in contents {
                var find = false
                let filePath = dir.appendingPathComponent(filename)
                entities.forEach { entity in
                    if entity.status == .PAUSED || entity.status == .PENDING || entity.status == .STARTED {
                        if let path = LZURLSessionUtil.tempFilePath(atURL: entity.remoteUrl, filename: entity.remoteUrl.lastPathComponent) {
                            if path == filePath {
                                find = true
                                
                                if entity.status == .PAUSED {
                                    do {
                                        let attributes = try FileManager.default.attributesOfItem(atPath: filePath)
                                        
                                        if let date = attributes[.modificationDate] as? Date {
                                            let timeInterval = abs(now.timeIntervalSince(date))
                                            // 将秒转换为天数，判断是否 ≤ 30 天
                                            let daysDifference = timeInterval / (24 * 60 * 60)
                                            find = daysDifference <= 15
                                        }
                                    } catch {
                                        BuzModuleLogType.BuzDownload.error(" 获取文件属性失败: " + error.localizedDescription)
                                    }
                                }
                            }
                        }
                    }
                }
                
                if !find {
                    try? FileManager.default.removeItem(atPath: filePath)
                }
            }
        }
    }
    
    //download
    @discardableResult
    private func addDownloaderWithEntity(_ entity:BuzFileDownloaderEntity) -> BuzFileDownloader {
        let downloader = BuzFileDownloader.init(entity: entity)
        self.downloadMap[entity.remoteUrl] = downloader
        self.entityMap[entity.remoteUrl] = entity
        
        downloader.onStatusChange = { [weak self] previsousStatus, value in
            guard let self = self else { return }
            let remoteUrl = value.entity.remoteUrl
            BuzFileDownloaderDao.sharedInstance().addOrUpdate([value.entity])
            
            if let callbacks = self.callbacks[remoteUrl] {
                callbacks.forEach { callback in
                    callback(value)
                }
                
                self.callbacks.removeValue(forKey: remoteUrl)
            }
            
            self.publisher.statusDidChange.send((previsousStatus, value))
        }
        
        return downloader
    }
    
    public func logout() {
        var entities : [BuzFileDownloaderEntity] = []
        self.userId = BuzFileDownloadManager.invalidId
        LZURLSessionMgr.instance().cancelAllTasks(true)
        self.downloadMap.forEach { (key: String, value: BuzFileDownloader) in
            entities.append(value.entity)
        }
        
        self.downloadMap.removeAll()
        BuzFileDownloaderDao.sharedInstance().addOrUpdate(entities)
    }
    
    public func get(url : String) -> BuzFileDownloaderEntity? {
        return self.entityMap[url]
    }
    
    //don't call this function before login()
    @discardableResult
    public func start(url : String,
                      filename : String,
                      totalLength : Int64,
                      targetId : Int64,
                      source : String,
                      manualCallback : ((BuzFileDownloader) -> Void)? = nil) -> BuzFileDownloader? { //sessionKey : use it for identifying storage id
        if self.userId == BuzFileDownloadManager.invalidId {
            BuzModuleLogType.BuzDownload.error(" haven't call login userId " + url)
            return nil
        }
        
        if let value = self.downloadMap[url],
            value.entity.status == .SUCCESS,
           FileManager.default.fileExists(atPath: BuzFileUtil.filePath(url: url, filename: filename)) {
            manualCallback?(value)
            return value
        }
        
        if let manualCallback = manualCallback {
            if var calls = self.callbacks[url] {
                calls.append(manualCallback)
            } else {
                self.callbacks[url] = [manualCallback]
            }
        }
        
        if let value = self.downloadMap[url], value.entity.status == .STARTED {
            return value
        }
        
        let ts = self.nowTs()
        ///already in queue, not started, update priority, else add task
        if let downloader = self.downloadMap[url] {
            downloader.entity.priority = ts
            
            if downloader.entity.status != .STARTED {
                downloader.status = .PENDING
            }
        } else {
            let entity = BuzFileDownloaderEntity.init()
            entity.totalLength = totalLength
            entity.remoteUrl = url
            entity.md5 = url.md5()
            entity.startTime = ts
            entity.updateTime = ts
            entity.userId = self.userId
            entity.targetId = targetId
            entity.source = source
            entity.priority = ts
            entity.filename = filename
            let downloader = self.addDownloaderWithEntity(entity)
            downloader.status = .PENDING
        }
        
        self.downloadMaximunPriority()
        return self.downloadMap[url]
    }
    
    public func pauseTastUntil(callback : ((Int, [BuzFileDownloaderEntity], @escaping ((Bool)) -> Void) -> Void)) {
        var downloadStatusMap : [String:BuzFileDownloaderStatus] = [:]
        var downloadingCount : Int = 0
        var entities : [BuzFileDownloaderEntity] = []
        
        self.downloadMap.forEach { (key: String, value: BuzFileDownloader) in
            downloadStatusMap[key] = value.status
        }
        
        self.downloadMap.forEach { (key: String, value: BuzFileDownloader) in
            if value.status == .STARTED || value.status == .PENDING {
                value.pause()
                downloadingCount = downloadingCount + 1
                entities.append(value.entity)
            }
        }
        
        callback(downloadingCount, entities) { continueDownload in
            if continueDownload {
                downloadStatusMap.forEach { (key: String, value: BuzFileDownloaderStatus) in
                    if value == .STARTED {
                        self.downloadMap[key]?.resume()
                    } else {
                        self.downloadMap[key]?.status = .PENDING
                    }
                }
            }
        }
    }
    
    private func downloadMaximunPriority() {
        let downloadings = self.downloadMap.filter { (key: String, value: BuzFileDownloader) in
            return value.entity.status == .STARTED
        }.values
        
        ///check whether the downloading task reach max count, if true pause the minimum priority task to ensure the latest one can download right now
        if downloadings.count >= Self.maxDownloadings {
            if let value = downloadings.min(by: { v1, v2 in
                return v1.entity.priority < v2.entity.priority
            }) {
                value.pending()
            }
        }
        
        ///download the maximum priority task
        if let maxItem = self.downloadMap.values.filter({ value in
            return value.entity.status == .PENDING
        }).max(by: { v1, v2 in
            return v1.entity.priority > v2.entity.priority
        }) {
            ///be paused before
            if maxItem.canResuem() {
                maxItem.resume()
            } else if maxItem.entity.status == .PENDING {
                maxItem.checkForUpdate { [weak maxItem, weak self] receivedSize, expectedSize, progressVal in
                    guard let maxItem = maxItem else { return }
                    guard let self = self else { return }
                    self.publisher.progressDidChange.send((maxItem, progressVal))
                } callback: { [weak maxItem] toFilePath in
                    guard let maxItem = maxItem else { return }
                    let directory = BuzFileUtil.directory(url: maxItem.entity.remoteUrl)
                    let filepath = BuzFileUtil.filePath(url: maxItem.entity.remoteUrl, filename: maxItem.entity.filename)
                    var targetExist = false
                    
                    if let toFilePath = toFilePath, FileManager.default.fileExists(atPath: toFilePath) {
                        targetExist = true
                        
                        if maxItem.status == .SUCCESS {
                            if FileManager.default.fileExists(atPath: filepath) {
                                BuzModuleLogType.BuzDownload.log("file already exist for:" + maxItem.entity.remoteUrl)
                            } else {
                                do {
                                    try FileManager.default.createDirectory(atPath: directory, withIntermediateDirectories: true)
                                    try FileManager.default.moveItem(atPath: toFilePath,
                                                                     toPath: filepath)
                                    BuzModuleLogType.BuzDownload.log("download file to:" + toFilePath)
                                } catch {
                                    BuzModuleLogType.BuzDownload.debug("download file success, move error: \(maxItem.entity.remoteUrl)")
                                }
                            }
                        } else {
                            BuzModuleLogType.BuzDownload.debug("download file error: \(maxItem.entity.remoteUrl)")
                        }
                        
                        ///clear cache
                        try? FileManager.default.removeItem(atPath: toFilePath)
                    }
                    
                    BuzModuleLogType.BuzDownload.debug("download file status: \(maxItem.status)  file exist:\(targetExist)")
                }
                
                BuzModuleLogType.BuzDownload.debug("start download pendingstatus : \(maxItem.entity.remoteUrl)")
            }
        }
        
#if DEBUG
        self.downloadMap.forEach { (key: String, value: BuzFileDownloader) in
            if value.entity.status == .STARTED {
                BuzModuleLogType.BuzDownload.debug(" downloadMaximunPriority DOWNLOADING : \(value.entity.remoteUrl)")
            } else if value.entity.status == .PENDING {
                BuzModuleLogType.BuzDownload.debug(" downloadMaximunPriority PENDING : \(value.entity.remoteUrl)")
            }
        }
#endif
    }
    
    //private func resume(url : String) -> BuzDownloader //only resume, judge status to determine call start or resume
    @discardableResult
    public func pause(url : String) -> BuzFileDownloader? {
        if let downloader = self.downloadMap[url] {
            downloader.pause()
            return downloader
        } else {
            LZURLSessionMgr.instance().pauseDownloadTask(withURL: url)
        }
        
        return nil
    }
    
    @discardableResult
    public func cancel(url : String) -> BuzFileDownloader? {
        var download : BuzFileDownloader?
        if let downloader = self.downloadMap[url] {
            downloader.cancel()
            download = downloader
        }
        
        DispatchQueue.main.async {
            if let path = LZURLSessionUtil.tempFilePath(atURL: url, filename: url.lastPathComponent) {
                do {
                    try FileManager.default.removeItem(atPath: path)
                    BuzModuleLogType.BuzDownload.log("cancel remove file:" + path)
                } catch {
                    BuzModuleLogType.BuzDownload.log("cancel remove file error: \(path)")
                }
            }
        }
        
        return download
    }
    
    //待确定，可能会用increasePriority + 下载池决定优先级
    func increasePriority(url : String) -> Void { //在当前UI展示时调用可增加下载响应速度
        if let downloader = self.downloadMap[url] {
            downloader.entity.priority = self.nowTs()
            self.downloadMaximunPriority()
        }
    }
    
    //待确定，可能会用lowestPriorityIfNeed + 下载池决定优先级
    func lowestPriorityIfNeed(url : String) -> Void { //在不再需要显示时调用=pause+priority，会将任务拉到最后
        if let downloader = self.downloadMap[url] {
            downloader.entity.priority = 0
        }
    }
}
