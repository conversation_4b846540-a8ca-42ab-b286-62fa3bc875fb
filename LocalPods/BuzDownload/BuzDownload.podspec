Pod::Spec.new do |s|
  s.name         = 'BuzDownload'
  s.summary      = 'High performance model framework for iOS/OSX.'
  s.version      = '1.0.0'
  s.license      = { :type => 'MIT', :file => 'LICENSE' }
  s.authors      = { 'ibireme' => '<EMAIL>' }
  s.social_media_url = 'http://blog.ibireme.com'
  s.homepage     = 'https://localhost/BuzDownload'

  s.ios.deployment_target = '6.0'
  s.osx.deployment_target = '10.7'
  s.watchos.deployment_target = '2.0'
  s.tvos.deployment_target = '9.0'

  s.source       = { :git => 'https://localhost/BuzDownload.git', :tag => s.version.to_s }
  
  s.requires_arc = true
  s.source_files = 'Classes/*.{h,m,mm,swift}'
  s.private_header_files = 'Classes/BuzFileDownloaderEntity+WCTTableCoding.h'
  
  s.frameworks = 'Foundation', 'CoreFoundation'
  
  s.dependency 'LZFileDownload'
  s.dependency 'WCDB'
  s.dependency 'BuzConfig'
  s.dependency 'BuzAppConfiger'
end
