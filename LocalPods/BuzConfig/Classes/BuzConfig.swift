//
//  AppGlobalConfig.swift
//  buz
//
//  Created by liuyufeng on 2022/7/29.
//  Copyright © 2022 lizhi. All rights reserved.
//

import UIKit

@objc
public enum BuzMediaType : Int {
    case none = 0
    case image = 1
    case video
    case lottie
    case audio
}

@objc public class BuzConfig: NSObject {
    //MARK: – system definition
    public static let groupStoreId = "group.com.interfun.buz"
    
    
    
    //MARK: – widget definition
    public static let appComplicationIdentity = "BuzWatchDefaultComplication"
    public static let userComplicationIdentity = "BuzWatchUserComplication"
    public static let modeComplicationIdentity = "BuzWatchModeComplication"
    
    
    
    //MARK: – time constant
    //pushRouteDelay must bigger that deallocRouteDelay
    public static let pushRouteDelay  = 0.3
    public static let deallocRouteDelay  = 0.15
    public static let navigationBackAnimateDelay  = 0.40
    
    
    
    //MARK: – size constant
    //im default message page count
    public static let defaultMessagePageCount : Int = 50
    //im default max length on watch
    public static let defaultMaxImMsgHistory : Int32 = 150
    //sdweb max length
    @objc public static let defaultMaxLengthOfOriginImage : CGFloat = 5000
    
    //MARK: – watch route key
    public static let watchSettingRouteKey = "WatchSettingView"
    public static let watchHistoryContentRouteKey = "WatchHistoryContentView"
    public static let watchSettingVoiceModeRouteKey = "WatchSettingVoiceModeView"
    //notification config
    public static let notificationCategoryIdentifier = "com.interfun.buz.watch"
    
    //MARK: – Module database
    //for guidance tip exclusive strategy, if the exclusiveId has existed, then don't do something again
    // like history chat navigate to autoplay on/off tip, then doesn't post request again
    @objc public static let reportFeatureGuideCompleteExclusiveId : String = "reportFeatureGuideCompleteExclusiveIdKey"
    @objc public static let buzModuleDBPath : NSString = {
        var path : NSString = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first! as NSString
        var dir = path.appendingPathComponent("ModuleDB") as NSString
        
        if !FileManager.default.fileExists(atPath: dir as String) {
            do {
                try FileManager.default.createDirectory(atPath: dir as String, withIntermediateDirectories: true)
            }catch {
            }
        }
        
        return dir.appendingPathComponent("buzModule.db") as NSString
    }()
    
    @objc public static let buzModuleDBQueue : DispatchQueue = DispatchQueue.init(label: "BuzModuleDBQueue")
    
    //MARK: – Storage
    @objc public static let defaultSdwebSeperateKey : NSString = "_buz_sdwebimage_buz"
    
//    @objc public static func sessionPrefixFilename(userId : Int64, targetId : Int64, convType : Int) -> String {
//        return "\(userId)_\(targetId)_\(convType)_"
//    }
    
    @objc public static let IMMediaTempDir : String? = {
        var path : NSString = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first! as NSString
        var dir = path.appendingPathComponent("IMMediaTemp") as NSString
        
        if !FileManager.default.fileExists(atPath: dir as String) {
            do {
                try FileManager.default.createDirectory(atPath: dir as String, withIntermediateDirectories: true)
            }catch {
            }
        }
        
        return dir as String
    }()
    
    @objc public static let IMMediaDir : String? = {
        var path : NSString = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first! as NSString
        var dir = path.appendingPathComponent("IMMedia") as NSString
        
        if !FileManager.default.fileExists(atPath: dir as String) {
            do {
                try FileManager.default.createDirectory(atPath: dir as String, withIntermediateDirectories: true)
            }catch {
            }
        }
        
        return dir as String
    }()
    
    @objc public static let HXPhotoCacheDir : String = {
        var cacheFolderPath: String = (NSSearchPathForDirectoriesInDomains(.cachesDirectory, .userDomainMask, true).last ?? "")
        cacheFolderPath.append(contentsOf: "/BZHXPhoto")
        
        if !FileManager.default.fileExists(atPath: cacheFolderPath) {
            do {
                try FileManager.default.createDirectory(atPath: cacheFolderPath as String, withIntermediateDirectories: true)
            }catch {
            }
        }
        
        return cacheFolderPath
    }()
    
    @objc public static let OtherFileCacheDir : String = {
        var cacheFolderPath: String = (NSSearchPathForDirectoriesInDomains(.cachesDirectory, .userDomainMask, true).last ?? "")
        cacheFolderPath.append(contentsOf: "/BuzOtherFile")
        
        if !FileManager.default.fileExists(atPath: cacheFolderPath) {
            do {
                try FileManager.default.createDirectory(atPath: cacheFolderPath as String, withIntermediateDirectories: true)
            }catch {
            }
        }
        
        return cacheFolderPath
    }()
    
    @objc public static let fileCacheDir : String = {
        var path : NSString = NSSearchPathForDirectoriesInDomains(.documentDirectory, .userDomainMask, true).first! as NSString
        var dir = path.appendingPathComponent("BuzFile") as NSString
        
        if !FileManager.default.fileExists(atPath: dir as String) {
            do {
                try FileManager.default.createDirectory(atPath: dir as String, withIntermediateDirectories: true)
            }catch {
            }
        }
        
        return dir as String
    }()
    
    @objc public static let HXCacheSilenceFolderPath: String = {
        var cacheFolderPath: String = (NSSearchPathForDirectoriesInDomains(.cachesDirectory, .userDomainMask, true).last ?? "")
        cacheFolderPath.append(contentsOf: "/com.silence.HXPhotoPicker/cache")
        return cacheFolderPath
    }()
}
