Pod::Spec.new do |s|
  s.name        = "BuzConfig"
  s.version     = "1.0.0"
  s.summary     = "SwiftyJSON makes it easy to deal with JSON data in Swift"
  s.homepage    = "https://github.com/BuzConfig/BuzConfig"
  s.license     = { :type => "MIT" }
  s.authors     = { "lingoer" => "<EMAIL>", "tangplin" => "<EMAIL>" }

  s.requires_arc = true
  s.swift_version = "5.0"
  s.osx.deployment_target = "10.9"
  s.ios.deployment_target = "9.0"
  s.watchos.deployment_target = "3.0"
  s.tvos.deployment_target = "9.0"
  s.source   = { :git => "https://github.com/BuzConfig/BuzConfig.git", :tag => s.version }
  s.source_files = "Classes/**/*.{swift,h}"
end
