//
//  ConfigNetwork.swift
//  buz
//
//  Created by liuyufeng on 2022/7/29.
//  Copyright © 2022 lizhi. All rights reserved.
//

import Foundation
import BuzIDL
import UIKit
import ITNetLibrary
import BuzLog
import BuzNetworker

class ConfigNetwork {
    
    @discardableResult
    static func requestAppConfigWithoutLogin(complete : ((BuzNetworkResponse<ITResponse<ResponseAppConfigWithoutLogin>>) -> Void)?) -> ITFuture
    {

        let requestServiceClient : BuzNetCommonServiceClient = BuzNetCommonServiceClient.init()
        
        return requestServiceClient.appConfigWithoutLogin(request: RequestAppConfigWithoutLogin()) { result in
           
            switch result {
            case .success(let responseInfo):
                complete?(BuzNetworkResponse.init(rcode: responseInfo.code, isSuccess: responseInfo.code == 0 , rawResponseObj: responseInfo))
                BuzLog.info("RequestAppConfigWithoutLogin request success config = \(String(describing: responseInfo.data?.h5Urls))")
                
            case .failure(let error):
                BuzLog.error("RequestAppConfigWithoutLogin ！！error = \(error)")
                complete?(BuzNetworkResponse.init(rcode: NetworkRcode.requestFailure.rawValue, isSuccess: false, rawResponseObj: nil , error: error))
            }
        }
    }

    
    ///请求获取
    @discardableResult
    static func requestAppConfigWithLoginStatus(complete : ((BuzNetworkResponse<ITResponse<ResponseAppConfigWithLogin>>) -> Void)?) -> ITFuture
    {
        let requestServiceClient : BuzNetCommonServiceClient = BuzNetCommonServiceClient.init()
        
        return requestServiceClient.appConfigWithLogin(request: RequestAppConfigWithLogin()) { result in
            
            switch result {
            case .success(let responseInfo):
    
                guard responseInfo.code == 0 else{
                    BuzLog.error("RequestAppConfigWithLogin request failure ！！code = \(responseInfo.code)")
                    complete?(BuzNetworkResponse.init(rcode: responseInfo.code, isSuccess: false , rawResponseObj: responseInfo))
                    return
                }
                complete?(BuzNetworkResponse.init(rcode: 0, isSuccess: true, rawResponseObj: responseInfo))
                
            case .failure(let error):
                
                BuzLog.error("RequestAppConfigWithLogin ！！error = \(error)")
                complete?(BuzNetworkResponse.init(rcode: NetworkRcode.requestFailure.rawValue, isSuccess: false, rawResponseObj: nil , error: error))
            }
        }
    }
}
