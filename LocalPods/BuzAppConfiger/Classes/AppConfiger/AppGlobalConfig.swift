//
//  AppGlobalConfig.swift
//  buz
//
//  Created by liuyufeng on 2022/7/29.
//  Copyright © 2022 lizhi. All rights reserved.
//

import UIKit
import YYModel
import Localizable
import BuzLocalizable
import BuzIDL
import BuzDataStore
import BuzLog

public struct BuzSwitchConfig: Codable  {
    public var type: Int64 = 0
    public var status: Bool = false
    
    public static func parseJSONString(jsonString: String) -> [BuzSwitchConfig]? {
        guard let jsonData = jsonString.data(using: .utf8) else {
            return nil
        }
        
        do {
            let decoder = JSONDecoder()
            let modelArray = try decoder.decode([BuzSwitchConfig].self, from: jsonData)
            return modelArray
        } catch {
            print("Error decoding JSON: \(error)")
            return nil
        }
    }
}

public struct BuzVoicemojiConfig: Codable {
    /// 快速反应VE表情列表配置
    public var qucikReactionIds: [Int64]?
    /// voicemoji统计时间区间
    public var voicemojiTimeInterval: Int32?
    /// voicemoji连续发送的次数
    public var voicemojiSendNumber: Int32?
    /// voicemoji冷却的时间
    public var voicemojiForbidTime: Int32?
}

///未登录状态下获取到的配置数据
@objcMembers
public class AppGlobalConfigWithoutLogin: NSObject {
    public static var shared : AppGlobalConfigWithoutLogin?
    private var _isAdjust : Bool = true
    private var _supportUrl : String?
    private var _feedbackUrl : String?
    private var _userAgreementUrl : String?
    private var _communityRuleUrl : String?
    private var _privacyPolicyUrl : String?
    private var _attributionUrl : String?
    private var _closeAccountAgreementUrl : String?
    private var _uploadDomain: String?
    private var _downloadDomain: String?
    public var afConfig_templateId : String?
    public var emailRegex : String?
    public var _clipboardPrefix : String?
    /// 根据IP解析的国家码，大写字母
    public var ipCountry: String?
    /// 开启语音验证码的国家
    public var enablePhoneCallVerifyCountrys: [String]?
    /// 是否开启IM互踢方案（不开启则依赖罗马推送做互踢）
    public var enableIMKickOut: Bool = false
    
    public var enableAutoResend: Bool = true
    
    public var resendPeriod: NSNumber = NSNumber(value:  3 * 24 * 3600 * 1000)
    
    public var uploadDomain: String? {
        set {
            _uploadDomain = newValue
        }
        get {
            guard let domain = _uploadDomain, !domain.isEmpty else {
                return Config.withoutLogin.uploadDomain.rawValue
            }
            return domain
        }
    }
    
    public var downloadDomain: String? {
        set {
            _downloadDomain = newValue
        }
        get {
            guard let domain = _downloadDomain, !domain.isEmpty else {
                
            #if DEBUG
                return "https://romefs.yfxn.lzpsap1.com"
            #else
                return "https://cdn.buz-app.com"
            #endif
            }
            return domain
        }
    }
    
    public var isAdjust: Bool {
        set{
            _isAdjust = newValue
        }
        get {
            return  _isAdjust
        }
    }
    
    /// 反馈页地址 *
    public var supportUrl: String? {
        
        set{
            _supportUrl = newValue
        }
        
        get {
            guard let url = _supportUrl , url.utf16.count > 0 else {
                return Config.withoutLogin.supportUrl.rawValue
            }
            return url
        }
    }

    /// 反馈页地址 *
    public var feedbackUrl: String? {
        
        set{
            _feedbackUrl = newValue
        }
        
        get {
            guard let url = _feedbackUrl , url.utf16.count > 0 else {
                return Config.withoutLogin.feedbackUrl.rawValue
            }
            return url
        }
    }

    /// 用户协议地址 *
    public var userAgreementUrl: String? {
        
        set{
            _userAgreementUrl = newValue
        }
        
        get {
            guard let url = _userAgreementUrl , url.utf16.count > 0 else {
                return Config.withoutLogin.userAgreementUrl.rawValue
            }
            return url
        }
    }
    
    /// 用户协议地址 *
    public var communityRuleUrl: String? {
        
        set{
            _communityRuleUrl = newValue
        }
        
        get {
            guard let url = _communityRuleUrl , url.utf16.count > 0 else {
                return nil
            }
            return url
        }
    }

    /// 隐私协议URL *
    public var privacyPolicyUrl: String? {
        set{
            _privacyPolicyUrl = newValue
        }
        
        get {
            guard let url = _privacyPolicyUrl , url.utf16.count > 0 else {
                return Config.withoutLogin.privacyPolicyUrl.rawValue
            }
            return url
        }
    }
    
    
    /// attribution URL *
    public var attributionUrl: String? {
        set{
            _attributionUrl = newValue
        }
        
        get {
            guard let url = _attributionUrl , url.utf16.count > 0 else {
                return Config.withoutLogin.attributionUrl.rawValue
            }
            return url
        }
    }

    
    ///注销账号协议URL
    public var closeAccountAgreementUrl: String? {
        set{
            _closeAccountAgreementUrl = newValue
        }
        
        get {
            guard let url = _closeAccountAgreementUrl , url.utf16.count > 0 else {
                return Config.withoutLogin.closeAccountAgreementUrl.rawValue
            }
            return url
        }
    }
    
    ///邀请粘贴板前缀
    public var clipboardPrefix: String? {
        set {
            _clipboardPrefix = newValue
        }
        get {
            guard let clipboardPrefix = _clipboardPrefix, !clipboardPrefix.isEmpty else {
                return "buz_invitation_"
            }
            return clipboardPrefix
        }
    }
    
    required convenience init?(coder aDecoder: NSCoder) {
       self.init()
       self.yy_modelInit(with: aDecoder)
    }
       
    func encode(with aCoder: NSCoder) {
       self.yy_modelEncode(with: aCoder)
    }
    
    public func fetchFeedbackUrl(with source: FeedbackType) -> URL? {
        guard let fbUrlString = feedbackUrl, let url = URL(string: fbUrlString) else {
            return nil
        }
        guard var urlComps = URLComponents(url: url, resolvingAgainstBaseURL: true)  else { return nil }
        var newQueryItems = [URLQueryItem]()
        
        if let queryItems = urlComps.queryItems {
            for item in queryItems {
                newQueryItems.append(item)
            }
        }
        let sourceItem = URLQueryItem(name: "source", value: String(source.rawValue))
        newQueryItems.append(sourceItem)
        urlComps.queryItems = newQueryItems
        let result = urlComps.url
        return result
    }
    
}

@objc
public enum FeedbackConfigPopTimeType : Int32 {
    case none = 0
    // 1-次日首次启动APP时弹出
    case torrowFristLaunch = 1
    // 2-累计发对讲机消息满wtMsgCount次时弹出
    case sendWalkitTalkeReachCount = 2
}

///已登录状态下获取到的配置数据
@objcMembers
public class AppGlobalConfigInLoginStatus: NSObject {
    public static var shared : AppGlobalConfigInLoginStatus?
    
    private var _downloadLink: String?
    private var _systemShareText: String?
    private var _smsShareText: String?
    private var _groupShareText: String?
    private var _maxGroupMemberCount : Int32 = 0
    
    private var _pttGudieVideo: String?
    private var _islandPttGudieVideo: String?

    public var communityGuideConfig_content : String?
    public var communityGuideConfig_schemeList : [String]?
    public var feedbackConfig_popTime : FeedbackConfigPopTimeType = .none
    public var feedbackConfig_wtMsgCount : Int32 = 0
    public var appPagePopupDay: Int32 = 0
    public var appPageGuideContent: String?
    public var sendMsgContent: String?
    public var groupMsgContent: String?
    public var singleMsgContent: String?
    public var isActivatePTTByIM : Bool = true
    public var isSupportInterviewInvite: Bool = false
    public var interviewInvitePopupDay: Int32?
    public var aiPrivacyStatement: String?
    public var enableAIPictureMsg: Bool = false
    public var enableLogoutBindPhone: Bool = false
    public var enableAIGroupEntrancePinToTop: Bool = false
    public var enableVadCheck : Bool = false
    public var useFallbackAudioProcess : Bool = false

    /// 是否开启实时活动功能
    public var enbaleLiveActivity: Bool = false

    /// 是否开启转录文本功能
    public var enableTranscribeText: Bool = false

    /// 是否开启LiveActivity新功能介绍弹窗
    public var enableLiveActivityPopUp: Bool = false

    public var enableWatchPopup: Bool = false
    public var showBindPhonePopup: Bool = true
    public var maxImMsgHistory: Int32?
    
    //the max number of AI in group
    public var maxGroupBotLimit: Int32 = 1
    
    //Ai introduce url
    public var aiLearnMoreUrl: String?

    public var buzSwitchConfigList:  [BuzSwitchConfig]?
    
    public var enableAIGroupTextVoiceMsg: Bool = false
    
    public var enableMsgHyperlinkParse: Bool = false
    
    public var traceSamplingRate: Int32?
    
    public var traceTag: String?
    
    /// 下载链接 *
    public var downloadLink: String? {
        set{
            _downloadLink = newValue
        }
        
        get{
            guard let url = _downloadLink , url.utf16.count > 0 else {
                return Config.InLoginStatus.downloadLink.rawValue
            }
            return url
        }
    }

    /// 系统分享文案 *
    public var systemShareText: String? {
        set{
            _systemShareText = newValue
        }
        
        get{
            guard let url = _systemShareText , url.utf16.count > 0 else {
                return Localizable.configShareText
            }
            return url
        }
    }

    /// 短信分享文案 *
    public var smsShareText: String? {
        set{
            _smsShareText = newValue
        }
        
        get{
            guard let url = _smsShareText , url.utf16.count > 0 else {
                return Localizable.configShareText
            }
            return url
        }
    }
    
    /// 群短信分享文案 *
    public var groupShareText: String? {
        set{
            _groupShareText = newValue
        }
        
        get{
            guard let text = _groupShareText , text.utf16.count > 0 else {
                return Localizable.configGroupShareText
            }
            return text
        }
    }
    
    public var sharePlatformList: [Int32]?
    
    public var videoSharePlatformList: [Int32]?

    public var maxGroupMemberCount: Int32 {
        set{
            _maxGroupMemberCount = newValue
        }
        
        get{
            guard self._maxGroupMemberCount > 0 else {
                return Config.GroupConfig.maxMemberCount.rawValue
            }
            return _maxGroupMemberCount
        }
    }
    
    public var pttGudieVideo: String? {
        set{
            _pttGudieVideo = newValue
        }
        
        get{
            guard let url = _pttGudieVideo , url.utf16.count > 0 else {
                return Config.InLoginStatus.pttGudieVideo.rawValue
            }
            return url
        }
    }
    
    public var islandPttGudieVideo: String? {
        set{
            _islandPttGudieVideo = newValue
        }
        
        get{
            guard let url = _islandPttGudieVideo , url.utf16.count > 0 else {
                return Config.InLoginStatus.islandPttGudieVideo.rawValue
            }
            return url
        }
    }
    
    public var imConfigUnsupportMsgTips: [UnsupportedMsgTip]?
    
    var _groupCallMaxMemberNum: Int32 = 0
    public var groupCallMaxMemberNum: Int32 {
        set {
            _groupCallMaxMemberNum = newValue
        }
        get {
            guard self._groupCallMaxMemberNum > 0 else {
                return Config.RealTimeCallConfig.groupCallMaxMemberNum.rawValue
            }
            return _groupCallMaxMemberNum
        }
    }
    
    var _enablePrivateVoiceCall: Bool = true
    public var enablePrivateVoiceCall : Bool{
        set {
            _enablePrivateVoiceCall = newValue
        }
        get {
            return _enablePrivateVoiceCall
        }
    }
    
    var _enableGroupVoiceCall: Bool = false
    public var enableGroupVoiceCall : Bool{
        set {
            _enableGroupVoiceCall = newValue
        }
        get {
            return _enableGroupVoiceCall
        }
    }
    
    var _enableVideoCall: Bool = false
    public var enableVideoCall: Bool{
        set {
            _enableVideoCall = newValue
        }
        get {
            return _enableVideoCall
        }
    }
    
    var _callEndShowEvaluationPage: Bool = false
    public var callEndShowEvaluationPage: Bool{
        set {
            _callEndShowEvaluationPage = newValue
        }
        get {
            return _callEndShowEvaluationPage
        }
    }
    
    public var closePanelCategoryTypes: [Int32]?
    /// 快速反应VE表情列表配置
    public var qucikReactionIds: [Int64]?
    /// voicemoji统计时间区间
    private var _voicemojiTimeInterval: Int32?
    public var voicemojiTimeInterval: Int32? {
        set {
            self._voicemojiTimeInterval = newValue
        }
        get {
            guard let value = self._voicemojiTimeInterval else {
                return Config.Voicemoji.voicemojiTimeInterval
            }
            return value
        }
    }
    
    /// voicemoji连续发送的次数
    private var _voicemojiSendNumber: Int32?
    public var voicemojiSendNumber: Int32? {
        set {
            self._voicemojiSendNumber = newValue
        }
        get {
            guard let value = self._voicemojiSendNumber else {
                return Config.Voicemoji.voicemojiSendNumber
            }
            return value
        }
    }
    
    /// voicemoji冷却的时间
    private var _voicemojiForbidTime: Int32?
    public var voicemojiForbidTime: Int32? {
        set {
            self._voicemojiForbidTime = newValue
        }
        get {
            guard let value = self._voicemojiForbidTime else {
                return Config.Voicemoji.voicemojiForbidTime
            }
            return value
        }
    }
    
    /// voicemoji冷却的时间
    public var voicemojiLastestTimestamp: Int32 = 0
    
    private var _enableSearchVoiceGif: Bool?
    public var enableSearchVoiceGif: Bool? {
        set {
            self._enableSearchVoiceGif = newValue
        }
        get {
            guard let value = self._enableSearchVoiceGif else {
                return false
            }
            return value
        }
    }
    
    
    
    /// 离线通知ASR预览超时时间，单位秒
    public var notificationAsrPreviewTimeout: Int32 = 5
    /// 离线通知ASR预览开关,默认关闭
    public var notificationAsrPreviewSwitch: Bool = false
    /// IOS客户端接收离线语音消息，调用获取ASR接口的地址
    public var offlineNotificationDomain: String = ""
    /// 用户智能ASR开关 true 开启（收到的语音消息自动转义）
    public var smartAsrSwitch: Bool = false
    /// 用户当前是否具备ASR功能，包含智能ASR、手动ASR、一键ASR功能
    public var asrFunctionSwitch: Bool = false
    /// 客户端展示一键ASR按钮的开关
    public var showOneKeyAsrSwitch: Bool = false
    /// 全局智能ASR功能开关，当关闭时，所有用户不能使用智能ASR功能
    public var smartAsrGlobalSwitch: Bool = false
    /// 等待ASR结果超时时间，单位秒
    public var asrTimeout: Int32 = 15
    /// 是否全量消息ASR,根据国家去开放
    public var isAllMsgSmartAsr: Bool = false

    /// 消息撤回开关
    public var enableMsgRecall: Bool = false

    /// 消息撤回允许最大时间（单位：分钟）
    public var msgRecallMaxMinutes: Int64 = 24 * 60

    /// 消息转发开关
    public var enableMsgForward: Bool = false
    
    /// 消息引用（敏杰要求默认值改成开启）
    public var enableReferMsg: Bool = true
    

    /// 消息内发送map开关
    public var enableMap: Bool = false

    /// map附近地点列表开关
    public var enableMapNearbyPlace: Bool = false

    /// map地点搜索开关
    public var enableMapPlaceSearch: Bool = false

    /// map附近地点列表查询半径 (单位：米，用于给iOS调用Apple Map方案使用)
    public var nearbyPlaceRadius: Int64 = 1000

    /// map地点搜索查询半径 (单位：米，用于给iOS调用Apple Map方案使用)
    public var placeSearchRadius: Int64 = 1000

    /// map附近地点列表距离阈值 (单位：米，用于给Android防止频繁查询接口，定位偏离该值以内不重复查询接口)
    public var nearbyPlaceDistanceThreshold: Int64 = 50

    /// 好友上线通知开关
    public var enableFriendOnlineNotify: Bool = false
    public var friendOnlineNotifyIntervalSeconds: Int32 = 600
    /// livePlace重连超时时间，在此事件内没有连上就会退出房间。
    public var livePlaceReconnectRetryDuration: Int32 = 60
    //是否开启群组LivePlace功能
    public var enableGroupLivePlace: Bool = true
    //是否开启个人LivePlace功能
    public var enablePersonalLivePlace: Bool = true
    //livePlace群组空间最大人数
    public var groupMaxMemberNum: Int32 = 12
    //livePlace个人空间最大人数
    public var personalMaxMemberNum: Int32 = 12
    
    /// 是否开启 OnAir 功能
    public var enableOnAir: Bool = false
    /// 群频道最大人数
    public var groupOnAirMaxMemberNum: Int32 = 12

    /// 勿扰模式开关
    public var liveActivitySwitch: Bool = false
    public var earphoneDisconnectHint: Bool = false
    public var alertSoundType: Int32 = 0
    public var msgAutoPlayTime: Int32 = 120
    public var groupAutoPlayTime: Int32 = 5 * 60
    
    /// remote apns push data
    public var enableNewOfflinePushFormat: Bool = false
    
    /// 声音滤镜开关
    public var enableVoiceFilter: Bool = false
    /// 首页弹发完之后的缓冲时间 单位秒
    public var voiceFilterPopupDelay: Int32 = 5
    ///
    public var voiceFilterLastestTimestamp: Int32 = 0
    
    public var drawNumPerSec: Int32 = 0
    
    public var batchSendNum: Int32 = 0
    
    public var maxShowNum: Int32 = 0
    /// 等待Translate结果超时时间，单位秒
    public var translateTimeout: Int32 = 15
    public var maxFileCount: Int32 = 20
    public var maxSingleFileSize: Int32 = 2 * 1024
    public var totalFileReminderSize: Int32 = 256
    public var videoTemplateUrl: String?
    public var videoTemplateMd5: String?
    public var videoPreviewTemplateUrl: String?
    
    required convenience init?(coder aDecoder: NSCoder) {
       self.init()
       self.yy_modelInit(with: aDecoder)
    }
       
    func encode(with aCoder: NSCoder) {
       self.yy_modelEncode(with: aCoder)
    }
}


//兜底文案
private enum Config {
    
    enum withoutLogin : String {
        
#if DEBUG
        case supportUrl = "https://buz.yfxn.lzpsap1.com/static/official-website/help-center/"
        case feedbackUrl = "https://buz.yfxn.lzpsap1.com/static/new-feedback.html"
        case userAgreementUrl = "https://buz.yfxn.lzpsap1.com/static/official-website/user-agreement/"
        case privacyPolicyUrl = "http://buz.yfxn.lzpsap1.com/static/official-website/privacy-policy/"
        case attributionUrl = "http://buz.yfxn.lzpsap1.com/static/mono/attributions/index.html"
        case closeAccountAgreementUrl = "http://buz.yfxn.lzpsap1.com/static/account-deletion-instructions.html"
        case uploadDomain = "http://romefs.yfxn.lzpsap1.com"
#else
        case supportUrl = "https://www.buz.ai/help-center/"
        case feedbackUrl = "https://www.buz-app.com/new-feedback.html?source=1"
        case userAgreementUrl = "https://www.buz.ai/user-agreement/"
        case privacyPolicyUrl = "https://www.buz.ai/privacy-policy/"
        case attributionUrl = "https://www.buz-app.com/attributions/index.html"
        case closeAccountAgreementUrl = "https://www.buz-app.com/account-deletion-instructions.html"
        case uploadDomain = "https://upload.buz-app.com"
#endif
    }
    
    enum InLoginStatus : String {
        
#if DEBUG
        case downloadLink = "http://buz.yfxn.lzpsap1.com/static/download.html"
        case shareText = "Hey,I\'m using Buz to chat.Join me! Download it here:"
        case groupShareText = "Join us to chat on Buz!"
#else
        case downloadLink = "https://www.buz-app.com/download.html"
        case shareText = "Hey,I\'m using Buz to chat.Join me! Download it here:"
        case groupShareText = "Join us to chat on Buz!"
#endif
        
        case pttGudieVideo = "https://www.buz-app.com/_app/media/pttGuide.mp4"
        case islandPttGudieVideo = "https://www.buz-app.com/_app/media/pttGuideIsland.mp4"
    }
    
    enum GroupConfig : Int32 {
        case maxMemberCount = 21 //(包括自己21人)
    }
    
    enum RealTimeCallConfig: Int32 {
        case groupCallMaxMemberNum = 12
    }
    
    struct Voicemoji {
        /// voicemoji统计时间区间
        static let voicemojiTimeInterval: Int32 = 30
        /// voicemoji连续发送的次数
        static let voicemojiSendNumber: Int32 = 15
        /// voicemoji冷却的时间
        static let voicemojiForbidTime: Int32 = 30
    }
}

/**
 
 线上
 反馈页：https://www.buz-app.com/feedback.html
 用户协议：https://www.buz-app.com/user-agreement.html
 隐私协议：https://www.buz-app.com/privacy-policy.html
 用户注销协议：https://www.buz-app.com/cancellation.html
 下载app链接：https://www.buz-app.com/download.html
 系统邀请文案：Hey,I\'m using Buz to chat.Join me! Download it here:
 短信邀请文案：Hey,I\'m using Buz to chat.Join me! Download it here:


 测试环境：
 反馈页：http://buz.yfxn.lzpsap1.com/static/feedback.html
 用户协议：http://buz.yfxn.lzpsap1.com/static/user-agreement.html
 隐私协议：http://buz.yfxn.lzpsap1.com/static/privacy-policy.html
 用户注销协议：http://buz.yfxn.lzpsap1.com/static/cancellation.html
 下载app链接：http://buz.yfxn.lzpsap1.com/static/download.html
 系统邀请文案：Hey,I\'m using Buz to chat.Join me! Download it here:
 短信邀请文案：Hey,I\'m using Buz to chat.Join me! Download it here:
 */


// 消息自动播放时间配置
public class MessageAutoPlayTimeStoreCache: NSObject {
    enum MessageAutoPlayTimeStoreKey: String {
        case MessageAutoPlayTime = "MessageAutoPlayTimeKey"
        case GroupMessageAutoPlayTime = "GroupMessageAutoPlayTimeKey"
    }

    static func getValue(storeKey: MessageAutoPlayTimeStoreKey, defaultValue: Int32) -> Int32 {
        return MMKV.buz.int32(forKey: storeKey.rawValue, defaultValue: defaultValue)
    }

    static func setValue(storeKey: MessageAutoPlayTimeStoreKey, value: Int32) {
        MMKV.buz.set(value, forKey: storeKey.rawValue)
        BuzLog.info("MessageAutoPlayTimeStoreCache = \(storeKey.rawValue) : \(value)")
    }

    public static func setMessageAutoPlayTime(value: Int32) {
        setValue(storeKey: .MessageAutoPlayTime, value: value)
    }

    public static var messageAutoPlayTime: Int32 {
        return getValue(storeKey: .MessageAutoPlayTime, defaultValue: 120)
    }

    public static func setGroupMessageAutoPlayTime(value: Int32) {
        setValue(storeKey: .GroupMessageAutoPlayTime, value: value)
    }

    public static var groupMessageAutoPlayTime: Int32 {
        return getValue(storeKey: .GroupMessageAutoPlayTime, defaultValue: 300)
    }
}

public enum FeedbackType: Int32 {
    ///默认
    case FeedbackTypeDefault = 0
    ///设置页反馈
    case FeedbackTypeSetting = 1
    ///收集邮箱
    case FeedbackTypeCollectEmail = 2
    ///机器人反馈
    case FeedbackTypeBot = 3
    ///机器人反馈_Gc （例如：通过Chat history点击AI头像，再点击feedback，传回来的反馈类型需要是： From_Chatbot_BotName_Gc）
    case FeedbackTypeBot_Gc = 4
    ///LivePlace
    case FeedbackTypeLivePlace = 5
    case FeedbackTypeVoiceCall = 6
    case FeedbackTypeVideoCall = 7
    case FeedbackTypeRegister = 8
    case FeedbackTypeHelpCenter = 9
}
