//
//  BuzAppConfiger.swift
//  buz
//
//  Created by st.chio on 2024/9/9.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation
import BuzLog
import BuzDataStore
import BuzFoundation
import BaseTool
import BuzConfig
import Combine

//MARK: --BuzAppConfigerObserver---------
@objc
public protocol BuzAppConfigerObserver {
    @objc optional func appConfiger(configer: BuzAppConfiger, notLoggedConfigDidRequstSuccess config: AppGlobalConfigWithoutLogin)
    @objc optional func appConfiger(configer: BuzAppConfiger, loggedConfigDidRequstSuccess config: AppGlobalConfigInLoginStatus)
}

// MARK: --Combine Publisher---------
public extension BuzAppConfiger {
    class Publisher {
        public let notLoggedConfigDidRequstSuccess = PassthroughSubject<AppGlobalConfigWithoutLogin, Never>()
        public let loggedConfigDidRequstSuccess = PassthroughSubject<AppGlobalConfigInLoginStatus, Never>()
    }
}

//MARK: --EventPublisher---------
public protocol BuzAppConfigerObserverPublisher {
    func addObserver(_ observer: BuzAppConfigerObserver)
    func removeObserver(_ observer: BuzAppConfigerObserver)
}

protocol BuzAppConfigerObserverPublisherInternal {
    var observers: NSHashTable<BuzAppConfigerObserver> { get }
    func notifyObservers(_ event: (BuzAppConfigerObserver) -> Void)
}

extension BuzAppConfigerObserverPublisherInternal {
    public func addObserver(_ observer: BuzAppConfigerObserver) {
        observers.add(observer)
    }
    
    public func removeObserver(_ observer: BuzAppConfigerObserver) {
        observers.remove(observer)
    }
    
    func notifyObservers(_ event: (BuzAppConfigerObserver) -> Void) {
        for observer in observers.allObjects {
            event(observer)
        }
    }
}

//MARK: --enum---------
public extension BuzAppConfiger {
    
    private enum StoreKey: String {
        case appGlobalConfgWithoutLogin = "SessionStoreKey_appGlobalConfgWithoutLogin"
        case appGlobalConfgInLoginStatus = "SessionStoreKey_appGlobalConfgInLoginStatus"
        case appGlobalConfgShowOffline = "SessionStoreKey_appGlobalConfgShowOffline"
    }
    
    //App 配置数据来源
    enum DataSourceType: Int32 {
        //兜底数据
        case backup = 0
        //网络
        case network = 1
        //内存
        case memory = 2
        //磁盘
        case disk = 3
        
        public var rawValueString : String {
            switch self {
            case .backup:
                return "backup"
            case .network:
                return "network"
            case .memory:
                return "memory"
            case .disk:
                return "disk"
            }
        }
    }

}

//MARK: --BuzAppConfiger---------
@objc
@objcMembers
public class BuzAppConfiger: NSObject, BuzAppConfigerObserverPublisher, BuzAppConfigerObserverPublisherInternal {
    
    public static let shared = BuzAppConfiger()
    
    public private(set) var publisher = Publisher()
    
    internal let observers = NSHashTable<BuzAppConfigerObserver>.weakObjects()
    
    //MARK: -- Not logged in Config------
    ///未登录状态下获取到的配置数据
    public var appGlobalConfigWithoutLogin : AppGlobalConfigWithoutLogin?  {
        get {
            return AppGlobalConfigWithoutLogin.shared
        }
        set {
            AppGlobalConfigWithoutLogin.shared = newValue
        }
    }
    
    //MARK: -- logged in Config------
    public typealias FetchAppGlobalConfigInLoginStatusComplete = (AppGlobalConfigInLoginStatus, DataSourceType) -> Void
    public var appGlobalConfigWithLoginStatus : AppGlobalConfigInLoginStatus? {
        get {
            return AppGlobalConfigInLoginStatus.shared
        }
        set {
            AppGlobalConfigInLoginStatus.shared = newValue
        }
    }
    
    //防止多次发出请求
    private var isFetchingAppGlobalConfigInLoginStatus : Bool = false
    private var isFetchingAppGlobalConfigInLoginStatusCompletes : [FetchAppGlobalConfigInLoginStatusComplete] = []
    
    
    //MARK: -- ShowOffline------
    public lazy var appGlobalConfgShowOffline : Bool = MMKV.buz.bool(forKey: BuzAppConfiger.StoreKey.appGlobalConfgShowOffline.rawValue){
        didSet{
            let storeKey : String = BuzAppConfiger.StoreKey.appGlobalConfgShowOffline.rawValue
            let result = MMKV.buz.set(appGlobalConfgShowOffline, forKey: storeKey)
            BuzLog.info("存入appGlobalConfgShowOffline结果 : \(result) , appGlobalConfgShowOffline = \(appGlobalConfgShowOffline)")
        }
    }
}

//MARK: -- Not logged in Config------
public extension BuzAppConfiger {
    ///获取全局配置 对应后台数据 H5Urls : http://buz.pageweb.io/buz-doc/buz协议/IDL/bean/common.html#H5Urls
    /// isForceRefresh - 是否忽略本地缓存强制刷新 ； 一般用 默认false
    func fetchAppGlobalConfigWithoutLogin(isForceRefresh : Bool = false ,  completion : ((AppGlobalConfigWithoutLogin?) -> Void)?) {
        
//
//        if let config = self.appGlobalConfigWithoutLogin {
//            DispatchQueue.main.safeAsyncUIQueue {
//                BuzLog.info("AppConfig___config6")
//                completion?(config)
//            }
//            return
//        }
        
        let storeKey : String = BuzAppConfiger.StoreKey.appGlobalConfgWithoutLogin.rawValue
        
        let requestConfigAndCache : () -> Void = {
            //本地没有数据请求服务端
            ConfigNetwork.requestAppConfigWithoutLogin { response in
                BuzLog.info("AppConfig___config = \(String(describing: response.rawResponseObj))" + ":::isSuccess:::" + "\(response.isSuccess)")
                guard response.isSuccess == true else {
                    completion?(AppGlobalConfigWithoutLogin())
                    return
                }
                
                BuzLog.debug("requestAppConfig  WithoutLogin success.")
                let config = AppGlobalConfigWithoutLogin.init()
                config.feedbackUrl = response.rawResponseObj?.data?.h5Urls?.feedbackUrl
                config.supportUrl = response.rawResponseObj?.data?.h5Urls?.supportUrl
                config.privacyPolicyUrl = response.rawResponseObj?.data?.h5Urls?.privacyPolicyUrl
                config.userAgreementUrl = response.rawResponseObj?.data?.h5Urls?.userAgreementUrl
                config.communityRuleUrl = response.rawResponseObj?.data?.h5Urls?.communityRulesUrl
                config.attributionUrl = response.rawResponseObj?.data?.h5Urls?.attributionUrl
                config.closeAccountAgreementUrl = response.rawResponseObj?.data?.h5Urls?.closeAccountAgreementUrl
                config.afConfig_templateId = response.rawResponseObj?.data?.afconfig?.templateId
                config.uploadDomain = response.rawResponseObj?.data?.uploadDomain
                config.downloadDomain = response.rawResponseObj?.data?.downloadDomain
                config.ipCountry = response.rawResponseObj?.data?.generalConfig?.ipCountry
                config.enablePhoneCallVerifyCountrys = response.rawResponseObj?.data?.generalConfig?.enablePhoneCallVerifyCountrys
                config.enableIMKickOut = response.rawResponseObj?.data?.generalConfig?.enableIMKickOut ?? false
                config.emailRegex = response.rawResponseObj?.data?.generalConfig?.emailRegex
                config.clipboardPrefix = response.rawResponseObj?.data?.generalConfig?.clipboardPrefix
                
                config.isAdjust = true
                
                let isEnableIMAutoRetry = response.rawResponseObj?.data?.generalConfig?.isEnableIMAutoRetry
                
                config.enableAutoResend = isEnableIMAutoRetry ?? true
                
                if let maxIMAutoRetryMillis = response.rawResponseObj?.data?.generalConfig?.maxIMAutoRetryMillis {
                    config.resendPeriod = NSNumber(value: maxIMAutoRetryMillis)
                }else {
                    config.resendPeriod = NSNumber(value:  3 * 24 * 3600 * 1000)
                }
                
                self.appGlobalConfigWithoutLogin = config
                
                self.notifyObservers { observer in
                    observer.appConfiger?(configer: self, notLoggedConfigDidRequstSuccess: config)
                }
                self.publisher.notLoggedConfigDidRequstSuccess.send(config)
                
                completion?(config)
                if let data : Data = config.yy_modelToJSONData()
                {
                    MMKV.buz_async { kv in
                        kv.set(data, forKey: storeKey)
                    }
                }
            }
        }
        
        if isForceRefresh {
            requestConfigAndCache()
            return
        } else {
            
           if let config = self.appGlobalConfigWithoutLogin {
               DispatchQueue.main.safeAsyncUIQueue {
                   BuzLog.info("AppConfig___config6")
                   completion?(config)
               }
               return
           }
           
        }
//        if isForceRefresh {
//            requestConfigAndCache()
//            return
//        }
        
        //避免后期配置数据过大 放异步访问
        MMKV.buz_async { [weak self] kv in
            if let data = kv.data(forKey: storeKey) {
                BuzLog.info("AppConfig___config2")
                self?.appGlobalConfigWithoutLogin = AppGlobalConfigWithoutLogin.yy_model(withJSON: data)
            }
            BuzLog.info("AppConfig___config3")
        } complete: {

            //main queue
            if let config = self.appGlobalConfigWithoutLogin {
                BuzLog.info("AppConfig___config4")
                completion?(config)
            }else{
                BuzLog.info("AppConfig___config5")
                requestConfigAndCache()
            }
        }
    }
}

//MARK: -- logged in Config------
public extension BuzAppConfiger {
    ///登录状态下获取到的全局配置  http://buz.pageweb.io/buz-doc/buz协议/IDL/bean/common.html#ShareConfig
    func fetchAppGlobalConfigInLoginStatus(isForceRefresh : Bool = false ,  completion : FetchAppGlobalConfigInLoginStatusComplete?) {
        
        let storeKey : String = BuzAppConfiger.StoreKey.appGlobalConfgInLoginStatus.rawValue
        let requestConfigAndCache : () -> Void = {
            
            DispatchQueue.main.safeAsyncUIQueue {
                
                if let completion = completion {
                    self.isFetchingAppGlobalConfigInLoginStatusCompletes.append(completion)
                }
                guard self.isFetchingAppGlobalConfigInLoginStatus == false else {
                    return
                }
                
                self.isFetchingAppGlobalConfigInLoginStatus = true
            }
           
            //本地没有数据请求服务端
            ConfigNetwork.requestAppConfigWithLoginStatus { respone in
                
                self.isFetchingAppGlobalConfigInLoginStatus = false
                
                guard respone.isSuccess == true else {
                    for complete in self.isFetchingAppGlobalConfigInLoginStatusCompletes {
                        complete(AppGlobalConfigInLoginStatus() , .backup)
                    }
                    self.isFetchingAppGlobalConfigInLoginStatusCompletes.removeAll()
                    return
                }
                
                BuzLog.info("requestAppConfigWithLoginStatus success.")

                let config = AppGlobalConfigInLoginStatus.init()
                if let sConfig = respone.rawResponseObj?.data?.shareConfig {
                    config.downloadLink = sConfig.downloadLink
                    config.systemShareText = sConfig.systemShareText
                    config.smsShareText = sConfig.smsShareText
                    config.groupShareText = sConfig.groupShareText
                    config.sharePlatformList = sConfig.sharePlatformList
                    config.videoSharePlatformList = sConfig.videoSharePlatformList
                }
                
                if let sConfig = respone.rawResponseObj?.data?.feedbackConfig {
                    config.isSupportInterviewInvite = sConfig.supportInterviewInvite ?? false
                    config.interviewInvitePopupDay = sConfig.interviewInvitePopupDay
                }
                
                if let gConfig = respone.rawResponseObj?.data?.groupConfig {
                    config.maxGroupMemberCount = gConfig.maxMemberNum
                }
                
                if let commonConfig = respone.rawResponseObj?.data?.commonConfig {
                    config.pttGudieVideo = commonConfig.pttGudieVideo
                    config.islandPttGudieVideo = commonConfig.islandPttGudieVideo
                    config.isActivatePTTByIM = commonConfig.activatePTTByIM
                    config.aiPrivacyStatement = commonConfig.aiPrivacyStatement
                    config.enableAIPictureMsg = commonConfig.enableAIPictureMsg ?? false
                    config.enableLiveActivityPopUp = commonConfig.enableLiveActivityPopUp ?? false
                    config.enableTranscribeText = commonConfig.enableTranscribeText ?? false
                    config.enbaleLiveActivity = commonConfig.enbaleLiveActivity ?? false
                    config.enableLogoutBindPhone = commonConfig.enableLogoutBindPhone ?? false
                    config.aiLearnMoreUrl = commonConfig.aiLearnMoreUrl
                    config.enableAIGroupEntrancePinToTop = commonConfig.enableAIGroupEntrancePinToTop ?? false
                    
                    if let jsonString = commonConfig.switchConfigList , let buzSwitchConfigList  = BuzSwitchConfig.parseJSONString(jsonString: jsonString) {
                            config.buzSwitchConfigList = buzSwitchConfigList

                    }
                    
                    
                }
                config.appPagePopupDay = respone.rawResponseObj?.data?.feedbackConfig?.appPagePopupDay ?? 0
                config.appPageGuideContent = respone.rawResponseObj?.data?.feedbackConfig?.appPageGuideContent
                
                config.communityGuideConfig_content = respone.rawResponseObj?.data?.communityGuideConfig?.content
                config.communityGuideConfig_schemeList = respone.rawResponseObj?.data?.communityGuideConfig?.schemeList
                self.appGlobalConfgShowOffline = respone.rawResponseObj?.data?.commonConfig?.showOffline ?? false
                
                if let popTime = respone.rawResponseObj?.data?.feedbackConfig?.popupTime {
                    config.feedbackConfig_popTime = FeedbackConfigPopTimeType.init(rawValue: popTime) ?? .none
                }
                
                if let count = respone.rawResponseObj?.data?.feedbackConfig?.wtMsgCount {
                    config.feedbackConfig_wtMsgCount = count
                }
                
                config.singleMsgContent = respone.rawResponseObj?.data?.commonConfig?.singleMsgContent
                config.groupMsgContent = respone.rawResponseObj?.data?.commonConfig?.groupMsgContent
                config.enableWatchPopup = respone.rawResponseObj?.data?.commonConfig?.enableWatchPopUp ?? false
                config.maxImMsgHistory = respone.rawResponseObj?.data?.commonConfig?.maxImMsgHistory
                config.maxGroupBotLimit = respone.rawResponseObj?.data?.groupConfig?.maxGroupBotNum ?? 0
                config.enableAIGroupTextVoiceMsg = respone.rawResponseObj?.data?.commonConfig?.enableAIGroupTextVoiceMsg ?? false
                config.liveActivitySwitch = respone.rawResponseObj?.data?.commonConfig?.liveActivitySwitch ?? false
                config.earphoneDisconnectHint = respone.rawResponseObj?.data?.commonConfig?.earphoneDisconnectHint ?? false
                config.alertSoundType = respone.rawResponseObj?.data?.commonConfig?.alertSoundType ?? 0
                config.enableNewOfflinePushFormat = respone.rawResponseObj?.data?.commonConfig?.enableNewOfflinePushFormat ?? false
                config.useFallbackAudioProcess = respone.rawResponseObj?.data?.commonConfig?.useFallbackAudioProcess ?? false
                
                if let realTimeCallConfig = respone.rawResponseObj?.data?.realTimeCallConfig {
                    config.groupCallMaxMemberNum = realTimeCallConfig.groupCallMaxMemberNum
                    config.enablePrivateVoiceCall = realTimeCallConfig.enableFriendVoiceCall
                    config.enableGroupVoiceCall = realTimeCallConfig.enableGroupVoiceCall
                    config.enableVideoCall = realTimeCallConfig.enableVideoCall
                    config.callEndShowEvaluationPage = realTimeCallConfig.showEvaluationPage
                }
                      
                if let voicemojiConfig = respone.rawResponseObj?.data?.voicemojiConfig {
                    config.qucikReactionIds = voicemojiConfig.qucikReactionIds
                    config.voicemojiTimeInterval = voicemojiConfig.voicemojiTimeInterval
                    config.voicemojiSendNumber = voicemojiConfig.voicemojiSendNumber
                    config.voicemojiForbidTime = voicemojiConfig.voicemojiForbidTime
                    config.enableSearchVoiceGif = voicemojiConfig.enableSearchVoiceGif
                    config.closePanelCategoryTypes = voicemojiConfig.closePanelCategoryTypes
                    config.voicemojiLastestTimestamp = voicemojiConfig.latestTimestamp ?? 0
                }
                
                if let asrConfig = respone.rawResponseObj?.data?.asrConfig {
                    config.asrFunctionSwitch =  asrConfig.asrFunctionSwitch
                    config.smartAsrSwitch = asrConfig.smartAsrSwitch
                    config.smartAsrGlobalSwitch = asrConfig.smartAsrGlobalSwitch
                    config.showOneKeyAsrSwitch = asrConfig.showOneKeyAsrSwitch
                    config.asrTimeout = asrConfig.asrTimeout
                    config.isAllMsgSmartAsr = asrConfig.isAllMsgSmartAsr ?? false
                    config.notificationAsrPreviewTimeout = asrConfig.offlineNotificationDelaySecond ?? 5
                    config.notificationAsrPreviewSwitch = asrConfig.offlineNotificationDelaySwitch ?? false
                    config.offlineNotificationDomain = asrConfig.offlineNotificationDomain ?? ""
                }
                
                if let friendConfig = respone.rawResponseObj?.data?.friendConfig {
                    config.enableFriendOnlineNotify = friendConfig.enableFriendOnlineNotify ?? false
                    config.friendOnlineNotifyIntervalSeconds = friendConfig.friendOnlineNotifyIntervalSeconds ?? 600
                }
                
                if let commonImConfig = respone.rawResponseObj?.data?.imConfig {
                    config.imConfigUnsupportMsgTips = commonImConfig.unsupportedMsgTips
                    config.enableMsgRecall = commonImConfig.enableMsgRecall ?? false
                    config.msgRecallMaxMinutes = commonImConfig.msgRecallMaxMinutes ?? 24 * 60
                    
                    config.enableMsgForward = commonImConfig.enableMsgForward ?? false
                    config.enableReferMsg = commonImConfig.enableReferMsg ?? true   // 敏杰要求默认值改成开启
                    config.enableMap = commonImConfig.mapConfig?.enableMap ?? false
                    config.enableMapNearbyPlace = commonImConfig.mapConfig?.enableMapNearbyPlace ?? false
                    config.enableMapPlaceSearch = commonImConfig.mapConfig?.enableMapPlaceSearch ?? false
                    
                    config.nearbyPlaceRadius = commonImConfig.mapConfig?.nearbyPlaceRadius ?? 1000
                    config.placeSearchRadius = commonImConfig.mapConfig?.placeSearchRadius ?? 1000
                    config.nearbyPlaceDistanceThreshold = commonImConfig.mapConfig?.nearbyPlaceDistanceThreshold ?? 50
                    config.enableMsgHyperlinkParse =  commonImConfig.enableMsgHyperlinkParse ?? false
                    config.traceSamplingRate = commonImConfig.traceSamplingRate ?? 100
                    config.traceTag = commonImConfig.traceTag
                    config.enableVadCheck = commonImConfig.enableVadCheck ?? false
                    config.maxFileCount = commonImConfig.maxFileCount ?? 20
                    config.maxSingleFileSize = commonImConfig.maxSingleFileSize ?? 2 * 1024
                    config.totalFileReminderSize = commonImConfig.totalFileReminderSize ?? 256
                }
                
                if let onAirConfig = respone.rawResponseObj?.data?.onAirConfig{
                    config.enableOnAir = onAirConfig.enableOnAir
                    config.groupOnAirMaxMemberNum = onAirConfig.groupOnAirMaxMemberNum
                    
                    config.drawNumPerSec  = onAirConfig.giftConfig.drawNumPerSec
                    config.batchSendNum = onAirConfig.giftConfig.batchSendNum
                    config.maxShowNum = onAirConfig.giftConfig.maxShowNum
                    
                }
                
                if let asrConfig = respone.rawResponseObj?.data?.translateConfig {
                    config.translateTimeout =  asrConfig.translateTimeout
                }

                if let voiceFilterConfig = respone.rawResponseObj?.data?.voiceFilterConfig {
                    config.enableVoiceFilter = voiceFilterConfig.enableVoiceFilter
                    config.voiceFilterPopupDelay = voiceFilterConfig.voiceFilterPopupDelay
                    config.voiceFilterLastestTimestamp = voiceFilterConfig.latestTimestamp ?? 0
                }
                
                if let shareConfig = respone.rawResponseObj?.data?.shareConfig {
                    config.videoTemplateUrl = shareConfig.videoTemplate?.videoTemplateUrl
                    config.videoTemplateMd5 = shareConfig.videoTemplate?.videoTemplateMd5
                    config.videoPreviewTemplateUrl = shareConfig.videoTemplate?.videoPreviewTemplateUrl
                }
                
                if let livePlaceConfig = respone.rawResponseObj?.data?.livePlaceConfig{
                    config.enableGroupLivePlace = livePlaceConfig.enableGroupLivePlace
                    config.enablePersonalLivePlace = livePlaceConfig.enablePersonalLivePlace
                    config.groupMaxMemberNum = livePlaceConfig.groupMaxMemberNum
                    config.personalMaxMemberNum = livePlaceConfig.personalMaxMemberNum
                    config.livePlaceReconnectRetryDuration = livePlaceConfig.reconnectRetryDuration
                }
                
                /// ⚠️  配置本地磁盘缓存，如果需要缓存请把配置解析写在代码之前  ⚠️
                if let msgAutoPlayTime = respone.rawResponseObj?.data?.commonConfig?.msgAutoPlayTime{
                    MessageAutoPlayTimeStoreCache.setMessageAutoPlayTime(value: msgAutoPlayTime)
                }
                if let groupMsgAutoPlayTime = respone.rawResponseObj?.data?.commonConfig?.groupMsgAutoPlayTime{
                    MessageAutoPlayTimeStoreCache.setGroupMessageAutoPlayTime(value: groupMsgAutoPlayTime)
                }
                
                self.saveBuzShareStore()
                
                self.appGlobalConfigWithLoginStatus = config
                
                self.notifyObservers { observer in
                    observer.appConfiger?(configer: self, loggedConfigDidRequstSuccess: config)
                }
                self.publisher.loggedConfigDidRequstSuccess.send(config)
                
                for complete in self.isFetchingAppGlobalConfigInLoginStatusCompletes {
                    complete(config , .network)
                }
                self.isFetchingAppGlobalConfigInLoginStatusCompletes.removeAll()
                if let data : Data = config.yy_modelToJSONData()
                {
                    MMKV.buz_async { kv in
                        kv.set(data, forKey: storeKey)
                    }
                }
            }
        }
        
        if isForceRefresh {
            requestConfigAndCache()
            return
        } else {
            if let config = self.appGlobalConfigWithLoginStatus {
                DispatchQueue.main.safeAsyncUIQueue {
                    completion?(config , .memory)
                }
                return
            }
        }
        
        //避免后期配置数据过大 放异步访问
        MMKV.buz_async { [weak self] kv in
            
            if let data = kv.data(forKey: storeKey) {
                DispatchQueue.main.safeAsyncUIQueue {
                    let value = AppGlobalConfigInLoginStatus.yy_model(withJSON: data)
                    self?.appGlobalConfigWithLoginStatus = value
                    
                    do {
                        let json = try JSONSerialization.jsonObject(with: data)
                        if let dict = json as? [String : Any] , dict["isActivatePTTByIM"] != nil {
                        }else{
                            //当前缓存的配置没有 isActivatePTTByIM 字段 ， 默认为YES
                            self?.appGlobalConfigWithLoginStatus?.isActivatePTTByIM = true
                        }
                    }catch{
                        BuzLog.error("check config have isActivatePTTByIM error \(error)")
                    }
                }
            }
            
        } complete: {

            //main queue
            if let config = self.appGlobalConfigWithLoginStatus {
                completion?(config , .disk)
            }else{
                requestConfigAndCache()
            }
        }
    }
    
    func saveBuzShareStore() {
        // 将一些配置存入共享缓存
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId), let config = self.appGlobalConfigWithLoginStatus else {
            return
        }
        
        BuzASRLog.info("****Buz_ASR config notificationAsrPreviewTimeout:\(config.notificationAsrPreviewTimeout)")
        BuzASRLog.info("****Buz_ASR config notificationAsrPreviewSwitch:\(config.notificationAsrPreviewSwitch)")

        userDefaults.set(config.notificationAsrPreviewTimeout, forKey: "BuzShareStore_nseAsrTimeout")
        userDefaults.set(config.notificationAsrPreviewSwitch, forKey: "BuzShareStore_nseAsrSwitch")
        
        switch Environments.standard.envPosition {
        case .product:
            userDefaults.set(config.offlineNotificationDomain, forKey: "BuzShareStore_nseDomain_product")
        case .pre:
            userDefaults.set(config.offlineNotificationDomain, forKey: "BuzShareStore_nseDomain_pre")
        case .tower:
            userDefaults.set(config.offlineNotificationDomain, forKey: "BuzShareStore_nseDomain_tower")
        }
    }
    
}


// MARK: --Combine API---------
public extension BuzCombineKit where Base: BuzAppConfiger {
    func fetchAppGlobalConfigWithoutLogin(isForceRefresh: Bool = false) -> AnyPublisher<AppGlobalConfigWithoutLogin?, Never> {
        return Future { promise in
            self.object.fetchAppGlobalConfigWithoutLogin(isForceRefresh: isForceRefresh) { result in
                promise(.success(result))
            }
        }
        .eraseToAnyPublisher()
    }
    
    func fetchAppGlobalConfigInLoginStatus(isForceRefresh: Bool = false) -> AnyPublisher<(AppGlobalConfigInLoginStatus, BuzAppConfiger.DataSourceType), Never> {
        return Future { promise in
            self.object.fetchAppGlobalConfigInLoginStatus(isForceRefresh: isForceRefresh) { result, type in
                promise(.success((result, type)))
            }
        }
        .eraseToAnyPublisher()
    }
}
