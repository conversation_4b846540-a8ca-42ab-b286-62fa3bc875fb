//
//  BuzSourceType.swift
//  BuzAppConfig
//
//  Created by lizhifm on 2024/3/4.
//

import Foundation
import Logz

public enum BuzModuleLogType : String {
    case none = ""
    case BuzStorage = "BuzLocalStorage"
    case BuzEmojiBlindBox = "BuzEmojiBlindBox"
    case BuzDownloadBox = "BuzDownloadBox"
    case BuzLastMessage = "BuzLastMessage"
    case BuzAudioSessionManager = "BuzAudioSessionManager"
    case BuzMediaPlayer = "BuzMediaPlayer"
    case BuzMemeModule = "BuzMemeModule"
    case MediaCallModule = "MediaCallModule"
    case BuzDownload = "BuzDownload"
    case BuzHistoryChat = "BuzHistoryChat"
    case BuzConversationList = "BuzConversationList"
    
    public func log(_ content : String) {
        SLogz.info(tag: self.rawValue, content)
    }
    
    public func error(_ content : String) {
        SLogz.error(tag: self.rawValue, content)
    }
    
    public func debug(_ content : String) {
        SLogz.debug(tag: self.rawValue, content)
    }
    
    public func warning(_ content : String) {
        SLogz.warning(tag: self.rawValue, content)
    }
}
