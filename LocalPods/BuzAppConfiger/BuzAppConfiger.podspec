Pod::Spec.new do |s|
  s.name        = "BuzAppConfiger"
  s.version     = "1.0.0"
  s.summary     = "SwiftyJSON makes it easy to deal with JSON data in Swift"
  s.homepage    = "https://github.com/BuzAppConfiger/BuzAppConfiger"
  s.license     = { :type => "MIT" }
  s.authors     = { "lingoer" => "<EMAIL>", "tangplin" => "<EMAIL>" }

  s.requires_arc = true
  s.swift_version = "5.0"
  s.osx.deployment_target = "10.9"
  s.ios.deployment_target = "9.0"
  s.watchos.deployment_target = "3.0"
  s.tvos.deployment_target = "9.0"
  s.source   = { :git => "https://github.com/BuzAppConfiger/BuzAppConfiger.git", :tag => s.version }
  s.source_files = "Classes/*.{swift}"
  
  s.subspec 'AppConfiger' do |sp|
    sp.source_files = 'Classes/AppConfiger/**/*.{h,m,swift}'
    sp.requires_arc = ['Classes/AppConfiger/**/*.{m,swift}']
    
    sp.dependency 'BuzIDL'
    sp.dependency 'ITNetLibrary'
    sp.dependency 'BuzDataStore'
    sp.dependency 'BuzLog'
    sp.dependency 'BuzNetworker'
    sp.dependency 'BuzFoundation'
    sp.dependency 'BuzConfig'
  end
  
  s.subspec 'Else' do |sp|
      sp.source_files = 'Classes/Else/**/*.{h,m,swift}'
      sp.requires_arc = ['Classes/Else/**/*.{m,swift}']
  end

  s.dependency 'YYModel'
  s.dependency 'BuzLocalizable'
  
end
