//
//  NSDictionary+Safe.h
//  LizhiFM
//
//  Created by <PERSON>wu<PERSON> on 17/4/11.
//  Copyright © 2017年 yibasan. All rights reserved.
//  一种更加安全的方式获取字典内的值

#import <Foundation/Foundation.h>
/**
 *  一种更加安全的方式获取字典内的值
 */
@interface NSDictionary (Safe)

/**
 从字典内获取字符串类型的value

 @param key 关键字
 @return 字符串类型的value，如果该关键字对应的实例不是字符串类型或没值，则返回nil
 */
- (NSString *)stringForKey:(id)key;

/**
 从字典内获取字符串类型的value

 @param key 关键字
 @param fall 如果该关键字对应的实例不是字符串类型或没值，则返回的对象
 @return 字符串类型的value，如果该关键字对应的实例不是字符串类型或没值，则返回fall
 */
- (NSString *)stringForKey:(id)key or:(NSString *)fall;


/**
 从字典内获取NSNumber类型的value
 
 @param key 关键字
 @return NSNumber类型的value，如果该关键字对应的实例不是NSNumber类型或没值，则返回nil
 */
- (NSNumber *)numberForKey:(id)key;

/**
 从字典内获取NSNumber类型的value
 
 @param key 关键字
 @param fall 如果该关键字对应的实例不是NSNumber类型或没值，则返回的对象
 @return NSNumber类型的value，如果该关键字对应的实例不是NSNumber类型或没值，则返回fall
 */
- (NSNumber *)numberForKey:(id)key or:(NSNumber *)fall;


/**
 从字典内获取NSDictionary类型的value
 
 @param key 关键字
 @return NSDictionary类型的value，如果该关键字对应的实例不是NSDictionary类型或没值，则返回nil
 */
- (NSDictionary *)dictionaryForKey:(id)key;

/**
 从字典内获取NSDictionary类型的value
 
 @param key 关键字
 @param fall 如果该关键字对应的实例不是NSDictionary类型或没值，则返回的对象
 @return NSDictionary类型的value，如果该关键字对应的实例不是NSDictionary类型或没值，则返回fall
 */
- (NSDictionary *)dictionaryForKey:(id)key or:(NSDictionary *)fall;


/**
 从字典内获取NSArray类型的value
 
 @param key 关键字
 @return NSArray类型的value，如果该关键字对应的实例不是NSArray类型或没值，则返回nil
 */
- (NSArray *)arrayForKey:(id)key;

/**
 从字典内获取NSArray类型的value
 
 @param key 关键字
 @param fall 如果该关键字对应的实例不是NSArray类型或没值，则返回的对象
 @return NSArray类型的value，如果该关键字对应的实例不是NSArray类型或没值，则返回fall
 */
- (NSArray *)arrayForKey:(id)key or:(NSArray *)fall;

/**
 从字典内获取NSData类型的value
 
 @param key 关键字
 @return NSData类型的value，如果该关键字对应的实例不是NSData类型或没值，则返回nil
 */
- (NSData *)dataForKey:(id)key;

/**
 从字典内获取NSData类型的value
 
 @param key 关键字
 @param fall 如果该关键字对应的实例不是NSData类型或没值，则返回的对象
 @return NSData类型的value，如果该关键字对应的实例不是NSData类型或没值，则返回fall
 */
- (NSData *)dataForKey:(id)key or:(NSData *)fall;


/**
 从字典内获取指定类型的value
 
 @param key 关键字
 @param cls 指定的类型
 @return 指定类型的value，如果该关键字对应的实例不是指定的类型或没值，则返回nil
 */
- (id)objectForKey:(id)key expectedClass:(Class)cls;

/**
 从字典内获取指定类型的value
 
 @param key 关键字
 @param cls 指定的类型
 @param fall 如果该关键字对应的实例不是指定的类型或没值，则返回的对象
 @return 指定类型的value，如果该关键字对应的实例不是指定的类型或没值，则返回fall
 */
- (id)objectForKey:(id)key expectedClass:(Class)cls or:(id)fall;

@end

@interface NSDictionary (SafeCompatible)

/**
 从字典内获取字符串类型的value，如果字符串类型value不存在，则获取NSNumber类型的vaule替代

 @param key 关键字
 @return 字符串类型的value，如果字符串类型value不存在，则获取NSNumber类型的vaule替代
 */
- (NSString *)stringForKeyCompatibleNumber:(id)key;

/**
 从字典内获取NSNumber类型的value，如果NSNumber类型value不存在，则获取字符串类型的vaule替代
 
 @param key 关键字
 @return NSNumber类型的value，如果NSNumber类型value不存在，则获取字符串类型的vaule替代
 */
- (NSNumber *)numberForKeyCompatibleString:(id)key;

@end
