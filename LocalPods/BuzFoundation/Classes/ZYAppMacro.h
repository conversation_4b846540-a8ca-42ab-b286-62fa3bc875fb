//
//  ZYAppMacro.h
//  Tiya
//
//  Created by yuan on 2019/7/22.
//  Copyright © 2019 lizhi. All rights reserved.
//

#ifndef ZYAppMacro_h
#define ZYAppMacro_h

#import <pthread.h>

#define TYPE_STR(NAME)                          @#NAME
//常用的基础宏定义
#define TYPE_AND(VA,VB)                        ((VA)&(VB))
#define TYPE_OR(VA,VB)                         ((VA)|(VB))
#define TYPE_LS(VA,LN)                         ((VA) << (LN))
#define TYPE_RS(VA,RN)                         ((VA) >> (RN))
#define TYPE_NOT(VAL)                          (!(VAL))
#define TYPE_INT_MASK                          (-1)

#define IS_EVEN_INTEGER(V)                     (TYPE_AND(V,1) == 0)
#define IS_ODD_INTEGER(V)                      TYPE_AND(V,1)

#define MSEC_PER_SEC                            (1000)
#define SEC_PER_MIN                             (60)
#define SEC_PER_HOUR                            (3600)



#define SUPPRESS_AVAILABILITY_BEGIN         _Pragma("clang diagnostic push") \
                                            _Pragma("clang diagnostic ignored \"-Wunsupported-availability-guard\"")\
                                            _Pragma("clang diagnostic ignored \"-Wunguarded-availability-new\"")

#define SUPPRESS_AVAILABILITY_END           _Pragma("clang diagnostic pop")

#define AVAILABLE_GUARD(platform, os, future, conditions, IfAvailable, IfUnavailable) \
                                            SUPPRESS_AVAILABILITY_BEGIN \
                                            if (__builtin_available(platform os, future) && conditions) {\
                                                SUPPRESS_AVAILABILITY_END \
                                                if (@available(platform os, future)) { \
                                                    IfAvailable \
                                                } \
                                                else { \
                                                    IfUnavailable \
                                                } \
                                            } \
                                            else { \
                                                SUPPRESS_AVAILABILITY_END \
                                                IfUnavailable \
                                            } \

#define IOS_AVAILABLE_GUARD(os, conditions, IfAvailable, IfUnavailable)     \
                                            AVAILABLE_GUARD(iOS, os, *, conditions, IfAvailable, IfUnavailable)



//屏幕尺寸大小和导航栏的一些宏定义
#define SCREEN_BOUNDS                          [UIScreen mainScreen].bounds
#define SCREEN_SCALE                           [UIScreen mainScreen].scale

#define SCREEN_WIDTH                           [UIScreen mainScreen].bounds.size.width
#define SCREEN_HEIGHT                          [UIScreen mainScreen].bounds.size.height



#define AVAILABLE_IOS_V(IOS_V)                 ({ \
                                                    BOOL OK = NO; \
                                                    if(@available(iOS IOS_V,*)) \
                                                        OK = YES; \
                                                    OK; \
                                                })
#define AVAILABLE_IOS_11                        AVAILABLE_IOS_V(11.0)

#define SAFE_INSETS                            (SUPPRESS_AVAILABILITY_BEGIN \
                                                AVAILABLE_IOS_11 ? [[UIApplication sharedApplication].windows firstObject].safeAreaInsets : UIEdgeInsetsZero \
                                                SUPPRESS_AVAILABILITY_END)
#define SAFE_X                                 (AVAILABLE_IOS_11 ? SAFE_INSETS.left : 0)
#define SAFE_Y                                 (AVAILABLE_IOS_11 ? SAFE_INSETS.top : 0)
#define SAFE_BOTTOM                            (AVAILABLE_IOS_11 ? SAFE_INSETS.bottom : 0)
#define SAFE_RIGHT                             (AVAILABLE_IOS_11 ? SAFE_INSETS.right : 0)

#define SAFE_WIDTH                             (AVAILABLE_IOS_11 ? SCREEN_WIDTH - SAFE_INSETS.left - SAFE_INSETS.right : SCREEN_WIDTH)
#define SAFE_HEIGHT                            (AVAILABLE_IOS_11 ? SCREEN_HEIGHT - SAFE_INSETS.top - SAFE_INSETS.bottom : SCREEN_HEIGHT)
#define SAFE_SIZE                              CGSizeMake(SAFE_WIDTH,SAFE_HEIGHT)
#define SAFE_FRAME                             CGRectMake(SAFE_X,SAFE_Y,SAFE_WIDTH,SAFE_HEIGHT)
#define SAFE_BOUNDS                            CGRectMake(0,0,SAFE_WIDTH,SAFE_HEIGHT)
#define STATUS_BAR_FRAME                       [[UIApplication sharedApplication] statusBarFrame]
#define STATUS_BAR_HEIGHT                      STATUS_BAR_FRAME.size.height

#define CONST_NAV_BAR_HEIGHT                   (44)


//弱引用
#define WEAK_NSOBJ(NSOBJ,WEAK_NAME)             __weak __typeof(&*NSOBJ) WEAK_NAME = NSOBJ
#define WEAK_SELF(WEAK_NAME)                    __weak __typeof(&*self) WEAK_NAME = self
#define STRONG_SELF(STRONG_NAME)                __strong __typeof(&*weakSelf) STRONG_NAME = weakSelf
#define STRONG_SELF_NIL_RETURN(STRONG_NAME,R)     STRONG_SELF(STRONG_NAME); if (!STRONG_NAME) return R;

#if defined(DEBUG) && !defined(NDEBUG)
#define zy_keywordify autoreleasepool {}
#else
#define zy_keywordify try {} @catch (...) {}
#endif

#define zy_concat(A, B) A ## B
#define weakify(VAR) zy_keywordify __weak typeof(VAR) zy_concat(VAR, _weak_) = VAR;
#define strongify(VAR) zy_keywordify __strong typeof(VAR) VAR = zy_concat(VAR, _weak_);


#define NEW_NORMAL_FORMAT_STRING(OBJ)           ((OBJ) ? [[NSString alloc] initWithFormat:@"%@",OBJ] : (nil))

#define IS_OBJ_CLASS(OBJ,CLS)                       [OBJ isKindOfClass:[CLS class]]
#define IS_NULL_OBJ(OBJ)                            IS_OBJ_CLASS(OBJ,NSNull)
#define IS_NONNULL_OBJ(OBJ)                         (!IS_NULL_OBJ(OBJ))

#define NSOBJECT_VALUE(DICT,NAME)                   DICT[TYPE_STR(NAME)]

#define IS_NULL_VALUE(DICT,NAME)                    IS_OBJ_CLASS(NSOBJECT_VALUE(DICT,NAME), NSNull)

#define IS_NONNULL_VALUE(DICT,NAME)                 (!IS_NULL_VALUE(DICT,NAME))

#define BOOL_VALUE(DICT,NAME)                       (IS_NULL_VALUE(DICT,NAME) ? NO : [NSOBJECT_VALUE(DICT, NAME) boolValue])

#define FLOAT_VALUE(DICT,NAME)                      (IS_NULL_VALUE(DICT,NAME) ? 0 : [NSOBJECT_VALUE(DICT, NAME) floatValue])

#define INTEGER_VALUE(DICT,NAME)                    (IS_NULL_VALUE(DICT,NAME) ? 0 : [NSOBJECT_VALUE(DICT, NAME) integerValue])

#define INT_VALUE(DICT,NAME)                        (IS_NULL_VALUE(DICT,NAME) ? 0 : [NSOBJECT_VALUE(DICT, NAME) intValue])

#define STRING_VALUE(DICT,NAME)                     (IS_NULL_VALUE(DICT,NAME) ? nil : NEW_NORMAL_FORMAT_STRING(DICT[TYPE_STR(NAME)]))

#define NUMBER_VALUE(DICT,NAME)                     (IS_OBJ_CLASS(NSOBJECT_VALUE(DICT,NAME), NSNumber) ? NSOBJECT_VALUE(DICT,NAME) : nil)

#define DATA_VALUE(DICT,NAME)                       (IS_OBJ_CLASS(NSOBJECT_VALUE(DICT,NAME), NSData) ? NSOBJECT_VALUE(DICT,NAME) : nil)

#define DICTIONARY_VALUE(DICT,NAME)                 (IS_OBJ_CLASS(NSOBJECT_VALUE(DICT,NAME), NSDictionary) ? NSOBJECT_VALUE(DICT,NAME) : nil)

#define ARRAY_VALUE(DICT,NAME)                     (IS_OBJ_CLASS(NSOBJECT_VALUE(DICT,NAME), NSArray) ? NSOBJECT_VALUE(DICT,NAME) : nil)


#define USEC_FROM_DATE_SINCE1970(DATE)              ((uint64_t)([DATE timeIntervalSince1970] * USEC_PER_SEC))
#define DATE_FROM_USEC_SINCE1970(TIME)              ([NSDate dateWithTimeIntervalSince1970:TIME * 1.0/USEC_PER_SEC])

#define MSEC_FROM_DATE_SINCE1970(DATE)              ((uint64_t)([DATE timeIntervalSince1970] * MSEC_PER_SEC))
#define DATE_FROM_MSEC_SINCE1970(TIME)              ([NSDate dateWithTimeIntervalSince1970:TIME * 1.0/MSEC_PER_SEC])

#define USEC_FROM_DATE_SINCE1970_NOW                USEC_FROM_DATE_SINCE1970([NSDate date])
#define MSEC_FROM_DATE_SINCE1970_NOW                MSEC_FROM_DATE_SINCE1970([NSDate date])

#define FLOAT_EQUAL_DIFFER                          (0.001)
#define FLOAT_EQUAL(FA,FB)                          (fabs(FA-FB) <= FLOAT_EQUAL_DIFFER)

#define FRAME                                       CGRectMake(x, y, w, h)


static inline void dispatch_async_in_main_queue(void (^block)(void)) {
    dispatch_async(dispatch_get_main_queue(), block);
}

static inline void dispatch_in_main_queue(void (^block)(void)) {
    if (pthread_main_np()) {
        block();
    } else {
        dispatch_async(dispatch_get_main_queue(), block);
    }
}

static inline void dispatch_after_in_main_queue(NSTimeInterval after ,void (^block)(void)) {
    dispatch_after(dispatch_time(DISPATCH_TIME_NOW, after * NSEC_PER_SEC), dispatch_get_main_queue(), block);
}

static inline void sync_lock(dispatch_semaphore_t lock, void (^block)(void)) {
    dispatch_semaphore_wait(lock, DISPATCH_TIME_FOREVER);
    if (block) {
        block();
    }
    dispatch_semaphore_signal(lock);
}

typedef void(^YZH_cleanupBlock_t)(void);
static inline void YZH_executeCleanupBlock (YZH_cleanupBlock_t *block) {
    (*block)();
}

#ifndef dispatch_main_async_safe
#define dispatch_main_async_safe(block)\
    if (dispatch_queue_get_label(DISPATCH_CURRENT_QUEUE_LABEL) == dispatch_queue_get_label(dispatch_get_main_queue())) {\
        block();\
    } else {\
        dispatch_async(dispatch_get_main_queue(), block);\
    }
#endif

#ifndef dispatch_main_sync_safe
#define dispatch_main_sync_safe(block)\
    if (dispatch_queue_get_label(DISPATCH_CURRENT_QUEUE_LABEL) == dispatch_queue_get_label(dispatch_get_main_queue())) {\
        block();\
    } else {\
        dispatch_sync(dispatch_get_main_queue(), block);\
    }
#endif


#define _ATTR_CLEANUP(CF)                       __attribute__((cleanup(CF), unused))
#define _TYPE_ATTR_CLEANUP_DEC(T, IV, F, V)     T IV _ATTR_CLEANUP(F) = V
#define ON_RETURN                               _TYPE_ATTR_CLEANUP_DEC(YZH_cleanupBlock_t, zy_concat(imf_returnBlock_,__LINE__), YZH_executeCleanupBlock, ^)


#endif /* ZYAppMacro_h */
