//
//  DispatchQueueExtension.swift
//  buz
//
//  Created by liuyufeng on 2022/6/10.
//  Copyright © 2022 lizhi. All rights reserved.
//

import Foundation

public extension DispatchQueue {
    
    typealias Task = () -> Void

    func safeAsyncUIQueue(_ block: @escaping Task) {
        if self === DispatchQueue.main && Thread.isMainThread {
            block()
        } else {
            DispatchQueue.main.async { block() }
        }
    }

    @discardableResult
    func delay(_ seconds: Double, _ task: @escaping Task) -> DispatchWorkItem {
        let item = DispatchWorkItem(block: task)
        self.asyncAfter(deadline: DispatchTime.now() + seconds, execute: item)
        return item
    }

    func safeAsync(_ queue: DispatchQueue , _ task: @escaping Task) {

        let queueLable = __dispatch_queue_get_label(queue)
        let queueLableStr = String(cString: queueLable, encoding: .utf8)

        let currentQueueLable = __dispatch_queue_get_label(nil)
        let currentQueueLableStr = String(cString: currentQueueLable, encoding: .utf8)

        if queueLableStr == currentQueueLableStr {
            task()
        }else{
            queue.async {
                task()
            }
        }
    }

    //业务使用的全局并发队列
    static func defaultGlobal() -> DispatchQueue {
        return QueueManager.shared.defaultGlobalQueue
    }

    //业务使用的IO队列
    static func defaultIOQueue() -> DispatchQueue {
        return QueueManager.shared.ioQueue
    }
    
    //通讯录专用的并发队列
    static func contactQueue() -> DispatchQueue {
        return QueueManager.shared.contactsQueue
    }
    
    static  func resendSwapQueue() -> DispatchQueue {
        return QueueManager.shared.resendSwapQueue
    }
    
    static  func mixedSoundQueue() -> DispatchQueue {
        return QueueManager.shared.mixedSoundQueue
    }
}


public class DispatchOnce {
    
    private static var _onceTracker = [String]()
    
    private static func once(token: String, block: () -> Void) {
        objc_sync_enter(self)
        defer {
            objc_sync_exit(self)
        }
        guard !_onceTracker.contains(token) else { return }
        _onceTracker.append(token)
        block()
    }

    public static func execTask(file: String = #file, function: String = #function, line: Int = #line, block: () -> Void) {
        let token = "\(file):\(function):\(line)"
        once(token: token, block: block)
    }
}
