//
//  DelayedTaskManager.swift
//  buz
//
//  Created by lizhi on 2024/7/7.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation

public class DelayedTaskManager {
    
    public static let shared = DelayedTaskManager()
    
    private var tasks: [String: DispatchWorkItem] = [:]
    private let queue = DispatchQueue(label: "DelayedTaskManagerQueue", attributes: .concurrent)

    public func scheduleTask(withId taskId: String, after delay: TimeInterval, action: @escaping () -> Void) {
        // 创建新的WorkItem
        let workItem = DispatchWorkItem {
            action()
            self.queue.async(flags: .barrier) {
                self.tasks.removeValue(forKey: taskId)
            }
        }

        // 使用并发队列安全地存储任务
        queue.async(flags: .barrier) {
            self.tasks[taskId] = workItem
        }
        
        // 在指定的时间后执行新任务
        DispatchQueue.main.asyncAfter(deadline: .now() + delay, execute: workItem)
    }

    public func cancelTask(withId taskId: String) {
        // 使用并发队列安全地取消任务
        queue.async(flags: .barrier) {
            self.tasks[taskId]?.cancel()
            self.tasks.removeValue(forKey: taskId)
        }
    }
    
    public func cancelAllTask() {
        // 使用并发队列安全地取消所有任务
        queue.async(flags: .barrier) {
            for (_, workItem) in self.tasks {
                workItem.cancel()
            }
            self.tasks.removeAll()
        }
    }
}
