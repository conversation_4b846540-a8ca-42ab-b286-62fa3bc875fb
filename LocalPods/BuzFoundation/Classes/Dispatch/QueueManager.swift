//
//  QueueManager.swift
//  buz
//
//  Created by liuyufeng on 2022/6/10.
//  Copyright © 2022 lizhi. All rights reserved.
//

import UIKit

public class QueueManager: NSObject {
    
    @objc public static let shared = QueueManager()
    
    @objc public private(set) lazy var deviceIOQueue : DispatchQueue = {
        let ioQueue = DispatchQueue.init(label: "com.buz.business.deviceIO")
        return ioQueue
    }()
    
    @objc public private(set) lazy var ioQueue : DispatchQueue = {
        let ioQueue = DispatchQueue.init(label: "com.buz.business.io")
        return ioQueue
    }()
    
    @objc public private(set) lazy var contactsQueue : DispatchQueue = {
        let ioQueue = DispatchQueue.init(label: "com.buz.business.contacts")
        return ioQueue
    }()
    

    @objc private(set) lazy var resendSwapQueue : DispatchQueue = {
        let ioQueue = DispatchQueue.init(label: "com.buz.business.resendSwapQueue")
        return ioQueue
    }()
    
    @objc private(set) lazy var mixedSoundQueue : DispatchQueue = {
        let ioQueue = DispatchQueue.init(label: "com.buz.business.mixedSoundQueue")
        return ioQueue
    }()
    
   

    @objc public var defaultGlobalQueue : DispatchQueue {

        get {
            return YYDispatchQueueGetForQOS(.default)
        }
    }
}
