//
//  Protected.Sequence.swift
//  buz
//
//  Created by yutao on 2022/8/26.
//  Copyright © 2022 lizhi. All rights reserved.
//

import Foundation

extension Protected where T: RangeReplaceableCollection {
    /// Adds a new element to the end of this protected collection.
    ///
    /// - Parameter newElement: The `Element` to append.
    func append(_ newElement: T.Element) {
        write { (ward: inout T) in
            ward.append(newElement)
        }
    }

    /// Adds the elements of a sequence to the end of this protected collection.
    ///
    /// - Parameter newElements: The `Sequence` to append.
    func append<S: Sequence>(contentsOf newElements: S) where S.Element == T.Element {
        write { (ward: inout T) in
            ward.append(contentsOf: newElements)
        }
    }

    /// Add the elements of a collection to the end of the protected collection.
    ///
    /// - Parameter newElements: The `Collection` to append.
    func append<C: Collection>(contentsOf newElements: C) where C.Element == T.Element {
        write { (ward: inout T) in
            ward.append(contentsOf: newElements)
        }
    }
    
    /// Removes and returns the first element of the collection.
    func removeFirst() {
        write { ward in
            ward.removeFirst()
        }
    }
    
    /// Removes all elements from the array.
    func removeAll() {
        write { ward in
            ward.removeAll()
        }
    }
}
