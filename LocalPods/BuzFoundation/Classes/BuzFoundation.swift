//
//  BuzCoreCenter.swift
//  buz
//
//  Created by st.chio on 2024/7/23.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation

//MARK: --BuzFoundation NameSpace-----
public struct BuzFoundation<Base> {
    public let base: Base
    public init(_ base: Base) {
        self.base = base
    }
}

public protocol BuzFoundationProtocol {}

public extension BuzFoundationProtocol {
    var buzFoundation: BuzFoundation<Self> {
        return BuzFoundation(self)
    }

    static var buzFoundation: BuzFoundation<Self>.Type {
        return BuzFoundation.self
    }
}

extension NSObject: BuzFoundationProtocol {}


//MARK: - BuzCombineKit----
public struct BuzCombineKit<Base> {
    public let object: Base
    internal init(_ object: Base) {
        self.object = object
    }
}
public protocol BuzCombineKitWrappable {}

public extension BuzCombineKitWrappable {
    var combine: BuzCombineKit<Self> {
        return BuzCombineKit(self)
    }
    
    static var combine: BuzCombineKit<Self>.Type {
        return BuzCombineKit<Self>.self
    }
}

extension NSObject: BuzCombineKitWrappable {}
