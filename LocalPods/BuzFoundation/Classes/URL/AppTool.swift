//
//  AppTool.swift
//  buz
//
//  Created by l<PERSON><PERSON><PERSON> on 2023/5/6.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
//import PushToTalkLib
public class AppTool :NSObject{
    public static func isEmptyStr(str: String?)-><PERSON><PERSON>{
        guard let str = str else {return true}
        return str.count == 0
    }
    public static func isURL(_ string: String?) -> <PERSON><PERSON> {
        guard let string = string else {return false}
        let detector = try? NSDataDetector(types: NSTextCheckingResult.CheckingType.link.rawValue)
        let matches = detector?.matches(in: string, options: [], range: NSRange(location: 0, length: string.utf16.count))
        if let match = matches?.first {
            return match.range.length == string.utf16.count
        } else {
            return false
        }
    }
}
