//
//  AVAudioSession+Extension.m
//  buz
//
//  Created by yutao on 2022/9/16.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "AVAudioSession+Proority.h"

#import <objc/runtime.h>

@implementation AVAudioSession (Priority)

- (int64_t)increaseAudioSessionPriority {
    NSNumber *priority = objc_getAssociatedObject(self, "audioSessionPriority");
    
    if (priority == nil) {
        objc_setAssociatedObject(self, "audioSessionPriority", @(0), OBJC_ASSOCIATION_RETAIN);
    } else {
        priority = @(priority.longLongValue + 1);
        objc_setAssociatedObject(self, "audioSessionPriority", priority, OBJC_ASSOCIATION_RETAIN);
    }
    
    return priority.longLongValue;
}

- (int64_t)currentAudioSessionPriority {
    NSNumber *priority = objc_getAssociatedObject(self, "audioSessionPriority");
    return priority.longLongValue;
}

@end
