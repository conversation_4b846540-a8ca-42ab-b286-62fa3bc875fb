//
//  AVAudioSession+Extension.m
//  buz
//
//  Created by yutao on 2022/9/16.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "AVAudioSession+Extension.h"

#import "OCBuzLog.h"
#import <objc/runtime.h>

#define CALLSTACK_SYMBOLS @"" //[NSThread callStackSymbols]
#define BuzLogSetUpAudioSessionTag @"BuzLog_AudioSessionSetUp"
#define BuzLogAASLogD(frmt, ...) {LogzTagD(BuzLogSetUpAudioSessionTag, (@"%s [Line %d] " frmt @"\n\n"), __PRETTY_FUNCTION__, __LINE__, ##__VA_ARGS__)};
#define BuzLogAASLogI(frmt, ...) {LogzTagI(BuzLogSetUpAudioSessionTag, (@"%s [Line %d] " frmt @"\n\n"), __PRETTY_FUNCTION__, __LINE__, ##__VA_ARGS__)};


CG_INLINE void exchange(Class clz, SEL sel1, SEL sel2)
{
    Method srcMethod = class_getInstanceMethod(clz, sel1);
    Method tarMethod = class_getInstanceMethod(clz, sel2);
    if (srcMethod && tarMethod) {
        method_exchangeImplementations(srcMethod, tarMethod);
    }
    else {
        NSLog(@"NO SUCH METHOD");
    }
}

@implementation AVAudioSession (Extension)

+ (void)initialize
{
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        exchange(AVAudioSession.class, @selector(setCategory:error:), @selector(buz_setCategory:error:));
        exchange(AVAudioSession.class, @selector(setCategory:withOptions:error:), @selector(buz_setCategory:withOptions:error:));
        exchange(AVAudioSession.class, @selector(setCategory:mode:options:error:), @selector(buz_setCategory:mode:options:error:));
        exchange(AVAudioSession.class, @selector(overrideOutputAudioPort:error:), @selector(buz_overrideOutputAudioPort:error:));
        exchange(AVAudioSession.class, @selector(setActive:error:), @selector(buz_setActive:error:));
        exchange(AVAudioSession.class, @selector(setActive:withOptions:error:), @selector(buz_setActive:withOptions:error:));
        exchange(AVAudioSession.class, @selector(setMode:error:), @selector(buz_setMode:error:));
    });
}

- (BOOL)buz_setActive:(BOOL)active withOptions:(AVAudioSessionSetActiveOptions)options error:(NSError **)outError
{
    BuzLogAASLogI(@"callStack setActive:%d withOptions:%lu error: %@", active , (unsigned long)options ,CALLSTACK_SYMBOLS);
    BOOL result = [self buz_setActive:active withOptions:options error:outError];
    if (outError != nil){
        BuzLogAASLogI(@"AudioSession setActive:withOptions:error: -> error = %@", (*outError));
    }
    return result;
}


- (BOOL)buz_setActive:(BOOL)active error:(NSError **)outError {
    BuzLogAASLogI(@"callStack setActive:%d error: %@", active , CALLSTACK_SYMBOLS);
    BOOL result = [self buz_setActive:active error:outError];
    if (outError != nil){
        BuzLogAASLogI(@"AudioSession setActive:error: -> error = %@", (*outError));
    }
    return result;
}

- (BOOL)buz_overrideOutputAudioPort:(AVAudioSessionPortOverride)portOverride error:(NSError *__autoreleasing  _Nullable *)outError
{
    BuzLogAASLogI(@"callStack portOverride:error: %@", CALLSTACK_SYMBOLS);
    BuzLogAASLogI(@"AudioSession portOverride:%lu", (unsigned long)portOverride);
    BOOL result = [self buz_overrideOutputAudioPort:portOverride error:outError];
    if (outError != nil){
        BuzLogAASLogI(@"AudioSession buz_overrideOutputAudioPort error = %@", (*outError));
    }
    return result;
}

/// Set session category.
- (BOOL)buz_setCategory:(AVAudioSessionCategory)category error:(NSError **)outError API_AVAILABLE(ios(3.0), watchos(2.0), tvos(9.0)) API_UNAVAILABLE(macos)
{
    BuzLogAASLogI(@"callStack setCategory:%@ error: %@", category , CALLSTACK_SYMBOLS);
    BOOL result = [self buz_setCategory:category error:outError];
    if (outError != nil){
        BuzLogAASLogI(@"AudioSession buz_setCategory error = %@", (*outError));
    }
    return result;
}

/// Set session category with options.
- (BOOL)buz_setCategory:(AVAudioSessionCategory)category
        withOptions:(AVAudioSessionCategoryOptions)options
              error:(NSError **)outError
    API_AVAILABLE(ios(6.0), watchos(2.0), tvos(9.0)) API_UNAVAILABLE(macos)
{
    BuzLogAASLogI(@"callStack setCategory:%@ withOptions:%lu【allowBluetooth = %d】 error: %@", category , (unsigned long)options , ((options & AVAudioSessionCategoryOptionAllowBluetooth) == AVAudioSessionCategoryOptionAllowBluetooth) , CALLSTACK_SYMBOLS);
    BOOL result = [self buz_setCategory:category withOptions:options error:outError];
    if (outError != nil)
    {
        BuzLogAASLogI(@"AudioSession buz_setCategory withOptions error = %@", (*outError));
    }
    return result;
}

/// Set session category and mode with options.
- (BOOL)buz_setCategory:(AVAudioSessionCategory)category
               mode:(AVAudioSessionMode)mode
            options:(AVAudioSessionCategoryOptions)options
              error:(NSError **)outError
{
    BuzLogAASLogI(@"callStack setCategory:%@ mode:%@ options:%lu error: %@", category , mode , options, CALLSTACK_SYMBOLS);
    BuzLogAASLogI(@"AudioSession category:%@ - %@", category, mode);
    BOOL result = [self buz_setCategory:category mode:mode options:options error:outError];
    if (outError != nil)
    {
        BuzLogAASLogI(@"AudioSession buz_setCategory mode withOptions error = %@", (*outError));
    }
    return result;
}


- (BOOL)buz_setMode:(AVAudioSessionMode)mode error:(NSError *__autoreleasing  _Nullable *)outError
{
    BuzLogAASLogI(@"setMode:%@  callStack:%@", mode , CALLSTACK_SYMBOLS);
    BOOL result = [self buz_setMode:mode error:outError];
    if (outError != nil)
    {
        BuzLogAASLogI(@"setMode error = %@", (*outError));
    }
    return result;
}

@end
