//
//  Array.swift
//  Tiya
//
//  Created by ch<PERSON><PERSON><PERSON><PERSON> on 2020/6/5.
//  Copyright © 2020 lizhi. All rights reserved.
//

import Foundation

extension Array {
    
    public var second: Element? { elementOfIndex(1) }
    public var third: Element? { elementOfIndex(2) }
    public var fourth: Element? { elementOfIndex(3) }
    
    public func elementOfIndex(_ index: Int) -> Element? {
        if self.count < index+1 {
            return nil
        }
        return self[index]
    }
    
    public subscript(safe index : Index) -> Element?
    {
        let isValidIndex = index >= 0 && index < count
        return isValidIndex ? self[index] : nil
    }
    
    @discardableResult
    public mutating func removeSafely(at index : Index) -> Element?
    {
        let isValidIndex = index >= 0 && index < count
        guard isValidIndex else {
            return nil
        }
        return remove(at: index)
    }
    
}

extension Array {
    
    /// 非线程安全！
    public mutating func iterateElement() -> Element? {
        
        if let firstElement = first {
            append(firstElement)
            removeFirst()
        }
        return last
    }
}
