//
//  NSData+Md5.m
//  LizhiFM电台
//
//  Created by wzsam on 4/15/15.
//  Copyright (c) 2015 yibasan. All rights reserved.
//

#import "NSData+Md5.h"
#import <CommonCrypto/CommonDigest.h>


@implementation NSData (Md5)

- (NSString *)md5
{
    unsigned char digest[CC_MD5_DIGEST_LENGTH];
    
    CC_MD5_CTX md5;
    CC_MD5_Init(&md5);
    CC_MD5_Update(&md5, [self bytes], (CC_LONG)[self length]);
    CC_MD5_Final(digest, &md5);
    
    
    NSString *retString = [NSString stringWithFormat:@"%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X%02X",
                           digest[0], digest[1], digest[2], digest[3], digest[4], digest[5],
                           digest[6], digest[7], digest[8], digest[9], digest[10],
                           digest[11], digest[12], digest[13], digest[14], digest[15]];
    return retString;
}

@end
