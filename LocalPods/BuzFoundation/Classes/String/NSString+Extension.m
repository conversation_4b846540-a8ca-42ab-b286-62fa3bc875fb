//
//  NSString+FormateImageUrl.m
//  buz
//
//  Created by l<PERSON>hi on 2022/6/15.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "NSString+Extension.h"
#import "NSString+Regex.h"


@implementation NSString (Extension)

+ (NSString *)formateImageUrl:(NSString *)url width:(CGFloat)width height:(CGFloat)height{
    NSString *urlString = url;
    NSRange range = [urlString rangeOfString:@"." options:NSBackwardsSearch];
    if (range.location != NSNotFound) {
        NSString *text = [NSString stringWithFormat:@"_%dx%d.%@", (int)width, (int)height, [url pathExtension]];
        if ([url ?: @"" hasSuffix:text ?: @""]) {
            return url;
        }
        NSString *pattern = @"_[\\d]*x+[\\d]*\\.+[\\w]*";
        if ([url isMatchedWithRegex:pattern]) {
            NSString *result = [url stringByReplacingRegex:pattern withString:text];
            return result;
        }else
        {
            NSString *prefix = [urlString substringToIndex:range.location];
            NSString *suffix = [urlString substringFromIndex:range.location];
            NSMutableString *mutableString = [[NSMutableString alloc]initWithString:prefix];
            [mutableString appendFormat:@"_%dx%d",(int)width,(int)height];
            [mutableString appendString:suffix];
            return mutableString;
        }
    }
    return url;
}


// 18812345678 -> 188 1234 5678
- (NSString *)phoneAddWhitespace{
    if (self.length < 4 ) { return self; }
    
    NSMutableString *fieldText = [[NSMutableString alloc] initWithString:self];
    
    if (self.length > 7) {
        [fieldText insertString:@" " atIndex:7];
    }
    
    if (self.length > 3) {
        [fieldText insertString:@" " atIndex:3];
    }
    
    return [fieldText copy];
}

// 188 1234 5678 -> 18812345678
- (NSString *)phoneTrimWhitespace{
    return [self stringByReplacingOccurrencesOfString:@" " withString:@""];
}

- (NSString *)fullPhoneWithCode:(NSString *)code{
    return [NSString stringWithFormat:@"%@-%@", code, self];
}

- (NSString *)trimWithString:(NSString *)string{
    return [self stringByReplacingOccurrencesOfString:string withString:@""];
}

+ (NSString *)timezone
{
//    [[NSTimeZone localTimeZone] abbreviation];  GTM+8  GMT+10:30
    NSTimeZone* destinationTimeZone = [NSTimeZone localTimeZone];
    NSInteger sourceGMTOffset = [destinationTimeZone secondsFromGMTForDate:[NSDate date]]/3600;
    
    if (sourceGMTOffset == 0) return @"GTM+00:00";

    NSString *numStr = [NSString stringWithFormat:@"%ld:00",labs(sourceGMTOffset)];
    NSString *gtmStr = sourceGMTOffset > 0 ? @"GMT+" : @"GMT-";
    NSString *signStr = labs(sourceGMTOffset) < 10 ? @"0" : @"";
    
    return [NSString stringWithFormat:@"%@%@%@",gtmStr,signStr,numStr];;
}


@end
