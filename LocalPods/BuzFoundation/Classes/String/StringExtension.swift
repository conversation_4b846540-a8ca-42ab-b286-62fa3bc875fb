//
//  StringExtension.swift
//  Tiya
//
//  Created by ch<PERSON><PERSON><PERSON><PERSON> on 2020/6/10.
//  Copyright © 2020 lizhi. All rights reserved.
//

import Foundation
import SwiftyJSON
import SwifterSwift

public extension String {
    var intValue: Int {
        guard let ret = Int(self) else {
            return 0
        }
        return ret
    }
    
    func getWidth(height: CGFloat, font: UIFont) -> CGFloat {
        
        let rect = NSString(string: self).boundingRect(with: CGSize(width: CGFloat(MAXFLOAT), height: height), options: .usesLineFragmentOrigin, attributes: [NSAttributedString.Key.font: font], context: nil)
        return ceil(rect.width)
    }
    
    func getHeight(width: CGFloat, font: UIFont) -> CGFloat {
        
        let rect = NSString(string: self).boundingRect(with: CGSize(width: width, height: CGFloat(MAXFLOAT)), options: .usesLineFragmentOrigin, attributes: [NSAttributedString.Key.font: font], context: nil)
        return ceil(rect.height)
    }
    
    func getHeight(width: CGFloat, attributes: [NSAttributedString.Key: Any] ) -> CGFloat {
        
        let rect = NSString(string: self).boundingRect(with: CGSize(width: width, height: CGFloat.greatestFiniteMagnitude), options:  [.usesLineFragmentOrigin], attributes: attributes, context: nil)
        return ceil(rect.height)
    }
    
    func getWidth(height: CGFloat, attributes: [NSAttributedString.Key: Any] ) -> CGFloat {
        
        let rect = NSString(string: self).boundingRect(with: CGSize(width: CGFloat.greatestFiniteMagnitude, height:height ), options:  [.usesLineFragmentOrigin, .usesFontLeading], attributes: attributes, context: nil)
        return ceil(rect.width)
    }
    
    func getSize(MaxWidth: CGFloat, font: UIFont) -> CGSize {
        let mainDescribeParaStyle = NSMutableParagraphStyle()
        mainDescribeParaStyle.lineBreakMode = .byCharWrapping;
        let rect = NSString(string: self).boundingRect(with: CGSize(width: MaxWidth, height: CGFloat(MAXFLOAT)), options: .usesLineFragmentOrigin, attributes: [NSAttributedString.Key.font: font, NSAttributedString.Key.paragraphStyle : mainDescribeParaStyle], context: nil)
        return CGSize(width: rect.width, height: rect.height + 1)
    }
    
    var dictionaryValue: [String : Any]? {
        JSON(parseJSON: self).dictionaryObject
    }
    
    var length: Int {
        return utf16.count
    }
    
    var sequences: [String] {
        var arr = [String]()
        enumerateSubstrings(in: startIndex..<endIndex, options: .byComposedCharacterSequences) { (substring, _, _, _) in
            if let str = substring { arr += [str] }
        }
        return arr
    }
    
    func phoneComponent() -> [String] {
        var component : [String] = []
        var val : String = ""
        var hadRegion = false
        for index in self.indices {
            let character = self[index] // 直接使用索引访问字符，Swift会自动处理Unicode标量值和单一代码点的问题。
            
            if character >= "0" && character <= "9" {
                val.append(character)
            } else if val.length != 0 && character == "-" && !hadRegion {
                component.append(val)
                hadRegion = true
                val = ""
            }
        }
        
        component.append(val)
        return component
    }

    func substring(toLength: Int) -> String {
        guard toLength < length else {
                return self
        }
        var results = String()
        for index in 0 ..< sequences.count {
            if results.length + sequences[index].length <= toLength {
                results.append(sequences[index])
            }
            else {
                return results
            }
        }
        return self
    }
    
    func lineCount(with font: UIFont, at bounds: CGRect) -> Int {
        return getLines(with: font, at: bounds).count
    }
    
    func subString(with lineCount: Int, font: UIFont, at bounds: CGRect) -> String {
        var lines = getLines(with: font, at: bounds)
        guard lines.count > 0 else { return self }
        while lines.count > lineCount {
            lines.removeLast()
        }
        var content = ""
        for line in lines {
            let range = CTLineGetStringRange(line)
            let lineStr = (self as NSString).substring(with: NSRange(location: range.location, length: range.length))
            content.append(lineStr)
        }
       return content
    }
    
    func getLines(with font: UIFont, at bounds: CGRect) -> [CTLine] {
        let path = CGMutablePath()
        path.addRect(bounds)
        let content = NSAttributedString(string: self, attributes: [NSAttributedString.Key.font : font])
        let framesetter = CTFramesetterCreateWithAttributedString(content)
        let frameRef = CTFramesetterCreateFrame(framesetter, CFRangeMake(0, content.length), path, nil)
        if let lines = CTFrameGetLines(frameRef) as? Array<CTLine> {
            return lines
        }
        return [CTLine]()
    }
    
    var isBlank: Bool {
        let trimmedStr = self.trimmingCharacters(in: .whitespacesAndNewlines)
        return trimmedStr.isEmpty
    }
    
    //删除特殊字符
    func deleteSpecialCharacters() -> String {
        let pattern: String = "[^a-zA-Z0-9\u{4e00}-\u{9fa5}]"
        let express = try! NSRegularExpression(pattern: pattern, options: .caseInsensitive)
        return express.stringByReplacingMatches(in: self, options: [], range: NSMakeRange(0, self.count), withTemplate: "")
    }
    
    var numberStringsArray : [String] {
        get {
            let pattern = "\\d+" // 表示匹配数字
            if let regex = try? NSRegularExpression(pattern: pattern, options: []) {
                let result = regex.matches(in: self, options: [], range: NSMakeRange(0, self.count)).map {
                    String(self[Range($0.range, in: self)!])
                }
                return result
            }
            return []
        }
    }
    
    func toDict() -> [String : Any]?{
    
        if let data = data(using: String.Encoding.utf8) ,
           let dict = try? JSONSerialization.jsonObject(with: data, options: .mutableContainers) as? [String : Any]
        {
            return dict
        }
        return nil
    }
}


public extension String {
    func format(_ arguments: CVarArg...) -> String {
        let args = arguments.map {
            if let arg = $0 as? Int { return String(arg) }
            if let arg = $0 as? Float { return String(arg) }
            if let arg = $0 as? Double { return String(arg) }
            if let arg = $0 as? Int64 { return String(arg) }
            if let arg = $0 as? String { return String(arg) }
            if let arg = $0 as? Character { return String(arg) }

            return "(null)"
        } as [CVarArg]

        return String.init(format: self, arguments: args)
    }

}

public extension NSString {
    
    @objc static func failureMessageDate(timestamp : Int64) -> NSString
    {
        let date = Date(timeIntervalSince1970: TimeInterval(timestamp))
        var str = ""
        if date.isInToday {///今天
            str = date.string(withFormat: "hh:mm a")
        }else if date.isInYesterday {///昨天
            str = "Yesterday " + date.string(withFormat: "hh:mm a")
        }else{
            str = date.string(withFormat: "MM/dd hh:mm a")
        }
        return str as NSString
    }
}

public extension String {
    func isSixDigitNumber() -> Bool {
        let regexPattern = "^[0-9]{6}$"
        let regex = try? NSRegularExpression(pattern: regexPattern)
        let matches = regex?.matches(in: self, options: [], range: NSRange(location: 0, length: self.count))
        return matches?.count ?? 0 > 0
    }

    func isURLEncoded() -> Bool {
        // Check for the presence of '%' followed by two hex digits
        if self.range(of: "%[0-9A-Fa-f]{2}", options: .regularExpression) != nil {
            return true
        }
        
        // Attempt to decode the string
        if let decodedString = self.removingPercentEncoding {
            // If the decoded string is different, it was encoded
            return decodedString != self
        }
        
        // If removingPercentEncoding fails, the string wasn't encoded
        return false
    }
    
    static func transformDurationStr(seconds : Int) -> String {
        let minutes = Int(seconds / 60)
        let remainingSeconds = Int(seconds) % 60

        let formattedMinutes = String(format: "%d", minutes)
        let formattedSeconds = String(format: "%02d", remainingSeconds)

        return "\(formattedMinutes):\(formattedSeconds)"
    }
}

public extension String {
    static func dataflowString(bytes : Int64) -> String {
//        if bytes < 1024 {
//            return "\(bytes)B"
//        } else
        if bytes < 1024 * 1024 {
            return String.init(format: "%dKB", bytes / 1024)
        } else {
            return String.init(format: "%.2fMB", Double(bytes) / (1024.0 * 1024.0))
        }
    }
    
    func isRemoteUrl() -> Bool {
        return self.hasPrefix("http")
    }
    
    func base64Encode() -> String? {
        guard let inputData = self.data(using: .utf8) else {return nil }
        return inputData.base64EncodedString()
    }
    func base64Decode() -> String? {
        guard let decodedData = Data(base64Encoded: self) else {return nil }
        return String(data: decodedData, encoding: .utf8)
    }
}


public extension String {
    var isValidURL: Bool {
        if (self.length == 0) {
            return false
        }
        
        let url = NSURL.init(string: self)// [NSURL URLWithString:self];
        if (url == nil || url?.host == nil) {
            return false
        }
        
        return true
//
//        // 检查字符串长度
//        if self.count < 1 {
//            return false
//        }
//    
//        let urlString = self
//        // 定义正则表达式模式，支持域名和IP地址
//        let urlRegex = "^((https?|ftp|file)://)?(([\\w\\-]+\\.){1,5}((.+))|((25[0-5]|2[0-4]\\d|1?\\d?\\d)\\.){3}(25[0-5]|2[0-4]\\d|1?\\d?\\d))(\\:[0-9]{1,5})?(\\/\\S*)?$"
//    
//        // 使用NSPredicate进行正则表达式匹配
//        let urlTest = NSPredicate(format: "SELF MATCHES %@", urlRegex)
//        return urlTest.evaluate(with: urlString)
    }
    
    func filename() -> String {
        guard let url = URL(string: self) else { return self.lastPathComponent }
        let path = url.path // 获取不包含查询参数的路径
        let components = path.components(separatedBy: "/")
        return components.last ?? self.lastPathComponent // 返回最后一个组件（即文件名）
    }
    
    func extractEmails() -> [(index: Int, length: Int)] {
        let pattern = "[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}"
        do {
            let regex = try NSRegularExpression(pattern: pattern, options: .caseInsensitive)
            let matches = regex.matches(in: self, options: [], range: NSRange(location: 0, length: self.utf16.count))
            
            return matches.map { match in
                if let range = Range(match.range, in: self) {
                    let index = self.distance(from: self.startIndex, to: range.lowerBound)
                    let length = self.distance(from: range.lowerBound, to: range.upperBound)
                    return (index, length)
                }else{
                    return (0 , 0)
                }
            }
        } catch {
            return []
        }
    }
}
