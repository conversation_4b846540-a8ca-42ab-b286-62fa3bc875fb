//
//  NSString+FormateImageUrl.h
//  buz
//
//  Created by lizhi on 2022/6/15.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <UIKit/UIKit.h>

NS_ASSUME_NONNULL_BEGIN

@interface NSString (Extension)

+ (NSString *)formateImageUrl:(NSString *)url width:(CGFloat)width height:(CGFloat)height;



#pragma mark - Phone
// 18812345678 -> 188 1234 5678
- (NSString *)phoneAddWhitespace;

// 188 1234 5678 -> 18812345678
- (NSString *)phoneTrimWhitespace;

// fullPhone = code-phone
- (NSString *)fullPhoneWithCode:(NSString *)code;

///去除知道字符串
- (NSString *)trimWithString:(NSString *)string;

+ (NSString *)timezone;

@end

NS_ASSUME_NONNULL_END
