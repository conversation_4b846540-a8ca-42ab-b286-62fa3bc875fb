//
//  Array.swift
//  Tiya
//
//  Created by ch<PERSON><PERSON><PERSON><PERSON> on 2020/6/5.
//  Copyright © 2020 lizhi. All rights reserved.
//

#import "NSString+Encode.h"
@import CommonCrypto;

static inline NSString *
MD5Encoding(NSString *str) {
    NSData *data = [str dataUsingEncoding:NSUTF8StringEncoding];
    unsigned char result[CC_MD5_DIGEST_LENGTH];
    CC_MD5(data.bytes, (CC_LONG)data.length, result);
    return [NSString stringWithFormat:
            @"%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x%02x",
            result[0], result[1], result[2], result[3],
            result[4], result[5], result[6], result[7],
            result[8], result[9], result[10], result[11],
            result[12], result[13], result[14], result[15]];
}

@implementation NSString (Encoding)

- (NSString *)md5Code {
    return MD5Encoding(self);
}

@end
