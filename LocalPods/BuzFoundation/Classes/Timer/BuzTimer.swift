//
//  BuzTimer.swift
//  buz
//
//  Created by lizhi on 2022/6/28.
//  Copyright © 2022 lizhi. All rights reserved.
//

import UIKit
import BuzLog

///BuzTimer 销毁时 系统的Timer自动 invalidate 。 无需手动在持有者的 deinit 中调用 invalidate()
public class BuzTimer: NSObject {
    
    private(set) var sysTimer: Timer?
   
    public class func scheduledTimer(timeInterval:TimeInterval, repeats: Bool , block: @escaping ((BuzTimer) -> Void)) -> BuzTimer {
       
        let buzTimer = BuzTimer()

        let sTimer = Timer.init(timeInterval: timeInterval, repeats: repeats) { [weak buzTimer] timer in
            guard let buzTimer = buzTimer else{
                return
            }
            block(buzTimer)
        }
        
        DispatchQueue.main.safeAsyncUIQueue {
            RunLoop.current.add(sTimer, forMode: .common)
        }
        
        buzTimer.sysTimer = sTimer
        return buzTimer
   }
   
    public func invalidate()
    {
        DispatchQueue.main.safeAsyncUIQueue {
            self.sysTimer?.invalidate()
            self.sysTimer = nil
        }
    }
    
   deinit {
       invalidate()
       BuzLog.debug("计时器已销毁")
   }
}
