//
//  BuzGCDTimer.swift
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/8/19.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation

public class BuzGCDTimer {
    
    private var dispatchTimer: DispatchSourceTimer?
    private var interval: TimeInterval
    private var isSuspended: Bool
    private var willRepeat: Bool
    
    // Initializer
    public init(interval: TimeInterval, actionBlock: @escaping () -> Void, willRepeat: Bool, dispatchQueue: DispatchQueue = .main) {
        self.interval = interval
        self.isSuspended = true
        self.willRepeat = willRepeat
        
        // Initialize the dispatch timer
        self.dispatchTimer = DispatchSource.makeTimerSource(queue: dispatchQueue)
        self.dispatchTimer?.setEventHandler {
            if !self.willRepeat {
                self.stop()
            }
            actionBlock()
        }
    }
    
    // Schedule a timer
    public static func scheduleTimer(interval: TimeInterval, actionBlock: @escaping () -> Void, willRepeat: Bool, dispatchQueue: DispatchQueue = .main) -> BuzGCDTimer {
        let timer = BuzGCDTimer(interval: interval, actionBlock: actionBlock, willRepeat: willRepeat, dispatchQueue: dispatchQueue)
        timer.start()
        return timer
    }
    
    // Start the timer
    public func start() {
        guard isValid() && isSuspended else { return }
        
        isSuspended = false
        let nanoseconds = UInt64(interval * Double(NSEC_PER_SEC))
        
        if willRepeat {
            dispatchTimer?.schedule(deadline: .now() + interval, repeating: interval)
        } else {
            dispatchTimer?.schedule(deadline: .now() + interval, repeating: .infinity)
        }
        
        dispatchTimer?.resume()
    }
    
    // Stop the timer
    public func stop() {
        if isValid() {
            dispatchTimer?.cancel()
        }
    }
    
    // Suspend the timer
    public func suspend() {
        if isValid() && !isSuspended {
            dispatchTimer?.suspend()
            isSuspended = true
        }
    }
    
    // Invalidate the timer
    public func invalidate() {
        stop()
    }
    
    // Check if the timer is valid
    public func isValid() -> Bool {
        return dispatchTimer != nil && dispatchTimer?.isCancelled == false
    }
    
    deinit {
        if isValid() && isSuspended {
            dispatchTimer?.resume()
        }
        stop()
    }
}
