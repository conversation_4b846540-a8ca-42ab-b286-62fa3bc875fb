//
//  NSString+Safe.m
//  LizhiFM
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/28.
//  Copyright © 2017年 yibasan. All rights reserved.
//

#import "NSString+Safe.h"
#import <LZLog/LogConfig.h>

#if !__has_feature(objc_arc)
#error This file must be compiled with ARC.
#endif

@implementation NSString(Safe)

- (BOOL)isNilOrEmpty
{
    return ([self length] == 0);
}

- (NSString *)validString
{
    if ([self isKindOfClass:[NSString class]] && [self length] > 0) {
        return [NSString stringWithString:self];
    }
    return [NSString stringWithFormat:@" "];
}

//判断内容是否全部为空格  yes 全部为空格  no 不是
- (BOOL)isEmptyStr{
    
    //A character set containing only the whitespace characters space (U+0020) and tab (U+0009) and the newline and nextline characters (U+000A–U+000D, U+0085).
    NSCharacterSet *set = [NSCharacterSet whitespaceAndNewlineCharacterSet];
    
    //Returns a new string made by removing from both ends of the receiver characters contained in a given character set.
    NSString *trimedString = [self stringByTrimmingCharactersInSet:set];
    
    if ([trimedString length] == 0) {
        return true;
    } else {
        return false;
    }
}

- (NSString *)trimEmptyContent
{
    NSError *error = nil;
    NSRegularExpression *regex = [NSRegularExpression regularExpressionWithPattern:@"\n+" options:NSRegularExpressionCaseInsensitive error:&error];
    NSString *modifiedStr = [regex stringByReplacingMatchesInString:self options:0 range:NSMakeRange(0, self.length) withTemplate:@"\n"];
    NSArray *matches = [regex matchesInString:self options:0 range:NSMakeRange(0, self.length)];
    if (matches.count > 20) {
        NSTextCheckingResult *match = matches[20];
        NSRange resultRange = [match rangeAtIndex:0];
        NSUInteger length = 0;
        if (modifiedStr.length > resultRange.location) {
            length = modifiedStr.length - 1 - resultRange.location;
        }
        return [modifiedStr stringByReplacingOccurrencesOfString:@"\n" withString:@"" options:NSCaseInsensitiveSearch range:NSMakeRange(resultRange.location, length)];
    }
    return modifiedStr;
}

- (BOOL)isValidUrlString
{
    if ([self isKindOfClass:[NSString class]] == NO) {
        return NO;
    }
    
    if (self.length == 0) {
        return NO;
    }
    
    NSURL *url = [NSURL URLWithString:self];
    if (url == nil || url.host == nil) {
        return NO;
    }
    
    return YES;
}

- (NSString *)validUrlString
{
    CHECKP(self);
    if ([self isKindOfClass:[NSString class]]) {
        return [self stringByReplacingOccurrencesOfString:@" " withString:@""];
    }
    return @"";
}

+ (NSString *)stringToValidString:(NSString *)string
{
    if ([string isKindOfClass:[NSString class]] && [string length] > 0) {
        return [NSString stringWithString:string];
    }
    return [NSString stringWithFormat:@""];
}

+ (NSString *)stringToValidJsonString:(NSString *)string
{
    if ([string isKindOfClass:[NSString class]] && [string length] > 0) {
        return [NSString stringWithString:string];
    }
    return [NSString stringWithFormat:@"{}"];
}

+ (BOOL)isValidTrackUrl:(NSString *)urlString
{
    if (urlString && [urlString isKindOfClass:[NSString class]] && [urlString length] > 0) {
        NSURL *url = [NSURL URLWithString:urlString];
        NSString *host = [url host];
        if ([host hasSuffix:@"lizhi.fm"] == YES) {
            return YES;
        }
    }
    return NO;
}


@end
