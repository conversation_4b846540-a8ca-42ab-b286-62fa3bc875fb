//
//  NSString+AttributedString.h
//  LizhiFM
//
//  Created by <PERSON> on 14-4-10.
//  Copyright (c) 2014年 yibasan. All rights reserved.
//

#import <Foundation/Foundation.h>

@interface NSString (LZAttributedString)

/**
 *  兼容ios版本：计算字符串绘制到屏幕的size
 *
 *  @param aFont        字体
 *  @param aSize        限定的最大边界
 *  @param aLineSpacing 行间距（针对NSAttributedString）
 *
 *  @return 字符串绘制到屏幕的size
 */
- (CGSize)lz_sizeWithFont:(UIFont *)aFont
        constrainedToSize:(CGSize)aSize
              lineSpacing:(CGFloat)aLineSpacing;


/**
 *  兼容ios版本：计算字符串绘制到屏幕的size
 *
 *  @param aFont        字体
 *  @param aSize        限定的最大边界
 *  @return 字符串绘制到屏幕的size
 */
- (CGSize)lz_sizeWithFont:(UIFont *)aFont
        constrainedToSize:(CGSize)aSize;


/**
 *  兼容ios版本：计算字符串绘制到屏幕的size
 *
 *  @param aFont        字体
 *  @return 字符串绘制到屏幕的size
 */
- (CGSize)lz_sizeWithFont:(UIFont *)aFont;

#pragma mark -

/**
 富文本增加特性为指定的字体和指定的行间距

 @param font 指定的字体
 @param lineSpacing 指定的行间距
 @return 处理后的富文本
 */
- (NSMutableAttributedString *)attributedStringFromStringWithFont:(UIFont *)font
                                                 withLineSpacing:(CGFloat)lineSpacing;

/**
 富文本增加特性为指定的字体和指定的行间距，以及指定的字符换行模式

 @param font 指定的字体
 @param lineSpacing 指定的行间距
 @param lineBreakMode 指定的字符换行模式
 @return 处理后的富文本
 */
- (NSMutableAttributedString *)attributedStringFromStringWithFont:(UIFont *)font
                                                 withLineSpacing:(CGFloat)lineSpacing
                                               withLineBreakMode:(NSLineBreakMode)lineBreakMode;
@end
