//
//  NSString+AttributedString.m
//  LizhiFM
//
//  Created by <PERSON> on 14-4-10.
//  Copyright (c) 2014年 yibasan. All rights reserved.
//

#import "NSString+AttributedString.h"


#if !__has_feature(objc_arc)
#error This file must be compiled with ARC.
#endif

@implementation NSString (LZAttributedString)

/**
 *  兼容ios版本：计算字符串绘制到屏幕的size
 *
 *  @param aFont        字体
 *  @param aSize        限定的最大边界
 *  @param aLineSpacing 行间距（针对NSAttributedString）
 *
 *  @return 字符串绘制到屏幕的size
 */

- (CGSize)lz_sizeWithFont:(UIFont *)aFont
        constrainedToSize:(CGSize)aSize
              lineSpacing:(CGFloat)aLineSpacing
{
    CGSize size = CGSizeZero;
    if (!aFont) {
        return size;
    }
    
    size = [self boundingRectWithSize:aSize
                         withTextFont:aFont
                      withLineSpacing:aLineSpacing];
    return size;
}

- (CGSize)lz_sizeWithFont:(UIFont *)aFont
        constrainedToSize:(CGSize)aSize
{
    return [self lz_sizeWithFont:aFont constrainedToSize:aSize lineSpacing:0];
}

- (CGSize)lz_sizeWithFont:(UIFont *)aFont
{
    return [self lz_sizeWithFont:aFont constrainedToSize:CGSizeMake(CGFLOAT_MAX, CGFLOAT_MAX) lineSpacing:0];
}

- (CGSize)boundingRectWithSize:(CGSize)size
                  withTextFont:(UIFont *)font
               withLineSpacing:(CGFloat)lineSpacing
{
    NSMutableAttributedString *attributedText = [self attributedStringFromStringWithFont:font
                                                                        withLineSpacing:lineSpacing];
    CGSize textSize = [attributedText boundingRectWithSize:size
                                                   options:NSStringDrawingUsesLineFragmentOrigin
                                                   context:nil].size;
    textSize.width = MIN(textSize.width, size.width);
    textSize.height = MIN(textSize.height, size.height);
    
    return textSize;
}

- (NSMutableAttributedString *)attributedStringFromStringWithFont:(UIFont *)font
                                                 withLineSpacing:(CGFloat)lineSpacing
{
    if (self && self.length>0 && font)
    {
        NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:self attributes:@{NSFontAttributeName:font}];
        
        if (lineSpacing != 0) {
            NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
            [paragraphStyle setLineSpacing:lineSpacing];
            
            [attributedStr addAttribute:NSParagraphStyleAttributeName
                                  value:paragraphStyle
                                  range:NSMakeRange(0, [self length])];
        }

        return attributedStr;
    }
    return nil;
}

- (NSMutableAttributedString *)attributedStringFromStringWithFont:(UIFont *)font withLineSpacing:(CGFloat)lineSpacing withLineBreakMode:(NSLineBreakMode)lineBreakMode
{
    if (self && self.length > 0)
    {
        NSMutableAttributedString *attributedStr = [[NSMutableAttributedString alloc] initWithString:self attributes:@{NSFontAttributeName:font}];
        
        NSMutableParagraphStyle *paragraphStyle = [[NSMutableParagraphStyle alloc] init];
        [paragraphStyle setLineSpacing:lineSpacing];
        [paragraphStyle setLineBreakMode:lineBreakMode];
        
        [attributedStr addAttribute:NSParagraphStyleAttributeName
                              value:paragraphStyle
                              range:NSMakeRange(0, [self length])];
        
        return attributedStr;
    }
    return nil;
}

@end