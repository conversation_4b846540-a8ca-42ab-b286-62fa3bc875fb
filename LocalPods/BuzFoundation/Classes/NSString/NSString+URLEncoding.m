//
//  NSString+URLEncoding.m
//  LizhiFM
//
//  Created by BILLY HO on 2/1/16.
//  Copyright © 2016 yibasan. All rights reserved.
//

#import "NSString+URLEncoding.h"

@implementation NSString (URLEncoding)

-(NSString *)urlEncodeString
{
    CFStringRef ref = CFURLCreateStringByAddingPercentEscapes(NULL,
                                                              (CFStringRef)self,
                                                              NULL,
                                                              (CFStringRef)@"!*'\"();:@&=+$,/?%#[]% ",
                                                              kCFStringEncodingUTF8);
    NSString *string = [NSString stringWithString:(__bridge NSString *)ref];
    CFRelease(ref);
    return string;
}

- (NSString *)urlDecodeString
{
    NSString *result = [(NSString *)self stringByReplacingPercentEscapesUsingEncoding:NSUTF8StringEncoding];
    return result;
}

@end
