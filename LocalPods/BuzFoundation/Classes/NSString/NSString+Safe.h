//
//  NSString+Safe.h
//  LizhiFM
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2017/9/28.
//  Copyright © 2017年 yibasan. All rights reserved.
//  字符串合法性处理

#import <Foundation/Foundation.h>

@interface NSString(Safe)


/**
 判断字符串是否为nil或者长度为0

 @return YES，字符串为nil或者长度为0；NO，字符串长度不为0
 */
- (BOOL)isNilOrEmpty;


/**
  字符串是否有效，即长度是否大于0

 @return 返回有效的字符串，如果不大于0，则返回@" "
 */
- (NSString *)validString;

//判断内容是否全部为空格  yes 全部为空格  no 不是
- (BOOL)isEmptyStr;

/**
 *  是否 为合法的 Url 字符串
 */
- (BOOL)isValidUrlString;

/**
 * 1. 连续回车替换成一个回车 2. 最多20行
 */
- (NSString *)trimEmptyContent;

/**
 *  去除 url 字符串的空格
 *
 *  @return 不带空格的字符串
 */
- (NSString *)validUrlString;

/**
 *  转换 合法的字符串
 *
 *  @param string 原字符串
 *
 *  @return 合法的字符串
 */
+ (NSString *)stringToValidString:(NSString *)string;

/**
 *  转换 合法的 Json 字符串
 *
 *  @param string 原字符串
 *
 *  @return 合法的 Json 字符串
 */
+ (NSString *)stringToValidJsonString:(NSString *)string;


/**
 是否合法的TrackUrl地址

 @param urlString url地址
 @return YES，合法；NO，不合法
 */
+ (BOOL)isValidTrackUrl:(NSString *)urlString;


@end
