//
//  BuzThrottle.swift
//  buz
//
//  Created by li666 on 2023/6/15.
//  Copyright © 2023 lizhi. All rights reserved.
//

public class BuzThrottle {
    private var lastRun: Date = Date.distantPast
    private let delay: TimeInterval
    private var pendingWorkItem: DispatchWorkItem?
    private let queue: DispatchQueue
    
    public init(delay: TimeInterval, queue: DispatchQueue = DispatchQueue.main) {
        self.delay = delay
        self.queue = queue
    }
    
    public func throttle(_ block: @escaping () -> Void) {
        // 取消之前的任务
        pendingWorkItem?.cancel()
        
        // 创建新的任务
        let workItem = DispatchWorkItem { [weak self] in
            self?.lastRun = Date()
            block()
        }
        
        // 如果距离上一次运行的时间小于设定的延迟时间，就延迟执行任务
        let now = Date()
        let elapsed = now.timeIntervalSince(lastRun)
        if elapsed > delay {
            lastRun = now
            queue.async(execute: workItem)
        } else {
            pendingWorkItem = workItem
            queue.asyncAfter(deadline: .now() + (delay - elapsed), execute: workItem)
        }
    }
    
    public func cancel() {
        pendingWorkItem?.cancel()
    }
}
