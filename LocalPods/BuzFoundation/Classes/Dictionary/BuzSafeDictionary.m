//
//  BuzSafeDictionary.m
//  buz
//
//  Created by liuyufeng on 2022/8/29.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "BuzSafeDictionary.h"

static inline void Buz_sync_lock(dispatch_semaphore_t lock, void (^block)(void)) {
    dispatch_semaphore_wait(lock, DISPATCH_TIME_FOREVER);
    if (block) {
        block();
    }
    dispatch_semaphore_signal(lock);
}

@interface BuzSafeDictionary()
@property (nonatomic, strong, readonly) dispatch_semaphore_t lock;
@property (nonatomic, strong, readonly) NSMutableDictionary *mDict;
@end

@implementation BuzSafeDictionary

- (instancetype)init
{
    self = [super init];
    if (self) {
        _lock = dispatch_semaphore_create(1);
        _mDict = [[NSMutableDictionary alloc] init];
    }
    return self;
}

- (instancetype)initWithCapacity:(NSUInteger)numItems
{
    self = [super init];
    if (self) {
        _lock = dispatch_semaphore_create(1);
        _mDict = [[NSMutableDictionary alloc] initWithCapacity:numItems];
    }
    return self;
}

- (void)setObject:(id)anObject forKey:(id<NSCopying>)aKey
{
    Buz_sync_lock(_lock, ^{
        [self.mDict setObject:anObject forKey:aKey];
    });
}

- (id)objectForKey:(id)aKey
{
    __block id object = nil;
    Buz_sync_lock(self.lock, ^{
        object = [self.mDict objectForKey:aKey];
    });
    return object;
}

- (void)removeObjectForKey:(id)aKey
{
    Buz_sync_lock(self.lock, ^{
        [self.mDict removeObjectForKey:aKey];
    });
}

@end


