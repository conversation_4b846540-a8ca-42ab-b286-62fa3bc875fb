//
//  DictionaryExtension.swift
//  Tiya
//
//  Created by patton on 2020/7/6.
//  Copyright © 2020 lizhi. All rights reserved.
//

import Foundation
import BuzLog

public extension NSDictionary {
    
    static func convertDictionaryToJSONString(dict:NSDictionary?)->String {
        if let data = try? JSONSerialization.data(withJSONObject: dict!, options: JSONSerialization.WritingOptions.init(rawValue: 0)) {
            let jsonStr = NSString(data: data, encoding: String.Encoding.utf8.rawValue) as String?
            return jsonStr ?? ""
        }
        return ""
    }

    static func convertJSONStringToDictionary(text: String) -> [String:AnyObject]? {
        if let data = text.data(using: String.Encoding.utf8) {
            do {
                return try JSONSerialization.jsonObject(with: data, options: [JSONSerialization.ReadingOptions.init(rawValue: 0)]) as? [String:AnyObject]
            } catch let error as NSError {
                BuzLog.info(error.domain)
            }
        }
        return nil
    }
    
  
}

// MARK: 字典转字符串
public extension Dictionary {
    
    func toJsonString() -> String? {
        guard let data = try? JSONSerialization.data(withJSONObject: self,
                                                     options: []) else {
            return nil
        }
        guard let str = String(data: data, encoding: .utf8) else {
            return nil
        }
        return str
     }
    
}
