//
//  BuzSafeDictionary.h
//  buz
//
//  Created by liuyufeng on 2022/8/29.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

@interface BuzSafeDictionary<__covariant KeyType, __covariant ObjectType> : NSObject
- (instancetype)initWithCapacity:(NSUInteger)numItems;
- (void)setObject:(ObjectType)anObject forKey:(KeyType <NSCopying>)aKey;
- (nullable ObjectType)objectForKey:(KeyType)aKey;
- (void)removeObjectForKey:(KeyType)aKey;
@end

NS_ASSUME_NONNULL_END
