//
//  NSDictionary+Extension.m
//  buz
//
//  Created by liuyufeng on 2022/7/13.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "NSDictionary+Extension.h"
#import "OCBuzLog.h"

@implementation NSDictionary (Extension)

+ (NSDictionary *)dictionaryWithJsonString:(NSString *)jsonString {
    
    if ([jsonString isKindOfClass:[NSString class]] == NO)
    {
        return nil;
    }
    
    if (jsonString == nil) {
        return nil;
    }
    
    NSData *jsonData = [jsonString dataUsingEncoding:NSUTF8StringEncoding];
    NSError *err;
    NSDictionary *dic = [NSJSONSerialization JSONObjectWithData:jsonData
                                                        options:NSJSONReadingMutableContainers
                                                          error:&err];
    if(err) {
        BuzLogE(@"jsonString to NSDictionary error");
        return nil;
    }
    return dic;
}

@end
