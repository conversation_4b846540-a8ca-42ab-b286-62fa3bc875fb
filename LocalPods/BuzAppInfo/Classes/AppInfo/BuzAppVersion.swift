//
//  BuzAppVersionKey.swift
//  buz
//
//  Created by lizhi on 2024/8/12.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation
import BuzDataStore

public enum BuzAppVersionKey : String {
    case isInstalledFlag = "isInstalledFlag"                // 安装标识
    case isFirstInstall = "isFirstInstall"                  // 第一次安装
    case firstInstallTimestamp = "firstInstallTimestamp"    // 第一次安装的时间戳
    case buzBuildVersion = "BuzAppVersionKey_buzBuildVersion"
}

public class BuzAppVersion {
    /// 首次安装
    public class func isFirstInstall() -> Bool {
        return __isFirstInstall()
    }
    
    /// 覆盖安装
    public class func isOverlayInstall() -> Bool {
        let isOldVersionInstalled = __isFirstInstall_deprecated()
        return !__isFirstInstall() || isOldVersionInstalled
    }
    
    /// 精确的覆盖安装，只会在覆盖安装那一次启动为 true
    public static var isRealOverlayInstall: Bool = false
    
    /// 冷启时运行
    public class func run() {
        install()
    }
    
    /// 新版本的标识
    private class func __isFirstInstall() -> Bool {
        let key = BuzAppVersionKey.isFirstInstall.rawValue
        return MMKV.buz.bool(forKey: key, defaultValue: false)
    }
    
    /// 旧版本的标识（后面要弃用）
    private class func __isFirstInstall_deprecated() -> Bool {
        let key = BuzAppVersionKey.isFirstInstall.rawValue
        return MMKV.buz.bool(forKey: key, defaultValue: false)
    }
    
    private class func install() {
        let isInstalled = MMKV.buz.bool(forKey: BuzAppVersionKey.isInstalledFlag.rawValue, defaultValue: false)
        if !isInstalled {
            // 设置安装标识
            MMKV.buz.set(true, forKey: BuzAppVersionKey.isInstalledFlag.rawValue)
            
            // 记录第一次安装的时间戳
            let key2 = BuzAppVersionKey.firstInstallTimestamp.rawValue
            let now = Date().timeIntervalSince1970 * 1000
            MMKV.buz.set(now, forKey: key2)
            
            print("firstInstallTimestamp \(now)")
            
            // 标识第一次安装
            MMKV.buz.set(true, forKey: BuzAppVersionKey.isFirstInstall.rawValue)
        } else {
            MMKV.buz.set(false, forKey: BuzAppVersionKey.isFirstInstall.rawValue)
        }
        
        checkRealOverlayInstall(isInstalled: isInstalled)
    }
    
    private class func checkRealOverlayInstall(isInstalled: Bool) {
        let storeBuildVersion = MMKV.buz.string(forKey: BuzAppVersionKey.buzBuildVersion.rawValue, defaultValue: "")
        let buildVersion = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? ""
        
        if storeBuildVersion?.isEmpty == false || isInstalled, buildVersion.isEmpty == false, storeBuildVersion != buildVersion {
            isRealOverlayInstall = true
        } else {
            isRealOverlayInstall = false
        }
        
        MMKV.buz.set(buildVersion, forKey: BuzAppVersionKey.buzBuildVersion.rawValue)
    }
}
