//
//  AppInfos.swift
//  buz
//
//  Created by lizhi on 2020/7/15.
//  Copyright © 2020 lizhi. All rights reserved.
//

import BaseTool
import BuzConfig
import Foundation
import LZDeviceUtil
import SMSDK

@objcMembers 
public class AppInfos: NSObject {
    public static let appName: String = "Buz"
    public static let deviceName: String = LZDeviceMgr.deviceName()
    public static let osVersion: String = LZDeviceMgr.osVersion()
    public static let appVersionName: String = LZDeviceMgr.appVersionName()
    public static let machineName: String = LZDeviceMgr.machineName()

    // 闪电平台的appid
    public static let flashAppId: Int = 87075309
    public static let flashSubAppId: Int = 0
    public static let channel: String = "AppStore"
    public static let deviceId: String = LZDeviceMgr.deviceId()
    public static let appVersion = LZDeviceMgr.appVersion()
    public static var isInBackground: Bool = false
    public static var isResignedActive: Bool = false

    // 应用商店appID
    public static let appStoreAppID = "1628292843"

    // 商店评分链接
    public static let storeMarkingURL = "https://itunes.apple.com/app/id1628292843?mt=8&action=write-review"

    /// 星斗配置  接入文档：https://lizhi2021.feishu.cn/wiki/wikcnSZL3bUJUrM9T4GIh60Qf8b
    public static let trackerProductId: String = "2172"
    public static let trackerAppKey: String = "5d44b618387b0a5f8b42abbf4aaeba18"
    public static let trackerAppName: String = appName

    public static var trackerServerURL: String {
        // 注意:海外平台不要再用测试地址上报，全部用正式上报地址
        // 星斗采集从 美东机房 迁移至 马来机房
        // "https://usstat.tiyalive.com/sa?project=ac_production"   // 美东
        "https://stat.dp.vocalbeats.com/sa?project=ac_production"   // 马来
    }

    /// RDS配置  接入文档：https://lizhi2021.feishu.cn/wiki/wikcn8MuiI7Fxg8HrnTgPVp30cg
    public static let rdsChannel: String = channel
    public static let rdsAppId: String = String(flashAppId)
    public static let rdsHost: String = "https://rdstat.tiyalive.com"

    /// 长链推送配置  接入文档：https://lizhi2021.feishu.cn/wiki/wikcnv2IyNL9xg4mOcPOsRjVpPb
    public static let pushHostAPP: String = "" /// 选填
    public static let pushAppID: String = String(flashAppId)
    public static var pushDefaultHosts: Array<String> {
        #if DEBUG
        if Environments.standard.envPosition == .tower {
            return ["ws://************:39999/push"]
        } else if Environments.standard.envPosition == .pre {
            return ["wss://httpproxypre.buz-app.com:4051/push"]
        } else {
            return [
                "wss://push101.buz-app.com/push",
                "wss://push102.buz-app.com/push",
            ]
        }
        #else
        [
            "wss://push101.buz-app.com/push",
            "wss://push102.buz-app.com/push",
        ]
        #endif
    }

    /// bugly统计
    public static var buglyAppId: String {
        #if DEBUG
        "d21707e690"
        #else
        "ba489bb015"
        #endif
    }

    /// 数美
    public static let smAppId: String = "buz"
    public static let smHost: String = "https://tycollectproxy.tiyalive.com"
    public static let smOrganization: String = "2OaFzWJr4Iq43hwimOdh"
    public static let smUrl: String = "/" + smAppId + "/v3/profile/ios"
    public static let smContactUrl: String = "/" + smAppId + "/v3/profile/ios"
    public static let smConfUrl: String = "/" + smAppId + "/v3/cloudconf"
    public static let smChannel: String = channel // 选填，传入渠道标识
    public static let RTCAppId : String = "Lizhi_Buz_20220609"
    
    public static var smId: String = SmAntiFraud.shareInstance().getDeviceId()

    // af 开发者key
    public static let afAppsFlyerDevKey = "GRdhm2LMCiwHzr54QbVXPj"

    // 极验ID
    public static let captchaID = "e089b28b85d511282e3b9cace1f22d71"

    public static var window: UIWindow?
    
    ///分享新域名
    public static let shareNewDomain = "i.buz.ai"
    
    // mushroom 加密公钥URL
    enum MushroomKey: String {
        case tower = "http://mushroom.yfxn.lizhi.fm/protocol/sec/update_key"
        case product = "https://mushroom.buz-app.com/protocol/sec/update_key"
    }
    
    public static var mushroomSporesURL: String {
        if isProductionEnv() {
            return MushroomKey.product.rawValue
        } else {
            return MushroomKey.tower.rawValue
        }
    }
    
    static func isProductionEnv() -> Bool {
        #if DEBUG
        // 预发和生产都用生产Key
        return Environments.standard.envPosition != .tower
        #else
        return true
        #endif
    }

}

public enum BuzDoremeInfo {
    public enum AppId : String {
        case voiceCall = "Lizhi_Buz_20220609"
        case onAir = "Lizhi_BuzOnAir_20240813"
    }
}

@objcMembers 
public class VoderXInfo: NSObject {
    enum AppKey: String {
        case tower = "ef77e66f4a322ce0a5eff8e1d144be70"
        case product = "a94767967bc6e3e3122d98a75b789682"
    }

    enum E2EEAppKey: String {
        case tower = "763ee101bbdfdd02792fc4a36cd0f2ba"
        case product = "24498ecf8da72ef6432ead2a1793328a"
    }
    
    public static func isProductionEnv() -> Bool {
        #if DEBUG
        // 预发和生产都用生产Key
        return Environments.standard.envPosition != .tower
        #else
        return true
        #endif
    }

    public static var appKey: String {
        if isProductionEnv() {
            return AppKey.product.rawValue
        } else {
            return AppKey.tower.rawValue
        }
    }

    public static let appHost = "buz-app.com"

    public static var appGroups: String {
        BuzConfig.groupStoreId
    }

    public static var e2eeAppKey: String {
        if isProductionEnv() {
            return E2EEAppKey.product.rawValue
        } else {
            return E2EEAppKey.tower.rawValue
        }
    }

    public static func isDisableE2EEInit() -> Bool {
        return isDisableE2EEPeerInit()
    }

    public static func isDisableE2EEPeerInit() -> Bool {
        // E2EE 下线
        return true;
    }

    public static func isEnableE2EEPeerSend() -> Bool {
        // E2EE 下线
        return false
    }
}
