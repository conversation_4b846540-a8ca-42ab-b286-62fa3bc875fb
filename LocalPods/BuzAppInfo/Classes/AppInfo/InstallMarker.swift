//
//  Install.swift
//  SwiftBuz
//
//  Created by 方煜逵 on 2024/6/11.
//

public struct InstallMarker {
    private static var isFristInstall: Bool?
    fileprivate static let userDefaultsKey = "adjustisInstalleKey"
    public static func checkIfFirstInstall() -> Bool {
        if let isFristInstall = isFristInstall   {
            return isFristInstall
        }else if UserDefaults.standard.bool(forKey: Self.userDefaultsKey) == false {
            Self.markerInstalled()
            return true
        }else {
            Self.isFristInstall = false
            return false
        }
        
    }
    
    private static func markerInstalled() {
        UserDefaults.standard.setValue(true, forKey: Self.userDefaultsKey)
        Self.isFristInstall = true
    }
    
    static func unMarkerInstalled() {
        UserDefaults.standard.setValue(false, forKey: Self.userDefaultsKey)
        Self.isFristInstall = false
    }
    
}
