#
#  Be sure to run `pod spec lint PCSocialCenter.podspec' to ensure this is a
#  valid spec and to remove all comments including this before submitting the spec.
#
#  To learn more about Podspec attributes see https://guides.cocoapods.org/syntax/podspec.html
#  To see working Podspecs in the CocoaPods repo see https://github.com/CocoaPods/Specs/
#

Pod::Spec.new do |s|
  
  s.name     = "BuzDatabase"
  s.version  = "0.0.1"
  s.summary  = "A short description of BuzDatabase."
  
  s.homepage = "https://gitlab.lizhi.fm/zhiyaPods/BuzDatabase"
  s.license  = { :type => 'Copyright', :file => 'LICENSE' }
  s.author   = { 'st.chio' => '<EMAIL>' }
  s.source   = { :git => "https://gitlab.lizhi.fm/zhiyaPods/BuzDatabase.git", :tag => s.version.to_s }
  
  s.ios.deployment_target = '10.0'
  
  s.requires_arc = true
  
  #使用自定义modulemap方式，排除Manager模块@import引用方式
  s.source_files = 'Classes/**/*.{h,m,mm,swift}'
  s.public_header_files = 'Classes/**/*.h'
  s.exclude_files = 'Classes/Manager/**/*.{h,m,mm}'
  s.module_map = 'Classes/modulemap/module.modulemap'
  
  #这个模块内的代码仅限OC类使用，且只能通过#import方式引用~
  #原因：WCDB组件是OC版的，内部且多维C++类，这个组件未提供modulemap引用方式，因此导致Manager也支持modulemap方式，所以这个模块不能被Swift调用~
  s.subspec 'Manager' do |sp|
    sp.source_files = 'Classes/Manager/**/*.{h,m,mm}'
    sp.requires_arc = true
    
    sp.dependency 'WCDB'
  end
  
  s.subspec 'Database' do |sp|
    sp.source_files = 'Classes/Database/**/*.{h,m,mm}'
    sp.public_header_files = 'Classes/Database/**/*.h'
  end
  
end
