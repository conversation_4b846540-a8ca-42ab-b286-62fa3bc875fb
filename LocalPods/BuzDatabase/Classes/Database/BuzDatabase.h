//
//  BuzDatabase.h
//  buz
//
//  Created by lizhi on 2022/8/18.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef int64_t (^BuzDatabaseFetchUserIdBlock)();
@interface BuzDatabaseConfiger : NSObject

@property (nonatomic, strong) BuzDatabaseFetchUserIdBlock fetchUserIdBlock;

@end

//解决swift无法直接访问BuzDatabaseManager问题
@interface BuzDatabase : NSObject

@property (class, nonatomic, strong) BuzDatabaseConfiger *configer;

+ (void)closeDB;

@end

NS_ASSUME_NONNULL_END
