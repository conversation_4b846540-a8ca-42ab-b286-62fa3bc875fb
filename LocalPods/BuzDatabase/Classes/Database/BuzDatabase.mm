//
//  BuzDatabaseTool.m
//  buz
//
//  Created by lizhi on 2022/8/18.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "BuzDatabase.h"
#import "BuzDatabaseManager.h"

@implementation BuzDatabaseConfiger

@end

@implementation BuzDatabase
static BuzDatabaseConfiger *_configer = nil;

+ (BuzDatabaseConfiger *)configer {
    return _configer;
}

+ (void)setConfiger:(BuzDatabaseConfiger *)configer {
    _configer = configer;
}

+ (void)closeDB {
    [[BuzDatabaseManager sharedInstance] close];
}

@end
