//
//  BuzDatabaseManager.h
//  WCDB
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/8/10.
//  Copyright © 2018 Lizhi. All rights reserved.
//

#import <Foundation/Foundation.h>
#import <WCDB/WCDB.h>
#import "BuzDatabaseQueue.h"

NS_ASSUME_NONNULL_BEGIN

#define DATABASE [BuzDatabaseManager database]

@interface BuzDatabaseManager : NSObject

+ (instancetype)sharedInstance;

+ (WCTDatabase *)database;

- (void)close;

@end

NS_ASSUME_NONNULL_END
