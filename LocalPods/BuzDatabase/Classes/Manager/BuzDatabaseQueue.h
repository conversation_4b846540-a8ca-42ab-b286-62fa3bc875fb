//
//  BuzDatabaseQueue.h
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/9/4.
//

#import <Foundation/Foundation.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef buz_dispatch_queue_async_safe
#define buz_dispatch_queue_async_safe(queue, block)\
    if (dispatch_queue_get_label(DISPATCH_CURRENT_QUEUE_LABEL) == dispatch_queue_get_label(queue)) {\
        block();\
    } else {\
        dispatch_async(queue, block);\
    }
#endif

#ifndef buz_dispatch_main_async_safe
#define buz_dispatch_main_async_safe(block) buz_dispatch_queue_async_safe(dispatch_get_main_queue(), block)
#endif

    
/**
 当前线程是否数据库专用异步串行队列

 @return 是否，布尔值
 */
extern bool buz_is_current_database_queue(void);

    
/**
 在数据库专用异步串行队列里执行任务
 
 @param task 任务block
 @param completion 主线程回调block
 */
extern void buz_dispatch_async_database(void(^task)(void), void(^completion)(void));
    

extern dispatch_queue_t buz_database_queue(void);
#ifdef __cplusplus
}
#endif

@interface BuzDatabaseQueue : NSObject

@end
