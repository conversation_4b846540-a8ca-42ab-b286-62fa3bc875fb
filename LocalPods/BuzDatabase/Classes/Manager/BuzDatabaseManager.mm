//
//  BuzDatabaseManager.m
//  WCDB
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/8/10.
//  Copyright © 2018 Lizhi. All rights reserved.
//

#import "BuzDatabaseManager.h"
#import "LogzMacros.h"
#import "BuzDatabase.h"

@interface BuzDatabaseManager ()

@property (nonatomic, strong) WCTDatabase *database;

@end

@implementation BuzDatabaseManager

static id s_instance = nil;
+ (instancetype)sharedInstance {
    if (s_instance == nil) {
        static dispatch_once_t onceToken;
        dispatch_once(&onceToken, ^{
            s_instance = [[self alloc] init];
            [s_instance open];
        });
    }else{
        [s_instance open];
    }
    return s_instance;
}

- (void)open {
    if (_database) { return; }
    [self openDatabase];
    LogzI(@"Open Database Path: %@", [self databaseDirectory])
    //XHJ_TD:加入数据库升级（不使用版本号，单表升级
}

- (void)close {
    [_database close];
    _database = nil;
}

- (NSString *)databaseDirectory {
    int64_t uid = BuzDatabase.configer.fetchUserIdBlock ? BuzDatabase.configer.fetchUserIdBlock() : 0;
    NSString *path = [NSString stringWithFormat:@"/Library/WCDB/%lld/Database",uid];
    return [NSHomeDirectory() stringByAppendingPathComponent:path];
}

- (void)openDatabase {
    NSString *path = [self databaseDirectory];
    _database = [[WCTDatabase alloc] initWithPath:path];
    [_database close:^{}];
}

+ (WCTDatabase *)database {
    // TODO: AUTO CLOSE
    return [[self.class sharedInstance] database];
}


@end
