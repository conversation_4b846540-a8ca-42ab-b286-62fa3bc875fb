//
//  BuzDatabaseQueue.m
//  Pods
//
//  Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/9/4.
//

#import "BuzDatabaseQueue.h"

@implementation BuzDatabaseQueue

@end

static void *queueKey = "";

dispatch_queue_t buz_database_queue(void)
{
    static dispatch_once_t onceToken;
    static dispatch_queue_t databaseQueue;
    dispatch_once(&onceToken, ^{
        databaseQueue = dispatch_queue_create("com.buz.database", DISPATCH_QUEUE_SERIAL);
        
        dispatch_queue_set_specific(databaseQueue, queueKey, &queueKey, NULL);
    });
    return databaseQueue;
}

bool buz_is_current_database_queue(void)
{
    return dispatch_get_specific(queueKey)?true:false;
}

void buz_dispatch_async_database(void(^task)(void), void(^completion)(void))
{
    dispatch_async(buz_database_queue(), ^{
        if (task) {
            task();
        }
        dispatch_async(dispatch_get_main_queue(), ^{
            if (completion) {
                completion();
            }
        });
    });    
}
