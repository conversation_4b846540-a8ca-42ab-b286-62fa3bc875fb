//
//  BuzDefaultStatusView.swift
//  Pods
//
//  Created by <PERSON> on 06/09/2024.
//

import Lottie
import BuzUIStyle
import BuzUIKit


public class BuzDefaultStatusView: UIView {

    public var isLoading = false
    public var actionBlock: (() -> Void)?
    private var showRotatingAnimation: Bool
    private var lottieSize: CGSize = .zero
    private var buttonLayoutStyle:BuzButton.RegularStyle = BuzButton.RegularStyle.primary
    
    private(set) lazy var lottieAnimationView: LottieAnimationView = {
        let lottie = LottieAnimationView()
        lottie.isUserInteractionEnabled = false
        lottie.loopMode = .playOnce
        return lottie
    }()
    
    private(set) lazy var textView: UILabel = {
        let label = UILabel()
        label.numberOfLines = 0
        label.lineBreakMode = .byWordWrapping
        label.fontStyle = .textBodyLarge
        label.textColor = .text_white_tertiary
        label.textAlignment = .center
        return label
    }()
    
    private(set) lazy var actionButton: BuzButton = {
        let layout = BuzButton.RegularStyleLayout(style:buttonLayoutStyle, sizeType: .small)
        let button = BuzButton.init(layout: layout)
        button.layerCornerRadius = 16
        button.contentEdgeInsets = UIEdgeInsets(top: 0, left: 20, bottom: 0, right: 20)
        button.addTarget(self, action: #selector(self.buttonAction(_:)), for: .touchUpInside)
        button.isHidden = true
        return button
    }()
    
    public func resetActionButton(hidden : Bool) {
        self.actionButton.isHidden = hidden
    }
    
    private var lottieAnimationName: String
    
    public init(frame: CGRect, 
                offset: CGFloat,
                lottieSize : CGSize = CGSize(width: 200.0, height: 200.0),
                textLabel: String?,
                textLabelColor: UIColor = UIColor.text_white_tertiary,
                lottieAnimationName: String,
                buttonTitle: String?,
                buttonLayoutStyle:BuzButton.RegularStyle = BuzButton.RegularStyle.primary,
                showRotatingAnimation: Bool = false) {
        self.showRotatingAnimation = showRotatingAnimation
        self.lottieAnimationName = lottieAnimationName
        self.lottieSize = lottieSize
        self.buttonLayoutStyle = buttonLayoutStyle
        super.init(frame: frame)
        
        self.loadContentView(with: offset, with: textLabel, with: buttonTitle)
        self.textView.textColor = textLabelColor
      
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func loadContentView(with offset: CGFloat, with textLabel: String?, with buttonTitle: String?) {
        self.addSubviews([self.lottieAnimationView, self.textView, self.actionButton])
        
        self.lottieAnimationView.animation = LottieAnimation.named(self.lottieAnimationName)
        lottieAnimationView.snp.makeConstraints { make in
            make.centerY.equalToSuperview().offset(-offset)
            make.centerX.equalToSuperview()
            make.size.equalTo(self.lottieSize)
        }
        
        textView.text = textLabel
        textView.snp.makeConstraints { make in
            make.top.equalTo(lottieAnimationView.snp.bottom).offset(-20)
            make.leading.equalToSuperview().offset(40)
            make.trailing.equalToSuperview().offset(-40)
        }
        
        if let title = buttonTitle {
            self.actionButton.isHidden = false
            self.actionButton.setTitle(title, for: .normal)
            self.actionButton.snp.makeConstraints { make in
                make.top.equalTo(self.textView.snp.bottom).offset(20)
                make.centerX.equalTo(self)
                make.height.equalTo(32)
            }
        }
        
        DispatchQueue.main.asyncAfter(delay: 0.3) {
            self.playLottieAnimation()
        }
    }

    public func playLottieAnimation() {
        self.lottieAnimationView.play()
    }
    
    public func addTarget(_ target: Any?, action: Selector) {
        lottieAnimationView.isUserInteractionEnabled = true
        textView.isUserInteractionEnabled = true
        let tap = UITapGestureRecognizer(target: target, action: action)
        addGestureRecognizer(tap)
    }
    
    @objc func buttonAction(_ b: BuzButton) {
        actionBlock?()
        if showRotatingAnimation {
            isLoading.toggle()
            if isLoading {
                b.beginLoading()
            } else {
                b.endLoading()
            }
        }
    }
}
