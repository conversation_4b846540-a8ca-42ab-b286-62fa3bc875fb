# BuzDefaultStatusDisplay

[![CI Status](https://img.shields.io/travis/<PERSON>/BuzDefaultStatusDisplay.svg?style=flat)](https://travis-ci.org/<PERSON>/BuzDefaultStatusDisplay)
[![Version](https://img.shields.io/cocoapods/v/BuzDefaultStatusDisplay.svg?style=flat)](https://cocoapods.org/pods/BuzDefaultStatusDisplay)
[![License](https://img.shields.io/cocoapods/l/BuzDefaultStatusDisplay.svg?style=flat)](https://cocoapods.org/pods/BuzDefaultStatusDisplay)
[![Platform](https://img.shields.io/cocoapods/p/BuzDefaultStatusDisplay.svg?style=flat)](https://cocoapods.org/pods/BuzDefaultStatusDisplay)

## Example

To run the example project, clone the repo, and run `pod install` from the Example directory first.

## Requirements

## Installation

BuzDefaultStatusDisplay is available through [CocoaPods](https://cocoapods.org). To install
it, simply add the following line to your Podfile:

```ruby
pod 'BuzDefaultStatusDisplay'
```

## Author

Sam Zachary Chee Hao Yuan, <EMAIL>

## License

BuzDefaultStatusDisplay is available under the MIT license. See the LICENSE file for more info.
