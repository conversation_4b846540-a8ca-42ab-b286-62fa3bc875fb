#
# Be sure to run `pod lib lint BuzDefaultStatusDisplay.podspec' to ensure this is a
# valid spec before submitting.
#
# Any lines starting with a # are optional, but their use is encouraged
# To learn more about a Podspec see https://guides.cocoapods.org/syntax/podspec.html
#

Pod::Spec.new do |s|
  s.name             = 'BuzDefaultStatusDisplay'
  s.version          = '0.1.0'
  s.summary          = 'BuzDefaultStatusDisplay supports showing the lottie associated with default statuses (empty chat, lack of access to permissions) in Buz'
  s.homepage         = "https://BuzDefaultStatusDisplay"
  s.license          = { :type => 'MIT', :file => 'LICENSE' }
  s.author           = { '<PERSON>' => '<EMAIL>' }

  s.pod_target_xcconfig = {'DEFINES_MODULE' => 'YES'}

  s.swift_version = "5.0"
  s.osx.deployment_target = "10.9"
  s.ios.deployment_target = "9.0"
  s.watchos.deployment_target = "3.0"
  s.source           = { :git => "https://BuzDefaultStatusDisplay", :tag => s.version }
  s.source_files = "Classes/**/*.{swift}"
  
  s.dependency 'lottie-ios'
  s.dependency 'BuzUIStyle'
  s.dependency 'BuzUIKit'
end
