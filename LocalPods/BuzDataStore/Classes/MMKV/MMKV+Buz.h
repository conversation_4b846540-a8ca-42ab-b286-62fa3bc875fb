//
//  MMKV+Buz.h
//  buz
//
//  Created by liuyufeng on 2022/6/10.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import <MMKV/MMKV.h>

NS_ASSUME_NONNULL_BEGIN

@interface MMKV (Buz)


/** 同步、异步两个kv数据不互通  ，  */

//同步访问MMKV
@property(class, nonatomic, readonly) MMKV* buz;

+ (MMKV*)asyncBuz;
//异步访问MMKV
+ (void)buz_async:(void(^ _Nonnull)(MMKV *))task complete:(void(^ _Nullable)(void))complete;

@end

NS_ASSUME_NONNULL_END
