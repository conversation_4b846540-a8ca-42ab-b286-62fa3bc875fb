//
//  MMKV+Buz.m
//  buz
//
//  Created by liuyufeng on 2022/6/10.
//  Copyright © 2022 lizhi. All rights reserved.
//

#import "MMKV+Buz.h"
@import MMKV;



dispatch_queue_t defaultIOQueue(void)
{
    static dispatch_queue_t queue;
    static dispatch_once_t onceToken;
    dispatch_once(&onceToken, ^{
        queue = dispatch_queue_create("com.buz.business.queue.oc", 0);
    });
    
    return queue;
}

@implementation MMKV (Buz)

+ (NSString*)buzPath {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    if (paths.count > 0)
    {
        NSString *documentsDirectory = paths.firstObject;
        return [documentsDirectory stringByAppendingPathComponent:@"buzMMKV"];
    }
    return @"";
}

+ (NSString*)buzAsyncPath {
    NSArray *paths = NSSearchPathForDirectoriesInDomains(NSDocumentDirectory, NSUserDomainMask, YES);
    if (paths.count > 0)
    {
        NSString *documentsDirectory = paths.firstObject;
        return [documentsDirectory stringByAppendingPathComponent:@"buzAsyncMMKV"];
    }
    return @"";
}


+ (MMKV*)buz {
    static MMKV* mmkv = nil;
    static dispatch_once_t once;
    dispatch_once(&once, ^{
        [MMKV initializeMMKV:nil];
        mmkv = [MMKV mmkvWithID:@"buz_mmkv" rootPath:[self buzPath]];
    });
    
//    [MMKV buz]
    
    return mmkv;
}

+ (MMKV*)asyncBuz {
    static MMKV* mmkv = nil;
    static dispatch_once_t once;
    dispatch_once(&once, ^{
        [MMKV initializeMMKV:nil];
        mmkv = [MMKV mmkvWithID:@"buz_async_mmkv" rootPath:[self buzAsyncPath]];
    });
    
//    [MMKV buz]
    
    return mmkv;
}

+ (void)buz_async:(void (^)(MMKV * _Nonnull))task complete:(void (^)(void))complete
{
    MMKV* mmkv = MMKV.asyncBuz;
    
    dispatch_async(defaultIOQueue(), ^{
        
        task(mmkv);
        if (complete)
        {
            dispatch_async(dispatch_get_main_queue(), ^{
                complete();
            });
        }
    });
}


@end
