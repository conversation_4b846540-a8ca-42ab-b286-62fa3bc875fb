//
//  ZYLocalizableStringManager+iOS.m
//  Localizable
//
//  Created by lizhifm on 2023/6/2.
//

#import "ZYLocalizableStringManager+iOS.h"

@implementation ZYLocalizableStringManager (iOS)

- (void)setupLayoutDirection {
    NSLocaleLanguageDirection languageDirection = [NSLocale characterDirectionForLanguage:self.currentLanguage];
    UIUserInterfaceLayoutDirection layoutDirection = UIUserInterfaceLayoutDirectionLeftToRight;
    switch (languageDirection) {
        case NSLocaleLanguageDirectionLeftToRight:
            layoutDirection = UIUserInterfaceLayoutDirectionLeftToRight;
            if (@available(iOS 9.0, *)) {
                [[UIView appearance] setSemanticContentAttribute:UISemanticContentAttributeForceLeftToRight];
                [[UICollectionView appearance] setSemanticContentAttribute:UISemanticContentAttributeForceLeftToRight];

                
            } else {
                // Fallback on earlier versions
            }
            break;
        case NSLocaleLanguageDirectionRightToLeft:
            layoutDirection = UIUserInterfaceLayoutDirectionRightToLeft;
            if (@available(iOS 9.0, *)) {
                [[UIView appearance] setSemanticContentAttribute:UISemanticContentAttributeForceRightToLeft];
                [[UICollectionView appearance] setSemanticContentAttribute:UISemanticContentAttributeForceRightToLeft];

            } else {
                // Fallback on earlier versions
            }
            break;
        default:
            layoutDirection = [UIApplication sharedApplication].userInterfaceLayoutDirection;
            break;
    }
    self.layoutDirection = layoutDirection;
}

- (NSString *)bundleName {
    return @"IosLocalizable";
}

- (void)refreshLanguage {
}

@end
