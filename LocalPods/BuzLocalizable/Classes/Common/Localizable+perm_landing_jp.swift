import Localizable

extension Localizable {
  @objc public static var authorization_page_title : String {
    get {
      return "authorization_page_title".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var login_register_button_text : String {
    get {
      return "login_register_button_text".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var buz_has_added_you_to : String {
    get {
      return "buz_has_added_you_to".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var buz_share_link : String {
    get {
      return "buz_share_link".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var login_info_edit_buz_id : String {
    get {
      return "login_info_edit_buz_id".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var buz_rate_us_now : String {
    get {
      return "buz_rate_us_now".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var android_mic_auth_page_title : String {
    get {
      return "android_mic_auth_page_title".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var display_name : String {
    get {
      return "display_name".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var transcribe_feedback : String {
    get {
      return "transcribe_feedback".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var buz_auto_play_by_silent_tips : String {
    get {
      return "buz_auto_play_by_silent_tips".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var buz_share_invite : String {
    get {
      return "buz_share_invite".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var login_info_buz_id_change_name_to_xxxx_once_a_year : String {
    get {
      return "login_info_buz_id_change_name_to_xxxx_once_a_year".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var login_info_buz_id_you_change_name_to_xxxx : String {
    get {
      return "login_info_buz_id_you_change_name_to_xxxx".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var buz_auto_play_by_focus_tips : String {
    get {
      return "buz_auto_play_by_focus_tips".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var buz_id : String {
    get {
      return "buz_id".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var Tagalog : String {
    get {
      return "Tagalog".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var login_info_edit_buz_id_rule : String {
    get {
      return "login_info_edit_buz_id_rule".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var login_info_buz_id_change_popup_tip : String {
    get {
      return "login_info_buz_id_change_popup_tip".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var login_info_buz_id_change_name_to_xxxx : String {
    get {
      return "login_info_buz_id_change_name_to_xxxx".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var transcription_auto_tips : String {
    get {
      return "transcription_auto_tips".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var android_mic_auth_page_content : String {
    get {
      return "android_mic_auth_page_content".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var buz_will_not_be_notified_tips : String {
    get {
      return "buz_will_not_be_notified_tips".localizedIn(table:"perm_landing_jp")
    }
  }

  @objc public static var buz_shortcut : String {
    get {
      return "buz_shortcut".localizedIn(table:"perm_landing_jp")
    }
  }

}
