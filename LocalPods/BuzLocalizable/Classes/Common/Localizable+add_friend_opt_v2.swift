import Localizable

extension Localizable {
  @objc public static var reload : String {
    get {
      return "reload".localizedIn(table:"add_friend_opt_v2")
    }
  }

  @objc public static var expired : String {
    get {
      return "expired".localizedIn(table:"add_friend_opt_v2")
    }
  }

  @objc public static var profile_page_load_failed : String {
    get {
      return "profile_page_load_failed".localizedIn(table:"add_friend_opt_v2")
    }
  }

  @objc public static var contacts_tips_friend_on_buz : String {
    get {
      return "contacts_tips_friend_on_buz".localizedIn(table:"add_friend_opt_v2")
    }
  }

  @objc public static var select_country_code : String {
    get {
      return "select_country_code".localizedIn(table:"add_friend_opt_v2")
    }
  }

  @objc public static var contacts_tips_friends_on_buz : String {
    get {
      return "contacts_tips_friends_on_buz".localizedIn(table:"add_friend_opt_v2")
    }
  }

  @objc public static var recommended_country_code : String {
    get {
      return "recommended_country_code".localizedIn(table:"add_friend_opt_v2")
    }
  }

  @objc public static var invalid_country_code : String {
    get {
      return "invalid_country_code".localizedIn(table:"add_friend_opt_v2")
    }
  }

  @objc public static var contacts_tips_friend_on_buz_singular : String {
    get {
      return "contacts_tips_friend_on_buz_singular".localizedIn(table:"add_friend_opt_v2")
    }
  }

  @objc public static var sent_requests_expire : String {
    get {
      return "sent_requests_expire".localizedIn(table:"add_friend_opt_v2")
    }
  }

}
