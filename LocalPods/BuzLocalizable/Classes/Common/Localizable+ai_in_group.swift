import Localizable


extension Localizable {

	@objc public static var groupchat_someone_remove_ai : String {
		get {
			return "groupchat_someone_remove_ai".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var groupchat_you_have_remove_ai : String {
		get {
			return "groupchat_you_have_remove_ai".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_can_invited_to_group : String {
		get {
			return "ai_can_invited_to_group".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var learn_more : String {
		get {
			return "learn_more".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var no_available_ai_character : String {
		get {
			return "no_available_ai_character".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_market_choose_group : String {
		get {
			return "ai_market_choose_group".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_market_bot_title : String {
		get {
			return "ai_market_bot_title".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_tip_in_group : String {
		get {
			return "ai_tip_in_group".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var groupchat_ai_monk : String {
		get {
			return "groupchat_ai_monk".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var group_add_ai_character : String {
		get {
			return "group_add_ai_character".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var groupchat_ai_entermessage : String {
		get {
			return "groupchat_ai_entermessage".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_chatting : String {
		get {
			return "ai_chatting".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var group_add_ai_tip : String {
		get {
			return "group_add_ai_tip".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_invited_to_group : String {
		get {
			return "ai_invited_to_group".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var group_add_ai : String {
		get {
			return "group_add_ai".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_retry_recognize : String {
		get {
			return "ai_retry_recognize".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_already_in_group : String {
		get {
			return "ai_already_in_group".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_helpcenter_introduce : String {
		get {
			return "ai_helpcenter_introduce".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_reach_max_in_group : String {
		get {
			return "ai_reach_max_in_group".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var groupchat_address_you : String {
		get {
			return "groupchat_address_you".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var groupchat_removed_ai : String {
		get {
			return "groupchat_removed_ai".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var groupchat_someone_invite_ai : String {
		get {
			return "groupchat_someone_invite_ai".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_address_in_group : String {
		get {
			return "ai_address_in_group".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var groupchat_wilnt_be_notified : String {
		get {
			return "groupchat_wilnt_be_notified".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var groupchat_have_invite_ai : String {
		get {
			return "groupchat_have_invite_ai".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_person_add_to_group : String {
		get {
			return "ai_person_add_to_group".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_feature_in_group : String {
		get {
			return "ai_feature_in_group".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var groupchat_ai_joined : String {
		get {
			return "groupchat_ai_joined".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var groupchat_notcatchit_sayitagain : String {
		get {
			return "groupchat_notcatchit_sayitagain".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var groupchat_ai_psychologist_tip : String {
		get {
			return "groupchat_ai_psychologist_tip".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_no_group_yet : String {
		get {
			return "ai_no_group_yet".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var groupchat_address_title : String {
		get {
			return "groupchat_address_title".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var groupchat_ai_invite_success : String {
		get {
			return "groupchat_ai_invite_success".localizedIn(table:"ai_in_group")
		}
	}

	@objc public static var ai_dont_recognize : String {
		get {
			return "ai_dont_recognize".localizedIn(table:"ai_in_group")
		}
	}

}

