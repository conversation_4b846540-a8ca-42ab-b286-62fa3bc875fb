import Localizable

extension Localizable {
  @objc public static var ugc_share_download : String {
    get {
      return "ugc_share_download".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var saved_to_photos : String {
    get {
      return "saved_to_photos".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var tap_button_preview : String {
    get {
      return "tap_button_preview".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var filter_loading_failed : String {
    get {
      return "filter_loading_failed".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var buz_voice_message : String {
    get {
      return "buz_voice_message".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var download_canceled : String {
    get {
      return "download_canceled".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var ugc_share_share : String {
    get {
      return "ugc_share_share".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var release_to_preview_record : String {
    get {
      return "release_to_preview_record".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var failed_to_save : String {
    get {
      return "failed_to_save".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var share_canceled : String {
    get {
      return "share_canceled".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var ugc_share_featured : String {
    get {
      return "ugc_share_featured".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var ugc_share_all : String {
    get {
      return "ugc_share_all".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var not_available_voice_filter : String {
    get {
      return "not_available_voice_filter".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var export_voice : String {
    get {
      return "export_voice".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var processing : String {
    get {
      return "processing".localizedIn(table:"voice_filter_ugc_share")
    }
  }

  @objc public static var oops_this_filter_support : String {
    get {
      return "oops_this_filter_support".localizedIn(table:"voice_filter_ugc_share")
    }
  }

}
