import Localizable

extension Localizable {
  @objc public static var auto_translate_opt_chat_tips_portal : String {
    get {
      return "auto_translate_opt_chat_tips_portal".localizedIn(table:"auto_translate_opt")
    }
  }

  @objc public static var call_supports_maximum_tips : String {
    get {
      return "call_supports_maximum_tips".localizedIn(table:"auto_translate_opt")
    }
  }

  @objc public static var auto_translate_opt_chat_tips : String {
    get {
      return "auto_translate_opt_chat_tips".localizedIn(table:"auto_translate_opt")
    }
  }

}
