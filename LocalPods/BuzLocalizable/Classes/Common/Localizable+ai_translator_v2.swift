import Localizable

extension Localizable {
  @objc public static var translator_chat_with_ai : String {
    get {
      return "translator_chat_with_ai".localizedIn(table:"ai_translator_v2")
    }
  }

  @objc public static var translator_between_lan_to_lan : String {
    get {
      return "translator_between_lan_to_lan".localizedIn(table:"ai_translator_v2")
    }
  }

  @objc public static var translator_oh_thanks_feedback : String {
    get {
      return "translator_oh_thanks_feedback".localizedIn(table:"ai_translator_v2")
    }
  }

  @objc public static var translator_fail : String {
    get {
      return "translator_fail".localizedIn(table:"ai_translator_v2")
    }
  }

  @objc public static var translator_recent_used : String {
    get {
      return "translator_recent_used".localizedIn(table:"ai_translator_v2")
    }
  }

  @objc public static var translator_thanks_feedback : String {
    get {
      return "translator_thanks_feedback".localizedIn(table:"ai_translator_v2")
    }
  }

  @objc public static var translator_recognize_empty : String {
    get {
      return "translator_recognize_empty".localizedIn(table:"ai_translator_v2")
    }
  }

  @objc public static var translator_language_list : String {
    get {
      return "translator_language_list".localizedIn(table:"ai_translator_v2")
    }
  }

}
