import Localizable


extension Localizable {

	@objc public static var chat_increase_volume_to_hear_tip : String {
		get {
			return "chat_increase_volume_to_hear_tip".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var feedback_i_am_interested : String {
		get {
			return "feedback_i_am_interested".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var feedback_research_limit_adult_tip : String {
		get {
			return "feedback_research_limit_adult_tip".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var wt_notification_bar_volume_low : String {
		get {
			return "wt_notification_bar_volume_low".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var mode_available : String {
		get {
			return "mode_available".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var phone_call : String {
		get {
			return "phone_call".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var common_loading_old : String {
		get {
			return "common_loading".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var common_connect_service_fail : String {
		get {
			return "common_connect_service_fail".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var feedback_add_buz_research_success : String {
		get {
			return "feedback_add_buz_research_success".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var chat_hold_blue_button_and_speak : String {
		get {
			return "chat_hold_blue_button_and_speak".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var buz_research : String {
		get {
			return "buz_research".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var common_clap : String {
		get {
			return "common_clap".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var feedback_want_to_hear_from_you : String {
		get {
			return "feedback_want_to_hear_from_you".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var sms : String {
		get {
			return "sms".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var feedback_invite_to_join_research : String {
		get {
			return "feedback_invite_to_join_research".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var notification_retrieving_new_message : String {
		get {
			return "notification_retrieving_new_message".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var common_connecting : String {
		get {
			return "common_connecting".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var feedback_research_adult_confirm : String {
		get {
			return "feedback_research_adult_confirm".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var chat_speak_time_left_tip : String {
		get {
			return "chat_speak_time_left_tip".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var chat_notification_receive_and_click_voice_msg : String {
		get {
			return "chat_notification_receive_and_click_voice_msg".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var chat_tap_and_hold_to_speak : String {
		get {
			return "chat_tap_and_hold_to_speak".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var wt_record_max_time_limit_tip : String {
		get {
			return "wt_record_max_time_limit_tip".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var common_waiting_for_network : String {
		get {
			return "common_waiting_for_network".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var feedback_buz_research_account : String {
		get {
			return "feedback_buz_research_account".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var common_bless : String {
		get {
			return "common_bless".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var login_no_receive_phone_call_tips : String {
		get {
			return "login_no_receive_phone_call_tips".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var login_no_receive_phone_call_wait_to_resend : String {
		get {
			return "login_no_receive_phone_call_wait_to_resend".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var chat_switch_mode_to_avoid_autoplay : String {
		get {
			return "chat_switch_mode_to_avoid_autoplay".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var feedback_introduce_buz_research_team : String {
		get {
			return "feedback_introduce_buz_research_team".localizedIn(table:"v1.13.0")
		}
	}

	@objc public static var login_resend_code_by_sms_or_phone : String {
		get {
			return "login_resend_code_by_sms_or_phone".localizedIn(table:"v1.13.0")
		}
	}

}

