import Localizable

extension Localizable {
  @objc public static var storage_available_progress_legend : String {
    get {
      return "storage_available_progress_legend".localizedIn(table:"storage")
    }
  }

  @objc public static var buz_storage_usage_title : String {
    get {
      return "buz_storage_usage_title".localizedIn(table:"storage")
    }
  }

  @objc public static var auto_remove_1_day : String {
    get {
      return "auto_remove_1_day".localizedIn(table:"storage")
    }
  }

  @objc public static var auto_remove_6_months : String {
    get {
      return "auto_remove_6_months".localizedIn(table:"storage")
    }
  }

  @objc public static var auto_remove_7_days : String {
    get {
      return "auto_remove_7_days".localizedIn(table:"storage")
    }
  }

  @objc public static var cache_usage_title : String {
    get {
      return "cache_usage_title".localizedIn(table:"storage")
    }
  }

  @objc public static var clear_cache_with_size : String {
    get {
      return "clear_cache_with_size".localizedIn(table:"storage")
    }
  }

  @objc public static var clear_cache_dialog_title : String {
    get {
      return "clear_cache_dialog_title".localizedIn(table:"storage")
    }
  }

  @objc public static var clear_cache_dialog_desc : String {
    get {
      return "clear_cache_dialog_desc".localizedIn(table:"storage")
    }
  }

  @objc public static var auto_remove_1_month : String {
    get {
      return "auto_remove_1_month".localizedIn(table:"storage")
    }
  }

  @objc public static var clear_cache_desc : String {
    get {
      return "clear_cache_desc".localizedIn(table:"storage")
    }
  }

  @objc public static var retention_period : String {
    get {
      return "retention_period".localizedIn(table:"storage")
    }
  }

  @objc public static var storage_title : String {
    get {
      return "storage_title".localizedIn(table:"storage")
    }
  }

  @objc public static var auto_remove_cached_files_desc : String {
    get {
      return "auto_remove_cached_files_desc".localizedIn(table:"storage")
    }
  }

  @objc public static var clear_cache : String {
    get {
      return "clear_cache".localizedIn(table:"storage")
    }
  }

  @objc public static var buz_used_desc : String {
    get {
      return "buz_used_desc".localizedIn(table:"storage")
    }
  }

  @objc public static var cache_has_been_cleared_dialog : String {
    get {
      return "cache_has_been_cleared_dialog".localizedIn(table:"storage")
    }
  }

  @objc public static var auto_remove_always_keep : String {
    get {
      return "auto_remove_always_keep".localizedIn(table:"storage")
    }
  }

  @objc public static var clearing_cache_loading_dialog : String {
    get {
      return "clearing_cache_loading_dialog".localizedIn(table:"storage")
    }
  }

  @objc public static var storage_apps_and_other_progress_legend : String {
    get {
      return "storage_apps_and_other_progress_legend".localizedIn(table:"storage")
    }
  }

  @objc public static var available_size_desc : String {
    get {
      return "available_size_desc".localizedIn(table:"storage")
    }
  }

  @objc public static var auto_remove_cached_files_title : String {
    get {
      return "auto_remove_cached_files_title".localizedIn(table:"storage")
    }
  }

}
