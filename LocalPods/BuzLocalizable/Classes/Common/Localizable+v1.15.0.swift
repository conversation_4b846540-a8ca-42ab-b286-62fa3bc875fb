import Localizable


extension Localizable {

	@objc public static var setting_watch_failed_transcribe : String {
		get {
			return "setting_watch_failed_transcribe".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var login_please_on_iphone_tip : String {
		get {
			return "login_please_on_iphone_tip".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_watch_authorization : String {
		get {
			return "setting_watch_authorization".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var record_tap_to_send : String {
		get {
			return "record_tap_to_send".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var appstore_watch_convenient : String {
		get {
			return "appstore_watch_convenient".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var notification_view_message : String {
		get {
			return "notification_view_message".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var complications_mode_switch : String {
		get {
			return "complications_mode_switch".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var login_effortless_on_the_go : String {
		get {
			return "login_effortless_on_the_go".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var appstore_watch_connect : String {
		get {
			return "appstore_watch_connect".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_watch_record_introduce : String {
		get {
			return "setting_watch_record_introduce".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var notification_play_voice_message : String {
		get {
			return "notification_play_voice_message".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_watch_setup_now : String {
		get {
			return "setting_watch_setup_now".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var system_please_allow_access_mic : String {
		get {
			return "system_please_allow_access_mic".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_watch_not_now : String {
		get {
			return "setting_watch_not_now".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var appstore_watch_complications : String {
		get {
			return "appstore_watch_complications".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_watch_volume : String {
		get {
			return "setting_watch_volume".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_watch_speaking : String {
		get {
			return "setting_watch_speaking".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var complications_buz_app : String {
		get {
			return "complications_buz_app".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var popup_friends_now : String {
		get {
			return "popup_friends_now".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var system_set_on_iphone : String {
		get {
			return "system_set_on_iphone".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_watch_thinking : String {
		get {
			return "setting_watch_thinking".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_watch_retry : String {
		get {
			return "setting_watch_retry".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_watch_loading : String {
		get {
			return "setting_watch_loading".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var complications_recent_contacts : String {
		get {
			return "complications_recent_contacts".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var appstore_watch_connect_intruduce : String {
		get {
			return "appstore_watch_connect_intruduce".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var appstore_watch_complications_introduce : String {
		get {
			return "appstore_watch_complications_introduce".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var system_access_mic_for_sending : String {
		get {
			return "system_access_mic_for_sending".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var record_tap_to_start : String {
		get {
			return "record_tap_to_start".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var notification_reply_message : String {
		get {
			return "notification_reply_message".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var notification_open_to_receive : String {
		get {
			return "notification_open_to_receive".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var friend_add_anyone_before_chat : String {
		get {
			return "friend_add_anyone_before_chat".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_watch_network_error : String {
		get {
			return "setting_watch_network_error".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var record_has_sent : String {
		get {
			return "record_has_sent".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_volume_control : String {
		get {
			return "setting_volume_control".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var appstore_watch_convenient_introduce : String {
		get {
			return "appstore_watch_convenient_introduce".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_watch_introduce : String {
		get {
			return "setting_watch_introduce".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_buz_on_watch : String {
		get {
			return "setting_buz_on_watch".localizedIn(table:"v1.15.0")
		}
	}

	@objc public static var setting_watch_no_chats : String {
		get {
			return "setting_watch_no_chats".localizedIn(table:"v1.15.0")
		}
	}

}

