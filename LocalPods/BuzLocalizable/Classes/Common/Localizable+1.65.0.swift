import Localizable

extension Localizable {
  @objc public static var chat_more : String {
    get {
      return "chat_more".localizedIn(table:"1.65.0")
    }
  }

  @objc public static var hd_is_on : String {
    get {
      return "hd_is_on".localizedIn(table:"1.65.0")
    }
  }

  @objc public static var chat_history_release_to_cancel : String {
    get {
      return "chat_history_release_to_cancel".localizedIn(table:"1.65.0")
    }
  }

  @objc public static var release_to_preview : String {
    get {
      return "release_to_preview".localizedIn(table:"1.65.0")
    }
  }

  @objc public static var record_release_to_cancel : String {
    get {
      return "record_release_to_cancel".localizedIn(table:"1.65.0")
    }
  }

  @objc public static var try_this_filter : String {
    get {
      return "try_this_filter".localizedIn(table:"1.65.0")
    }
  }

  @objc public static var chat_hold_to_record : String {
    get {
      return "chat_hold_to_record".localizedIn(table:"1.65.0")
    }
  }

  @objc public static var voice_filter_not_available : String {
    get {
      return "voice_filter_not_available".localizedIn(table:"1.65.0")
    }
  }

  @objc public static var chat_history_release_to_preview : String {
    get {
      return "chat_history_release_to_preview".localizedIn(table:"1.65.0")
    }
  }

}
