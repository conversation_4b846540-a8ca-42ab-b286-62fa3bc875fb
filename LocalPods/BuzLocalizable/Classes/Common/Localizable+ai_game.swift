import Localizable


extension Localizable {

	@objc public static var ai_game_tap_to_address_ai_tip : String {
		get {
			return "ai_game_tap_to_address_ai_tip".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_long_desc : String {
		get {
			return "ai_game_long_desc".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_new_function_read_more : String {
		get {
			return "ai_game_new_function_read_more".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_ai_name : String {
		get {
			return "ai_game_ai_name".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_ai_busy_now : String {
		get {
			return "ai_game_ai_busy_now".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_say_hello : String {
		get {
			return "ai_game_say_hello".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_now_you_are_address_an_ai_tip : String {
		get {
			return "ai_game_now_you_are_address_an_ai_tip".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_unavailable_for_reason : String {
		get {
			return "ai_game_unavailable_for_reason".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_unavailable_for_version : String {
		get {
			return "ai_game_unavailable_for_version".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_help_desc : String {
		get {
			return "ai_game_help_desc".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_new_function_got_it : String {
		get {
			return "ai_game_new_function_got_it".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_push_to_talk_tip : String {
		get {
			return "ai_game_push_to_talk_tip".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_new_function_desc : String {
		get {
			return "ai_game_new_function_desc".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_unsupport_language : String {
		get {
			return "ai_game_unsupport_language".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_learn_how_play : String {
		get {
			return "ai_game_learn_how_play".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_new_function_title : String {
		get {
			return "ai_game_new_function_title".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_short_desc : String {
		get {
			return "ai_game_short_desc".localizedIn(table:"ai_game")
		}
	}

	@objc public static var ai_game_ai_is_thinking : String {
		get {
			return "ai_game_ai_is_thinking".localizedIn(table:"ai_game")
		}
	}

}

