import Localizable

extension Localizable {
  @objc public static var ve_over_limit_tips_1 : String {
    get {
      return "ve_over_limit_tips_1".localizedIn(table:"ve_1.3")
    }
  }

  @objc public static var ve_over_limit_tips_2 : String {
    get {
      return "ve_over_limit_tips_2".localizedIn(table:"ve_1.3")
    }
  }

  @objc public static var ve_over_limit_tips_3 : String {
    get {
      return "ve_over_limit_tips_3".localizedIn(table:"ve_1.3")
    }
  }

  @objc public static var qr_new_feature_dialog_title : String {
    get {
      return "qr_new_feature_dialog_title".localizedIn(table:"ve_1.3")
    }
  }

  @objc public static var qr_notify_tip : String {
    get {
      return "qr_notify_tip".localizedIn(table:"ve_1.3")
    }
  }

  @objc public static var qr_new_feature_dialog_content : String {
    get {
      return "qr_new_feature_dialog_content".localizedIn(table:"ve_1.3")
    }
  }

  @objc public static var qr_tap_to_remove : String {
    get {
      return "qr_tap_to_remove".localizedIn(table:"ve_1.3")
    }
  }

}
