import Localizable

extension Localizable {
  @objc public static var account_has_deleted_by_x : String {
    get {
      return "account_has_deleted_by_x".localizedIn(table:"dnd")
    }
  }

  @objc public static var goto_deleted_account : String {
    get {
      return "goto_deleted_account".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_add_back_to_contact : String {
    get {
      return "profile_add_back_to_contact".localizedIn(table:"dnd")
    }
  }

  @objc public static var group_info_share_item : String {
    get {
      return "group_info_share_item".localizedIn(table:"dnd")
    }
  }

  @objc public static var buz_suspended_due_policy_violation : String {
    get {
      return "buz_suspended_due_policy_violation".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_if_report_x : String {
    get {
      return "profile_if_report_x".localizedIn(table:"dnd")
    }
  }

  @objc public static var dnd_notification : String {
    get {
      return "dnd_notification".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_if_remove_x : String {
    get {
      return "profile_if_remove_x".localizedIn(table:"dnd")
    }
  }

  @objc public static var buz_auto_play_by_focus_state_tips : String {
    get {
      return "buz_auto_play_by_focus_state_tips".localizedIn(table:"dnd")
    }
  }

  @objc public static var buz_banned_due_policy_violation : String {
    get {
      return "buz_banned_due_policy_violation".localizedIn(table:"dnd")
    }
  }

  @objc public static var group_info_view_all_members : String {
    get {
      return "group_info_view_all_members".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_goto_leave : String {
    get {
      return "profile_goto_leave".localizedIn(table:"dnd")
    }
  }

  @objc public static var account_has_banned : String {
    get {
      return "account_has_banned".localizedIn(table:"dnd")
    }
  }

  @objc public static var chat_home_quiet_off_guide_tips_title : String {
    get {
      return "chat_home_quiet_off_guide_tips_title".localizedIn(table:"dnd")
    }
  }

  @objc public static var dnd_my_profile_item_content : String {
    get {
      return "dnd_my_profile_item_content".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_if_from_x_remove_x : String {
    get {
      return "profile_if_from_x_remove_x".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_btn_voice_call : String {
    get {
      return "profile_btn_voice_call".localizedIn(table:"dnd")
    }
  }

  @objc public static var focus_mode_permiss_to_get_tips : String {
    get {
      return "focus_mode_permiss_to_get_tips".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_item_clear_history_success : String {
    get {
      return "profile_item_clear_history_success".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_x_remove_form_contacts : String {
    get {
      return "profile_x_remove_form_contacts".localizedIn(table:"dnd")
    }
  }

  @objc public static var chat_bypass_right_btn : String {
    get {
      return "chat_bypass_right_btn".localizedIn(table:"dnd")
    }
  }

  @objc public static var sync_with_focus_tips : String {
    get {
      return "sync_with_focus_tips".localizedIn(table:"dnd")
    }
  }

  @objc public static var voice_mssage_auto_play_upon_receiving : String {
    get {
      return "voice_mssage_auto_play_upon_receiving".localizedIn(table:"dnd")
    }
  }

  @objc public static var sync_with_focus : String {
    get {
      return "sync_with_focus".localizedIn(table:"dnd")
    }
  }

  @objc public static var chat_home_quiet_on_guide_tips_title : String {
    get {
      return "chat_home_quiet_on_guide_tips_title".localizedIn(table:"dnd")
    }
  }

  @objc public static var notification_quiet_mode_on : String {
    get {
      return "notification_quiet_mode_on".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_item_show_notification : String {
    get {
      return "profile_item_show_notification".localizedIn(table:"dnd")
    }
  }

  @objc public static var chat_bypass_DND_content_ios : String {
    get {
      return "chat_bypass_DND_content_ios".localizedIn(table:"dnd")
    }
  }

  @objc public static var chat_bypass_DND_content : String {
    get {
      return "chat_bypass_DND_content".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_goto_dimiss : String {
    get {
      return "profile_goto_dimiss".localizedIn(table:"dnd")
    }
  }

  @objc public static var notification_quiet_mode_off : String {
    get {
      return "notification_quiet_mode_off".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_goto_report_x : String {
    get {
      return "profile_goto_report_x".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_goto_report : String {
    get {
      return "profile_goto_report".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_if_clear_history_x_title : String {
    get {
      return "profile_if_clear_history_x_title".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_if_dimiss_x : String {
    get {
      return "profile_if_dimiss_x".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_x_removed_not_reveive_tips : String {
    get {
      return "profile_x_removed_not_reveive_tips".localizedIn(table:"dnd")
    }
  }

  @objc public static var dnd_my_profile_item_tips : String {
    get {
      return "dnd_my_profile_item_tips".localizedIn(table:"dnd")
    }
  }

  @objc public static var account_has_suspended : String {
    get {
      return "account_has_suspended".localizedIn(table:"dnd")
    }
  }

  @objc public static var chat_home_quiet_off_guide_tips : String {
    get {
      return "chat_home_quiet_off_guide_tips".localizedIn(table:"dnd")
    }
  }

  @objc public static var dnd_audio_device_lost_content : String {
    get {
      return "dnd_audio_device_lost_content".localizedIn(table:"dnd")
    }
  }

  @objc public static var chat_float_view_auto_play_off : String {
    get {
      return "chat_float_view_auto_play_off".localizedIn(table:"dnd")
    }
  }

  @objc public static var dnd_includes_dnd_and_sleep : String {
    get {
      return "dnd_includes_dnd_and_sleep".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_block_x : String {
    get {
      return "profile_block_x".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_if_unblock_x : String {
    get {
      return "profile_if_unblock_x".localizedIn(table:"dnd")
    }
  }

  @objc public static var account_has_deleted : String {
    get {
      return "account_has_deleted".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_leave_x : String {
    get {
      return "profile_leave_x".localizedIn(table:"dnd")
    }
  }

  @objc public static var chat_dnd_guide_dialog_content : String {
    get {
      return "chat_dnd_guide_dialog_content".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_if_clear_all_history_x : String {
    get {
      return "profile_if_clear_all_history_x".localizedIn(table:"dnd")
    }
  }

  @objc public static var dnd_bypass_focus : String {
    get {
      return "dnd_bypass_focus".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_item_auto_play_voice_message : String {
    get {
      return "profile_item_auto_play_voice_message".localizedIn(table:"dnd")
    }
  }

  @objc public static var dnd_audio_device_lost : String {
    get {
      return "dnd_audio_device_lost".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_if_clear_history_x_content : String {
    get {
      return "profile_if_clear_history_x_content".localizedIn(table:"dnd")
    }
  }

  @objc public static var voice_mssage_not_auto_play_and_transcription : String {
    get {
      return "voice_mssage_not_auto_play_and_transcription".localizedIn(table:"dnd")
    }
  }

  @objc public static var profile_only_clear_hitory_tips : String {
    get {
      return "profile_only_clear_hitory_tips".localizedIn(table:"dnd")
    }
  }

  @objc public static var dnd_my_profile_item_title : String {
    get {
      return "dnd_my_profile_item_title".localizedIn(table:"dnd")
    }
  }

  @objc public static var chat_dnd_guide_dialog_content_ios : String {
    get {
      return "chat_dnd_guide_dialog_content_ios".localizedIn(table:"dnd")
    }
  }

  @objc public static var chat_bypass_DND_title : String {
    get {
      return "chat_bypass_DND_title".localizedIn(table:"dnd")
    }
  }

  @objc public static var chat_home_quiet_on_guide_tips : String {
    get {
      return "chat_home_quiet_on_guide_tips".localizedIn(table:"dnd")
    }
  }

  @objc public static var buz_need_access_device_focus_tips : String {
    get {
      return "buz_need_access_device_focus_tips".localizedIn(table:"dnd")
    }
  }

}
