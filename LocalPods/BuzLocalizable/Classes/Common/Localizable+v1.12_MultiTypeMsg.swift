import Localizable

extension Localizable {

	@objc public static var chat_enter_message : String {
		get {
			return "chat_enter_message".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_failed_to_send_message : String {
		get {
			return "chat_failed_to_send_message".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_input_text_limited_tips : String {
		get {
			return "chat_input_text_limited_tips".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_merge_msg_tip : String {
		get {
			return "chat_merge_msg_tip".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_new_feature : String {
		get {
			return "chat_new_feature".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_no_message_here_yet : String {
		get {
			return "chat_no_message_here_yet".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_pop_msg_photo_tag : String {
		get {
			return "chat_pop_msg_photo_tag".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_pop_msg_unsupported_tag : String {
		get {
			return "chat_pop_msg_unsupported_tag".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_pop_msg_voice_tag : String {
		get {
			return "chat_pop_msg_voice_tag".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_push_image_message : String {
		get {
			return "chat_push_image_message".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_rtp_speaking : String {
		get {
			return "chat_rtp_speaking".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_send_to_sb : String {
		get {
			return "chat_send_to_sb".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_support_word_photos_tip : String {
		get {
			return "chat_support_word_photos_tip".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_support_word_photos_title : String {
		get {
			return "chat_support_word_photos_title".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_tips_say_hello : String {
		get {
			return "chat_tips_say_hello".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_unread_messages : String {
		get {
			return "chat_unread_messages".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var chat_xxx_unread : String {
		get {
			return "chat_xxx_unread".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var common_copy : String {
		get {
			return "common_copy".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var login_geetest_tips : String {
		get {
			return "login_geetest_tips".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var login_resend_sms : String {
		get {
			return "login_resend_sms".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var search_your_friends_and_group : String {
		get {
			return "search_your_friends_and_group".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}

	@objc public static var wt_record_error_prompt : String {
		get {
			return "wt_record_error_prompt".localizedIn(table:"v1.12_MultiTypeMsg")
		}
	}
}

