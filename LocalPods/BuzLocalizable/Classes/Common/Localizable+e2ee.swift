import Localizable


extension Localizable {

	@objc public static var sender_decrypt_failed : String {
		get {
			return "sender_decrypt_failed".localizedIn(table:"e2ee")
		}
	}

	@objc public static var e2ee_help_connect_us : String {
		get {
			return "e2ee_help_connect_us".localizedIn(table:"e2ee")
		}
	}

	@objc public static var receiver_decrypt_failed : String {
		get {
			return "receiver_decrypt_failed".localizedIn(table:"e2ee")
		}
	}

	@objc public static var e2ee_help_title : String {
		get {
			return "e2ee_help_title".localizedIn(table:"e2ee")
		}
	}

	@objc public static var e2ee_help_what : String {
		get {
			return "e2ee_help_what".localizedIn(table:"e2ee")
		}
	}

	@objc public static var e2ee_help_what_title : String {
		get {
			return "e2ee_help_what_title".localizedIn(table:"e2ee")
		}
	}

	@objc public static var e2ee_help_connect_us_content : String {
		get {
			return "e2ee_help_connect_us_content".localizedIn(table:"e2ee")
		}
	}

	@objc public static var notify_decrypt_failed : String {
		get {
			return "notify_decrypt_failed".localizedIn(table:"e2ee")
		}
	}

	@objc public static var e2ee_help_how_content1 : String {
		get {
			return "e2ee_help_how_content1".localizedIn(table:"e2ee")
		}
	}

	@objc public static var e2ee_help_how_title : String {
		get {
			return "e2ee_help_how_title".localizedIn(table:"e2ee")
		}
	}

	@objc public static var e2ee_help_how_content3 : String {
		get {
			return "e2ee_help_how_content3".localizedIn(table:"e2ee")
		}
	}

	@objc public static var e2ee_help_main : String {
		get {
			return "e2ee_help_main".localizedIn(table:"e2ee")
		}
	}

	@objc public static var e2ee_help_how_content2 : String {
		get {
			return "e2ee_help_how_content2".localizedIn(table:"e2ee")
		}
	}

}

