import Localizable

extension Localizable {
  @objc public static var transcription_preview : String {
    get {
      return "transcription_preview".localizedIn(table:"asr_v2")
    }
  }

  @objc public static var transcribing : String {
    get {
      return "transcribing".localizedIn(table:"asr_v2")
    }
  }

  @objc public static var notification_tile_photo : String {
    get {
      return "notification_tile_photo".localizedIn(table:"asr_v2")
    }
  }

  @objc public static var notification_tile_video : String {
    get {
      return "notification_tile_video".localizedIn(table:"asr_v2")
    }
  }

  @objc public static var notification_tile_voicemoji : String {
    get {
      return "notification_tile_voicemoji".localizedIn(table:"asr_v2")
    }
  }

  @objc public static var new_msg_count_text : String {
    get {
      return "new_msg_count_text".localizedIn(table:"asr_v2")
    }
  }

  @objc public static var notification_tile_voice : String {
    get {
      return "notification_tile_voice".localizedIn(table:"asr_v2")
    }
  }

  @objc public static var notification_tile_location : String {
    get {
      return "notification_tile_location".localizedIn(table:"asr_v2")
    }
  }

  @objc public static var transcribe_feedback_successful : String {
    get {
      return "transcribe_feedback_successful".localizedIn(table:"asr_v2")
    }
  }

  @objc public static var transcription_preview_tips : String {
    get {
      return "transcription_preview_tips".localizedIn(table:"asr_v2")
    }
  }

  @objc public static var transcribe_error : String {
    get {
      return "transcribe_error".localizedIn(table:"asr_v2")
    }
  }

  @objc public static var auto_transcribe : String {
    get {
      return "auto_transcribe".localizedIn(table:"asr_v2")
    }
  }

}
