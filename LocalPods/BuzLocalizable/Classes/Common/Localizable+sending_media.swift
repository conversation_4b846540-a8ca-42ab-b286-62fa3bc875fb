import Localizable

extension Localizable {
  @objc public static var camera_photo : String {
    get {
      return "camera_photo".localizedIn(table:"sending_media")
    }
  }

  @objc public static var storage_issue_unable_to_record : String {
    get {
      return "storage_issue_unable_to_record".localizedIn(table:"sending_media")
    }
  }

  @objc public static var album_recents : String {
    get {
      return "album_recents".localizedIn(table:"sending_media")
    }
  }

  @objc public static var chat_media_saved_msg : String {
    get {
      return "chat_media_saved_msg".localizedIn(table:"sending_media")
    }
  }

  @objc public static var camera_photo_capital : String {
    get {
      return "camera_photo_capital".localizedIn(table:"sending_media")
    }
  }

  @objc public static var camera_button_desc : String {
    get {
      return "camera_button_desc".localizedIn(table:"sending_media")
    }
  }

  @objc public static var album_preview : String {
    get {
      return "album_preview".localizedIn(table:"sending_media")
    }
  }

  @objc public static var video_processing : String {
    get {
      return "video_processing".localizedIn(table:"sending_media")
    }
  }

  @objc public static var auto_download_desc : String {
    get {
      return "auto_download_desc".localizedIn(table:"sending_media")
    }
  }

  @objc public static var calling_unable_to_play_vid : String {
    get {
      return "calling_unable_to_play_vid".localizedIn(table:"sending_media")
    }
  }

  @objc public static var album_permission_alert_dialog_title : String {
    get {
      return "album_permission_alert_dialog_title".localizedIn(table:"sending_media")
    }
  }

  @objc public static var media_download_failed : String {
    get {
      return "media_download_failed".localizedIn(table:"sending_media")
    }
  }

  @objc public static var chat_push_video_message : String {
    get {
      return "chat_push_video_message".localizedIn(table:"sending_media")
    }
  }

  @objc public static var camera_video : String {
    get {
      return "camera_video".localizedIn(table:"sending_media")
    }
  }

  @objc public static var stay_media : String {
    get {
      return "stay_media".localizedIn(table:"sending_media")
    }
  }

  @objc public static var leave_media : String {
    get {
      return "leave_media".localizedIn(table:"sending_media")
    }
  }

  @objc public static var album_send : String {
    get {
      return "album_send".localizedIn(table:"sending_media")
    }
  }

  @objc public static var share_fail : String {
    get {
      return "share_fail".localizedIn(table:"sending_media")
    }
  }

  @objc public static var album_photos : String {
    get {
      return "album_photos".localizedIn(table:"sending_media")
    }
  }

  @objc public static var media_download_option : String {
    get {
      return "media_download_option".localizedIn(table:"sending_media")
    }
  }

  @objc public static var album_permission_no_access_content : String {
    get {
      return "album_permission_no_access_content".localizedIn(table:"sending_media")
    }
  }

  @objc public static var album_no_photos_yet : String {
    get {
      return "album_no_photos_yet".localizedIn(table:"sending_media")
    }
  }

  @objc public static var leave_media_dialog_desc : String {
    get {
      return "leave_media_dialog_desc".localizedIn(table:"sending_media")
    }
  }

  @objc public static var from_you : String {
    get {
      return "from_you".localizedIn(table:"sending_media")
    }
  }

  @objc public static var media_save_error : String {
    get {
      return "media_save_error".localizedIn(table:"sending_media")
    }
  }

  @objc public static var album_failed_to_send_msg : String {
    get {
      return "album_failed_to_send_msg".localizedIn(table:"sending_media")
    }
  }

  @objc public static var video_too_short : String {
    get {
      return "video_too_short".localizedIn(table:"sending_media")
    }
  }

  @objc public static var album_permission_allow_photos_access_desc : String {
    get {
      return "album_permission_allow_photos_access_desc".localizedIn(table:"sending_media")
    }
  }

  @objc public static var media_went_wrong : String {
    get {
      return "media_went_wrong".localizedIn(table:"sending_media")
    }
  }

  @objc public static var camera_video_capital : String {
    get {
      return "camera_video_capital".localizedIn(table:"sending_media")
    }
  }

  @objc public static var calling_unable_to_record : String {
    get {
      return "calling_unable_to_record".localizedIn(table:"sending_media")
    }
  }

  @objc public static var video_too_large : String {
    get {
      return "video_too_large".localizedIn(table:"sending_media")
    }
  }

  @objc public static var chat_media_save_to_photos : String {
    get {
      return "chat_media_save_to_photos".localizedIn(table:"sending_media")
    }
  }

  @objc public static var chat_media_share : String {
    get {
      return "chat_media_share".localizedIn(table:"sending_media")
    }
  }

  @objc public static var leave_media_dialog_title : String {
    get {
      return "leave_media_dialog_title".localizedIn(table:"sending_media")
    }
  }

  @objc public static var auto_download_title : String {
    get {
      return "auto_download_title".localizedIn(table:"sending_media")
    }
  }

  @objc public static var chat_pop_msg_video_tag : String {
    get {
      return "chat_pop_msg_video_tag".localizedIn(table:"sending_media")
    }
  }

  @objc public static var update_to_view_video_msg : String {
    get {
      return "update_to_view_video_msg".localizedIn(table:"sending_media")
    }
  }

  @objc public static var album_permission_allow_access_title : String {
    get {
      return "album_permission_allow_access_title".localizedIn(table:"sending_media")
    }
  }

  @objc public static var album_permission_allow_all_photos_access : String {
    get {
      return "album_permission_allow_all_photos_access".localizedIn(table:"sending_media")
    }
  }

  @objc public static var album_permission_deny_all_access_desc : String {
    get {
      return "album_permission_deny_all_access_desc".localizedIn(table:"sending_media")
    }
  }

  @objc public static var photo_set_to_hd_toast : String {
    get {
      return "photo_set_to_hd_toast".localizedIn(table:"sending_media")
    }
  }

  @objc public static var album_permission_allow_access_button : String {
    get {
      return "album_permission_allow_access_button".localizedIn(table:"sending_media")
    }
  }

  @objc public static var chat_media_from : String {
    get {
      return "chat_media_from".localizedIn(table:"sending_media")
    }
  }

  @objc public static var select_media_reach_limit : String {
    get {
      return "select_media_reach_limit".localizedIn(table:"sending_media")
    }
  }

}
