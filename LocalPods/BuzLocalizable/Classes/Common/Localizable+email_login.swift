import Localizable


extension Localizable {

	@objc public static var login_info_edit_username_special_character_rule : String {
		get {
			return "login_info_edit_username_special_character_rule".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_edit_user_name_tip : String {
		get {
			return "login_info_edit_user_name_tip".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_resend_via_email : String {
		get {
			return "login_resend_via_email".localizedIn(table:"email_login")
		}
	}

	@objc public static var can_not_find_the_user : String {
		get {
			return "can_not_find_the_user".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_set_user_name_tip : String {
		get {
			return "login_info_set_user_name_tip".localizedIn(table:"email_login")
		}
	}

	@objc public static var make_it_easier_for_friend_to_find_you : String {
		get {
			return "make_it_easier_for_friend_to_find_you".localizedIn(table:"email_login")
		}
	}

	@objc public static var bind_phone_number : String {
		get {
			return "bind_phone_number".localizedIn(table:"email_login")
		}
	}

	@objc public static var unable_to_find_this_user : String {
		get {
			return "unable_to_find_this_user".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_edit_username_rule : String {
		get {
			return "login_info_edit_username_rule".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_user_name_change_name_to_xxxx_once_a_year : String {
		get {
			return "login_info_user_name_change_name_to_xxxx_once_a_year".localizedIn(table:"email_login")
		}
	}

	@objc public static var add_phone : String {
		get {
			return "add_phone".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_email_entrance : String {
		get {
			return "login_email_entrance".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_user_name_not_use_offensive : String {
		get {
			return "login_info_user_name_not_use_offensive".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_user_name_change_popup_tip : String {
		get {
			return "login_info_user_name_change_popup_tip".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_edit_username : String {
		get {
			return "login_info_edit_username".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_user_name_you_change_name_to_xxxx : String {
		get {
			return "login_info_user_name_you_change_name_to_xxxx".localizedIn(table:"email_login")
		}
	}

	@objc public static var email_bind_phone_has_binded : String {
		get {
			return "email_bind_phone_has_binded".localizedIn(table:"email_login")
		}
	}

	@objc public static var email_user_can_not_logout_tip : String {
		get {
			return "email_user_can_not_logout_tip".localizedIn(table:"email_login")
		}
	}

	@objc public static var verify_phone_number : String {
		get {
			return "verify_phone_number".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_user_name_must_begin_with_letter : String {
		get {
			return "login_info_user_name_must_begin_with_letter".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_user_name_had_been_taken : String {
		get {
			return "login_info_user_name_had_been_taken".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_edit_checking_user_name : String {
		get {
			return "login_info_edit_checking_user_name".localizedIn(table:"email_login")
		}
	}

	@objc public static var name : String {
		get {
			return "name".localizedIn(table:"email_login")
		}
	}

	@objc public static var chats : String {
		get {
			return "chats".localizedIn(table:"email_login")
		}
	}

	@objc public static var phone_number_had_been_binded : String {
		get {
			return "phone_number_had_been_binded".localizedIn(table:"email_login")
		}
	}

	@objc public static var copy_username_to_share_with_your_friends : String {
		get {
			return "copy_username_to_share_with_your_friends".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_user_name_avaliable : String {
		get {
			return "login_info_user_name_avaliable".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_email_invalid : String {
		get {
			return "login_email_invalid".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_user_name_must_end_with_letter : String {
		get {
			return "login_info_user_name_must_end_with_letter".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_enter_phone_number : String {
		get {
			return "login_enter_phone_number".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_set_user_name : String {
		get {
			return "login_info_set_user_name".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_user_name_not_support_letter : String {
		get {
			return "login_info_user_name_not_support_letter".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_username_cannot_more_than_one_period : String {
		get {
			return "login_info_username_cannot_more_than_one_period".localizedIn(table:"email_login")
		}
	}

	@objc public static var email_login_email : String {
		get {
			return "email_login_email".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_edit_user_name : String {
		get {
			return "login_info_edit_user_name".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_user_name_limit : String {
		get {
			return "login_info_user_name_limit".localizedIn(table:"email_login")
		}
	}

	@objc public static var make_it_easier_to_find_you : String {
		get {
			return "make_it_easier_to_find_you".localizedIn(table:"email_login")
		}
	}

	@objc public static var verify : String {
		get {
			return "verify".localizedIn(table:"email_login")
		}
	}

	@objc public static var common_complete : String {
		get {
			return "common_complete".localizedIn(table:"email_login")
		}
	}

	@objc public static var edit_fullname : String {
		get {
			return "edit_fullname".localizedIn(table:"email_login")
		}
	}

	@objc public static var ask_you_to_bind_phone_number : String {
		get {
			return "ask_you_to_bind_phone_number".localizedIn(table:"email_login")
		}
	}

	@objc public static var email_bind_phone_success : String {
		get {
			return "email_bind_phone_success".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_cannot_resend_email_code_now_tip : String {
		get {
			return "login_cannot_resend_email_code_now_tip".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_user_name_change_once_a_year : String {
		get {
			return "login_info_user_name_change_once_a_year".localizedIn(table:"email_login")
		}
	}

	@objc public static var the_username_belong_to_yourself : String {
		get {
			return "the_username_belong_to_yourself".localizedIn(table:"email_login")
		}
	}

	@objc public static var username_must_at_least_5_characters : String {
		get {
			return "username_must_at_least_5_characters".localizedIn(table:"email_login")
		}
	}

	@objc public static var common_copied : String {
		get {
			return "common_copied".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_email_input : String {
		get {
			return "login_email_input".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_user_name_change_name_to_xxxx : String {
		get {
			return "login_info_user_name_change_name_to_xxxx".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_user_name_offensive_tip : String {
		get {
			return "login_info_user_name_offensive_tip".localizedIn(table:"email_login")
		}
	}

	@objc public static var please_check_content_if_right : String {
		get {
			return "please_check_content_if_right".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_info_register_edit_username_rule : String {
		get {
			return "login_info_register_edit_username_rule".localizedIn(table:"email_login")
		}
	}

	@objc public static var login_email_address : String {
		get {
			return "login_email_address".localizedIn(table:"email_login")
		}
	}

	@objc public static var username : String {
		get {
			return "username".localizedIn(table:"email_login")
		}
	}

}

