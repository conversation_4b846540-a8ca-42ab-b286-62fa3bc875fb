import Localizable


extension Localizable {

	@objc public static var ve_voiceemoji_tip : String {
		get {
			return "ve_voiceemoji_tip".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_start_send : String {
		get {
			return "ve_start_send".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_playbackBusy : String {
		get {
			return "ve_playbackBusy".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_banPreveiew : String {
		get {
			return "ve_banPreveiew".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_coicemoji : String {
		get {
			return "ve_coicemoji".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_preview : String {
		get {
			return "ve_preview".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_Presets : String {
		get {
			return "ve_Presets".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_sendDecription : String {
		get {
			return "ve_sendDecription".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_attribution : String {
		get {
			return "ve_attribution".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_send : String {
		get {
			return "ve_send".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_sendave : String {
		get {
			return "ve_sendave".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_Replay : String {
		get {
			return "ve_Replay".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_tapstop : String {
		get {
			return "ve_tapstop".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_Creative : String {
		get {
			return "ve_Creative".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_voiceemoji_placehold : String {
		get {
			return "ve_voiceemoji_placehold".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_previewEffect : String {
		get {
			return "ve_previewEffect".localizedIn(table:"Voicemoji")
		}
	}

	@objc public static var ve_unsupport : String {
		get {
			return "ve_unsupport".localizedIn(table:"Voicemoji")
		}
	}

}

