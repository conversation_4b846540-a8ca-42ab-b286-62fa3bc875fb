import Localizable


extension Localizable {

	@objc public static var translation_failed : String {
		get {
			return "translation_failed".localizedIn(table:"bot_setting1")
		}
	}

	@objc public static var translation_settings : String {
		get {
			return "translation_settings".localizedIn(table:"bot_setting1")
		}
	}

	@objc public static var auto_detect : String {
		get {
			return "auto_detect".localizedIn(table:"bot_setting1")
		}
	}

	@objc public static var source_language : String {
		get {
			return "source_language".localizedIn(table:"bot_setting1")
		}
	}

	@objc public static var target_language : String {
		get {
			return "target_language".localizedIn(table:"bot_setting1")
		}
	}

	@objc public static var can_not_translate_image_when_auto_detect : String {
		get {
			return "can_not_translate_image_when_auto_detect".localizedIn(table:"bot_setting1")
		}
	}

	@objc public static var english : String {
		get {
			return "english".localizedIn(table:"bot_setting1")
		}
	}

	@objc public static var set_from_lan_to_lan : String {
		get {
			return "set_from_lan_to_lan".localizedIn(table:"bot_setting1")
		}
	}

	@objc public static var error_try_again : String {
		get {
			return "error_try_again".localizedIn(table:"bot_setting1")
		}
	}

}

