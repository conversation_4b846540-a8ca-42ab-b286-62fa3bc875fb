import Localizable

extension Localizable {
  @objc public static var send_you_quick_reaction_was_no_sent : String {
    get {
      return "send_you_quick_reaction_was_no_sent".localizedIn(table:"ResendMessage")
    }
  }

  @objc public static var resend_cancel_resend : String {
    get {
      return "resend_cancel_resend".localizedIn(table:"ResendMessage")
    }
  }

  @objc public static var resend_you_message_no_send : String {
    get {
      return "resend_you_message_no_send".localizedIn(table:"ResendMessage")
    }
  }

  @objc public static var resend_sending : String {
    get {
      return "resend_sending".localizedIn(table:"ResendMessage")
    }
  }

}
