import Localizable

extension Localizable {
  @objc public static var vf_find_voice_filter_here : String {
    get {
      return "vf_find_voice_filter_here".localizedIn(table:"vf_1.1")
    }
  }

  @objc public static var vf_press_and_hold_to_send_first_msg : String {
    get {
      return "vf_press_and_hold_to_send_first_msg".localizedIn(table:"vf_1.1")
    }
  }

  @objc public static var vf_check_profile_in_setting_page : String {
    get {
      return "vf_check_profile_in_setting_page".localizedIn(table:"vf_1.1")
    }
  }

  @objc public static var vf_chat_with_voice_filter : String {
    get {
      return "vf_chat_with_voice_filter".localizedIn(table:"vf_1.1")
    }
  }

  @objc public static var vf_open_voice_filter : String {
    get {
      return "vf_open_voice_filter".localizedIn(table:"vf_1.1")
    }
  }

}
