import Localizable

extension Localizable {
    @objc public static func cancel() -> String {
		return "cancel".localized
	}

	public static func searchbynumber() -> String {
		return "searchbynumber".localized
	}

	@objc public static func saved() -> String {
		return "saved".localized
	}
    
    @objc
    public static func profileaddfriendprofileaddfriendpending() -> String {
        return "profileaddfriendprofileaddfriendpending".localized
    }

	public static func commoncopylinktoclipboardtip() -> String {
		return "commoncopylinktoclipboardtip".localized
	}
    
    public static func copysuccess() -> String {
        return "copysuccess".localized
    }

	public static func Removed() -> String {
		return "Removed".localized
	}

	public static func Findandtapthe() -> String {
		return "Findandtapthe".localized
	}

	public static func pleaseinputphonenumber() -> String {
		return "pleaseinputphonenumber".localized
	}

	public static func userprivacypolicy() -> String {
		return "userprivacypolicy".localized
	}

	public static func loginaddfriendfromcontact() -> String {
		return "loginaddfriendfromcontact".localized
	}

	public static func feedbackenteryouremail() -> String {
		return "feedbackenteryouremail".localized
	}

    @objc public static func sure() -> String {
		return "sure".localized
	}

	public static func KeepConnected() -> String {
		return "KeepConnected".localized
	}

	public static func profile() -> String {
		return "profile".localized
	}

	public static func chatvoicetooshorttosend() -> String {
		return "chatvoicetooshorttosend".localized
	}

	public static func contactsnofriendsandgroupyet() -> String {
		return "contactsnofriendsandgroupyet".localized
	}

	public static func invitefriendtipscontent() -> String {
		return "invitefriendtipscontent".localized
	}

	public static func chatrecordmaxtimelimittips() -> String {
		return "chatrecordmaxtimelimittips".localized
	}

	public static func stop() -> String {
		return "stop".localized
	}

	public static func profileaddfriendaccept() -> String {
		return "profileaddfriendaccept".localized
	}

	public static func GroupMembers() -> String {
		return "GroupMembers".localized
	}

	public static func Nomobilecontactpermission() -> String {
		return "Nomobilecontactpermission".localized
	}

	public static func TurnonthepermissionXandyouwillberemindedquicklywhenyourfriendssendyouamessageX() -> String {
		return "TurnonthepermissionXandyouwillberemindedquicklywhenyourfriendssendyouamessageX".localized
	}

	public static func AreyousureyouwanttoclearallchathistorywithXX() -> String {
		return "AreyousureyouwanttoclearallchathistorywithXX".localized
	}

	public static func NameXX() -> String {
		return "NameXX".localized
	}

	public static func takephoto() -> String {
		return "takephoto".localized
	}

	public static func EmailLOptionalR() -> String {
		return "EmailLOptionalR".localized
	}

	public static func LogoutX() -> String {
		return "LogoutX".localized
	}

	public static func NotNow() -> String {
		return "NotNow".localized
	}

	public static func search() -> String {
		return "search".localized
	}

	public static func loginprofileemptytips() -> String {
		return "loginprofileemptytips".localized
	}

	public static func userlogout() -> String {
		return "userlogout".localized
	}

	public static func quiet() -> String {
		return "quiet".localized
	}

	public static func norelatedfriends() -> String {
		return "norelatedfriends".localized
	}

	public static func loginfriendsalreadyonbuz() -> String {
		return "loginfriendsalreadyonbuz".localized
	}

	public static func sendrequest() -> String {
		return "sendrequest".localized
	}

	public static func bluewavebutton() -> String {
		return "bluewavebutton".localized
	}

	public static func feedbackguidetitle() -> String {
		return "feedbackguidetitle".localized
	}

	public static func SwitchtonewcallX() -> String {
		return "SwitchtonewcallX".localized
	}

	public static func NetworkerrorXUnabletoUnmuteXPleasetryagainX() -> String {
		return "NetworkerrorXUnabletoUnmuteXPleasetryagainX".localized
	}

	public static func added() -> String {
		return "added".localized
	}

	public static func chatgroupbeenkickedtips() -> String {
		return "chatgroupbeenkickedtips".localized
	}

	public static func feedbackemailoptional() -> String {
		return "feedbackemailoptional".localized
	}

	public static func GiveFeedback() -> String {
		return "GiveFeedback".localized
	}

	public static func feedbackguidedescribe() -> String {
		return "feedbackguidedescribe".localized
	}

	public static func CanXtsendmessagesX() -> String {
		return "CanXtsendmessagesX".localized
	}

	public static func clicktoretry() -> String {
		return "clicktoretry".localized
	}

	public static func DidnXtReceiveacodeXPleasewaitXtoresend() -> String {
		return "DidnXtReceiveacodeXPleasewaitXtoresend".localized
	}

	public static func profilecontactname() -> String {
		return "profilecontactname".localized
	}

	public static func sharetoinvite() -> String {
		return "sharetoinvite".localized
	}

	public static func later() -> String {
		return "later".localized
	}

	public static func leave() -> String {
		return "leave".localized
	}

	public static func contact() -> String {
		return "contact".localized
	}

	public static func loginsentto() -> String {
		return "loginsentto".localized
	}

	public static func warning() -> String {
		return "warning".localized
	}

	public static func add() -> String {
		return "add".localized
	}

	public static func logininvitefriendsjoinbuz() -> String {
		return "logininvitefriendsjoinbuz".localized
	}

	public static func commonbuzbuzmenow() -> String {
		return "commonbuzbuzmenow".localized
	}

	public static func Modifyuserfailed() -> String {
		return "Modifyuserfailed".localized
	}

	public static func loginfillyourfullname() -> String {
		return "loginfillyourfullname".localized
	}

	public static func contactsinvitevialink() -> String {
		return "contactsinvitevialink".localized
	}

	public static func NoRequests() -> String {
		return "NoRequests".localized
	}

	public static func deleterequesttips() -> String {
		return "deleterequesttips".localized
	}

	public static func tiptoinvitefriend() -> String {
		return "tiptoinvitefriend".localized
	}

	public static func loginaddthemtoyourcontact() -> String {
		return "loginaddthemtoyourcontact".localized
	}

	public static func HoldthebuttonuntilyoufinishspeakingXX() -> String {
		return "HoldthebuttonuntilyoufinishspeakingXX".localized
	}

	public static func feedbackenterfeedbacktip() -> String {
		return "feedbackenterfeedbacktip".localized
	}

	public static func profileremovefriend() -> String {
		return "profileremovefriend".localized
	}

	public static func loginpermissionnotificationdesc() -> String {
		return "loginpermissionnotificationdesc".localized
	}

	public static func reportsuccess() -> String {
		return "reportsuccess".localized
	}

	public static func AreyousureyouwanttounblockXX() -> String {
		return "AreyousureyouwanttounblockXX".localized
	}

	public static func StoprecordingafterX() -> String {
		return "StoprecordingafterX".localized
	}

	public static func AreyousuretoremovethisgroupX() -> String {
		return "AreyousuretoremovethisgroupX".localized
	}

	public static func userdeleteaccount() -> String {
		return "userdeleteaccount".localized
	}

	public static func Addwidgetstoyourhomescreen() -> String {
		return "Addwidgetstoyourhomescreen".localized
	}

	public static func ignore() -> String {
		return "ignore".localized
	}

	public static func chatholdtotalk() -> String {
		return "chatholdtotalk".localized
	}

	public static func commonqrcodeexpiretip() -> String {
		return "commonqrcodeexpiretip".localized
	}

	public static func retry() -> String {
		return "retry".localized
	}

	public static func notice() -> String {
		return "notice".localized
	}

	public static func owner() -> String {
		return "owner".localized
	}

	public static func wttryit() -> String {
		return "wttryit".localized
	}

	public static func feedbackdescribe() -> String {
		return "feedbackdescribe".localized
	}

	public static func notificationminago() -> String {
		return "notificationminago".localized
	}

	public static func contactssentrequests() -> String {
		return "contactssentrequests".localized
	}

	public static func scansuccessful() -> String {
		return "scansuccessful".localized
	}

	public static func BuzinBackgroundOld() -> String {
		return "BuzinBackground".localized
	}

	public static func wtrobotguidancepopupprompt() -> String {
		return "wtrobotguidancepopupprompt".localized
	}

	public static func XXsinvitation() -> String {
		return "XXsinvitation".localized
	}

	public static func wtrobotguidancedialogtipsparagraph2() -> String {
		return "wtrobotguidancedialogtipsparagraph2".localized
	}

	public static func userdeleteaccountwarningtips() -> String {
		return "userdeleteaccountwarningtips".localized
	}

	public static func chatjoingroup() -> String {
		return "chatjoingroup".localized
	}

	public static func searchyourfriendsandgroup() -> String {
		return "searchyourfriendsandgroup".localized
	}

	public static func searchbynameornumber() -> String {
		return "searchbynameornumber".localized
	}

	public static func chatgetStarted() -> String {
		return "chatgetStarted".localized
	}

	public static func searchyourfriends() -> String {
		return "searchyourfriends".localized
	}

	public static func StartBuzzing() -> String {
		return "StartBuzzing".localized
	}

	public static func chatlowaudiovolume() -> String {
		return "chatlowaudiovolume".localized
	}

	public static func NextXLetXssetupsomepermissions() -> String {
		return "NextXLetXssetupsomepermissions".localized
	}

	public static func chatgroupnamechanged() -> String {
		return "chatgroupnamechanged".localized
	}

	public static func chatleavegroup() -> String {
		return "chatleavegroup".localized
	}

	public static func block() -> String {
		return "block".localized
	}

	public static func chathomenotifygosetting() -> String {
		return "chathomenotifygosetting".localized
	}

	public static func join() -> String {
		return "join".localized
	}

	public static func feedbacktitle() -> String {
		return "feedbacktitle".localized
	}

	public static func Youcandeletethegroupinsettings() -> String {
		return "Youcandeletethegroupinsettings".localized
	}

	public static func onlinechatnetworknotgood() -> String {
		return "onlinechatnetworknotgood".localized
	}

	public static func chatinvitejoingroupbylinktip() -> String {
		return "chatinvitejoingroupbylinktip".localized
	}

	public static func dismiss() -> String {
		return "dismiss".localized
	}

	public static func loginfullnameemptytips() -> String {
		return "loginfullnameemptytips".localized
	}

	public static func invalidsearch() -> String {
		return "invalidsearch".localized
	}

	public static func chatguidethirdtitle() -> String {
		return "chatguidethirdtitle".localized
	}

	public static func groupmembers() -> String {
		return "groupmembers".localized
	}

	public static func Searchforfriends() -> String {
		return "Searchforfriends".localized
	}

	public static func PleaseaddfriendfirstX() -> String {
		return "PleaseaddfriendfirstX".localized
	}

	public static func Unblockfailed() -> String {
		return "Unblockfailed".localized
	}

	public static func chatvolumeistolowtolisten() -> String {
		return "chatvolumeistolowtolisten".localized
	}

	public static func wtspeaking() -> String {
		return "wtspeaking".localized
	}

	public static func MessageloadingXXX() -> String {
		return "MessageloadingXXX".localized
	}

	public static func phonenumber() -> String {
		return "phonenumber".localized
	}

	public static func chatreleasetocancel() -> String {
		return "chatreleasetocancel".localized
	}

	public static func logintitledesc() -> String {
		return "logintitledesc".localized
	}

	public static func chatupdatedescription() -> String {
		return "chatupdatedescription".localized
	}

	public static func friendrequestsend() -> String {
		return "friendrequestsend".localized
	}

	public static func userprivacy() -> String {
		return "userprivacy".localized
	}

	public static func useragreement() -> String {
		return "useragreement".localized
	}

	public static func userdeletemyaccountlowercase() -> String {
		return "userdeletemyaccountlowercase".localized
	}

	@objc public static func share() -> String {
		return "share".localized
	}

	public static func SearchuserX() -> String {
		return "SearchuserX".localized
	}

	public static func commoncopylink() -> String {
		return "commoncopylink".localized
	}

	public static func tipenterphonenumberandsearch() -> String {
		return "tipenterphonenumberandsearch".localized
	}

	public static func Pleaseallowtherecordingpermissiontoconnect() -> String {
		return "Pleaseallowtherecordingpermissiontoconnect".localized
	}
    
	public static func loginpermissionnotificationtitle() -> String {
		return "loginpermissionnotificationtitle".localized
	}

	public static func XyournewvoiceXbasedAIassistantforinstantanswersandfunconversationsanytimeXanywhereX() -> String {
		return "XyournewvoiceXbasedAIassistantforinstantanswersandfunconversationsanytimeXanywhereX".localized
	}

	public static func currentcountrycode() -> String {
		return "currentcountrycode".localized
	}

	public static func BlockX() -> String {
		return "BlockX".localized
	}

	public static func continuetext() -> String {
		return "continuetext".localized
	}

    @objc
	public static func report() -> String {
		return "report".localized
	}

	public static func mutemessage() -> String {
		return "mutemessage".localized
	}

	public static func unsupportqrcode() -> String {
		return "unsupportqrcode".localized
	}

	public static func scanbuzqrcodetip() -> String {
		return "scanbuzqrcodetip".localized
	}

	public static func feedbackratingtitle() -> String {
		return "feedbackratingtitle".localized
	}

	public static func TryitOut() -> String {
		return "TryitOut".localized
	}

	public static func ifisonlineX() -> String {
		return "ifisonlineX".localized
	}

	public static func download() -> String {
		return "download".localized
	}

	public static func wtinvitetoturnon() -> String {
		return "wtinvitetoturnon".localized
	}

	public static func loginverificationcode() -> String {
		return "loginverificationcode".localized
	}

	public static func loginsharetoinviteonbuz() -> String {
		return "loginsharetoinviteonbuz".localized
	}

	public static func commonfailtoloadqrcodetip() -> String {
		return "commonfailtoloadqrcodetip".localized
	}

	public static func SearchFailed() -> String {
		return "SearchFailed".localized
	}

	public static func all() -> String {
		return "all".localized
	}

	public static func settings() -> String {
		return "settings".localized
	}

	public static func Addwidgetstothehomescreen() -> String {
		return "Addwidgetstothehomescreen".localized
	}

	public static func Callyourfriends() -> String {
		return "Callyourfriends".localized
	}

	public static func titlesearchresult() -> String {
		return "titlesearchresult".localized
	}

	public static func Connectionfailed() -> String {
		return "Connectionfailed".localized
	}

	public static func friends() -> String {
		return "friends".localized
	}

	public static func phonenumberunder() -> String {
		return "phonenumberunder".localized
	}

	public static func chataddmembers() -> String {
		return "chataddmembers".localized
	}

	public static func userdeleteaccountcontent() -> String {
		return "userdeleteaccountcontent".localized
	}

	public static func scanqrcode() -> String {
		return "scanqrcode".localized
	}

	public static func SelectmodehereX() -> String {
		return "SelectmodehereX".localized
	}

	public static func InvalidphonenumberXPleasetryagainX() -> String {
		return "InvalidphonenumberXPleasetryagainX".localized
	}

	public static func feedbackappstoreguidetitle() -> String {
		return "feedbackappstoreguidetitle".localized
	}

	public static func Editprofile() -> String {
		return "Editprofile".localized
	}

	public static func contactsinvitefromcontacts() -> String {
		return "contactsinvitefromcontacts".localized
	}

	public static func chatfingertosend() -> String {
		return "chatfingertosend".localized
	}

	public static func chatremovethisfriend() -> String {
		return "chatremovethisfriend".localized
	}

	public static func selectmode() -> String {
		return "selectmode".localized
	}

	public static func preparescan() -> String {
		return "preparescan".localized
	}

	public static func feedbackcontenttips() -> String {
		return "feedbackcontenttips".localized
	}

	public static func delete() -> String {
		return "delete".localized
	}

	public static func SureXlogout() -> String {
		return "SureXlogout".localized
	}

	public static func tipsfailedtosendmsg() -> String {
		return "tipsfailedtosendmsg".localized
	}

	public static func busy() -> String {
		return "busy".localized
	}

	public static func AddChannel() -> String {
		return "AddChannel".localized
	}

	public static func chataddfriendselectedptttips1() -> String {
		return "chataddfriendselectedptttips1".localized
	}

    @objc
	public static func ok() -> String {
		return "ok".localized
	}

	public static func chataddfriendselectedptttips2() -> String {
		return "chataddfriendselectedptttips2".localized
	}

	public static func wtinvitesuccesstoast() -> String {
		return "wtinvitesuccesstoast".localized
	}

	public static func AreyousuretoleavethisgroupX() -> String {
		return "AreyousuretoleavethisgroupX".localized
	}

	public static func Createagroupwith() -> String {
		return "Createagroupwith".localized
	}

	public static func PrivacyPolicy() -> String {
		return "PrivacyPolicy".localized
	}

	public static func friendrequestaddsuccess() -> String {
		return "friendrequestaddsuccess".localized
	}

	public static func contactrobotthanksforencouragement() -> String {
		return "contactrobotthanksforencouragement".localized
	}

	public static func Modifyfailed() -> String {
		return "Modifyfailed".localized
	}

	public static func wtoffline() -> String {
		return "wtoffline".localized
	}

	public static func sendrequestfail() -> String {
		return "sendrequestfail".localized
	}

	public static func groupchat() -> String {
		return "groupchat".localized
	}

	public static func Blocklist() -> String {
		return "Blocklist".localized
	}

	public static func Ihavereadtheagreementandagreetotheterms() -> String {
		return "Ihavereadtheagreementandagreetotheterms".localized
	}

	public static func feedbackratingdescribe() -> String {
		return "feedbackratingdescribe".localized
	}

    @objc
	public static func tipsnetworkerror() -> String {
		return "tipsnetworkerror".localized
	}

	public static func allowaccesstocamera() -> String {
		return "allowaccesstocamera".localized
	}

	public static func NotifyMiaby() -> String {
		return "NotifyMiaby".localized
	}

	public static func wtinviteall() -> String {
		return "wtinviteall".localized
	}

	public static func Tapandholdthebuttontosayhito() -> String {
		return "Tapandholdthebuttontosayhito".localized
	}

	public static func chatdeletechat() -> String {
		return "chatdeletechat".localized
	}

	public static func NetworkerrorXUnabletoMuteXPleasetryagainX() -> String {
		return "NetworkerrorXUnabletoMuteXPleasetryagainX".localized
	}

	public static func chatdeletemsgdialogtitle() -> String {
		return "chatdeletemsgdialogtitle".localized
	}

	public static func CannotaddmorethanXmembers() -> String {
		return "CannotaddmorethanXmembers".localized
	}

	public static func contactsaddyourcontacts() -> String {
		return "contactsaddyourcontacts".localized
	}
    
	public static func PleaserateyourexperienceX() -> String {
		return "PleaserateyourexperienceX".localized
	}

	public static func commongotosetting() -> String {
		return "commongotosetting".localized
	}

	public static func nomobilecontactpermission() -> String {
		return "nomobilecontactpermission".localized
	}

	public static func contactsnew() -> String {
		return "contactsnew".localized
	}

	public static func speaking() -> String {
		return "speaking".localized
	}

	public static func qrcodeexpired() -> String {
		return "qrcodeexpired".localized
	}

	public static func HelloXXXnAddaprofilepicture() -> String {
		return "HelloXXXnAddaprofilepicture".localized
	}

	public static func Xisofflinenow() -> String {
		return "Xisofflinenow".localized
	}

	public static func wtonlinesearchmemberresult() -> String {
		return "wtonlinesearchmemberresult".localized
	}

	public static func wtinvitegroupjoindialogtips() -> String {
		return "wtinvitegroupjoindialogtips".localized
	}

	public static func scanerror() -> String {
		return "scanerror".localized
	}

	public static func edit() -> String {
		return "edit".localized
	}

	public static func AreyousureyouwanttoremoveXX() -> String {
		return "AreyousureyouwanttoremoveXX".localized
	}

	public static func loginwelcometo() -> String {
		return "loginwelcometo".localized
	}

	public static func Invalidphonenumber() -> String {
		return "Invalidphonenumber".localized
	}

	public static func received() -> String {
		return "received".localized
	}

	public static func commonshareprofile() -> String {
		return "commonshareprofile".localized
	}

	public static func Thiswilldisconnectyourcurrentcall() -> String {
		return "Thiswilldisconnectyourcurrentcall".localized
	}

	public static func accept() -> String {
		return "accept".localized
	}

	public static func chatnotificationchannelfriendsrequest() -> String {
		return "chatnotificationchannelfriendsrequest".localized
	}

	public static func wtguidancepopupprompt() -> String {
		return "wtguidancepopupprompt".localized
	}

	public static func wtswitchguidetexttitle() -> String {
		return "wtswitchguidetexttitle".localized
	}

	public static func contactsviewall() -> String {
		return "contactsviewall".localized
	}

	public static func addfriendfrommobilecontact() -> String {
		return "addfriendfrommobilecontact".localized
	}

	public static func RequestsentX() -> String {
		return "RequestsentX".localized
	}

	public static func wtrecordmaxtimeprompt() -> String {
		return "wtrecordmaxtimeprompt".localized
	}

	public static func XinvitedyoutojoinX() -> String {
		return "XinvitedyoutojoinX".localized
	}

	public static func useraboutbuz() -> String {
		return "useraboutbuz".localized
	}

	public static func blocksuccess() -> String {
		return "blocksuccess".localized
	}

	public static func qrcodetryagain() -> String {
		return "qrcodetryagain".localized
	}

	public static func Connecting() -> String {
		return "Connecting".localized
	}

	public static func wtsbisonlinenow() -> String {
		return "wtsbisonlinenow".localized
	}

	public static func chatinviteexceed() -> String {
		return "chatinviteexceed".localized
	}

    @objc
	public static func imsendaprivatevoiceleavemessage() -> String {
		return "imsendaprivatevoiceleavemessage".localized
	}

	public static func newgroup() -> String {
		return "newgroup".localized
	}

	public static func HelloXnAddaprofilepicture() -> String {
		return "HelloXnAddaprofilepicture".localized
	}

	public static func chatbeinvitedtogrouptipstop() -> String {
		return "chatbeinvitedtogrouptipstop".localized
	}

	public static func chatholdtosayuser() -> String {
		return "chatholdtosayuser".localized
	}

	public static func email() -> String {
		return "email".localized
	}

	public static func AreyousureyouwanttoblockXX() -> String {
		return "AreyousureyouwanttoblockXX".localized
	}

	public static func AllowingthefollowingpermissionswouldprovideyouwiththebestBuzexperienceXWevalueyourprivacyXsecurityatBuzXseehowitworksX() -> String {
		return "AllowingthefollowingpermissionswouldprovideyouwiththebestBuzexperienceXWevalueyourprivacyXsecurityatBuzXseehowitworksX".localized
	}

	public static func PleaseAllowAccess() -> String {
		return "PleaseAllowAccess".localized
	}

	public static func AreyousureyouwanttoremoveXfromgroupXX() -> String {
		return "AreyousureyouwanttoremoveXfromgroupXX".localized
	}

	public static func LogOut() -> String {
		return "LogOut".localized
	}

	public static func XfromgroupInvitesyoutojoinchat() -> String {
		return "XfromgroupInvitesyoutojoinchat".localized
	}

	public static func Removefromchatlist() -> String {
		return "Removefromchatlist".localized
	}

	public static func invite() -> String {
		return "invite".localized
	}

	public static func contacts() -> String {
		return "contacts".localized
	}

	public static func GroupName() -> String {
		return "GroupName".localized
	}

	public static func widgetsetting() -> String {
		return "widgetsetting".localized
	}

	public static func available() -> String {
		return "available".localized
	}

	public static func loginpermissionmicrophonetitle() -> String {
		return "loginpermissionmicrophonetitle".localized
	}
    
	public static func Leavefailed() -> String {
		return "Leavefailed".localized
	}

	public static func Pleaseaddfriendfrist() -> String {
		return "Pleaseaddfriendfrist".localized
	}

	public static func profilenotifybylinkprefix() -> String {
		return "profilenotifybylinkprefix".localized
	}

	public static func chatberemovedtip() -> String {
		return "chatberemovedtip".localized
	}

	public static func chatupdaterequire() -> String {
		return "chatupdaterequire".localized
	}

	public static func RemovefromGroup() -> String {
		return "RemovefromGroup".localized
	}

	public static func scanfailed() -> String {
		return "scanfailed".localized
	}

	public static func NevermissamessagefromyourfriendsX() -> String {
		return "NevermissamessagefromyourfriendsX".localized
	}

	public static func YouXllreceiveandhearlivevoicemessagesfromanyonlinefriendXXnUpgradingsystemto16X1willallowyoutoreplytomessagesoutsidetheappX() -> String {
		return "YouXllreceiveandhearlivevoicemessagesfromanyonlinefriendXXnUpgradingsystemto16X1willallowyoutoreplytomessagesoutsidetheappX".localized
	}

	public static func ShareyourexperiencewithusandearnX50AmazoneXgiftcardXEnteryouremailtogetfurtherinformationX() -> String {
		return "ShareyourexperiencewithusandearnX50AmazoneXgiftcardXEnteryouremailtogetfurtherinformationX".localized
	}

	public static func ExitGroup() -> String {
		return "ExitGroup".localized
	}

	public static func support() -> String {
		return "support".localized
	}

	public static func myqrcode() -> String {
		return "myqrcode".localized
	}

	public static func next() -> String {
		return "next".localized
	}

	public static func CodeexpiredXpleasetryagainX() -> String {
		return "CodeexpiredXpleasetryagainX".localized
	}

	public static func Turnon() -> String {
		return "Turnon".localized
	}

	public static func AddsomefriendsfirstandformagrouplaterX() -> String {
		return "AddsomefriendsfirstandformagrouplaterX".localized
	}

	public static func firstname() -> String {
		return "firstname".localized
	}

	public static func submit() -> String {
		return "submit".localized
	}

	public static func wtinvitefriendjoindialogtips() -> String {
		return "wtinvitefriendjoindialogtips".localized
	}

	public static func YouwereinvitedtogroupbyX() -> String {
		return "YouwereinvitedtogroupbyX".localized
	}

	public static func loadingXXX() -> String {
		return "loadingXXX".localized
	}

	public static func loginuseragreementandpolicy() -> String {
		return "loginuseragreementandpolicy".localized
	}

	public static func contactsshareqrcode() -> String {
		return "contactsshareqrcode".localized
	}

	public static func update() -> String {
		return "update".localized
	}

	public static func chatgroupmemberwillnotnotify() -> String {
		return "chatgroupmemberwillnotnotify".localized
	}
    
	public static func chatpleaseselectyourfriends() -> String {
		return "chatpleaseselectyourfriends".localized
	}

	public static func qrcodenotdetected() -> String {
		return "qrcodenotdetected".localized
	}

	public static func InvalidcodeXPleasetryagain() -> String {
		return "InvalidcodeXPleasetryagain".localized
	}

	public static func wtbeinginvitedbyfrienddialogtips() -> String {
		return "wtbeinginvitedbyfrienddialogtips".localized
	}

	public static func UnitedState() -> String {
		return "UnitedState".localized
	}

	public static func Nofriendyet() -> String {
		return "Nofriendyet".localized
	}

	public static func invitefriends() -> String {
		return "invitefriends".localized
	}

	public static func LeaveVoiceMessage() -> String {
		return "LeaveVoiceMessage".localized
	}

	public static func chatclearchathistory() -> String {
		return "chatclearchathistory".localized
	}

	public static func Thisuserisnotinyourcontact() -> String {
		return "Thisuserisnotinyourcontact".localized
	}

	public static func ConnectionfailedduetolackofrecordingpermissionXPleaseallowtherecordingpermissionX() -> String {
		return "ConnectionfailedduetolackofrecordingpermissionXPleaseallowtherecordingpermissionX".localized
	}

	public static func loginbuz() -> String {
		return "loginbuz".localized
	}

	public static func contactscontactsonbuz() -> String {
		return "contactscontactsonbuz".localized
	}

	public static func chatgroupmaximumcapacitynpeople() -> String {
		return "chatgroupmaximumcapacitynpeople".localized
	}

	public static func feedbackappstoreguidedesc() -> String {
		return "feedbackappstoreguidedesc".localized
	}

	public static func userquietontip() -> String {
		return "userquietontip".localized
	}

	public static func Openpermission() -> String {
		return "Openpermission".localized
	}

	public static func profilenamenotoncontacts() -> String {
		return "profilenamenotoncontacts".localized
	}

	public static func Addnewfriendsthroughphonenumber() -> String {
		return "Addnewfriendsthroughphonenumber".localized
	}

	public static func Selectyourcountry() -> String {
		return "Selectyourcountry".localized
	}

	public static func Getearlyaccessto() -> String {
		return "Getearlyaccessto".localized
	}

	public static func feedbackOld() -> String {
		return "feedback".localized
	}

	public static func editgroupname() -> String {
		return "editgroupname".localized
	}

	public static func unblock() -> String {
		return "unblock".localized
	}

	public static func addfriends() -> String {
		return "addfriends".localized
	}

	public static func BuzAI() -> String {
		return "BuzAI".localized
	}

	public static func BuzneedscameraaccesssothatyoucantakephotosX() -> String {
		return "BuzneedscameraaccesssothatyoucantakephotosX".localized
	}

	public static func Enteryournumber() -> String {
		return "Enteryournumber".localized
	}

	public static func loginpermissionmicrophonedesc() -> String {
		return "loginpermissionmicrophonedesc".localized
	}

	public static func ThankyouX() -> String {
		return "ThankyouX".localized
	}

	public static func PleasewaitXtoresendcodeXCheckifthenumberiscorrect() -> String {
		return "PleasewaitXtoresendcodeXCheckifthenumberiscorrect".localized
	}
    
    @objc
	public static func okay() -> String {
		return "okay".localized
	}

	public static func updatebuztip() -> String {
		return "updatebuztip".localized
	}

	public static func Nofriends() -> String {
		return "Nofriends".localized
	}

	public static func commonqrcode() -> String {
		return "commonqrcode".localized
	}

	public static func chatrecordpermissiondenieddialogtext() -> String {
		return "chatrecordpermissiondenieddialogtext".localized
	}

	public static func chatnotvoicemsg() -> String {
		return "chatnotvoicemsg".localized
	}

	public static func lastname() -> String {
		return "lastname".localized
	}

	public static func Canloadinvitedata() -> String {
		return "Canloadinvitedata".localized
	}

    @objc
	public static func wtforegroundnotificationprompt() -> String {
		return "wtforegroundnotificationprompt".localized
	}

	public static func feedbackcollectemailtitle() -> String {
		return "feedbackcollectemailtitle".localized
	}

	public static func Members() -> String {
		return "Members".localized
	}

	public static func chatareyousureyouwanttoleavegroup() -> String {
		return "chatareyousureyouwanttoleavegroup".localized
	}

	public static func feedbackratingscore3() -> String {
		return "feedbackratingscore3".localized
	}

	public static func profilesendmessage() -> String {
		return "profilesendmessage".localized
	}

	public static func feedbackratingscore2() -> String {
		return "feedbackratingscore2".localized
	}

	public static func OnlineMembers() -> String {
		return "OnlineMembers".localized
	}

	public static func feedbackratingscore1() -> String {
		return "feedbackratingscore1".localized
	}

	public static func Description() -> String {
		return "Description".localized
	}

	public static func DidnXtreceiveacodeX() -> String {
		return "DidnXtreceiveacodeX".localized
	}

	public static func feedbackratingscore5() -> String {
		return "feedbackratingscore5".localized
	}

	public static func invitetobuz() -> String {
		return "invitetobuz".localized
	}

	public static func feedbackratingscore4() -> String {
		return "feedbackratingscore4".localized
	}

	public static func notificationjustnow() -> String {
		return "notificationjustnow".localized
	}

	public static func requests() -> String {
		return "requests".localized
	}

	public static func userbuzcancellationnotice() -> String {
		return "userbuzcancellationnotice".localized
	}

	public static func Pushtotalk() -> String {
		return "Pushtotalk".localized
	}

	public static func AddFriend() -> String {
		return "AddFriend".localized
	}

	public static func BuzinBackgroundiscurrentlyOFFOld() -> String {
		return "BuzinBackgroundiscurrentlyOFF".localized
	}

	public static func chatnewmemberingroup() -> String {
		return "chatnewmemberingroup".localized
	}

	public static func Friend() -> String {
		return "Friend".localized
	}

	public static func Phonenumbermissed() -> String {
		return "Phonenumbermissed".localized
	}

	public static func nosearchresult() -> String {
		return "nosearchresult".localized
	}

	public static func profileeditdisplayname() -> String {
		return "profileeditdisplayname".localized
	}

	public static func more() -> String {
		return "more".localized
	}

	public static func loginreceivecodebackconfirm() -> String {
		return "loginreceivecodebackconfirm".localized
	}

	public static func Allowaccess() -> String {
		return "Allowaccess".localized
	}

	public static func userdeletemyaccount() -> String {
		return "userdeletemyaccount".localized
	}

	public static func wtrobotguidancedialogtitle() -> String {
		return "wtrobotguidancedialogtitle".localized
	}

	public static func chatdialogreportconfirmtitle() -> String {
		return "chatdialogreportconfirmtitle".localized
	}

	public static func loginresendsms() -> String {
		return "loginresendsms".localized
	}

	public static func YourfriendisnolongerinthegroupX() -> String {
		return "YourfriendisnolongerinthegroupX".localized
	}

	public static func wtswitchguidetext() -> String {
		return "wtswitchguidetext".localized
	}

	public static func WidgetsSetting() -> String {
		return "WidgetsSetting".localized
	}

	public static func Failtosend() -> String {
		return "Failtosend".localized
	}

	public static func contactscreategroup() -> String {
		return "contactscreategroup".localized
	}

	public static func loginpermissioncontacttitle() -> String {
		return "loginpermissioncontacttitle".localized
	}

	public static func chatgotochat() -> String {
		return "chatgotochat".localized
	}

	public static func mutenotification() -> String {
		return "mutenotification".localized
	}

	public static func BuzinBackgroundiscurrentlyONOld() -> String {
		return "BuzinBackgroundiscurrentlyON".localized
	}

	public static func Reportfailed() -> String {
		return "Reportfailed".localized
	}

	public static func feedbackdiscord() -> String {
		return "feedbackdiscord".localized
	}

	public static func RequestssentX() -> String {
		return "RequestssentX".localized
	}

	public static func wtcannotrecordwhileisincall() -> String {
		return "wtcannotrecordwhileisincall".localized
	}

	public static func create() -> String {
		return "create".localized
	}

	public static func loginpermissioncontactdesc() -> String {
		return "loginpermissioncontactdesc".localized
	}

	public static func NoXThanks() -> String {
		return "NoXThanks".localized
	}

	public static func group() -> String {
		return "group".localized
	}

	public static func ifXisonlineX() -> String {
		return "ifXisonlineX".localized
	}

	public static func SelectAllLXXXR() -> String {
		return "SelectAllLXXXR".localized
	}

	public static func intheStatusBarX() -> String {
		return "intheStatusBarX".localized
	}

	public static func Createchannelfailed() -> String {
		return "Createchannelfailed".localized
	}

	public static func FeedBack() -> String {
		return "FeedBack".localized
	}

	public static func Yesterday() -> String {
		return "Yesterday".localized
	}

	public static func Channel() -> String {
		return "Channel".localized
	}

	public static func Quicklyaccessyourclosefriendsonyourhomescreen() -> String {
		return "Quicklyaccessyourclosefriendsonyourhomescreen".localized
	}

	public static func Removefailed() -> String {
		return "Removefailed".localized
	}

	public static func loginintobuz() -> String {
		return "loginintobuz".localized
	}

	public static func done() -> String {
		return "done".localized
	}

	public static func sent() -> String {
		return "sent".localized
	}

	public static func networkerror() -> String {
		return "networkerror".localized
	}

	public static func personalprofile() -> String {
		return "personalprofile".localized
	}

	public static func OnlineFriends() -> String {
		return "OnlineFriends".localized
	}

	public static func Gosetting() -> String {
		return "Gosetting".localized
	}

	public static func wtoverlaytips() -> String {
		return "wtoverlaytips".localized
	}

	public static func link() -> String {
		return "link".localized
	}

	public static func XhasdeclinedtoreceiveyourmessageX() -> String {
		return "XhasdeclinedtoreceiveyourmessageX".localized
	}

	public static func SelectAll() -> String {
		return "SelectAll".localized
	}

	public static func chatactioncannotundo() -> String {
		return "chatactioncannotundo".localized
	}

	public static func loginenablepermissions() -> String {
		return "loginenablepermissions".localized
	}

	public static func reject() -> String {
		return "reject".localized
	}

	public static func profilemanagecontact() -> String {
		return "profilemanagecontact".localized
	}

	public static func contactsnogroupyet() -> String {
		return "contactsnogroupyet".localized
	}

	public static func GotitX() -> String {
		return "GotitX".localized
	}

	public static func talkedtoyou() -> String {
		return "talkedtoyou".localized
	}

	public static func commonenablenotificationtitle() -> String {
		return "commonenablenotificationtitle".localized
	}

	public static func feedbackinvitejoincommunity() -> String {
		return "feedbackinvitejoincommunity".localized
	}

	public static func CanXtsendmessageX() -> String {
		return "CanXtsendmessageX".localized
	}

	public static func album() -> String {
		return "album".localized
	}

	public static func invitedyoutoregisteronBuzX() -> String {
		return "invitedyoutoregisteronBuzX".localized
	}

	public static func clear() -> String {
		return "clear".localized
	}

	public static func inviteyoutoonlinechat() -> String {
		return "inviteyoutoonlinechat".localized
	}

	public static func commonsaveqrcodewithoutpermissiontip() -> String {
		return "commonsaveqrcodewithoutpermissiontip".localized
	}

	public static func logincontactuswithproblems() -> String {
		return "logincontactuswithproblems".localized
	}

	public static func contactsnofriendsyet() -> String {
		return "contactsnofriendsyet".localized
	}

	public static func userquietofftip() -> String {
		return "userquietofftip".localized
	}

	public static func contactrobothowcanidobetter() -> String {
		return "contactrobothowcanidobetter".localized
	}

	public static func suggestions() -> String {
		return "suggestions".localized
	}

	public static func account() -> String {
		return "account".localized
	}
    
    public static var quiet_mode : String {
        get {
            return "quiet_mode".localized
        }
    }
    
    public static var live_activity_accessibility : String {
        get {
            return "live_activity_accessibility".localized
        }
    }
    
    public static var live_activity_enable_speech_recognition : String {
        get {
            return "live_activity_enable_speech_recognition".localized
        }
    }
    
    public static var live_activity_features : String {
        get {
            return "live_activity_features".localized
        }
    }
    
    public static var live_activity_off : String {
        get {
            return "live_activity_off".localized
        }
    }
    
    public static var live_activity_permission_disabled : String {
        get {
            return "live_activity_permission_disabled".localized
        }
    }
    
    public static var live_activity_Allow_transcriptions : String {
        get {
            return "live_activity_Allow_transcriptions".localized
        }
    }
    
    public static var live_activity_Transcribing : String {
        get {
            return "live_activity_Transcribing".localized
        }
    }
    
    public static var notificaiton_recieve_friend_request : String {
        get {
            return "notificaiton_recieve_friend_request".localized
        }
    }
    
    public static var invite_friend_close : String {
        get {
            return "invite_friend_close".localized
        }
    }
    
    public static var recording_time_is_too_short : String {
        get {
            return "recordingTimeIsTooShort".localized
        }
    }
    
    public static var recording_time_cannot_exceed_5_minutes : String {
        get {
            return "recordingTimeCannotExceed5Minutes".localized
        }
    }
}

