import Localizable

extension Localizable {
  @objc public static var setting_notification_setting_tips : String {
    get {
      return "setting_notification_setting_tips".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var chat_home_notify_permission_setting_v2 : String {
    get {
      return "chat_home_notify_permission_setting_v2".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var transcription_preview_tips_v2 : String {
    get {
      return "transcription_preview_tips_v2".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var notification_setting_allow_notification_content : String {
    get {
      return "notification_setting_allow_notification_content".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var notification_prob_popup_dialog_content : String {
    get {
      return "notification_prob_popup_dialog_content".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var notification_setting_run_in_background_content : String {
    get {
      return "notification_setting_run_in_background_content".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var notification_setting_run_in_background_unsupported_model : String {
    get {
      return "notification_setting_run_in_background_unsupported_model".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var notification_setting_allow_notification_tips : String {
    get {
      return "notification_setting_allow_notification_tips".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var notification_setting_run_in_background_tips : String {
    get {
      return "notification_setting_run_in_background_tips".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var search_v2 : String {
    get {
      return "search_v2".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var setting_notification_setting_title : String {
    get {
      return "setting_notification_setting_title".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var setting_notification_setting_content : String {
    get {
      return "setting_notification_setting_content".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var group_v2 : String {
    get {
      return "group_v2".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var blocklist_v2 : String {
    get {
      return "blocklist_v2".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var notification_setting_allow_start_tips : String {
    get {
      return "notification_setting_allow_start_tips".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var notification_setting_run_in_background_enabled : String {
    get {
      return "notification_setting_run_in_background_enabled".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var notification_setting_allow_notification_enabled : String {
    get {
      return "notification_setting_allow_notification_enabled".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var chat_home_notify_go_setting_v2 : String {
    get {
      return "chat_home_notify_go_setting_v2".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var photos_v2 : String {
    get {
      return "photos_v2".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var notification_setting_allow_start_content : String {
    get {
      return "notification_setting_allow_start_content".localizedIn(table:"offline_push_v1")
    }
  }

  @objc public static var on : String {
    get {
      return "on".localizedIn(table:"offline_push_v1")
    }
  }

}
