import Localizable

extension Localizable {
  @objc public static var add_a_friend_first : String {
    get {
      return "add_a_friend_first".localizedIn(table:"blind_box")
    }
  }

  @objc public static var blind_box_come_back_tmr_hint : String {
    get {
      return "blind_box_come_back_tmr_hint".localizedIn(table:"blind_box")
    }
  }

  @objc public static var collect_blind_box_dialog_desc : String {
    get {
      return "collect_blind_box_dialog_desc".localizedIn(table:"blind_box")
    }
  }

  @objc public static var blind_box_category_day_count : String {
    get {
      return "blind_box_category_day_count".localizedIn(table:"blind_box")
    }
  }

  @objc public static var collect_blind_box_dialog_title : String {
    get {
      return "collect_blind_box_dialog_title".localizedIn(table:"blind_box")
    }
  }

  @objc public static var blind_box_claimed_toast : String {
    get {
      return "blind_box_claimed_toast".localizedIn(table:"blind_box")
    }
  }

  @objc public static var blind_box_unlocked_title : String {
    get {
      return "blind_box_unlocked_title".localizedIn(table:"blind_box")
    }
  }

  @objc public static var voicemoji_secret_label : String {
    get {
      return "voicemoji_secret_label".localizedIn(table:"blind_box")
    }
  }

  @objc public static var voicemoji_interrupt_toast : String {
    get {
      return "voicemoji_interrupt_toast".localizedIn(table:"blind_box")
    }
  }

  @objc public static var collect_blind_box_dialog_bold_span_desc : String {
    get {
      return "collect_blind_box_dialog_bold_span_desc".localizedIn(table:"blind_box")
    }
  }

  @objc public static var collect_blind_box_dialog_collect_now_button : String {
    get {
      return "collect_blind_box_dialog_collect_now_button".localizedIn(table:"blind_box")
    }
  }

  @objc public static var recover_missed_voicemoji_dialog_title : String {
    get {
      return "recover_missed_voicemoji_dialog_title".localizedIn(table:"blind_box")
    }
  }

  @objc public static var recover_missed_voicemoji_dialog_invite_button : String {
    get {
      return "recover_missed_voicemoji_dialog_invite_button".localizedIn(table:"blind_box")
    }
  }

  @objc public static var recover_missed_voicemoji_dialog_desc : String {
    get {
      return "recover_missed_voicemoji_dialog_desc".localizedIn(table:"blind_box")
    }
  }

}
