import Localizable

extension Localizable {
  @objc public static var preview_title : String {
    get {
      return "preview_title".localizedIn(table:"ftue")
    }
  }

  @objc public static var group_recommend_share_text : String {
    get {
      return "group_recommend_share_text".localizedIn(table:"ftue")
    }
  }

  @objc public static var please_complete_onboarding : String {
    get {
      return "please_complete_onboarding".localizedIn(table:"ftue")
    }
  }

  @objc public static var or_text : String {
    get {
      return "or_text".localizedIn(table:"ftue")
    }
  }

  @objc public static var login_user_agreement_and_policy_new_with_next_line : String {
    get {
      return "login_user_agreement_and_policy_new_with_next_line".localizedIn(table:"ftue")
    }
  }

  @objc public static var name_title : String {
    get {
      return "name_title".localizedIn(table:"ftue")
    }
  }

  @objc public static var name_subtitle : String {
    get {
      return "name_subtitle".localizedIn(table:"ftue")
    }
  }

  @objc public static var buzid_title : String {
    get {
      return "buzid_title".localizedIn(table:"ftue")
    }
  }

  @objc public static var auth_notification_subtitle : String {
    get {
      return "auth_notification_subtitle".localizedIn(table:"ftue")
    }
  }

  @objc public static var recommend_share_text : String {
    get {
      return "recommend_share_text".localizedIn(table:"ftue")
    }
  }

  @objc public static var send_code : String {
    get {
      return "send_code".localizedIn(table:"ftue")
    }
  }

  @objc public static var preview_subtitle : String {
    get {
      return "preview_subtitle".localizedIn(table:"ftue")
    }
  }

  @objc public static var auth_contact_title : String {
    get {
      return "auth_contact_title".localizedIn(table:"ftue")
    }
  }

  @objc public static var auth_notification_title : String {
    get {
      return "auth_notification_title".localizedIn(table:"ftue")
    }
  }

  @objc public static var auth_battery_title : String {
    get {
      return "auth_battery_title".localizedIn(table:"ftue")
    }
  }

  @objc public static var onboarding_desc : String {
    get {
      return "onboarding_desc".localizedIn(table:"ftue")
    }
  }

  @objc public static var audio_conflict : String {
    get {
      return "audio_conflict".localizedIn(table:"ftue")
    }
  }

  @objc public static var please_check_sms_whatsapp : String {
    get {
      return "please_check_sms_whatsapp".localizedIn(table:"ftue")
    }
  }

  @objc public static var portrait_title : String {
    get {
      return "portrait_title".localizedIn(table:"ftue")
    }
  }

  @objc public static var send_your_first_message : String {
    get {
      return "send_your_first_message".localizedIn(table:"ftue")
    }
  }

  @objc public static var get_started : String {
    get {
      return "get_started".localizedIn(table:"ftue")
    }
  }

  @objc public static var hooray : String {
    get {
      return "hooray".localizedIn(table:"ftue")
    }
  }

  @objc public static var welcome_to_buz : String {
    get {
      return "welcome_to_buz".localizedIn(table:"ftue")
    }
  }

  @objc public static var auth_contact_ios : String {
    get {
      return "auth_contact_ios".localizedIn(table:"ftue")
    }
  }

  @objc public static var auto_play_tooltip : String {
    get {
      return "auto_play_tooltip".localizedIn(table:"ftue")
    }
  }

  @objc public static var enter_verification_code : String {
    get {
      return "enter_verification_code".localizedIn(table:"ftue")
    }
  }

  @objc public static var system_version_not_support : String {
    get {
      return "system_version_not_support".localizedIn(table:"ftue")
    }
  }

  @objc public static var has_setup_complete : String {
    get {
      return "has_setup_complete".localizedIn(table:"ftue")
    }
  }

  @objc public static var press_and_hold_to_say : String {
    get {
      return "press_and_hold_to_say".localizedIn(table:"ftue")
    }
  }

  @objc public static var continue_with_google_ftue : String {
    get {
      return "continue_with_google_ftue".localizedIn(table:"ftue")
    }
  }

  @objc public static var please_wait_to_resend_code : String {
    get {
      return "please_wait_to_resend_code".localizedIn(table:"ftue")
    }
  }

  @objc public static var buzid_desc : String {
    get {
      return "buzid_desc".localizedIn(table:"ftue")
    }
  }

  @objc public static var auth_contact_android : String {
    get {
      return "auth_contact_android".localizedIn(table:"ftue")
    }
  }

  @objc public static var auth_battery_subtitle : String {
    get {
      return "auth_battery_subtitle".localizedIn(table:"ftue")
    }
  }

}
