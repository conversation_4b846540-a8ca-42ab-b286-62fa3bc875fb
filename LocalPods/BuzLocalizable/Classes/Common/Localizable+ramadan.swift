import Localizable

extension Localizable {
  @objc public static var contact_remove_from_chat_list : String {
    get {
      return "contact_remove_from_chat_list".localizedIn(table:"ramadan")
    }
  }

  @objc public static var operation_not_supported : String {
    get {
      return "operation_not_supported".localizedIn(table:"ramadan")
    }
  }

  @objc public static var contact_ai_recommendation_title : String {
    get {
      return "contact_ai_recommendation_title".localizedIn(table:"ramadan")
    }
  }

  @objc public static var contact_ai_add_to_chat_list : String {
    get {
      return "contact_ai_add_to_chat_list".localizedIn(table:"ramadan")
    }
  }

}
