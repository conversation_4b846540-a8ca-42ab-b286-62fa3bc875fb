import Localizable

extension Localizable {
  @objc public static var google_register_bind_phone_btn : String {
    get {
      return "google_register_bind_phone_btn".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var current_country_and_region : String {
    get {
      return "current_country_and_region".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var add_to_buz_contacts : String {
    get {
      return "add_to_buz_contacts".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var invalid_country_or_region_code : String {
    get {
      return "invalid_country_or_region_code".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var google_register_bind_phone_title : String {
    get {
      return "google_register_bind_phone_title".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var login_register_v2_enter_phone_number : String {
    get {
      return "login_register_v2_enter_phone_number".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var google_register_bind_phone_tips : String {
    get {
      return "google_register_bind_phone_tips".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var network_is_unstable : String {
    get {
      return "network_is_unstable".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var confirm : String {
    get {
      return "confirm".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var back_to_edit : String {
    get {
      return "back_to_edit".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var verification_code_notification_content : String {
    get {
      return "verification_code_notification_content".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var select_country_and_region : String {
    get {
      return "select_country_and_region".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var network_unstable : String {
    get {
      return "network_unstable".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var phone_input_notification_content : String {
    get {
      return "phone_input_notification_content".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var is_correct_number : String {
    get {
      return "is_correct_number".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var recommended_country_or_region_code : String {
    get {
      return "recommended_country_or_region_code".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var please_confirm_country_or_region_code : String {
    get {
      return "please_confirm_country_or_region_code".localizedIn(table:"login_register_v2")
    }
  }

  @objc public static var no_internet : String {
    get {
      return "no_internet".localizedIn(table:"login_register_v2")
    }
  }

}
