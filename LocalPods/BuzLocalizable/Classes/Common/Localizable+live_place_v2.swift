import Localizable

extension Localizable {
  @objc public static var liveplace_customize_background_sound : String {
    get {
      return "liveplace_customize_background_sound".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_topic_empty_tips : String {
    get {
      return "live_place_topic_empty_tips".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var invite_live_place : String {
    get {
      return "invite_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_not_support : String {
    get {
      return "live_place_not_support".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var ambient_sound : String {
    get {
      return "ambient_sound".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_share_invite : String {
    get {
      return "live_place_share_invite".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var notifed_selected_friends : String {
    get {
      return "notifed_selected_friends".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var sure_exit_current_call_and_join_new_call : String {
    get {
      return "sure_exit_current_call_and_join_new_call".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_xxx_invites_you : String {
    get {
      return "live_place_xxx_invites_you".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_knock_content : String {
    get {
      return "liveplace_knock_content".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_voice_output_volume : String {
    get {
      return "liveplace_voice_output_volume".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_topic_introduce : String {
    get {
      return "liveplace_topic_introduce".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_and_voiceCall_cannot_simultaneously : String {
    get {
      return "liveplace_and_voiceCall_cannot_simultaneously".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_liveplace : String {
    get {
      return "live_place_liveplace".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var name_the_place_desc : String {
    get {
      return "name_the_place_desc".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var exit_current_call_and_try : String {
    get {
      return "exit_current_call_and_try".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_created : String {
    get {
      return "live_place_created".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var invite_only_owner : String {
    get {
      return "invite_only_owner".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var notifed_all_friends : String {
    get {
      return "notifed_all_friends".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_join_topic : String {
    get {
      return "live_place_join_topic".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_select : String {
    get {
      return "live_place_select".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_sure_answer_new_call : String {
    get {
      return "live_place_sure_answer_new_call".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var ambient_sound_volume_settings : String {
    get {
      return "ambient_sound_volume_settings".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var shared_live_place_successfully : String {
    get {
      return "shared_live_place_successfully".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var no_sound : String {
    get {
      return "no_sound".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var shared_live_place_join_desc : String {
    get {
      return "shared_live_place_join_desc".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_updated_group : String {
    get {
      return "liveplace_updated_group".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var join_live_place : String {
    get {
      return "join_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var create_topic_sub_title : String {
    get {
      return "create_topic_sub_title".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_share_no_friend_tips : String {
    get {
      return "live_place_share_no_friend_tips".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var shared_live_place_end_desc : String {
    get {
      return "shared_live_place_end_desc".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_owner_title : String {
    get {
      return "live_place_owner_title".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var open_live_place : String {
    get {
      return "open_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_how_change_background_sound : String {
    get {
      return "liveplace_how_change_background_sound".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_chat_title : String {
    get {
      return "live_place_chat_title".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var discover_live_place : String {
    get {
      return "discover_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_lets_open : String {
    get {
      return "live_place_lets_open".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var setup_your_live_place_done : String {
    get {
      return "setup_your_live_place_done".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_knocked : String {
    get {
      return "live_place_knocked".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_open_private : String {
    get {
      return "liveplace_open_private".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_not_open_tip : String {
    get {
      return "live_place_not_open_tip".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_add_feature : String {
    get {
      return "live_place_add_feature".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var welcome_to_live_place : String {
    get {
      return "welcome_to_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var you_knocked : String {
    get {
      return "you_knocked".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var customize_live_place : String {
    get {
      return "customize_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_missed_group : String {
    get {
      return "liveplace_missed_group".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var score_highly_satisfied : String {
    get {
      return "score_highly_satisfied".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_invites_group : String {
    get {
      return "liveplace_invites_group".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_opened : String {
    get {
      return "live_place_opened".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_who_can_join : String {
    get {
      return "liveplace_who_can_join".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var your_live_place : String {
    get {
      return "your_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_update_private : String {
    get {
      return "live_place_update_private".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_want_share_feedback : String {
    get {
      return "liveplace_want_share_feedback".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_soundboard_volume : String {
    get {
      return "liveplace_soundboard_volume".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_topic_limit : String {
    get {
      return "live_place_topic_limit".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var end_for_all : String {
    get {
      return "end_for_all".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_lets_chat : String {
    get {
      return "live_place_lets_chat".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_invite_card_share : String {
    get {
      return "live_place_invite_card_share".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_choose_photo_tip : String {
    get {
      return "live_place_choose_photo_tip".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_choose_sound_tip : String {
    get {
      return "live_place_choose_sound_tip".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var notify_when_updated : String {
    get {
      return "notify_when_updated".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_topic_settingup : String {
    get {
      return "liveplace_topic_settingup".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_update_tips : String {
    get {
      return "liveplace_update_tips".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var create_my_live_place : String {
    get {
      return "create_my_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_who_can_access : String {
    get {
      return "liveplace_who_can_access".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_chat_update_to_live_place_tip : String {
    get {
      return "live_chat_update_to_live_place_tip".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_invites_private : String {
    get {
      return "liveplace_invites_private".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var notifed_group_member : String {
    get {
      return "notifed_group_member".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_accept_new_call : String {
    get {
      return "live_place_accept_new_call".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var update_settings : String {
    get {
      return "update_settings".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var go_create_my_live_place : String {
    get {
      return "go_create_my_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_topic_setting : String {
    get {
      return "live_place_topic_setting".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var name_the_place_title : String {
    get {
      return "name_the_place_title".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_closed : String {
    get {
      return "live_place_closed".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_invite_only : String {
    get {
      return "live_place_invite_only".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_ended : String {
    get {
      return "live_place_ended".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_share_sure_to_join : String {
    get {
      return "live_place_share_sure_to_join".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_share_send : String {
    get {
      return "live_place_share_send".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var create_group_live_place_alert_title : String {
    get {
      return "create_group_live_place_alert_title".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_select_friend : String {
    get {
      return "live_place_select_friend".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var friends_can_drop_in : String {
    get {
      return "friends_can_drop_in".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_invitation_expired : String {
    get {
      return "live_place_invitation_expired".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_introduce : String {
    get {
      return "liveplace_introduce".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var would_you_end_live_place : String {
    get {
      return "would_you_end_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var no_one_can_join : String {
    get {
      return "no_one_can_join".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var konck_knock : String {
    get {
      return "konck_knock".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_feedback : String {
    get {
      return "liveplace_feedback".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var group_member_be_notified : String {
    get {
      return "group_member_be_notified".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_share_sent : String {
    get {
      return "live_place_share_sent".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var environment_background : String {
    get {
      return "environment_background".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var konck_konck_push_title : String {
    get {
      return "konck_konck_push_title".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var create_live_place : String {
    get {
      return "create_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var sound_when_liive : String {
    get {
      return "sound_when_liive".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_ambient_sound_volume : String {
    get {
      return "liveplace_ambient_sound_volume".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_updated_private : String {
    get {
      return "liveplace_updated_private".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var select_bg_image : String {
    get {
      return "select_bg_image".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var who_can_join : String {
    get {
      return "who_can_join".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_share_add_friend : String {
    get {
      return "live_place_share_add_friend".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_knock_title : String {
    get {
      return "liveplace_knock_title".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_enter_my : String {
    get {
      return "live_place_enter_my".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var friends_of_count : String {
    get {
      return "friends_of_count".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_ended_group : String {
    get {
      return "liveplace_ended_group".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_knock : String {
    get {
      return "live_place_knock".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_you_enjoy : String {
    get {
      return "liveplace_you_enjoy".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_update_content : String {
    get {
      return "liveplace_update_content".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var setting_place_tip : String {
    get {
      return "setting_place_tip".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var lets_open_live_place : String {
    get {
      return "lets_open_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_lets_go : String {
    get {
      return "live_place_lets_go".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var realy_to_go_live_title : String {
    get {
      return "realy_to_go_live_title".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_want_chat : String {
    get {
      return "live_place_want_chat".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var notify_when_open : String {
    get {
      return "notify_when_open".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_share_join : String {
    get {
      return "live_place_share_join".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_share_card_ended : String {
    get {
      return "live_place_share_card_ended".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var konck_push_content : String {
    get {
      return "konck_push_content".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_trunoff_sound_tips : String {
    get {
      return "liveplace_trunoff_sound_tips".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var upper_limit_char_tip : String {
    get {
      return "upper_limit_char_tip".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_topic : String {
    get {
      return "live_place_topic".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var lets_chat : String {
    get {
      return "lets_chat".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place : String {
    get {
      return "live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var friend_in_group_of_count : String {
    get {
      return "friend_in_group_of_count".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var edit_alias : String {
    get {
      return "edit_alias".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_experience : String {
    get {
      return "liveplace_experience".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_share_to : String {
    get {
      return "live_place_share_to".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var leave_live_place : String {
    get {
      return "leave_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var select_friends_from_group : String {
    get {
      return "select_friends_from_group".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_volume_settting : String {
    get {
      return "liveplace_volume_settting".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var selected_friends : String {
    get {
      return "selected_friends".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var let_know_you_are_here : String {
    get {
      return "let_know_you_are_here".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var group_live_place_create_tip_msg : String {
    get {
      return "group_live_place_create_tip_msg".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var recents_chats : String {
    get {
      return "recents_chats".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_settings : String {
    get {
      return "live_place_settings".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var all_my_friends : String {
    get {
      return "all_my_friends".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_done : String {
    get {
      return "live_place_done".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var end_live_place : String {
    get {
      return "end_live_place".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_opend_group : String {
    get {
      return "liveplace_opend_group".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_what_is : String {
    get {
      return "liveplace_what_is".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_enter : String {
    get {
      return "live_place_enter".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var create_group_live_place_alert_desc : String {
    get {
      return "create_group_live_place_alert_desc".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_volume_settting_introduce : String {
    get {
      return "liveplace_volume_settting_introduce".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_name : String {
    get {
      return "live_place_name".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var liveplace_taptap : String {
    get {
      return "liveplace_taptap".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_invites_you : String {
    get {
      return "live_place_invites_you".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_chat_desc : String {
    get {
      return "live_place_chat_desc".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var who_can_join_and_notic : String {
    get {
      return "who_can_join_and_notic".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var visit : String {
    get {
      return "visit".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_knocking : String {
    get {
      return "live_place_knocking".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var knock_knock : String {
    get {
      return "knock_knock".localizedIn(table:"live_place_v2")
    }
  }

  @objc public static var live_place_update_content : String {
    get {
      return "live_place_update_content".localizedIn(table:"live_place_v2")
    }
  }

}
