import Localizable


extension Localizable {
    @inline(__always)
    private static func localized(_ key: String) -> String {
        key.localizedIn(table:"v1.16.0")
    }

	@objc public static var popup_introduce_live_activity_detail : String {
		get {
			return "popup_introduce_live_activity_detail".localizedIn(table:"v1.16.0")
		}
	}

	@objc public static var live_activity_failed_transcribe : String {
		get {
			return "live_activity_failed_transcribe".localizedIn(table:"v1.16.0")
		}
	}

	@objc public static var seting_live_activity_go : String {
		get {
			return "seting_live_activity_go".localizedIn(table:"v1.16.0")
		}
	}

	@objc public static var live_activity_switch_on : String {
		get {
			return "live_activity_switch_on".localizedIn(table:"v1.16.0")
		}
	}

	@objc public static var live_activity_enable_setting : String {
		get {
			return "live_activity_enable_setting".localizedIn(table:"v1.16.0")
		}
	}

	@objc public static var setting_live_activity_turn_on : String {
		get {
			return "setting_live_activity_turn_on".localizedIn(table:"v1.16.0")
		}
	}

	@objc public static var seting_live_activity_update : String {
		get {
			return "seting_live_activity_update".localizedIn(table:"v1.16.0")
		}
	}

	@objc public static var setting_live_activity : String {
		get {
			return "setting_live_activity".localizedIn(table:"v1.16.0")
		}
	}

	@objc public static var popup_introduce_close : String {
		get {
			return "popup_introduce_close".localizedIn(table:"v1.16.0")
		}
	}

	@objc public static var popup_introduce_upgrade : String {
		get {
			return "popup_introduce_upgrade".localizedIn(table:"v1.16.0")
		}
	}

	@objc public static var seting_live_activity_later : String {
		get {
			return "seting_live_activity_later".localizedIn(table:"v1.16.0")
		}
	}

	@objc public static var authorization_transcription_siri : String {
		get {
			return "authorization_transcription_siri".localizedIn(table:"v1.16.0")
		}
	}

	@objc public static var popup_introduce_live_activity_title : String {
		get {
			return "popup_introduce_live_activity_title".localizedIn(table:"v1.16.0")
		}
	}

	@objc public static var popup_introduce_live_activity_upgrade : String {
		get {
			return "popup_introduce_live_activity_upgrade".localizedIn(table:"v1.16.0")
		}
	}

}

