import Localizable


extension Localizable {

	@objc public static var contacts_friend_requests_sent : String {
		get {
			return "contacts_friend_requests_sent".localizedIn(table:"share_invite")
		}
	}

	@objc public static var contacts_last_step_title : String {
		get {
			return "contacts_last_step_title".localizedIn(table:"share_invite")
		}
	}

	@objc public static var contacts_ok : String {
		get {
			return "contacts_ok".localizedIn(table:"share_invite")
		}
	}

	@objc public static var contacts_sent : String {
		get {
			return "contacts_sent".localizedIn(table:"share_invite")
		}
	}

	@objc public static var contacts_invite_more_friends : String {
		get {
			return "contacts_invite_more_friends".localizedIn(table:"share_invite")
		}
	}

	@objc public static var contacts_invited_you : String {
		get {
			return "contacts_invited_you".localizedIn(table:"share_invite")
		}
	}

}

