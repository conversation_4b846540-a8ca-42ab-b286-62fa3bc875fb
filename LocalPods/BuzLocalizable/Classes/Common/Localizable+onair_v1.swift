import Localizable

extension Localizable {
  @objc public static var air_air_with_friend_tip : String {
    get {
      return "air_air_with_friend_tip".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_have_send_gift : String {
    get {
      return "air_have_send_gift".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_member_in_group : String {
    get {
      return "air_member_in_group".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_are_not_friend : String {
    get {
      return "air_are_not_friend".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_join_air_anytime : String {
    get {
      return "air_join_air_anytime".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_start_air : String {
    get {
      return "air_start_air".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_user_no_response : String {
    get {
      return "air_user_no_response".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_diy_ve : String {
    get {
      return "air_diy_ve".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_invite_join : String {
    get {
      return "air_invite_join".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_somebody_start_something_air : String {
    get {
      return "air_somebody_start_something_air".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_msg_limitReached : String {
    get {
      return "air_msg_limitReached".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_notify_somebody : String {
    get {
      return "air_notify_somebody".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_start_air_ready : String {
    get {
      return "air_start_air_ready".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_busy_tip : String {
    get {
      return "air_busy_tip".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_did_leave : String {
    get {
      return "air_did_leave".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_ve_board : String {
    get {
      return "air_ve_board".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_somebody_leve : String {
    get {
      return "air_somebody_leve".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_live_chat : String {
    get {
      return "air_live_chat".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_join_air : String {
    get {
      return "air_join_air".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_somebody_send_gift : String {
    get {
      return "air_somebody_send_gift".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_pls_update_app : String {
    get {
      return "air_pls_update_app".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_limit_air_tip : String {
    get {
      return "air_limit_air_tip".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var pls_handle_call_invite_first : String {
    get {
      return "pls_handle_call_invite_first".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_user_delete : String {
    get {
      return "air_user_delete".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_msg_timeout : String {
    get {
      return "air_msg_timeout".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_poor_network : String {
    get {
      return "air_poor_network".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_are_not_group_member : String {
    get {
      return "air_are_not_group_member".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_user_band : String {
    get {
      return "air_user_band".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_save_ve_success : String {
    get {
      return "air_save_ve_success".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_discover : String {
    get {
      return "air_discover".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_miss_air_invited : String {
    get {
      return "air_miss_air_invited".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_reject_invite : String {
    get {
      return "air_reject_invite".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_unknow_exception : String {
    get {
      return "air_unknow_exception".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_click_to_send_gift_tip : String {
    get {
      return "air_click_to_send_gift_tip".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_somebody_started_onair : String {
    get {
      return "air_somebody_started_onair".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_conflict_vc_and_air : String {
    get {
      return "air_conflict_vc_and_air".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_have_invited : String {
    get {
      return "air_have_invited".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_select_ve : String {
    get {
      return "air_select_ve".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_have_created : String {
    get {
      return "air_have_created".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_msg_declined : String {
    get {
      return "air_msg_declined".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_have_invited_tip : String {
    get {
      return "air_have_invited_tip".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_pls_add_friends : String {
    get {
      return "air_pls_add_friends".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_air_end : String {
    get {
      return "air_air_end".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_msg_busy : String {
    get {
      return "air_msg_busy".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_somebody_join : String {
    get {
      return "air_somebody_join".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_introduce_tip : String {
    get {
      return "air_introduce_tip".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_call_somebody : String {
    get {
      return "air_call_somebody".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_pls_exit_cur_onair : String {
    get {
      return "air_pls_exit_cur_onair".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_notify_group_air : String {
    get {
      return "air_notify_group_air".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_user_busy : String {
    get {
      return "air_user_busy".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_unknow_exception_exit : String {
    get {
      return "air_unknow_exception_exit".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_limit_tip : String {
    get {
      return "air_limit_tip".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_join_on_air : String {
    get {
      return "air_join_on_air".localizedIn(table:"onair_v1")
    }
  }

  @objc public static var air_already_in_must_not_maximum : String {
    get {
      return "air_already_in_must_not_maximum".localizedIn(table:"onair_v1")
    }
  }

}
