import Localizable

extension Localizable {
  @objc public static var find_vf_you_like_tip : String {
    get {
      return "find_vf_you_like_tip".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var locate_vf_feature : String {
    get {
      return "locate_vf_feature".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var what_is_vf : String {
    get {
      return "what_is_vf".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var vf_feature_desc : String {
    get {
      return "vf_feature_desc".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var replay_the_recording : String {
    get {
      return "replay_the_recording".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var ai_interaction_ended : String {
    get {
      return "ai_interaction_ended".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var voice_flter_expired : String {
    get {
      return "voice_flter_expired".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var preview_is_available : String {
    get {
      return "preview_is_available".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var vf_available_in_lobby_tip : String {
    get {
      return "vf_available_in_lobby_tip".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var vf_on_the_way : String {
    get {
      return "vf_on_the_way".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var voice_filter : String {
    get {
      return "voice_filter".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var release_to_preview_recording : String {
    get {
      return "release_to_preview_recording".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var vf_release : String {
    get {
      return "vf_release".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var send_the_recording : String {
    get {
      return "send_the_recording".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var swipe_to_apply : String {
    get {
      return "swipe_to_apply".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var try_it_now : String {
    get {
      return "try_it_now".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var unable_to_load_filter : String {
    get {
      return "unable_to_load_filter".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var select_filter_tip : String {
    get {
      return "select_filter_tip".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var swipe_vf_to_preview : String {
    get {
      return "swipe_vf_to_preview".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var try_it_out : String {
    get {
      return "try_it_out".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var unable_to_load : String {
    get {
      return "unable_to_load".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var voice_effects_surprise_friends : String {
    get {
      return "voice_effects_surprise_friends".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var ptt_vf_you_selected : String {
    get {
      return "ptt_vf_you_selected".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var vf_send : String {
    get {
      return "vf_send".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var add_fun_vf : String {
    get {
      return "add_fun_vf".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var preview_filter : String {
    get {
      return "preview_filter".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var replay : String {
    get {
      return "replay".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var tap_button_access_vf_tip : String {
    get {
      return "tap_button_access_vf_tip".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var vf_stop : String {
    get {
      return "vf_stop".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var cancel_the_recording : String {
    get {
      return "cancel_the_recording".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var exit : String {
    get {
      return "exit".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var voice_filter_not_support_tip : String {
    get {
      return "voice_filter_not_support_tip".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var voice_filter_introduce_title : String {
    get {
      return "voice_filter_introduce_title".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var no_filter : String {
    get {
      return "no_filter".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var send_voice_with_vf_tip : String {
    get {
      return "send_voice_with_vf_tip".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var no_voice_detected : String {
    get {
      return "no_voice_detected".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var vf_clear : String {
    get {
      return "vf_clear".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var vf_enhanced : String {
    get {
      return "vf_enhanced".localizedIn(table:"voice_filter_v1")
    }
  }

  @objc public static var pls_exit_previewing : String {
    get {
      return "pls_exit_previewing".localizedIn(table:"voice_filter_v1")
    }
  }

}
