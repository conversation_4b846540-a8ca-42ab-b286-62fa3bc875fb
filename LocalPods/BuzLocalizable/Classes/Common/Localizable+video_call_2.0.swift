import Localizable

extension Localizable {
  @objc public static var quality_of_call : String {
    get {
      return "quality_of_call".localizedIn(table:"video_call_2.0")
    }
  }

  @objc public static var call_evaluate_feedback_thanks_toast : String {
    get {
      return "call_evaluate_feedback_thanks_toast".localizedIn(table:"video_call_2.0")
    }
  }

  @objc public static var call_evaluate_not_good : String {
    get {
      return "call_evaluate_not_good".localizedIn(table:"video_call_2.0")
    }
  }

  @objc public static var call_evaluate_good : String {
    get {
      return "call_evaluate_good".localizedIn(table:"video_call_2.0")
    }
  }

  @objc public static var call_evaluate_privacy_policy : String {
    get {
      return "call_evaluate_privacy_policy".localizedIn(table:"video_call_2.0")
    }
  }

  @objc public static var evaluate_page_call_end : String {
    get {
      return "evaluate_page_call_end".localizedIn(table:"video_call_2.0")
    }
  }

  @objc public static var call_evaluate_bottom_tip : String {
    get {
      return "call_evaluate_bottom_tip".localizedIn(table:"video_call_2.0")
    }
  }

}
