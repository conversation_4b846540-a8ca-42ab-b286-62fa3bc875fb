//
//  Localizable+MulLanguage.swift
//  Localizable
//
//  Created by lizhifm on 2023/5/24.
//

import Localizable

extension Localizable {
    public static var AppSettings : String {
        get {
            return "AppSettings".localized
        }
    }
    
    public static var Language : String {
        get {
            return "Language".localized
        }
    }
    
    public static var Save : String {
        get {
            return "Save".localized
        }
    }
    
    public static var XXAgo : String {
        get {
            return "XXAgo".localized
        }
    }
    
    public static var hours : String {
        get {
            return "hours".localized
        }
    }
    
    public static var hour : String {
        get {
            return "hour".localized
        }
    }
    
    public static var days : String {
        get {
            return "days".localized
        }
    }
    
    public static var day : String {
        get {
            return "day".localized
        }
    }
    
    public static var buz_others_658 : String {
        get {
            return "buz_others_658".localized
        }
    }
    
    public static var buz_others_660 : String {
        get {
            return "buz_others_660".localized
        }
    }
    
    public static var buz_others_661 : String {
        get {
            return "buz_others_661".localized
        }
    }
    
    
    public static var buz_others_657_old : String {
        get {
            return "buz_others_657".localized
        }
    }
    
    
    
    public static var Error : String {
        get {
            return "Error".localized
        }
    }
    
    public static var chat_add_friend_on_buz : String {
        get {
            return "chat_add_friend_on_buz".localized
        }
    }
    
    public static var chat_join_group_on_buz : String {
        get {
            return "chat_join_group_on_buz".localized
        }
    }
    
    public static var chat_welcome_to_new_group : String {
        get {
            return "chat_welcome_to_new_group".localized
        }
    }
    
    public static var buz_others_669 : String {
        get {
            return "buz_others_669".localized
        }
    }
    
    @objc
    public static var Buzwillkeepworking : String {
        get {
            return "Buzwillkeepworking".localized
        }
    }
    
    @objc
    public static var Skip : String {
        get {
            return "Skip".localized
        }
    }
    
    @objc
    public static var configShareText : String {
        get {
            return "configShareText".localized
        }
    }
    
    @objc
    public static var configGroupShareText : String {
        get {
            return "configGroupShareText".localized
        }
    }

    @objc
    public static var you : String {
        get {
            return "You".localized
        }
    }
    
    @objc
    public static var areYouSureReportThisGroup : String {
        get {
            return "areYouSureReportThisGroup".localized
        }
    }
    
    @objc
    public static var doYouLikeBuzAi : String {
        get {
            return "doYouLikeBuzAi".localized
        }
    }
    
    @objc
    public static var XWillBeNotified : String {
        get {
            return "XWillBeNotified".localized
        }
    }
    
    @objc
    public static var NotifyX : String {
        get {
            return "NotifyX".localized
        }
    }
    
    @objc
    public static var FindAndTapTheBlueWaveButtonInTheStatusBarX : String {
        get {
            return "FindAndTapTheBlueWaveButtonInTheStatusBarX".localized
        }
    }
    
    @objc
    public static var AddFriendsSuccess : String {
        get {
            return "AddFriendsSuccess".localized
        }
    }
    
    @objc
    public static var CameraUsageDescription : String {
        get {
            return "CameraUsageDescription".localized
        }
    }
    
    @objc
    public static var FailToSent : String {
        get {
            return "FailToSent".localized
        }
    }
    
    @objc
    public static var TheMessageFailedToBeSentDueToNetworkReasons : String {
        get {
            return "TheMessageFailedToBeSentDueToNetworkReasons".localized
        }
    }
    
    @objc
    public static var appImOgPng : String {
        get {
            return "appImOgPng".localized
        }
    }
}
