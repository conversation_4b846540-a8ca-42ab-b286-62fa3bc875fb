import Localizable

extension Localizable {
  @objc public static var mention_at_you : String {
    get {
      return "mention_at_you".localizedIn(table:"group_mention")
    }
  }

  @objc public static var mention_at_you_with_brackets : String {
    get {
      return "mention_at_you_with_brackets".localizedIn(table:"group_mention")
    }
  }

  @objc public static var ai_characters_header : String {
    get {
      return "ai_characters_header".localizedIn(table:"group_mention")
    }
  }

  @objc public static var mentioned_you_prefix_noti : String {
    get {
      return "mentioned_you_prefix_noti".localizedIn(table:"group_mention")
    }
  }

  @objc public static var mention_reach_limit : String {
    get {
      return "mention_reach_limit".localizedIn(table:"group_mention")
    }
  }

  @objc public static var mentioned_you : String {
    get {
      return "mentioned_you".localizedIn(table:"group_mention")
    }
  }

  @objc public static var group_members_header : String {
    get {
      return "group_members_header".localizedIn(table:"group_mention")
    }
  }

  @objc public static var x_mentioned_you : String {
    get {
      return "x_mentioned_you".localizedIn(table:"group_mention")
    }
  }

  @objc public static var mention_one_bot_only : String {
    get {
      return "mention_one_bot_only".localizedIn(table:"group_mention")
    }
  }

}
