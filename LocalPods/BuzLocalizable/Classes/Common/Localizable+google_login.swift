import Localizable

extension Localizable {
  @objc public static var verify_phone_number_tips : String {
    get {
      return "verify_phone_number_tips".localizedIn(table:"google_login")
    }
  }

  @objc public static var authorize_fail_retry : String {
    get {
      return "authorize_fail_retry".localizedIn(table:"google_login")
    }
  }

  @objc public static var verify_phone_number_for_login_tips : String {
    get {
      return "verify_phone_number_for_login_tips".localizedIn(table:"google_login")
    }
  }

  @objc public static var login_methods_tips : String {
    get {
      return "login_methods_tips".localizedIn(table:"google_login")
    }
  }

  @objc public static var new_friend_recommend_tips : String {
    get {
      return "new_friend_recommend_tips".localizedIn(table:"google_login")
    }
  }

  @objc public static var had_buz_account : String {
    get {
      return "had_buz_account".localizedIn(table:"google_login")
    }
  }

  @objc public static var phone_number_tips : String {
    get {
      return "phone_number_tips".localizedIn(table:"google_login")
    }
  }

  @objc public static var google_merge_email_tips : String {
    get {
      return "google_merge_email_tips".localizedIn(table:"google_login")
    }
  }

  @objc public static var sign_up_with_google : String {
    get {
      return "sign_up_with_google".localizedIn(table:"google_login")
    }
  }

  @objc public static var might_know_title : String {
    get {
      return "might_know_title".localizedIn(table:"google_login")
    }
  }

  @objc public static var email_merge_google_tips : String {
    get {
      return "email_merge_google_tips".localizedIn(table:"google_login")
    }
  }

  @objc public static var other_continue_with : String {
    get {
      return "other_continue_with".localizedIn(table:"google_login")
    }
  }

  @objc public static var google_account : String {
    get {
      return "google_account".localizedIn(table:"google_login")
    }
  }

  @objc public static var delete_my_buz_account : String {
    get {
      return "delete_my_buz_account".localizedIn(table:"google_login")
    }
  }

  @objc public static var log_in : String {
    get {
      return "log_in".localizedIn(table:"google_login")
    }
  }

  @objc public static var authorize_exception_retry : String {
    get {
      return "authorize_exception_retry".localizedIn(table:"google_login")
    }
  }

  @objc public static var continue_with_google : String {
    get {
      return "continue_with_google".localizedIn(table:"google_login")
    }
  }

  @objc public static var phone_number_from_google : String {
    get {
      return "phone_number_from_google".localizedIn(table:"google_login")
    }
  }

  @objc public static var welcom_back : String {
    get {
      return "welcom_back".localizedIn(table:"google_login")
    }
  }

  @objc public static var other_login_way : String {
    get {
      return "other_login_way".localizedIn(table:"google_login")
    }
  }

  @objc public static var access_phone_number : String {
    get {
      return "access_phone_number".localizedIn(table:"google_login")
    }
  }

  @objc public static var might_know_tips : String {
    get {
      return "might_know_tips".localizedIn(table:"google_login")
    }
  }

  @objc public static var people_you_might_known_introduce : String {
    get {
      return "people_you_might_known_introduce".localizedIn(table:"google_login")
    }
  }

}
