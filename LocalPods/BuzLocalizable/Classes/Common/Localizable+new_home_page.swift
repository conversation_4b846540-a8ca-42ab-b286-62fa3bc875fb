import Localizable

extension Localizable {
  @objc public static var home_pop_message_playing : String {
    get {
      return "home_pop_message_playing".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_preview_chat : String {
    get {
      return "home_preview_chat".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_more_panel_camera : String {
    get {
      return "home_more_panel_camera".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var foreground_quit_buz_completely : String {
    get {
      return "foreground_quit_buz_completely".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var foreground_stay : String {
    get {
      return "foreground_stay".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_search_recent_chats : String {
    get {
      return "home_search_recent_chats".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_more_panel_location : String {
    get {
      return "home_more_panel_location".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_preview_unread_noti : String {
    get {
      return "home_preview_unread_noti".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_preview_xxx_unread : String {
    get {
      return "home_preview_xxx_unread".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_recording : String {
    get {
      return "home_recording".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_preview_unsupported_message : String {
    get {
      return "home_preview_unsupported_message".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var profile_item_mute_notification : String {
    get {
      return "profile_item_mute_notification".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_preview_invite_to_create_group : String {
    get {
      return "home_preview_invite_to_create_group".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var foreground_auto_play_on : String {
    get {
      return "foreground_auto_play_on".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var foreground_auto_play_off : String {
    get {
      return "foreground_auto_play_off".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_pop_xxx_message_playing : String {
    get {
      return "home_pop_xxx_message_playing".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_release_to_cancel_recording : String {
    get {
      return "home_release_to_cancel_recording".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_more_panel_voice_call : String {
    get {
      return "home_more_panel_voice_call".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var profile_item_mute_voice_auto_play : String {
    get {
      return "profile_item_mute_voice_auto_play".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_preview_transcribing : String {
    get {
      return "home_preview_transcribing".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var foreground_quit : String {
    get {
      return "foreground_quit".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_more_panel_live_chat : String {
    get {
      return "home_more_panel_live_chat".localizedIn(table:"new_home_page")
    }
  }

  @objc public static var home_release_to_send_recording : String {
    get {
      return "home_release_to_send_recording".localizedIn(table:"new_home_page")
    }
  }

}
