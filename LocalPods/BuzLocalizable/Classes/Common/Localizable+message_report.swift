import Localizable

extension Localizable {
  @objc public static var account_suspension : String {
    get {
      return "account_suspension".localizedIn(table:"message_report")
    }
  }

  @objc public static var account_suspension_desc : String {
    get {
      return "account_suspension_desc".localizedIn(table:"message_report")
    }
  }

  @objc public static var account_ban_desc : String {
    get {
      return "account_ban_desc".localizedIn(table:"message_report")
    }
  }

  @objc public static var account_ban : String {
    get {
      return "account_ban".localizedIn(table:"message_report")
    }
  }

  @objc public static var account_suspension_toast : String {
    get {
      return "account_suspension_toast".localizedIn(table:"message_report")
    }
  }

  @objc public static var report_sent : String {
    get {
      return "report_sent".localizedIn(table:"message_report")
    }
  }

  @objc public static var operation_failed : String {
    get {
      return "operation_failed".localizedIn(table:"message_report")
    }
  }

}
