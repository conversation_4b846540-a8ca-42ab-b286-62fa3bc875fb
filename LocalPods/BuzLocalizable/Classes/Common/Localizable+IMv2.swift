import Localizable

extension Localizable {
  @objc public static var delete_for_me : String {
    get {
      return "delete_for_me".localizedIn(table:"IMv2")
    }
  }

  @objc public static var map_location_permission_need : String {
    get {
      return "map_location_permission_need".localizedIn(table:"IMv2")
    }
  }

  @objc public static var share_no_friends_yet : String {
    get {
      return "share_no_friends_yet".localizedIn(table:"IMv2")
    }
  }

  @objc public static var map_locating_failed : String {
    get {
      return "map_locating_failed".localizedIn(table:"IMv2")
    }
  }

  @objc public static var map_no_result : String {
    get {
      return "map_no_result".localizedIn(table:"IMv2")
    }
  }

  @objc public static var location_sent : String {
    get {
      return "location_sent".localizedIn(table:"IMv2")
    }
  }

  @objc public static var delete_for_everyone : String {
    get {
      return "delete_for_everyone".localizedIn(table:"IMv2")
    }
  }

  @objc public static var what_is_deleted_for_me : String {
    get {
      return "what_is_deleted_for_me".localizedIn(table:"IMv2")
    }
  }

  @objc public static var map_access_your_location : String {
    get {
      return "map_access_your_location".localizedIn(table:"IMv2")
    }
  }

  @objc public static var install_google_maps : String {
    get {
      return "install_google_maps".localizedIn(table:"IMv2")
    }
  }

  @objc public static var choose_a_place_nearby : String {
    get {
      return "choose_a_place_nearby".localizedIn(table:"IMv2")
    }
  }

  @objc public static var send_location : String {
    get {
      return "send_location".localizedIn(table:"IMv2")
    }
  }

  @objc public static var message_deleting : String {
    get {
      return "message_deleting".localizedIn(table:"IMv2")
    }
  }

  @objc public static var map_lat : String {
    get {
      return "map_lat".localizedIn(table:"IMv2")
    }
  }

  @objc public static var forward_sent_fail : String {
    get {
      return "forward_sent_fail".localizedIn(table:"IMv2")
    }
  }

  @objc public static var message_recalled : String {
    get {
      return "message_recalled".localizedIn(table:"IMv2")
    }
  }

  @objc public static var what_is_deleted_for_everyone : String {
    get {
      return "what_is_deleted_for_everyone".localizedIn(table:"IMv2")
    }
  }

  @objc public static var share_login_first : String {
    get {
      return "share_login_first".localizedIn(table:"IMv2")
    }
  }

  @objc public static var forward_sent_successfully : String {
    get {
      return "forward_sent_successfully".localizedIn(table:"IMv2")
    }
  }

  @objc public static var share_open_buz : String {
    get {
      return "share_open_buz".localizedIn(table:"IMv2")
    }
  }

  @objc public static var deleted_a_message_xxx : String {
    get {
      return "deleted_a_message_xxx".localizedIn(table:"IMv2")
    }
  }

  @objc public static var message_deleted : String {
    get {
      return "message_deleted".localizedIn(table:"IMv2")
    }
  }

  @objc public static var location_update_version_to_view : String {
    get {
      return "location_update_version_to_view".localizedIn(table:"IMv2")
    }
  }

  @objc public static var what_is_deleted_for_everyone_detail : String {
    get {
      return "what_is_deleted_for_everyone_detail".localizedIn(table:"IMv2")
    }
  }

  @objc public static var forward : String {
    get {
      return "forward".localizedIn(table:"IMv2")
    }
  }

  @objc public static var map_lng : String {
    get {
      return "map_lng".localizedIn(table:"IMv2")
    }
  }

  @objc public static var share_failed_a_file_xxx : String {
    get {
      return "share_failed_a_file_xxx".localizedIn(table:"IMv2")
    }
  }

  @objc public static var open_in_maps : String {
    get {
      return "open_in_maps".localizedIn(table:"IMv2")
    }
  }

  @objc public static var share_your_location_to_friends : String {
    get {
      return "share_your_location_to_friends".localizedIn(table:"IMv2")
    }
  }

  @objc public static var share_successfully : String {
    get {
      return "share_successfully".localizedIn(table:"IMv2")
    }
  }

  @objc public static var recalled_a_message : String {
    get {
      return "recalled_a_message".localizedIn(table:"IMv2")
    }
  }

  @objc public static var no_places_nearby : String {
    get {
      return "no_places_nearby".localizedIn(table:"IMv2")
    }
  }

  @objc public static var what_is_deleted_for_me_detail : String {
    get {
      return "what_is_deleted_for_me_detail".localizedIn(table:"IMv2")
    }
  }

  @objc public static var search_nearby_location : String {
    get {
      return "search_nearby_location".localizedIn(table:"IMv2")
    }
  }

  @objc public static var map_locating : String {
    get {
      return "map_locating".localizedIn(table:"IMv2")
    }
  }

  @objc public static var check_nearby_places : String {
    get {
      return "check_nearby_places".localizedIn(table:"IMv2")
    }
  }

  @objc public static var forward_sendto : String {
    get {
      return "forward_sendto".localizedIn(table:"IMv2")
    }
  }

  @objc public static var message_deleted_failed : String {
    get {
      return "message_deleted_failed".localizedIn(table:"IMv2")
    }
  }

  @objc public static var location : String {
    get {
      return "location".localizedIn(table:"IMv2")
    }
  }

  @objc public static var message_expired : String {
    get {
      return "message_expired".localizedIn(table:"IMv2")
    }
  }

  @objc public static var share_failed : String {
    get {
      return "share_failed".localizedIn(table:"IMv2")
    }
  }

  @objc public static var send_current_location : String {
    get {
      return "send_current_location".localizedIn(table:"IMv2")
    }
  }

}
