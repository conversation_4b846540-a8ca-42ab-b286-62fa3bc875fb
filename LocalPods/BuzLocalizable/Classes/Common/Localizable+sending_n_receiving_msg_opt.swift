import Localizable


extension Localizable {

	@objc public static var release_to_send : String {
		get {
			return "release_to_send".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var mic_already_in_use_tip : String {
		get {
			return "mic_already_in_use_tip".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var msg_under_quiet_mode : String {
		get {
			return "msg_under_quiet_mode".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var setting_sound_or_vibrate_tip_such_as : String {
		get {
			return "setting_sound_or_vibrate_tip_such_as".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var recording_interrupted : String {
		get {
			return "recording_interrupted".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var message_auto_laying_try_again_later : String {
		get {
			return "message_auto_laying_try_again_later".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var setting_sound_or_vibrate_tip : String {
		get {
			return "setting_sound_or_vibrate_tip".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var vibration : String {
		get {
			return "vibration".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var auto_played_voices_msgs : String {
		get {
			return "auto_played_voices_msgs".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var sounds : String {
		get {
			return "sounds".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var message_canceled : String {
		get {
			return "message_canceled".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var run_in_background_desc : String {
		get {
			return "run_in_background_desc".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var auto_played_msgs : String {
		get {
			return "auto_played_msgs".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var release_to_cancel : String {
		get {
			return "release_to_cancel".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var other_notifications : String {
		get {
			return "other_notifications".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var run_in_background : String {
		get {
			return "run_in_background".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

	@objc public static var notifications : String {
		get {
			return "notifications".localizedIn(table:"sending_n_receiving_msg_opt")
		}
	}

}

