import Localizable

extension Localizable {
  @objc public static var notification_current_download_stopped_by_net_change : String {
    get {
      return "notification_current_download_stopped_by_net_change".localizedIn(table:"send_file")
    }
  }

  @objc public static var more_panel_file_option : String {
    get {
      return "more_panel_file_option".localizedIn(table:"send_file")
    }
  }

  @objc public static var notification_current_download_stopped_by_net_error : String {
    get {
      return "notification_current_download_stopped_by_net_error".localizedIn(table:"send_file")
    }
  }

  @objc public static var notification_channel_send_file : String {
    get {
      return "notification_channel_send_file".localizedIn(table:"send_file")
    }
  }

  @objc public static var notification_sending_single_file : String {
    get {
      return "notification_sending_single_file".localizedIn(table:"send_file")
    }
  }

  @objc public static var file_not_found : String {
    get {
      return "file_not_found".localizedIn(table:"send_file")
    }
  }

  @objc public static var cancel_sending_message : String {
    get {
      return "cancel_sending_message".localizedIn(table:"send_file")
    }
  }

  @objc public static var not_supported_to_open_file : String {
    get {
      return "not_supported_to_open_file".localizedIn(table:"send_file")
    }
  }

  @objc public static var file_exceed_size_limit : String {
    get {
      return "file_exceed_size_limit".localizedIn(table:"send_file")
    }
  }

  @objc public static var download_anyway : String {
    get {
      return "download_anyway".localizedIn(table:"send_file")
    }
  }

  @objc public static var file_cannot_be_read : String {
    get {
      return "file_cannot_be_read".localizedIn(table:"send_file")
    }
  }

  @objc public static var send_file_insufficient_storage : String {
    get {
      return "send_file_insufficient_storage".localizedIn(table:"send_file")
    }
  }

  @objc public static var confirm_download_dialog_title : String {
    get {
      return "confirm_download_dialog_title".localizedIn(table:"send_file")
    }
  }

  @objc public static var notification_channel_pending_send_file : String {
    get {
      return "notification_channel_pending_send_file".localizedIn(table:"send_file")
    }
  }

  @objc public static var send_files_with_data_dialog_content : String {
    get {
      return "send_files_with_data_dialog_content".localizedIn(table:"send_file")
    }
  }

  @objc public static var send_files_with_wifi_dialog_content : String {
    get {
      return "send_files_with_wifi_dialog_content".localizedIn(table:"send_file")
    }
  }

  @objc public static var notification_sending_file : String {
    get {
      return "notification_sending_file".localizedIn(table:"send_file")
    }
  }

  @objc public static var notification_pause_count : String {
    get {
      return "notification_pause_count".localizedIn(table:"send_file")
    }
  }

  @objc public static var chat_pop_msg_file_tag : String {
    get {
      return "chat_pop_msg_file_tag".localizedIn(table:"send_file")
    }
  }

  @objc public static var notification_channel_download_file : String {
    get {
      return "notification_channel_download_file".localizedIn(table:"send_file")
    }
  }

  @objc public static var file_exceed_count_limit : String {
    get {
      return "file_exceed_count_limit".localizedIn(table:"send_file")
    }
  }

  @objc public static var file_deselected_toast : String {
    get {
      return "file_deselected_toast".localizedIn(table:"send_file")
    }
  }

}
