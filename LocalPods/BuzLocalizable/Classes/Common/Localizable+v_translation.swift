import Localizable

extension Localizable {
  @objc public static var trans_hide_translation : String {
    get {
      return "trans_hide_translation".localizedIn(table:"v_translation")
    }
  }

  @objc public static var trans_target_translation_lan : String {
    get {
      return "trans_target_translation_lan".localizedIn(table:"v_translation")
    }
  }

  @objc public static var trans_msg_translation : String {
    get {
      return "trans_msg_translation".localizedIn(table:"v_translation")
    }
  }

  @objc public static var trans_translation_desc : String {
    get {
      return "trans_translation_desc".localizedIn(table:"v_translation")
    }
  }

  @objc public static var trans_unable_translation : String {
    get {
      return "trans_unable_translation".localizedIn(table:"v_translation")
    }
  }

  @objc public static var trans_auto_translation : String {
    get {
      return "trans_auto_translation".localizedIn(table:"v_translation")
    }
  }

  @objc public static var trans_translating : String {
    get {
      return "trans_translating".localizedIn(table:"v_translation")
    }
  }

  @objc public static var trans_change_translation_lan : String {
    get {
      return "trans_change_translation_lan".localizedIn(table:"v_translation")
    }
  }

  @objc public static var trans_translate : String {
    get {
      return "trans_translate".localizedIn(table:"v_translation")
    }
  }

}
