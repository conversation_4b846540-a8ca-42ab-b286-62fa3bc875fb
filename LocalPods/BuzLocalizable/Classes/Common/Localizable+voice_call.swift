import Localizable

extension Localizable {
  @objc public static var group_ongoing_join_now_tip : String {
    get {
      return "group_ongoing_join_now_tip".localizedIn(table:"voice_call")
    }
  }

  @objc public static var accept_new_call : String {
    get {
      return "accept_new_call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_muted : String {
    get {
      return "call_muted".localizedIn(table:"voice_call")
    }
  }

  @objc public static var weak_connection : String {
    get {
      return "weak_connection".localizedIn(table:"voice_call")
    }
  }

  @objc public static var enjoy_voice_call : String {
    get {
      return "enjoy_voice_call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var someone_started_voice_call : String {
    get {
      return "someone_started_voice_call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var incoming_call_try_again : String {
    get {
      return "incoming_call_try_again".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_connecting : String {
    get {
      return "call_connecting".localizedIn(table:"voice_call")
    }
  }

  @objc public static var allow_microphone_access : String {
    get {
      return "allow_microphone_access".localizedIn(table:"voice_call")
    }
  }

  @objc public static var not_now : String {
    get {
      return "not_now".localizedIn(table:"voice_call")
    }
  }

  @objc public static var photos : String {
    get {
      return "photos".localizedIn(table:"voice_call")
    }
  }

  @objc public static var line_busy : String {
    get {
      return "line_busy".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_end_removed_from_group : String {
    get {
      return "call_end_removed_from_group".localizedIn(table:"voice_call")
    }
  }

  @objc public static var send_you_call_invitation : String {
    get {
      return "send_you_call_invitation".localizedIn(table:"voice_call")
    }
  }

  @objc public static var Unknown : String {
    get {
      return "Unknown".localizedIn(table:"voice_call")
    }
  }

  @objc public static var failed_to_join_voice_call : String {
    get {
      return "failed_to_join_voice_call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_hangup : String {
    get {
      return "call_hangup".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_waiting : String {
    get {
      return "call_waiting".localizedIn(table:"voice_call")
    }
  }

  @objc public static var join_voice_call : String {
    get {
      return "join_voice_call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var incoming_call : String {
    get {
      return "incoming_call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_busy : String {
    get {
      return "call_busy".localizedIn(table:"voice_call")
    }
  }

  @objc public static var incoming_group_call : String {
    get {
      return "incoming_group_call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var on_going_voice_call : String {
    get {
      return "on_going_voice_call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_supports_max_tip : String {
    get {
      return "call_supports_max_tip".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_again : String {
    get {
      return "call_again".localizedIn(table:"voice_call")
    }
  }

  @objc public static var earphone : String {
    get {
      return "earphone".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_microphone_access_tip : String {
    get {
      return "call_microphone_access_tip".localizedIn(table:"voice_call")
    }
  }

  @objc public static var name_connect : String {
    get {
      return "name_connect".localizedIn(table:"voice_call")
    }
  }

  @objc public static var not_firend_some : String {
    get {
      return "not_firend_some".localizedIn(table:"voice_call")
    }
  }

  @objc public static var speaker : String {
    get {
      return "speaker".localizedIn(table:"voice_call")
    }
  }

  @objc public static var name_name : String {
    get {
      return "name_name".localizedIn(table:"voice_call")
    }
  }

  @objc public static var no_answer : String {
    get {
      return "no_answer".localizedIn(table:"voice_call")
    }
  }

  @objc public static var bluetooth_access_tip : String {
    get {
      return "bluetooth_access_tip".localizedIn(table:"voice_call")
    }
  }

  @objc public static var calling_you : String {
    get {
      return "calling_you".localizedIn(table:"voice_call")
    }
  }

  @objc public static var you_missed_voice_call : String {
    get {
      return "you_missed_voice_call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var someone_invites_you_call : String {
    get {
      return "someone_invites_you_call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var Incoming_call_from : String {
    get {
      return "Incoming_call_from".localizedIn(table:"voice_call")
    }
  }

  @objc public static var voice_call_earpiece : String {
    get {
      return "voice_call_earpiece".localizedIn(table:"voice_call")
    }
  }

  @objc public static var Bluetooth : String {
    get {
      return "Bluetooth".localizedIn(table:"voice_call")
    }
  }

  @objc public static var name_waiting : String {
    get {
      return "name_waiting".localizedIn(table:"voice_call")
    }
  }

  @objc public static var voice_call_unable_maximize : String {
    get {
      return "voice_call_unable_maximize".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_ended : String {
    get {
      return "call_ended".localizedIn(table:"voice_call")
    }
  }

  @objc public static var network_error_try_again : String {
    get {
      return "network_error_try_again".localizedIn(table:"voice_call")
    }
  }

  @objc public static var select_members : String {
    get {
      return "select_members".localizedIn(table:"voice_call")
    }
  }

  @objc public static var output_device_switch_failed : String {
    get {
      return "output_device_switch_failed".localizedIn(table:"voice_call")
    }
  }

  @objc public static var text_messages : String {
    get {
      return "text_messages".localizedIn(table:"voice_call")
    }
  }

  @objc public static var unable_join_voice_call_reach_max : String {
    get {
      return "unable_join_voice_call_reach_max".localizedIn(table:"voice_call")
    }
  }

  @objc public static var Phone : String {
    get {
      return "Phone".localizedIn(table:"voice_call")
    }
  }

  @objc public static var join_call : String {
    get {
      return "join_call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var leave_system : String {
    get {
      return "leave_system".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_canceled : String {
    get {
      return "call_canceled".localizedIn(table:"voice_call")
    }
  }

  @objc public static var left_call : String {
    get {
      return "left_call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var x_members : String {
    get {
      return "x_members".localizedIn(table:"voice_call")
    }
  }

  @objc public static var mic_off_tip : String {
    get {
      return "mic_off_tip".localizedIn(table:"voice_call")
    }
  }

  @objc public static var voice_call_update_tip : String {
    get {
      return "voice_call_update_tip".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_answer : String {
    get {
      return "call_answer".localizedIn(table:"voice_call")
    }
  }

  @objc public static var headset : String {
    get {
      return "headset".localizedIn(table:"voice_call")
    }
  }

  @objc public static var voice_call_feature_desc : String {
    get {
      return "voice_call_feature_desc".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_ended_duration : String {
    get {
      return "call_ended_duration".localizedIn(table:"voice_call")
    }
  }

  @objc public static var leave_current_voice_call_tip : String {
    get {
      return "leave_current_voice_call_tip".localizedIn(table:"voice_call")
    }
  }

  @objc public static var max_capacity_voice_call : String {
    get {
      return "max_capacity_voice_call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var unable_join_group_voice_call_reach_max : String {
    get {
      return "unable_join_group_voice_call_reach_max".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call : String {
    get {
      return "call".localizedIn(table:"voice_call")
    }
  }

  @objc public static var voice_call_network_error : String {
    get {
      return "voice_call_network_error".localizedIn(table:"voice_call")
    }
  }

  @objc public static var allow_bluetooth_access : String {
    get {
      return "allow_bluetooth_access".localizedIn(table:"voice_call")
    }
  }

  @objc public static var voicecall_end : String {
    get {
      return "voicecall_end".localizedIn(table:"voice_call")
    }
  }

  @objc public static var answer_new_call_tip : String {
    get {
      return "answer_new_call_tip".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_mute : String {
    get {
      return "call_mute".localizedIn(table:"voice_call")
    }
  }

  @objc public static var call_reject : String {
    get {
      return "call_reject".localizedIn(table:"voice_call")
    }
  }

}
