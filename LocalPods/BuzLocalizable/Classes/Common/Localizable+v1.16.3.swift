import Localizable


extension Localizable {

	@objc public static var ai_center_msg_click : String {
		get {
			return "ai_center_msg_click".localizedIn(table:"v1.16.3")
		}
	}

	@objc public static var ai_voice_setup : String {
		get {
			return "ai_voice_setup".localizedIn(table:"v1.16.3")
		}
	}

	@objc public static var ai_available_robot_list : String {
		get {
			return "ai_available_robot_list".localizedIn(table:"v1.16.3")
		}
	}

	@objc public static var ai_end_chat : String {
		get {
			return "ai_end_chat".localizedIn(table:"v1.16.3")
		}
	}

	@objc public static var ai_description_text : String {
		get {
			return "ai_description_text".localizedIn(table:"v1.16.3")
		}
	}

	@objc public static var ai_language : String {
		get {
			return "ai_language".localizedIn(table:"v1.16.3")
		}
	}

	@objc public static var ai_voice_setting_text : String {
		get {
			return "ai_voice_setting_text".localizedIn(table:"v1.16.3")
		}
	}

	@objc public static var ai_choice_voice_language : String {
		get {
			return "ai_choice_voice_language".localizedIn(table:"v1.16.3")
		}
	}

	@objc public static var ai_chat_text : String {
		get {
			return "ai_chat_text".localizedIn(table:"v1.16.3")
		}
	}

	@objc public static var ai_buz_ai_character : String {
		get {
			return "ai_buz_ai_character".localizedIn(table:"v1.16.3")
		}
	}

	@objc public static var ai_center_msg_click_title : String {
		get {
			return "ai_center_msg_click_title".localizedIn(table:"v1.16.3")
		}
	}

	@objc public static var ai_market_entry : String {
		get {
			return "ai_market_entry".localizedIn(table:"v1.16.3")
		}
	}

	@objc public static var ai_choice_voice_style : String {
		get {
			return "ai_choice_voice_style".localizedIn(table:"v1.16.3")
		}
	}

}

