import Localizable

extension Localizable {
  @objc public static var im_reply_message_been_deleted : String {
    get {
      return "im_reply_message_been_deleted".localizedIn(table:"v_im_infrastructure")
    }
  }

  @objc public static var restartToLoginPage : String {
    get {
      return "restartToLoginPage".localizedIn(table:"v_im_infrastructure")
    }
  }

  @objc public static var im_reply_message_unavailable : String {
    get {
      return "im_reply_message_unavailable".localizedIn(table:"v_im_infrastructure")
    }
  }

  @objc public static var search_buz_id : String {
    get {
      return "search_buz_id".localizedIn(table:"v_im_infrastructure")
    }
  }

  @objc public static var im_reply : String {
    get {
      return "im_reply".localizedIn(table:"v_im_infrastructure")
    }
  }

  @objc public static var im_reply_message_not_found : String {
    get {
      return "im_reply_message_not_found".localizedIn(table:"v_im_infrastructure")
    }
  }

  @objc public static var im_reply_name : String {
    get {
      return "im_reply_name".localizedIn(table:"v_im_infrastructure")
    }
  }

  @objc public static var im_reply_removed_from_group : String {
    get {
      return "im_reply_removed_from_group".localizedIn(table:"v_im_infrastructure")
    }
  }

}
