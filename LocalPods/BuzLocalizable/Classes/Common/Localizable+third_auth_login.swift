import Localizable

extension Localizable {
  @objc public static var loginEmail : String {
    get {
      return "loginEmail".localizedIn(table:"third_auth_login")
    }
  }

  @objc public static var verifyPhoneNumberToMakeItEasierForYourFriendsToFindYou : String {
    get {
      return "verifyPhoneNumberToMakeItEasierForYourFriendsToFindYou".localizedIn(table:"third_auth_login")
    }
  }

  @objc public static var loginOrRegister : String {
    get {
      return "loginOrRegister".localizedIn(table:"third_auth_login")
    }
  }

  @objc public static var or : String {
    get {
      return "or".localizedIn(table:"third_auth_login")
    }
  }

  @objc public static var phoneNumber : String {
    get {
      return "phoneNumber".localizedIn(table:"third_auth_login")
    }
  }

  @objc public static var failedToAuthorizePleaseRetry : String {
    get {
      return "failedToAuthorizePleaseRetry".localizedIn(table:"third_auth_login")
    }
  }

  @objc public static var continueWithXXX : String {
    get {
      return "continueWithXXX".localizedIn(table:"third_auth_login")
    }
  }

}
