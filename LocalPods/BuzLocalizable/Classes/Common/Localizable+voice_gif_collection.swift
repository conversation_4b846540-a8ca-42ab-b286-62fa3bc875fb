import Localizable

extension Localizable {
  @objc public static var ve_panel_no_fav_data : String {
    get {
      return "ve_panel_no_fav_data".localizedIn(table:"voice_gif_collection")
    }
  }

  @objc public static var ve_has_been_removed : String {
    get {
      return "ve_has_been_removed".localizedIn(table:"voice_gif_collection")
    }
  }

  @objc public static var ve_has_been_collected : String {
    get {
      return "ve_has_been_collected".localizedIn(table:"voice_gif_collection")
    }
  }

  @objc public static var ve_fav_reach_max_limit : String {
    get {
      return "ve_fav_reach_max_limit".localizedIn(table:"voice_gif_collection")
    }
  }

  @objc public static var ve_unable_to_remove : String {
    get {
      return "ve_unable_to_remove".localizedIn(table:"voice_gif_collection")
    }
  }

  @objc public static var ve_unable_to_add : String {
    get {
      return "ve_unable_to_add".localizedIn(table:"voice_gif_collection")
    }
  }

}
