import Localizable

extension Localizable {
  @objc public static var auto_play_off : String {
    get {
      return "auto_play_off".localizedIn(table:"asr")
    }
  }

  @objc public static var convenient_transcription_desc : String {
    get {
      return "convenient_transcription_desc".localizedIn(table:"asr")
    }
  }

  @objc public static var enable_notification_tips : String {
    get {
      return "enable_notification_tips".localizedIn(table:"asr")
    }
  }

  @objc public static var unable_transcribe_2 : String {
    get {
      return "unable_transcribe_2".localizedIn(table:"asr")
    }
  }

  @objc public static var msg_setting : String {
    get {
      return "msg_setting".localizedIn(table:"asr")
    }
  }

  @objc public static var auto_play_off_tips_2 : String {
    get {
      return "auto_play_off_tips_2".localizedIn(table:"asr")
    }
  }

  @objc public static var auto_play_msg_alert : String {
    get {
      return "auto_play_msg_alert".localizedIn(table:"asr")
    }
  }

  @objc public static var transcribe : String {
    get {
      return "transcribe".localizedIn(table:"asr")
    }
  }

  @objc public static var msg_transcription : String {
    get {
      return "msg_transcription".localizedIn(table:"asr")
    }
  }

  @objc public static var smart_transcription : String {
    get {
      return "smart_transcription".localizedIn(table:"asr")
    }
  }

  @objc public static var auto_play_on : String {
    get {
      return "auto_play_on".localizedIn(table:"asr")
    }
  }

  @objc public static var smart_transcription_tips : String {
    get {
      return "smart_transcription_tips".localizedIn(table:"asr")
    }
  }

  @objc public static var auto_play_msg_alert_uppercase : String {
    get {
      return "auto_play_msg_alert_uppercase".localizedIn(table:"asr")
    }
  }

  @objc public static var enable_notification : String {
    get {
      return "enable_notification".localizedIn(table:"asr")
    }
  }

  @objc public static var msg_too_short : String {
    get {
      return "msg_too_short".localizedIn(table:"asr")
    }
  }

  @objc public static var net_err_unable_transcribe : String {
    get {
      return "net_err_unable_transcribe".localizedIn(table:"asr")
    }
  }

  @objc public static var convenient_transcription : String {
    get {
      return "convenient_transcription".localizedIn(table:"asr")
    }
  }

  @objc public static var got_it : String {
    get {
      return "got_it".localizedIn(table:"asr")
    }
  }

  @objc public static var auto_play_off_tips : String {
    get {
      return "auto_play_off_tips".localizedIn(table:"asr")
    }
  }

  @objc public static var hide_transcription : String {
    get {
      return "hide_transcription".localizedIn(table:"asr")
    }
  }

  @objc public static var asr_demo_text : String {
    get {
      return "asr_demo_text".localizedIn(table:"asr")
    }
  }

  @objc public static var in_app_notifications : String {
    get {
      return "in_app_notifications".localizedIn(table:"asr")
    }
  }

  @objc public static var auto_play_on_tips : String {
    get {
      return "auto_play_on_tips".localizedIn(table:"asr")
    }
  }

  @objc public static var unable_transcribe : String {
    get {
      return "unable_transcribe".localizedIn(table:"asr")
    }
  }

  @objc public static var transcribe_msg : String {
    get {
      return "transcribe_msg".localizedIn(table:"asr")
    }
  }

}
