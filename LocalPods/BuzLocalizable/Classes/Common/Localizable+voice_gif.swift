import Localizable

extension Localizable {
  @objc public static var recently_used : String {
    get {
      return "recently_used".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var ve_unable_to_add_to_fav : String {
    get {
      return "ve_unable_to_add_to_fav".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var unavailable : String {
    get {
      return "unavailable".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var no_results_found : String {
    get {
      return "no_results_found".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var ve_remove : String {
    get {
      return "ve_remove".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var end_of_search_results : String {
    get {
      return "end_of_search_results".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var ve_add_to_fav : String {
    get {
      return "ve_add_to_fav".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var voice_gif : String {
    get {
      return "voice_gif".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var ve_move_to_top : String {
    get {
      return "ve_move_to_top".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var sound_unavailable : String {
    get {
      return "sound_unavailable".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var content_unavailable : String {
    get {
      return "content_unavailable".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var ve_hold_to_preview : String {
    get {
      return "ve_hold_to_preview".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var ve_remove_from_fav : String {
    get {
      return "ve_remove_from_fav".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var voice_gif_tag : String {
    get {
      return "voice_gif_tag".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var ve_preview_send_to_xxx : String {
    get {
      return "ve_preview_send_to_xxx".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var ve_confirm_to_remove : String {
    get {
      return "ve_confirm_to_remove".localizedIn(table:"voice_gif")
    }
  }

  @objc public static var ve_added_to_fav : String {
    get {
      return "ve_added_to_fav".localizedIn(table:"voice_gif")
    }
  }

}
