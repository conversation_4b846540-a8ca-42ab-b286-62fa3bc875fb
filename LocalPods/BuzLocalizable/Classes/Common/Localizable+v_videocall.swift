import Localizable

extension Localizable {
  @objc public static var rtc_group_voicecall : String {
    get {
      return "rtc_group_voicecall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_x_start_private_videocall : String {
    get {
      return "rtc_x_start_private_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_calling_people : String {
    get {
      return "rtc_calling_people".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_update_and_unlock_videocall : String {
    get {
      return "rtc_update_and_unlock_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_handle_receiving_call_first : String {
    get {
      return "rtc_handle_receiving_call_first".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_x_already_open_camera : String {
    get {
      return "rtc_x_already_open_camera".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_who_have_not_join : String {
    get {
      return "rtc_who_have_not_join".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_you_miss_a_voicecall_invitation : String {
    get {
      return "rtc_you_miss_a_voicecall_invitation".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_incoming_group_voicecall : String {
    get {
      return "rtc_incoming_group_voicecall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_stop_liveplace_to_new_call : String {
    get {
      return "rtc_stop_liveplace_to_new_call".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_start_videocall_for_currentvoicecall_end : String {
    get {
      return "rtc_start_videocall_for_currentvoicecall_end".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_ongoing_voicecall : String {
    get {
      return "rtc_ongoing_voicecall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_you_miss_a_videocall_invitation : String {
    get {
      return "rtc_you_miss_a_videocall_invitation".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_call_again : String {
    get {
      return "rtc_call_again".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_x_no_answer : String {
    get {
      return "rtc_x_no_answer".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_incoming_group_videocall : String {
    get {
      return "rtc_incoming_group_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_x_busy : String {
    get {
      return "rtc_x_busy".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_group_videocall : String {
    get {
      return "rtc_group_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_add_friend_first : String {
    get {
      return "rtc_add_friend_first".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_try_again_for_ongoing_call : String {
    get {
      return "rtc_try_again_for_ongoing_call".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_retry_for_camera_is_taken : String {
    get {
      return "rtc_retry_for_camera_is_taken".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_onlyone_of_liveplace_and_call_canbeuse : String {
    get {
      return "rtc_onlyone_of_liveplace_and_call_canbeuse".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_camera_permission_access_tip : String {
    get {
      return "rtc_camera_permission_access_tip".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_ongoing_videocall : String {
    get {
      return "rtc_ongoing_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_start_videocall : String {
    get {
      return "rtc_start_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_callback : String {
    get {
      return "rtc_callback".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_your_version_only_support_voicecall : String {
    get {
      return "rtc_your_version_only_support_voicecall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_try_again_for_ongoing_liveplace : String {
    get {
      return "rtc_try_again_for_ongoing_liveplace".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_missed_videocall : String {
    get {
      return "rtc_missed_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_connecting : String {
    get {
      return "rtc_connecting".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_support_videocall_description : String {
    get {
      return "rtc_support_videocall_description".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_retry_for_using_call_now : String {
    get {
      return "rtc_retry_for_using_call_now".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_retry_after_leave_call : String {
    get {
      return "rtc_retry_after_leave_call".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_stop_groupvideocall_to_new_call : String {
    get {
      return "rtc_stop_groupvideocall_to_new_call".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_update_and_start_videocall : String {
    get {
      return "rtc_update_and_start_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_join : String {
    get {
      return "rtc_join".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_calling : String {
    get {
      return "rtc_calling".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_start_new_for_group_voicecall_end : String {
    get {
      return "rtc_start_new_for_group_voicecall_end".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_you_miss_a_voicecall : String {
    get {
      return "rtc_you_miss_a_voicecall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_videocall_not_support_area : String {
    get {
      return "rtc_videocall_not_support_area".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_x_invite_you_to_join_voicecall : String {
    get {
      return "rtc_x_invite_you_to_join_voicecall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_whether_to_open_voicecall : String {
    get {
      return "rtc_whether_to_open_voicecall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_ask_video_permission_description : String {
    get {
      return "rtc_ask_video_permission_description".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_usage_description_in__videocall : String {
    get {
      return "rtc_usage_description_in__videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_stop_currentcall_to_new_call : String {
    get {
      return "rtc_stop_currentcall_to_new_call".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_videocall_end : String {
    get {
      return "rtc_videocall_end".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_camera_turn_off_in_group_call : String {
    get {
      return "rtc_camera_turn_off_in_group_call".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_missed_voicecall : String {
    get {
      return "rtc_missed_voicecall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_incoming_voicecall : String {
    get {
      return "rtc_incoming_voicecall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_no_answer : String {
    get {
      return "rtc_no_answer".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_retry_for_using_liveplace_now : String {
    get {
      return "rtc_retry_for_using_liveplace_now".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_incoming_videocall : String {
    get {
      return "rtc_incoming_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_retry : String {
    get {
      return "rtc_retry".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_stop_groupvoicecall_to_new_call : String {
    get {
      return "rtc_stop_groupvoicecall_to_new_call".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_in_this_call : String {
    get {
      return "rtc_in_this_call".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_call_end : String {
    get {
      return "rtc_call_end".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_start_voicecall : String {
    get {
      return "rtc_start_voicecall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_gotit : String {
    get {
      return "rtc_gotit".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_whether_to_open_videocall : String {
    get {
      return "rtc_whether_to_open_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_voicecall : String {
    get {
      return "rtc_voicecall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_version_too_low_to_support_videocall : String {
    get {
      return "rtc_version_too_low_to_support_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_ask_mic_permission_description : String {
    get {
      return "rtc_ask_mic_permission_description".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_update_and_unlock_videocall_rightnow : String {
    get {
      return "rtc_update_and_unlock_videocall_rightnow".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_x_start_group_videocall : String {
    get {
      return "rtc_x_start_group_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_x_weak_network : String {
    get {
      return "rtc_x_weak_network".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_calling_x : String {
    get {
      return "rtc_calling_x".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_voicecall_maximun_support_12 : String {
    get {
      return "rtc_voicecall_maximun_support_12".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_x_mic_is_off : String {
    get {
      return "rtc_x_mic_is_off".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_busy : String {
    get {
      return "rtc_busy".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_update_now : String {
    get {
      return "rtc_update_now".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_lastest_version_support_videocall : String {
    get {
      return "rtc_lastest_version_support_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_x_invite_you_to_join_videocall : String {
    get {
      return "rtc_x_invite_you_to_join_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_retry_for_mic_is_taken : String {
    get {
      return "rtc_retry_for_mic_is_taken".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_voicecall_end : String {
    get {
      return "rtc_voicecall_end".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_no_people_answer : String {
    get {
      return "rtc_no_people_answer".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_start_videocall_for_currentvideocall_end : String {
    get {
      return "rtc_start_videocall_for_currentvideocall_end".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_videocall_maximun_support_12 : String {
    get {
      return "rtc_videocall_maximun_support_12".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_weak_network : String {
    get {
      return "rtc_weak_network".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_can_not_call_because_not_in_group : String {
    get {
      return "rtc_can_not_call_because_not_in_group".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_videocall : String {
    get {
      return "rtc_videocall".localizedIn(table:"v_videocall")
    }
  }

  @objc public static var rtc_start_call_fail : String {
    get {
      return "rtc_start_call_fail".localizedIn(table:"v_videocall")
    }
  }

}
