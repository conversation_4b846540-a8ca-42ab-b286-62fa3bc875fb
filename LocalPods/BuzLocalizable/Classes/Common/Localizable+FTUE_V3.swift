import Localizable

extension Localizable {
  @objc public static var ftue_v3_qrCodeExpires : String {
    get {
      return "ftue_v3_qrCodeExpires".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_linkCopied : String {
    get {
      return "ftue_v3_linkCopied".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_copylink : String {
    get {
      return "ftue_v3_copylink".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_addMembers : String {
    get {
      return "ftue_v3_addMembers".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_contactsOnBuz : String {
    get {
      return "ftue_v3_contactsOnBuz".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_accessBuzJoin : String {
    get {
      return "ftue_v3_accessBuzJoin".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_addFriends : String {
    get {
      return "ftue_v3_addFriends".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_rename : String {
    get {
      return "ftue_v3_rename".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_joinOnBuz : String {
    get {
      return "ftue_v3_joinOnBuz".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_inviteFromContacts : String {
    get {
      return "ftue_v3_inviteFromContacts".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_groupCreated : String {
    get {
      return "ftue_v3_groupCreated".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_pleaseCheckId : String {
    get {
      return "ftue_v3_pleaseCheckId".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_inviteFriendsOrStartaChat : String {
    get {
      return "ftue_v3_inviteFriendsOrStartaChat".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_newchat : String {
    get {
      return "ftue_v3_newchat".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_searchBuzId : String {
    get {
      return "ftue_v3_searchBuzId".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_inviteMoreToJoinyou : String {
    get {
      return "ftue_v3_inviteMoreToJoinyou".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_okay : String {
    get {
      return "ftue_v3_okay".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_usernotFound : String {
    get {
      return "ftue_v3_usernotFound".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_inviteOrCreate : String {
    get {
      return "ftue_v3_inviteOrCreate".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_close : String {
    get {
      return "ftue_v3_close".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_sGroup : String {
    get {
      return "ftue_v3_sGroup".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_chatwhithyourfriends : String {
    get {
      return "ftue_v3_chatwhithyourfriends".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_yourBuGroupIsReady : String {
    get {
      return "ftue_v3_yourBuGroupIsReady".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_buzIdCopied : String {
    get {
      return "ftue_v3_buzIdCopied".localizedIn(table:"FTUE_V3")
    }
  }

  @objc public static var ftue_v3_searchFriends : String {
    get {
      return "ftue_v3_searchFriends".localizedIn(table:"FTUE_V3")
    }
  }

}
