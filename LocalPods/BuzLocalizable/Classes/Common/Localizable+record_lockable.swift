import Localizable

extension Localizable {
  @objc public static var release_to_record_hands_free : String {
    get {
      return "release_to_record_hands_free".localizedIn(table:"record_lockable")
    }
  }

  @objc public static var play : String {
    get {
      return "play".localizedIn(table:"record_lockable")
    }
  }

  @objc public static var recording_in_progress_check_later : String {
    get {
      return "recording_in_progress_check_later".localizedIn(table:"record_lockable")
    }
  }

  @objc public static var Tap_to_send_recording : String {
    get {
      return "Tap_to_send_recording".localizedIn(table:"record_lockable")
    }
  }

}
