import Localizable

extension Localizable {
  @objc public static var ve_voiceemoji_tip_updated : String {
    get {
      return "ve_voiceemoji_tip_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var collect_blind_box_dialog_desc_new_updated : String {
    get {
      return "collect_blind_box_dialog_desc_new_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var collect_blind_box_dialog_title_updated : String {
    get {
      return "collect_blind_box_dialog_title_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var air_select_ve_updated : String {
    get {
      return "air_select_ve_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var notification_tile_voicemoji_updated : String {
    get {
      return "notification_tile_voicemoji_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var blind_box_unlocked_title_updated : String {
    get {
      return "blind_box_unlocked_title_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var ve_sendave_updated : String {
    get {
      return "ve_sendave_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var collect_blind_box_dialog_desc_updated : String {
    get {
      return "collect_blind_box_dialog_desc_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var ve_voiceemoji_placehold_updated : String {
    get {
      return "ve_voiceemoji_placehold_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var recover_missed_voicemoji_dialog_title_updated : String {
    get {
      return "recover_missed_voicemoji_dialog_title_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var qr_new_feature_dialog_content_updated : String {
    get {
      return "qr_new_feature_dialog_content_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var ve_banPreveiew_updated : String {
    get {
      return "ve_banPreveiew_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var ve_coicemoji_updated : String {
    get {
      return "ve_coicemoji_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var voicemoji_interrupt_toast_updated : String {
    get {
      return "voicemoji_interrupt_toast_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var recover_missed_voicemoji_dialog_desc_updated : String {
    get {
      return "recover_missed_voicemoji_dialog_desc_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var qr_new_feature_dialog_title_updated : String {
    get {
      return "qr_new_feature_dialog_title_updated".localizedIn(table:"ve_locale_new")
    }
  }

  @objc public static var ve_send_updated : String {
    get {
      return "ve_send_updated".localizedIn(table:"ve_locale_new")
    }
  }

}
