import Localizable

extension Localizable {
  @objc public static var turn_off_auto_play_tip_desc : String {
    get {
      return "turn_off_auto_play_tip_desc".localizedIn(table:"hyperlink_oa")
    }
  }

  @objc public static var explore_now : String {
    get {
      return "explore_now".localizedIn(table:"hyperlink_oa")
    }
  }

  @objc public static var add_buz_official : String {
    get {
      return "add_buz_official".localizedIn(table:"hyperlink_oa")
    }
  }

  @objc public static var remove_buz_official : String {
    get {
      return "remove_buz_official".localizedIn(table:"hyperlink_oa")
    }
  }

  @objc public static var turn_off_auto_play_tip_title : String {
    get {
      return "turn_off_auto_play_tip_title".localizedIn(table:"hyperlink_oa")
    }
  }

  @objc public static var official_acc_intro_desc : String {
    get {
      return "official_acc_intro_desc".localizedIn(table:"hyperlink_oa")
    }
  }

  @objc public static var contacts_buz_official_account : String {
    get {
      return "contacts_buz_official_account".localizedIn(table:"hyperlink_oa")
    }
  }

  @objc public static var turn_on_auto_play_tip_desc : String {
    get {
      return "turn_on_auto_play_tip_desc".localizedIn(table:"hyperlink_oa")
    }
  }

  @objc public static var chat_copy_link : String {
    get {
      return "chat_copy_link".localizedIn(table:"hyperlink_oa")
    }
  }

  @objc public static var chat_pop_msg_link_tag : String {
    get {
      return "chat_pop_msg_link_tag".localizedIn(table:"hyperlink_oa")
    }
  }

  @objc public static var band_input_text : String {
    get {
      return "band_input_text".localizedIn(table:"hyperlink_oa")
    }
  }

  @objc public static var official_acc_intro_title : String {
    get {
      return "official_acc_intro_title".localizedIn(table:"hyperlink_oa")
    }
  }

  @objc public static var view_account_content : String {
    get {
      return "view_account_content".localizedIn(table:"hyperlink_oa")
    }
  }

  @objc public static var turn_on_auto_play_tip_title : String {
    get {
      return "turn_on_auto_play_tip_title".localizedIn(table:"hyperlink_oa")
    }
  }

}
