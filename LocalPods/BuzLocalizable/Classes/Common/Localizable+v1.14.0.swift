import Localizable


extension Localizable {

	@objc public static var topic_unlock_food_recipes : String {
		get {
			return "topic_unlock_food_recipes".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_tell_me_a_joke : String {
		get {
			return "topic_tell_me_a_joke".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var robot_introduce_detail : String {
		get {
			return "robot_introduce_detail".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var robot_introduce_security : String {
		get {
			return "robot_introduce_security".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var robot_welcome_introduce : String {
		get {
			return "robot_welcome_introduce".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_discover_secrets : String {
		get {
			return "topic_discover_secrets".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var common_shortcut_introduce : String {
		get {
			return "common_shortcut_introduce".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var common_info_accuracy : String {
		get {
			return "common_info_accuracy".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_unlock_cooking : String {
		get {
			return "topic_unlock_cooking".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var feedback_contact_us : String {
		get {
			return "feedback_contact_us".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_astrology_reading : String {
		get {
			return "topic_astrology_reading".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var robot_topic : String {
		get {
			return "robot_topic".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_party_planner : String {
		get {
			return "topic_party_planner".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_unlock_secrets : String {
		get {
			return "topic_unlock_secrets".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var common_tap_payback : String {
		get {
			return "common_tap_payback".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_random_topic : String {
		get {
			return "topic_random_topic".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var robot_introduce : String {
		get {
			return "robot_introduce".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var robot_alert_answer_accuracy : String {
		get {
			return "robot_alert_answer_accuracy".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_break_ice : String {
		get {
			return "topic_break_ice".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_thoughts_embrace : String {
		get {
			return "topic_thoughts_embrace".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var common_failure_translate : String {
		get {
			return "common_failure_translate".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_learn_express_gratitude : String {
		get {
			return "topic_learn_express_gratitude".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var feedback_tip_contact_us : String {
		get {
			return "feedback_tip_contact_us".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_write_poem : String {
		get {
			return "topic_write_poem".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_heartfelt_gestures : String {
		get {
			return "topic_heartfelt_gestures".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_curious_minds : String {
		get {
			return "topic_curious_minds".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var common_start : String {
		get {
			return "common_start".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_funny_joke : String {
		get {
			return "topic_funny_joke".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var common_privacy_security : String {
		get {
			return "common_privacy_security".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_find_perfect : String {
		get {
			return "topic_find_perfect".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_daily_mindfulness : String {
		get {
			return "topic_daily_mindfulness".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_ideas_help : String {
		get {
			return "topic_ideas_help".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_gaming_ideas : String {
		get {
			return "topic_gaming_ideas".localizedIn(table:"v1.14.0")
		}
	}

	@objc public static var topic_creativity : String {
		get {
			return "topic_creativity".localizedIn(table:"v1.14.0")
		}
	}

}

