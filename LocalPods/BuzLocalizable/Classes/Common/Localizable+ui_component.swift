import Localizable

extension Localizable {
  @objc public static var alert_sound_list_retro : String {
    get {
      return "alert_sound_list_retro".localizedIn(table:"ui_component")
    }
  }

  @objc public static var alert_sound_list_pops_up : String {
    get {
      return "alert_sound_list_pops_up".localizedIn(table:"ui_component")
    }
  }

  @objc public static var communication_made_easy : String {
    get {
      return "communication_made_easy".localizedIn(table:"ui_component")
    }
  }

  @objc public static var alert_sound : String {
    get {
      return "alert_sound".localizedIn(table:"ui_component")
    }
  }

  @objc public static var alert_sound_page_error : String {
    get {
      return "alert_sound_page_error".localizedIn(table:"ui_component")
    }
  }

  @objc public static var alert_sound_page_title : String {
    get {
      return "alert_sound_page_title".localizedIn(table:"ui_component")
    }
  }

  @objc public static var alert_sound_list_bell : String {
    get {
      return "alert_sound_list_bell".localizedIn(table:"ui_component")
    }
  }

}
