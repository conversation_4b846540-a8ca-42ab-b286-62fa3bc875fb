import Localizable

extension Localizable {
  @objc public static var live_place_confirm_title : String {
    get {
      return "live_place_confirm_title".localizedIn(table:"live_plcae_v2.1")
    }
  }

  @objc public static var live_place_be_closed : String {
    get {
      return "live_place_be_closed".localizedIn(table:"live_plcae_v2.1")
    }
  }

  @objc public static var live_place_confirm_sub_title : String {
    get {
      return "live_place_confirm_sub_title".localizedIn(table:"live_plcae_v2.1")
    }
  }

  @objc public static var live_place_group : String {
    get {
      return "live_place_group".localizedIn(table:"live_plcae_v2.1")
    }
  }

  @objc public static var live_place_now_own : String {
    get {
      return "live_place_now_own".localizedIn(table:"live_plcae_v2.1")
    }
  }

  @objc public static var live_place_be_opened : String {
    get {
      return "live_place_be_opened".localizedIn(table:"live_plcae_v2.1")
    }
  }

  @objc public static var live_place_check_it_on : String {
    get {
      return "live_place_check_it_on".localizedIn(table:"live_plcae_v2.1")
    }
  }

  @objc public static var live_place_introduction : String {
    get {
      return "live_place_introduction".localizedIn(table:"live_plcae_v2.1")
    }
  }

  @objc public static var live_place_confirm_begin : String {
    get {
      return "live_place_confirm_begin".localizedIn(table:"live_plcae_v2.1")
    }
  }

}
