import Localizable


extension Localizable {

	@objc public static var web_feedback_tip_1 : String {
		get {
			return "web_feedback_tip_1".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var web_feedback_tip_2 : String {
		get {
			return "web_feedback_tip_2".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var upgrade_system : String {
		get {
			return "upgrade_system".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var contacts_share_qr_code : String {
		get {
			return "contacts_share_qr_code".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var chat_be_removed_tip : String {
		get {
			return "chat_be_removed_tip".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var login_account_verify_tip : String {
		get {
			return "login_account_verify_tip".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var wt_buz_overlay_is_off : String {
		get {
			return "wt_buz_overlay_is_off".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var BuzinBackgroundiscurrentlyON : String {
		get {
			return "BuzinBackgroundiscurrentlyON".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var web_request_log : String {
		get {
			return "web_request_log".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var web_delete_tip : String {
		get {
			return "web_delete_tip".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var common_loading : String {
		get {
			return "common_loading".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var feedback : String {
		get {
			return "feedback".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var BuzinBackgroundiscurrentlyOFF : String {
		get {
			return "BuzinBackgroundiscurrentlyOFF".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var tips_operation_too_frequent : String {
		get {
			return "tips_operation_too_frequent".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var wt_enable_overlay : String {
		get {
			return "wt_enable_overlay".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var system_low : String {
		get {
			return "system_low".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var wt_buz_overlay_is_on : String {
		get {
			return "wt_buz_overlay_is_on".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var feedback_rating_title : String {
		get {
			return "feedback_rating_title".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var wt_overlay_name : String {
		get {
			return "wt_overlay_name".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var tips_network_error : String {
		get {
			return "tips_network_error".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var common_qrcode : String {
		get {
			return "common_qrcode".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var upload_screenshot : String {
		get {
			return "upload_screenshot".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var contacts_no_suggestions : String {
		get {
			return "contacts_no_suggestions".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var chat_message_will_auto_play : String {
		get {
			return "chat_message_will_auto_play".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var chat_when_received_in_available_mode : String {
		get {
			return "chat_when_received_in_available_mode".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var wt_try_out_buz_overlay : String {
		get {
			return "wt_try_out_buz_overlay".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var feedback_service : String {
		get {
			return "feedback_service".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var using_to_chat : String {
		get {
			return "using_to_chat".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var login_account_logged_tip : String {
		get {
			return "login_account_logged_tip".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var ios_old_1 : String {
		get {
			return "ios_old_1".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var friend_request_send : String {
		get {
			return "friend_request_send".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var BuzinBackground : String {
		get {
			return "BuzinBackground".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var login_friends_already_on_buz : String {
		get {
			return "login_friends_already_on_buz".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var wt_buz_overlay_permission_is_not_enable : String {
		get {
			return "wt_buz_overlay_permission_is_not_enable".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var blocklist_empty : String {
		get {
			return "blocklist_empty".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var buz_others_657 : String {
		get {
			return "buz_others_657".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var chat_popup_can_chat_now : String {
		get {
			return "chat_popup_can_chat_now".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var profile_notify_by_link_prefix : String {
		get {
			return "profile_notify_by_link_prefix".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var web_feedback_tip_3 : String {
		get {
			return "web_feedback_tip_3".localizedIn(table:"v1.14.1")
		}
	}

	@objc public static var wt_buz_overlay_description : String {
		get {
			return "wt_buz_overlay_description".localizedIn(table:"v1.14.1")
		}
	}

}

