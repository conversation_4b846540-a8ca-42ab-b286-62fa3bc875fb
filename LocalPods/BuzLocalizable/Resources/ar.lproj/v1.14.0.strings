"topic_unlock_food_recipes" = "الطعام والوصفات";
"topic_tell_me_a_joke" = "قل لي نكتة";
"robot_introduce_detail" = "تعرف على Buz AI - مساعد الذكاء الاصطناعي الجديد الذي يعتمد على الصوت للحصول على إجابات فورية ومحادثات ممتعة في أي وقت وفي أي مكان.
اكتشف طريقة مثيرة للحصول على توصيات للوصفات ، وأفكار للعبة ، وقراءة علم التنجيم أو اسأل عن أي شيء باستخدام Buz AI واستكشف ما يمكنك الدردشة حوله الآن!";
"robot_introduce_security" = "يأخذ Buz خصوصية البيانات على محمل الجد. نحن لا نشارك محادثتك أو أي بيانات شخصية مع أي شخص / أي طرف ثالث. نحن لا نسجل أو نحلل محادثاتك باستثناء استخدام واجهة برمجة تطبيقات ChatGPT. بالإضافة إلى ذلك ، يتم بذل الجهود لإخفاء هويتك وحمايتها
معلومة.";
"robot_welcome_introduce" = "مرحبًا بك في ميزة الدردشة الجديدة مع Buz Al. يمكنك ببساطة إرسال رسالة صوتية ، أو
إرسال رسالة نصية للحصول على رد من Buz Al في غضون ثوان. لأية أسئلة أو تقارير أخطاء أو اقتراحات ، من فضلك لا تتردد في الاتصال بخدمة عملاء فريقنا.
شكرًا لك!";
"topic_discover_secrets" = "اكتشف أسرار التنسيق الناجح للحدث";
"common_shortcut_introduce" = "يمكنك إرسال رسائل من شاشتك الرئيسية ومع تطبيقات أخرى ، مما يجعل تعدد المهام أسهل باستخدام إختصار Buz.";
"common_info_accuracy" = "دقة المعلومات";
"topic_unlock_cooking" = "فتح عالم من أفكار الطهي اللذيذة";
"feedback_contact_us" = "اتصل بنا";
"topic_astrology_reading" = "قراءة التنجيم";
"robot_topic" = "العنوان:";
"topic_party_planner" = "مخطط حفلات";
"topic_unlock_secrets" = "فتح أسرار النجوم";
"common_tap_payback" = "انقر للاستماع";
"topic_random_topic" = "مولد موضوع عشوائي";
"robot_introduce" = "يستفيد Buz AI من نموذج لغة ChatGPT ليقدم لك محادثات طبيعية وإبداعية يمكن أن تنير حياتك اليومية وتساعدك على التواصل مع العائلة والأصدقاء.";
"robot_alert_answer_accuracy" = "ضع في اعتبارك أن الإجابات ليست دائمًا مثالية أو دقيقة أو محدثة. من الجيد دائمًا التحقق من المعلومات بمصادر موثوقة.";
"topic_break_ice" = "كسر الجليد بمواضيع جديدة وغير متوقعة";
"topic_thoughts_embrace" = "امسح أفكارك واحتضن الهدوء كل يوم";
"common_failure_translate" = "فشل في تحويل الصوت إلى نص";
"topic_learn_express_gratitude" = "تعلم كيفية التعبير عن المودة والامتنان لمن يهمهم الأمر";
"feedback_tip_contact_us" = "يرجى إخبارنا إذا كانت لديك أية مخاوف بشأن الأسئلة.";
"topic_write_poem" = "لنكتب قصيدة أو قصة أو نثير بعض الأفكار الإبداعية معًا";
"topic_heartfelt_gestures" = "إيماءات صادقة";
"topic_curious_minds" = "نرحب بالعقول الفضوليّة! اسألني أي شيء تريد معرفته";
"common_start" = "إبدأ";
"topic_funny_joke" = "دغدغة عظام مضحكة ، نكتة واحدة في كل مرة";
"common_privacy_security" = "خصوصية المعلومات وأمنها";
"topic_find_perfect" = "اعثر على اللعبة المثالية لمزاجك";
"topic_daily_mindfulness" = "الجرعة اليومية من اليقظة";
"topic_ideas_help" = "أفكار الموضوع";
"topic_gaming_ideas" = "أفكار الألعاب";
"topic_creativity" = "أطلق بعض الإبداع";
