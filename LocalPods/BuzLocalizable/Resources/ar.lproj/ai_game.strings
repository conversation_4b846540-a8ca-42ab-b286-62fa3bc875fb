"ai_game_tap_to_address_ai_tip" = "انقر على الزر لمخاطبة شخصيات الذكاء الاصطناعي";
"ai_game_long_desc" = "أنا شخصية الذكاء الإصطناعي ريدلر. يمكنني إنشاء ألغاز سهلة وممتعة لك ولأصدقائك! اذهب وانضم إلي في الدردشة الجماعية لنخوض مغامرة ألغاز معًا!";
"ai_game_new_function_read_more" = "تعلم أكثر";
"ai_game_ai_name" = "ريدل بال";
"ai_game_ai_busy_now" = "الذكاء الاصطناعي مشغول الآن ، يرجى المحاولة مرة أخرى";
"ai_game_say_hello" = "مرحبًا، أنا صديقك شخصية الذكاء الإصطناعي ريدل بال! دعنا نحظى بمغامرة مثيرة لإثارة العقل. قل لي \"ابدأ\" لبدء لغز جديد.";
"ai_game_now_you_are_address_an_ai_tip" = "لقد قمت الآن بمخاطبة شخصية الذكاء الاصطناعي";
"ai_game_unavailable_for_reason" = "هذه الشخصية الذكاء الاصطناعي غير متوفرة مؤقتا";
"ai_game_unavailable_for_version" = "النسخة الحالية لا تدعم";
"ai_game_help_desc" = "#كيف ألعب\n\nيمكنك بسهولة أن تقول \"ابدأ\" لـ ريدل بال لبدء اللعبة بسرعة. بمجرد أن يبدأ، يمكنك محاولة حل اللغز من خلال الإجابة على السؤال، ويمكنك أيضًا طرح الأسئلة عندما تتعثر. إذا كان اللغز صعبًا جدًا، يمكنك أن تقول \"أعطني تلميحًا\" للحصول على معلومات مفيدة لحل اللغز.\n\n#مثال\nريدل بال: ما هو الشيء الذي لديه العديد من المفاتيح، لكنه لا يستطيع فتح باب واحد؟\nانا: لوحة المفاتيح\nريدل بال: البنغو!\n\nريدل بال: كلما أخذت أكثر، كلما تركت خلفك. ما أنا؟\nأنا: لا فكرة، أعطني تلميحا.\nريد بال: بالتأكيد شيء! فكر في شيء تقوم بإنشائه في كل مرة تنتقل فيها من مكان إلى آخر، لكنك لا تراها تتراكم أبدًا. امل ان يساعد!";
"ai_game_new_function_got_it" = "فهمت";
"ai_game_push_to_talk_tip" = "انقر للتحدث والدردشة مع شخصية الذكاء الأصطناعي في المجموعة";
"ai_game_new_function_desc" = "انتظر .. خمن ماذا حدث؟ جرب روبوت الذكاء الاصطناعي الجديد - ريدل بال في الدردشات الجماعية!\nطريقة جديدة تمامًا لقضاء وقت ممتع مع عائلتك وأصدقائك!";
"ai_game_unsupport_language" = "اللغة الحالية غير مدعومة";
"ai_game_learn_how_play" = "تعلم كيفية اللعب مع ريدل بال";
"ai_game_new_function_title" = "جرب لعبة شخصية الذكاء الإصطناعي ريدل في الدردشة الجماعية!";
"ai_game_short_desc" = "لنلعب ألعاب الذكاء معًا!";
"ai_game_ai_is_thinking" = "شخصية الذكاء الاصطناعي تفكر...";
