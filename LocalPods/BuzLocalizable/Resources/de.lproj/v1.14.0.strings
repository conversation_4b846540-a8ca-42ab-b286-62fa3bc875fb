"topic_unlock_food_recipes" = "<PERSON>ssen und Rezepte";
"topic_tell_me_a_joke" = "<PERSON><PERSON><PERSON><PERSON> mir einen Witz";
"robot_introduce_detail" = "Lerne Buz AI kennen – deinen neuen sprachbasierten KI-Assistenten für sofortige Antworten und unterhaltsame Gespräche jederzeit und überall. 
Entdecke die aufregende Möglichkeit, Rezeptempfehlungen, Spielideen und Horoskope zu erhalten oder BUZ AI Fragen zu stellen und zu entdecken, worüber du chatten kannst!";
"robot_introduce_security" = "Buz nimmt den Datenschutz ernst. Wir geben deine Unterhaltungen oder deine persönlichen Daten niemals an Dritte
weiter. Wir protokollieren oder analysieren deine Unterhaltungen nicht, es sei denn, wir nutzen die API von ChatGPT. Darüber hinaus sind wir stets bemüht, deine Daten zu anonymisieren und zu
schützen";
"robot_welcome_introduce" = "Will<PERSON>mmen zu unserem neuen Feature „Chatte mit Buz Al“. Sende einfach eine Sprachnachricht oder
eine SMS, und Buz AI wird dir in wenigen Sekunden antworten. Wenn du Fragen oder Vorschläge hast oder einen Fehler melden möchtest, kannst du dich gern an unseren Kundenservice wenden.
Vielen Dank!";
"topic_discover_secrets" = "Entdecke die Geheimnisse einer erfolgreichen Event-Koordination";
"common_shortcut_introduce" = "Mit Buz Shortcut kannst du Nachrichten von deinem Startbildschirm und von anderen Apps senden für ein noch einfacheres Multitasking.";
"common_info_accuracy" = "Genauigkeit von Informationen";
"topic_unlock_cooking" = "Entdecke eine Welt voller leckerer Kochideen";
"feedback_contact_us" = "Schreibe uns";
"topic_astrology_reading" = "Horoskope";
"robot_topic" = "Thema:";
"topic_party_planner" = "Partyplaner";
"topic_unlock_secrets" = "Lüfte die Geheimnisse der Sterne";
"common_tap_payback" = "Tippen zum Anhören";
"topic_random_topic" = "Zufälliger Themengenerator";
"robot_introduce" = "Buz AI nutzt das ChatGPT-Sprachmodell, um natürliche und kreative Unterhaltungen zu ermöglichen, die dein Leben verschönern und dir helfen, mit Familie
und Freunden in Kontakt zu treten.";
"robot_alert_answer_accuracy" = "Bedenke, dass Antworten nicht immer perfekt, genau oder aktuell sein können. Daher empfehlen wir, Informationen stets anhand zuverlässiger Quellen zu überprüfen.";
"topic_break_ice" = "Brich das Eis mit aktuellen und überraschenden Themen";
"topic_thoughts_embrace" = "Ordne deine Gedanken und genieße tägliche Ruhe";
"common_failure_translate" = "Transkription fehlgeschlagen";
"topic_learn_express_gratitude" = "Erfahre, wie du deinen Liebsten Zuneigung und Dankbarkeit zeigen kannst";
"feedback_tip_contact_us" = "Bitte lass uns wissen, wenn du Bedenken oder Fragen hast.";
"topic_write_poem" = "Lass uns ein Gedicht oder eine Geschichte schreiben und kreative Ideen entwickeln";
"topic_heartfelt_gestures" = "Herzliche Gesten";
"topic_curious_minds" = "Willkommen, Wissbegierige(r)! Frag mich alles, was du willst";
"common_start" = "Start";
"topic_funny_joke" = "Training für die Lachmuskeln, Witze am laufenden Band";
"common_privacy_security" = "Datenschutz & Sicherheit";
"topic_find_perfect" = "Finde das perfekte Spiel für deine Stimmung";
"topic_daily_mindfulness" = "Tägliche Dosis Achtsamkeit";
"topic_ideas_help" = "Themen-Ideen";
"topic_gaming_ideas" = "Gaming-Ideen";
"topic_creativity" = "Sei kreativ";
