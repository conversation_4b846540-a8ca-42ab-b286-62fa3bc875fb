#
#  Be sure to run `pod spec lint PCSocialCenter.podspec' to ensure this is a
#  valid spec and to remove all comments including this before submitting the spec.
#
#  To learn more about Podspec attributes see https://guides.cocoapods.org/syntax/podspec.html
#  To see working Podspecs in the CocoaPods repo see https://github.com/CocoaPods/Specs/
#

Pod::Spec.new do |s|
  
  s.name     = "BuzDataShareKit"
  s.version  = "0.0.1"
  s.summary  = "A short description of BuzDataShareKit."
  
  s.homepage = "https://gitlab.lizhi.fm/zhiyaPods/BuzDataShareKit"
  s.license  = { :type => 'Copyright', :file => 'LICENSE' }
  s.author   = { 'st.chio' => '<EMAIL>' }
  s.source   = { :git => "https://gitlab.lizhi.fm/zhiyaPods/BuzDataShareKit.git", :tag => s.version.to_s }
  
  s.platforms = { :ios => "10.0", :watchos => "7.0"}
  
  s.pod_target_xcconfig = {'DEFINES_MODULE' => 'YES'}

  s.source_files  = "Classes", "Classes/**/*.{swift}"
  
  s.subspec 'AppGroup' do |sub|
    sub.subspec 'APNsExtension' do |sp|
      sp.source_files = 'Classes/AppGroup/APNsExtension/**/*.{swift}'
      sp.requires_arc = ['Classes/AppGroup/APNsExtension/**/*.{swift}']
      sp.platforms = { :ios => "10.0"}
      
      sp.dependency 'BuzConfig'
    end
    
    sub.subspec 'GlobalShare' do |sp|
      sp.source_files = 'Classes/AppGroup/GlobalShare/**/*.{swift}'
      sp.requires_arc = ['Classes/AppGroup/GlobalShare/**/*.{swift}']
      sp.platforms = { :ios => "10.0", :watchos => "7.0"}
      
      sp.dependency 'BuzConfig'
    end
  end
  
end
