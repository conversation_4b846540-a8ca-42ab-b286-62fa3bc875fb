//
//  PushShareDataTool.swift
//  BuzNotificationServiceExtension
//
//  Created by liuyufeng on 2022/8/20.
//  Copyright © 2022 lizhi. All rights reserved.
//

import Foundation
import BuzConfig

public enum PushShareDataKey : String {
    case contactMapKey = "PushShareDataContactMapKey"
    case unreadConversations = "UnreadConversations"
    case privateChatUnreadConversations = "privateChatUnreadConversations"
    case groupChatUnreadConversations = "groupChatUnreadConversations"
    case totalUnreadCount = "totalUnreadCount"
    case filterUnreadUserIds = "filterUnreadUserIds"
    case groupsMapKey = "PushShareDataGroupsMapKey"
    case feedbackUserInfoKey = "PushShareFeedbackUserInfoKey"
    case groupInfoPortraitKey = "PushShareDataKey_groupInfoPortraitKey"
    case groupInfoDisplayNameKey = "PushShareDataKey_groupInfoDisplayNameKey"
    case sessionUidKey = "PushShareDataKey_session_uid"
    case lastApplyFriendTs = "FriendRequestCountInfoKey_lastApplyFriendTs"
    case voiceFilter = "PushShareDataKey_voiceFilter"
    case userFullnameMapKey = "PushShareDataUserFullnameMapKey"
}

public enum ConversationType: Codable {
//    case contacts //联系人
    case peer //私聊
    case group //群聊
}

public class PushShareDataTool {
    
    public static let ShareGroupName = BuzConfig.groupStoreId
    
    public static func saveContactsMap(_ map: [Int64 : String])
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        let encoder = JSONEncoder()
        if let encodeData = try? encoder.encode(map) {
            userDefaults.set(encodeData, forKey: PushShareDataKey.contactMapKey.rawValue)
        }
    }
    
    public static func loadContactsMap() -> [Int64 : String]
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return [:]
        }
        if let data = userDefaults.value(forKey: PushShareDataKey.contactMapKey.rawValue) as? Data {
            let decoder = JSONDecoder()

            if let decoded = try? decoder.decode(Dictionary.self, from: data) as [Int64 : String] {
                return decoded
            } else {
                return [:]
            }
        } else {
            return [:]
        }
    }
    
    public static func saveUserFullnameMap(_ map: [Int64 : String])
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        let encoder = JSONEncoder()
        if let encodeData = try? encoder.encode(map) {
            userDefaults.set(encodeData, forKey: PushShareDataKey.userFullnameMapKey.rawValue)
        }
    }
    
    public static func loadUserFullnameMap() -> [Int64 : String]
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return [:]
        }
        if let data = userDefaults.value(forKey: PushShareDataKey.userFullnameMapKey.rawValue) as? Data {
            let decoder = JSONDecoder()

            if let decoded = try? decoder.decode(Dictionary.self, from: data) as [Int64 : String] {
                return decoded
            } else {
                return [:]
            }
        } else {
            return [:]
        }
    }
    
    /***
     {
        "privateChatUnreadConversations" : ["123" , "456"],
        "groupChatUnreadConversations" : ["234" , "567"]
     }
     */
    public static func saveUnreadConversations(_ conversationsDict : [String : [String]]?)
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        userDefaults.set(conversationsDict, forKey: PushShareDataKey.unreadConversations.rawValue)
    }
    
    
    public static func loadUnreadConversatios() -> [String : [String]]
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) ,
              let conversationsDict = userDefaults.object(forKey: PushShareDataKey.unreadConversations.rawValue) as? [String : [String]]
        else {
            return [:]
        }
        return conversationsDict
    }
    
    public static func savetotalUnreadCount(_ count: Int)
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        userDefaults.set(count, forKey: PushShareDataKey.totalUnreadCount.rawValue)
    }
    
    public static func totalUnreadCount() -> Int
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) ,
              let totalUnreadCount = userDefaults.object(forKey: PushShareDataKey.totalUnreadCount.rawValue) as? Int
        else {
            return 0
        }
        return totalUnreadCount
    }
    
    public static func saveLastApplyFriendTs(_ ts: Int64 = 0)
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        userDefaults.set(ts, forKey: PushShareDataKey.lastApplyFriendTs.rawValue)
    }
    
    public static func lastApplyFriendTs() -> Int64
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) ,
              let ts = userDefaults.object(forKey: PushShareDataKey.lastApplyFriendTs.rawValue) as? Int64
        else {
            return 0
        }
        return ts
    }
    
    public static func saveFilterUnreadUserIds(_ ids: [String])
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        userDefaults.set(ids.joined(separator: ","), forKey: PushShareDataKey.filterUnreadUserIds.rawValue)
    }
    
    public static func neverShowBadgeForStr() -> String {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) ,
              let filterUnreadUserIds = userDefaults.object(forKey: PushShareDataKey.filterUnreadUserIds.rawValue) as? String
        else {
            return ""
        }
        
        return filterUnreadUserIds
    }
    
    public static func neverShowBadgeFor(userId : String) -> Bool {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) ,
              let filterUnreadUserIds = userDefaults.object(forKey: PushShareDataKey.filterUnreadUserIds.rawValue) as? String
        else {
            return false
        }
        
        return filterUnreadUserIds.components(separatedBy: ",").contains(where: { uid in
            return uid == userId
        })
    }
    
    public static func saveGroupsMap(_ map: [Int64 : [String : String]])
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        let encoder = JSONEncoder()
        if let encodeData = try? encoder.encode(map) {
            userDefaults.set(encodeData, forKey: PushShareDataKey.groupsMapKey.rawValue)
        }
    }
    
    public static func loadGroupsMap() -> [Int64 : [String : String]]
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return [:]
        }
        if let data = userDefaults.value(forKey: PushShareDataKey.groupsMapKey.rawValue) as? Data {
            let decoder = JSONDecoder()

            if let decoded = try? decoder.decode(Dictionary.self, from: data) as [Int64 : [String : String]] {
                return decoded
            } else {
                return [:]
            }
        } else {
            return [:]
        }
    }
    
    public static func saveFeedbackNotificationUserInfo(_ userInfo: [AnyHashable : Any])
    {
        /*
         {
             aps =     {
                 alert =         {
                     groupId = 0001b903f68f437f4fd7828932d8fb1691be;
                     key = "{\"action\":{\"appData\":\"{\\\"title\\\":\\\"Feedback Response\\\",\\\"pushContent\\\":\\\"Click to view.\\\",\\\"titleReplaceInfo\\\":{},\\\"contentReplaceInfo\\\":{},\\\"type\\\":15,\\\"router\\\":{\\\"scheme\\\":\\\"common/webView\\\",\\\"extraData\\\":{\\\"url\\\":\\\"http://buz.yfxn.lizhi.fm/static/feedback.html\\\"}}}\"}}";
                     "loc-key" = "Click to view.";
                     pushType = 0;
                     title = "Feedback Response";
                     type = 20;
                 };
                 badge = 0;
                 groupId = 0001b903f68f437f4fd7828932d8fb1691be;
                 messageType = 1;
                 "mutable-content" = 1;
                 sound = default;
                 token = a3d58646a20c59b169d4f24bcfc0c5b947c1089dd0250939a0c478a94859770e;
             };
         */
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        userDefaults.set(userInfo, forKey: PushShareDataKey.feedbackUserInfoKey.rawValue)
    }
    
    public static func loadFeedbackNotificationUserInfo() -> [AnyHashable : Any]? {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return nil
        }
        if let userInfo = userDefaults.value(forKey: PushShareDataKey.feedbackUserInfoKey.rawValue) as? [AnyHashable : Any] {
            return userInfo
        } else {
            return nil
        }
    }
    
    public static func removeFeedbackNotificationUserInfo() {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        userDefaults.removeObject(forKey: PushShareDataKey.feedbackUserInfoKey.rawValue)
    }
    
    public static func saveSessionUserId(_ uid : Int64)
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        userDefaults.set(uid, forKey: PushShareDataKey.sessionUidKey.rawValue)
    }
    
    
    public static func loadSessionUserId() -> Int64
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) ,
              let uid = userDefaults.object(forKey: PushShareDataKey.sessionUidKey.rawValue) as? Int64
        else {
            return 0
        }
        return uid
    }
}

/// 声音滤镜

extension PushShareDataTool {
    public static func saveVoiceFilterMap(_ data: [String : String])
    {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        userDefaults.set(data, forKey: PushShareDataKey.voiceFilter.rawValue)
    }
    
    public static func getVoiceFilterName(_ filterId: Int64) -> String? {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return nil
        }
        if let data = userDefaults.value(forKey: PushShareDataKey.voiceFilter.rawValue) as? [String : String] {
            return data["\(filterId)"]
        }
        return nil
    }
}
