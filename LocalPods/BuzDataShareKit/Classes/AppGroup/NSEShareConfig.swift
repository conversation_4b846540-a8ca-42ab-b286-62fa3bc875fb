//
//  NSEShareConfig.swift
//  buz
//
//  Created by lid<PERSON><PERSON> on 2023/10/9.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import BuzConfig

@objcMembers
public class NSEShareConfig: NSObject {
    public static var appGroups: String {
        BuzConfig.groupStoreId
    }

    // TODO: read from ABTEST option
    public static var canIgnoreNotification: Bool {
        false
    }

    public static func saveSharedE2EEAppKey(_ appKey: String) {
        let userDefault = UserDefaults(suiteName: appGroups)
        userDefault?.setValue(appKey, forKey: "E2EESharedAppKey")
    }

    public static var e2eeAppKey: String? {
        let userDefault = UserDefaults(suiteName: appGroups)
        return userDefault?.object(forKey: "E2EESharedAppKey") as? String
    }

    public static var sharedUserId: String? {
        let userDefault = UserDefaults(suiteName: appGroups)
        return userDefault?.object(forKey: "SharedUserId") as? String
    }

    public static func saveSharedUserId(_ userId: String?) {
        let userDefault = UserDefaults(suiteName: appGroups)
        userDefault?.setValue(userId, forKey: "SharedUserId")
    }
    
    public static var sharedDeviceId: String {
        let userDefault = UserDefaults(suiteName: appGroups)
        return userDefault?.object(forKey: "SharedDeviceId") as? String ?? ""
    }
    
    public static func saveSharedDeviceId(_ deviceId: String?) {
        let userDefault = UserDefaults(suiteName: appGroups)
        userDefault?.setValue(deviceId, forKey: "SharedDeviceId")
    }
    public static let flashAppId: Int = 87075309
    public static let flashSubAppId: Int = 0
    public static let channel: String = "AppStore"
}
