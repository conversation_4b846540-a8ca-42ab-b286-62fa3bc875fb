//
//  BuzWatchComlications.swift
//  buz
//
//  Created by lizhi on 2023/8/8.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import SwiftUI

@main
struct  Complications : WidgetBundle {
    var body: some Widget {
        BuzWatchDefaultComplication()
        BuzWatchUserComplication()
        BuzWatchModeComplication()
    }
    
    func updateWeatherData() async {
            // fetches new weather data and updates app state
    }
}
