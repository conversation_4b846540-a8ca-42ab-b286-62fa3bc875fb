//
//  BuzWatchComplication1.swift
//  BuzWatchComplicationsExtension
//
//  Created by lizhi on 2023/8/8.
//  Copyright © 2023 lizhi. All rights reserved.
//

import WidgetKit
import SwiftUI
import Intents
import ClockKit
import BuzConfig
import BuzLocalizable
import Localizable


struct DefaultProvider: IntentTimelineProvider {
    func placeholder(in context: Context) -> DefaultEntry {
        DefaultEntry(date: Date(), configuration: ConfigurationIntent())
    }

    func getSnapshot(for configuration: ConfigurationIntent, in context: Context, completion: @escaping (DefaultEntry) -> ()) {
        let entry = DefaultEntry(date: Date(), configuration: configuration)
        completion(entry)
    }

    func getTimeline(for configuration: ConfigurationIntent, in context: Context, completion: @escaping (Timeline<DefaultEntry>) -> ()) {
        
        // Generate a timeline consisting of five entries an hour apart, starting from the current date.
        let currentDate = Date()
        let entryDate = Calendar.current.date(byAdding: .minute, value: 2, to: currentDate)!
        let entry = DefaultEntry(date: entryDate, configuration: configuration)

        let timeline = Timeline(entries: [entry], policy: .after(entryDate))
        completion(timeline)
    }

    func recommendations() -> [IntentRecommendation<ConfigurationIntent>] {
        return [
            IntentRecommendation(intent: ConfigurationIntent(), description: Localizable.complications_buz_app)
        ]
    }
}

struct DefaultEntry: TimelineEntry {
    let date: Date
    let configuration: ConfigurationIntent
}


struct BuzWatchDefaultEntryView : View {
    var entry: DefaultProvider.Entry
    @Environment(\.widgetFamily) private var family
    @Environment(\.widgetRenderingMode) var renderMode
    
    var body: some View {
        GeometryReader { reader in
            AccessoryWidgetBackground()
                .overlay {
                    switch renderMode {
                    case .fullColor:
                        Image("BUZ")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 21.0 * reader.size.width / 47.0, height: 21.0 * reader.size.width / 47.0, alignment: .center)
                    case .vibrant, .accented:
                        Image("BUZ")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 21.0 * reader.size.width / 47.0, height: 21.0 * reader.size.width / 47.0, alignment: .center)
                            .widgetAccentable()
                    default:
                        Image("BUZ")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 21.0 * reader.size.width / 47.0, height: 21.0 * reader.size.width / 47.0, alignment: .center)
                            .widgetAccentable()
                    }
                }
        }
    }

}


struct BuzWatchDefaultComplication: Widget {
    let kind: String = BuzConfig.appComplicationIdentity

    var body: some WidgetConfiguration {
        
        IntentConfiguration(kind: kind, intent: ConfigurationIntent.self, provider: DefaultProvider()) { entry in
            BuzWatchDefaultEntryView(entry: entry)
        }
        .configurationDisplayName(Localizable.complications_buz_app)
        .description(Localizable.complications_buz_app)
        .supportedFamilies([.accessoryCircular])
    }
}


struct BuzWatchDefaultComplication_Previews: PreviewProvider {
    static var previews: some View {
        BuzWatchDefaultEntryView(entry: DefaultEntry(date: Date(), configuration: ConfigurationIntent()))
            .previewContext(WidgetPreviewContext(family: .accessoryCircular))
    }
}

