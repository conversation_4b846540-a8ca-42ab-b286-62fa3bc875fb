//
//  BuzWatchComplications.swift
//  BuzWatchComplications
//
//  Created by lizhi on 2023/8/7.
//  Copyright © 2023 lizhi. All rights reserved.
//

import WidgetKit
import SwiftUI
import Intents
import WatchConnectivity
import BuzConfig
import BuzLocalizable
import Localizable

struct UserProvider: IntentTimelineProvider {
    func getEntry(for configuration: ConfigurationIntent, defaultPreview : Bool = false) -> UserEntry {
        var isPreview = false
        var userId = ""
        var iconUrl = ""
        
        if let icon = WatchDataSourceManager.shared.getRecentUser()?["icon"] as? String {
            iconUrl = icon
        } else {
            isPreview = defaultPreview
        }
        
        if let uId = WatchDataSourceManager.shared.getRecentUser()?["id"] as? String {
            userId = uId
        }
        
        let currentDate = Date()
        let entryDate = Calendar.current.date(byAdding: .minute, value: 1, to: currentDate)!
        let entry = UserEntry(date: entryDate, configuration: configuration ,icon: iconUrl, userId: userId,
                              isLogin: WatchDataSourceManager.shared.getIsLogin(), isPreview: isPreview)
        return entry
    }
    
    func placeholder(in context: Context) -> UserEntry {
        return UserEntry(date: Date(),
                         configuration: ConfigurationIntent(),
                         icon: nil,
                         userId: "",
                         isLogin: true,
                         isPreview : false)
    }

    func getSnapshot(for configuration: ConfigurationIntent, in context: Context, completion: @escaping (UserEntry) -> ()) {
        let entry = self.getEntry(for: configuration)
        completion(entry)
    }

    func getTimeline(for configuration: ConfigurationIntent, in context: Context, completion: @escaping (Timeline<UserEntry>) -> ()) {
        BuzWatchInfoLog.debug(msg: "getTimeline = \(WatchDataSourceManager.shared.complicationUserInfo ?? [:])")
        let entry = self.getEntry(for: configuration, defaultPreview: false)
        let timeline = Timeline(entries: [entry], policy: .after(entry.date))
        completion(timeline)
    }

    func recommendations() -> [IntentRecommendation<ConfigurationIntent>] {
        return [
            IntentRecommendation(intent: ConfigurationIntent(), description: Localizable.complications_recent_contacts)
        ]
    }
}

struct UserEntry: TimelineEntry {
    let date: Date
    let configuration: ConfigurationIntent
    let icon : String?
    let userId : String
    let isLogin : Bool
    var isPreview : Bool = false
}

struct BuzWatchUserEntryView : View {
    var entry: UserProvider.Entry
    @Environment(\.widgetFamily) private var family
    @Environment(\.widgetRenderingMode) var renderMode
    let shared = WatchSessionActivationManager.shared
    
    func imgPath() -> String {
        if self.entry.icon == nil {
            return ""
        }
        
        if let groupsPath = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: BuzConfig.groupStoreId)?.path {
            //4.拼接图片的路径
            let path = groupsPath + "/" + (entry.icon?.md5 ?? "") + ".jpg"
            
            if FileManager.default.fileExists(atPath: path) {
                return path
            }
        }
        
        return ""
    }
    
    @ViewBuilder
    func imageView(reader : GeometryProxy) -> some View {
        if self.entry.isPreview {
            Image(systemName: "message.fill")
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: reader.size.width, height: reader.size.width)
        }else if imgPath() == "" {
            switch family {
            case .accessoryCircular:
                Image(systemName: "message.fill")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 21.0 * reader.size.width / 47.0, height: 21.0 * reader.size.width / 47.0)
                    .widgetAccentable()
            case .accessoryCorner, .accessoryRectangular, .accessoryInline :
                Image(systemName: "message.fill")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: reader.size.width, height: reader.size.width)
                    .widgetAccentable()
            @unknown default:
                Image(systemName: "message.fill")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 21.0 * reader.size.width / 47.0, height: 21.0 * reader.size.width / 47.0)
                    .widgetAccentable()
            }
        } else {
            switch renderMode {
            case .fullColor:
                Image.init(uiImage: UIImage.init(contentsOfFile: imgPath())?.resizeImage(targetSize: reader.size) ?? UIImage())
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: reader.size.width, height: reader.size.height)
            case .accented:
                Image.init(uiImage: UIImage.init(contentsOfFile: imgPath())?.generateAlphaImage()?.resizeImage(targetSize: reader.size) ?? UIImage.init())
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: reader.size.width, height: reader.size.height)
                    .widgetAccentable()
            case .vibrant:
                Image.init(uiImage: UIImage.init(contentsOfFile: imgPath())?.generateAlphaImage()?.resizeImage(targetSize: reader.size) ?? UIImage.init())
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: reader.size.width, height: reader.size.height)
                    .widgetAccentable()
            default:
                Image.init(uiImage: UIImage.init(contentsOfFile: imgPath())?.generateAlphaImage()?.resizeImage(targetSize: reader.size) ?? UIImage.init())
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: reader.size.width, height: reader.size.height)
            }
        }
    }
        
    var body: some View {
        if !self.entry.isLogin {
            BuzWatchDefaultEntryView.init(entry: DefaultEntry(date: Date(), configuration: ConfigurationIntent()))
        } else {
            GeometryReader { reader in
                AccessoryWidgetBackground()
                    .overlay {
                        switch family {
                        case .accessoryCircular:
                            imageView(reader: reader)
                        case .accessoryCorner, .accessoryRectangular, .accessoryInline :
                            imageView(reader: reader)
                        @unknown default:
                            imageView(reader: reader)
                        }
                    }
            }
        }
    }
}



struct BuzWatchUserComplication: Widget {
    let kind: String = BuzConfig.userComplicationIdentity

    var body: some WidgetConfiguration {
        IntentConfiguration(kind: kind, intent: ConfigurationIntent.self, provider: UserProvider()) { entry in
            BuzWatchUserEntryView(entry: entry)
        }
        .configurationDisplayName(Localizable.complications_recent_contacts)
        .description(Localizable.complications_recent_contacts)
        .supportedFamilies([.accessoryCircular
//                            , .accessoryCorner
                           ])
    }
}


struct BuzWatchUserComplication_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            BuzWatchUserEntryView(entry: UserEntry(date: Date(),
                                                   configuration: ConfigurationIntent(),
                                                   icon: nil,
                                                   userId: "",
                                                   isLogin: true,
                                                   isPreview : true))
                .previewContext(WidgetPreviewContext(family: .accessoryCircular))
//            BuzWatchUserEntryView(entry: UserEntry(date: Date(),
//                                                   configuration: ConfigurationIntent(),
//                                                   icon: nil, userId: ""))
//                .previewContext(WidgetPreviewContext(family: .accessoryCorner))
        }
        
    }
}
