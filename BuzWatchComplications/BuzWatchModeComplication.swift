//
//  BuzWatchComplications.swift
//  BuzWatchComplications
//
//  Created by lizhi on 2023/8/7.
//  Copyright © 2023 lizhi. All rights reserved.
//

import WidgetKit
import SwiftUI
import Intents
import WatchConnectivity
import BuzConfig
import BuzLocalizable
import Localizable

struct ModeProvider: IntentTimelineProvider {
    func placeholder(in context: Context) -> ModeEntry {
        ModeEntry(date: Date(),
                  configuration: ConfigurationIntent() ,
                  mode: .available,
                  isLogin: true,
                  isPreview: false)
    }

    func getSnapshot(for configuration: ConfigurationIntent, in context: Context, completion: @escaping (ModeEntry) -> ()) {
        let entry = ModeEntry(date: Date(),
                              configuration: configuration ,
                              mode: .available,
                              isLogin: true,
                              isPreview: false)
        completion(entry)
    }

    func getTimeline(for configuration: ConfigurationIntent, in context: Context, completion: @escaping (Timeline<ModeEntry>) -> ()) {
        BuzWatchInfoLog.debug(msg: "getTimeline = \(WatchDataSourceManager.shared.complicationUserInfo ?? [:])")
        let currentDate = Date()
        let entryDate = Calendar.current.date(byAdding: .minute, value: 1, to: currentDate)!
        let entry = ModeEntry(date: entryDate,
                              configuration: configuration ,
                              mode: WatchDataSourceManager.shared.getMode(),
                              isLogin: WatchDataSourceManager.shared.getIsLogin(), isPreview: false)

        let timeline = Timeline(entries: [entry], policy: .after(entryDate))
        completion(timeline)
    }

    func recommendations() -> [IntentRecommendation<ConfigurationIntent>] {
        return [
            IntentRecommendation(intent: ConfigurationIntent(), description: Localizable.complications_mode_switch)
        ]
    }
}

struct ModeEntry: TimelineEntry {
    let date: Date
    let configuration: ConfigurationIntent
    let mode : PersonalStatusType
    let isLogin : Bool
    let isPreview : Bool
}

struct BuzWatchModeEntryView : View {
    var entry: ModeProvider.Entry
    @Environment(\.widgetFamily) private var family
    @Environment(\.widgetRenderingMode) var renderMode
    
    let shared = WatchSessionActivationManager.shared
    
    var body: some View {
        if self.entry.isPreview {
            GeometryReader { reader in
                AccessoryWidgetBackground()
                    .overlay {
                        let color = entry.mode == PersonalStatusType.available ? Color.watchAvailableColor(alpha: 1.0) : Color.watchQuietColor(alpha: 1.0)
                        Image.init(systemName: entry.mode == PersonalStatusType.available ? "speaker.wave.3.fill" : "speaker.slash.fill")
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .foregroundColor(color)
                        .frame(width: 22.0 * reader.size.width / 47.0, height: 22.0 * reader.size.width / 47.0, alignment: .center)
                    }
            }
        }else if !self.entry.isLogin {
            BuzWatchDefaultEntryView.init(entry: DefaultEntry(date: Date(), configuration: ConfigurationIntent()))
        } else {
            GeometryReader { reader in
                AccessoryWidgetBackground()
                    .overlay {
                        let color = entry.mode == PersonalStatusType.available ? Color.watchAvailableColor(alpha: 1.0) : Color.watchQuietColor(alpha: 1.0)

                        switch renderMode {
                        case .fullColor:
                            Image.init(systemName: entry.mode == PersonalStatusType.available ? "speaker.wave.3.fill" : "speaker.slash.fill")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .foregroundColor(color)
                            .frame(width: 22.0 * reader.size.width / 47.0, height: 22.0 * reader.size.width / 47.0, alignment: .center)
                        case .vibrant, .accented:
                            Image.init(systemName: entry.mode == PersonalStatusType.available ? "speaker.wave.3.fill" : "speaker.slash.fill")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 22.0 * reader.size.width / 47.0, height: 22.0 * reader.size.width / 47.0, alignment: .center)
                            .widgetAccentable()
                        default:
                            Image.init(systemName: entry.mode == PersonalStatusType.available ? "speaker.wave.3.fill" : "speaker.slash.fill")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 22.0 * reader.size.width / 47.0, height: 22.0 * reader.size.width / 47.0, alignment: .center)
                            .widgetAccentable()
                        }
                    }
            }
        }
    }
}



struct BuzWatchModeComplication: Widget {
    let kind: String = BuzConfig.modeComplicationIdentity

    var body: some WidgetConfiguration {
        
        IntentConfiguration(kind: kind, intent: ConfigurationIntent.self, provider: ModeProvider()) { entry in
            BuzWatchModeEntryView(entry: entry)
        }
        .configurationDisplayName(Localizable.complications_mode_switch)
        .description(Localizable.complications_mode_switch)
        .supportedFamilies([.accessoryCircular])
    }
}


struct BuzWatchModeComplication_Previews: PreviewProvider {
    static var previews: some View {
        BuzWatchModeEntryView(entry: ModeEntry(date: Date(),
                                               configuration: ConfigurationIntent() ,
                                               mode: .available,
                                               isLogin: true,
                                               isPreview:true))
            .previewContext(WidgetPreviewContext(family: .accessoryCircular))
    }
}
