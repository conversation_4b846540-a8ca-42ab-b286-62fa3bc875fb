//
//  WatchWithIphoneBusinessModel.swift
//  buz
//
//  Created by l<PERSON>hi on 2023/7/27.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import SwiftUI

//BuzUserQuietModeUnknow = 0,   //未知
//BuzUserQuietModeAvaliable = 1 , //Avaliable模式
//BuzUserQuietModeQuiet = 2 //Quiet模式

///首页列表
class WatchHomeListModel : NSObject {
    @objc var userId: String?
    @objc var name: String?
    @objc var portrait: String?
    @objc var conversationType : Int = 1
    @objc var isGroup: Bool {
        return conversationType == 3
    }
    @objc var unReadCount = 0
    @objc var quietMode = 0
    @objc var isSpeaking = false
    @objc var onlineState = false
    @objc var isThinking = false
    @objc var userType = 0  /// BuzUserTypeDefult = 0,   //普通好友 BuzUserTypeBot = 1,//AI bot BuzUserTypeOfficial = 2, // 官方账号
    var autoPlayMsgId : Int64?
    
    var emoji: String?
    
    func equalInformation(_ model : WatchHomeListModel) -> Bool {
        return self.quietMode == model.quietMode
        && self.unReadCount == model.unReadCount
        && self.onlineState == model.onlineState
        && self.portrait == model.portrait
        && self.name == model.name
        && self.isThinking == model.isThinking
    }
}

///历史列表
class WatchHistoryListModel : NSObject {
    @objc var target : Int64 = 0
    @objc var type : Int = 0
    @objc var msgId : Int64 = 0
    @objc var serverMsgId : Int64 = 0
    @objc var isSelf = false
    @objc var isRead = false
    @objc var isShowTime = false
    @objc var isAIbot =  false
    @objc var isGroup = false
    @objc var sendName = ""
    @objc var sendTime = ""
    @objc var convType = 1
    
    ///语音是Url,文字是具体值
    @objc var content : String = ""
    @objc var playerState = 0 /// [0 默认， 1缓冲 ，2 播放]
    ///语音时长
    @objc var duration : Double = 0.0
    ///图片size
    @objc var imageSizeType = 0 /// 0 (120,120) 1 (120,162) 2 (162,120)
    @objc var isSendFail = false
    ///AI机器人
    @objc var voiceTextType = 0 /// 0 转译文本失败 , 1 成功 , 2 没有内容
    @objc var voiceTextContent = ""
    @objc var cryptKey: String?
    @objc var cryptIV: String?
    
    @objc var voicemoji: String?
    
    @objc var senderNickName:String?
    
    @objc var commandType : Int = 0
    @objc var subBusinessType : Int = 0
    
    var imageSize : CGSize{
        get{
            if imageSizeType == 1{
                return CGSizeMake(120, 162)
            }else if imageSizeType == 2{
                return CGSizeMake(162, 120)
            }
            return CGSizeMake(120, 120)
        }
    }
}


class WatchSpeakingModel : NSObject  {
    
    @objc var url = ""
    @objc var presettedDuration = 0.0
    @objc var sourceId: String?
    @objc var msgId: Int64 = 0
    @objc var serverMsgId: Int64 = 0
    @objc var convType: Int = 1
    @objc var traceId = ""
    @objc var cryptKey: String?
    @objc var cryptIV: String?
    @objc var voicemoji: String?
    @objc var senderNickName: String?
}
