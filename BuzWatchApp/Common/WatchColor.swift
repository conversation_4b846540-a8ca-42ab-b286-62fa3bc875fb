//
//  WatchColor.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/18.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import UIKit
import SwiftUI
import BuzUIStyle

extension Color {
    static func watchWhiteColor(alpha : CGFloat = 1.0) -> Color {
        return Color.init(uiColor: UIColor.white.withAlphaComponent(alpha))
    }
    
    static func watchBlackColor(alpha : CGFloat = 1.0) -> Color {
        return Color.init(uiColor: UIColor.black.withAlphaComponent(alpha))
    }
    
    static func watchBgColor(alpha : CGFloat = 1.0) -> Color {
        return Color.init(UIColor.init(hexString: "#F2F4FC").withAlphaComponent(alpha))
    }
    
    
    
    static func watchAvailableColor(alpha : CGFloat = 1.0) -> Color {
        return Color.init(UIColor.token.color_text_highlight_default.withAlphaComponent(alpha))
    }
    
    static func watchQuietColor(alpha : CGFloat = 1.0) -> Color {
        return Color.init(UIColor.token.color_foreground_dnd_default.withAlphaComponent(alpha))
    }
    
    static func watchPrimaryColor() -> Color {
        return Color.init(UIColor.token.color_text_highlight_default)
    }
    
    static func watchYellowColor() -> Color {
        return Color.init(UIColor.init(hexString: "#FF9500"))
    }
    
    static func watchPurpleColor() -> Color {
        return Color.init(UIColor.token.color_foreground_dnd_default)
    }
    
    static func watchSecondaryErrorColor() -> Color {
        return Color.init(UIColor.init(hexString: "#EB4D3D"))
    }
    
    static func watchVoiceHowColor() -> Color {
        return Color.init(UIColor.init(hexString: "#424242"))
    }
    
    
}
