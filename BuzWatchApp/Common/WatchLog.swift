//
//  WatchLog.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/7/26.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import BaseTool

protocol WatchLogProtocol {
    
    static var tag : String {get}
    
}


extension WatchLogProtocol{
 
    static func debug(tag: String = Self.tag , msg : String)
    {
        
        ILog.debug(tag: tag, msg)
    }
    
    static func info(tag: String = Self.tag , msg : String)
    {
        
        ILog.info(tag: tag, msg)
    }
 
    static func error(tag: String = Self.tag , msg : String)
    {
        
        ILog.error(tag: tag, msg)
        WatchDataSourceManager.shared.outPutLog(log: tag + ":" + msg, completionHandler: nil)
    }
}


//watch日志
enum BuzWatchInfoLog : WatchLogProtocol {
    static let tag: String = "BuzLog_WatchInfo"
}
