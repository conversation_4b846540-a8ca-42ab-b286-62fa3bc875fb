//
//  CommonDataSourceModel.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/7/20.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation

@objc
enum WatchToIphoneCommonDataType : Int32 {
    case normal = 0  ///激活app
    case loginStatus = 1  ///登录状态
    case activateApp = 2  ///激活app
    case getHomeList = 3  ///获取首页列表
    case getHistoryList = 4  ///获取历史
    case sendIM = 5      ///发送IM消息
    case receiveIM  = 6   ///接受IM消息
    case outPutLog = 7   ///输出日志
    case getLocalFile = 8 ///获取本地文件
    case getAudioFileByMsgInfo  = 9///获取本地文件
    case logout = 10     ///用户退出
    case login   = 11      ///用户登录
    case sendIMReceipt = 12 ///发送消息回执
    case updateConversation = 13 ///更新会话
    case userSpeaking  = 14 ///用户说话
    case getWatchSetting  = 15 ///获取本地文件
    case setPersonalStatusType = 16 ///获取本地文件
    case watchSettingChangeType = 17 ///获取本地文件
    case updateConversationList = 18 ///更新会话列表
    case updateCurrentSpeakingUser = 19 ///当前说话人
    case getDeviceId = 20    ///设备信息
    case IMMessageSetRead = 21    ///消息已读
    case tekiplayerEventTrackingToRDS = 22    ///tekiplay上报
    case watchEventTracking  = 23   ///watchEventTracking上报
    case watchSettingTracking  = 24   ///watch setting
    case watchActiveComplicationsTracking  = 25 ///watchActiveComplicationsTracking watch widget action
    case updateIMMessage = 26    ///更新IM消息
    case deleteIMMessage = 27    ///删除IM消息
    case getAudioRemoteURL = 28   ///获取URL
    case getWatchIsCanSpeaking = 29   ///是否可以播放消息
}

enum MessageType : Int {
    case decryptFailed = -2
    case unknow = -1
    
    case text = 1
    case voice = 2
    case image = 3
    case video = 5
    case recalled = 99
    case unread = 1023
    case robotThinking = 1024
    case autoTranslateTip = 1025

    case command = 10003
    case leaveVoice = 10004
    case voiceText = 10005
    case newVoiceText = 10007
    case voicEmoji = 10008
    case localTip = 10009
    case location = 10010
    case mediaText = 10011
    case mediaActionText = 100111
    case compatibleVoiceEmoji = 10012
    case livePlaceShareCard = 10017
    case voicegif = 10016
    case callCard = 10019
    case fileAttachment = 10020

    case rtcVoice = 6000
    
    func isVoiceEmoji() -> Bool {
        return self == .voicEmoji || self == .compatibleVoiceEmoji
    }
    
    func isMediaText() -> Bool {
        return self == .mediaText || self == .mediaActionText
    }
    
    var eventTrackTypeName: String {
        if self == .text {
            return "word_message"
        } else if self == .image {
            return "photo_message"
        } else if self == .video {
            return "video_message"
        } else if self == .location {
            return "location_message"
        } else if self == .voice || self == .leaveVoice || self == .voiceText{
            return "voice_message"
        } else if self.isVoiceEmoji() {
            return "voicemoji_message"
         }else if self == .voicegif {
            return "VG_message"
         }else if self == .fileAttachment {
            return "File_message"
        } else {
            return "other_message"
        }
    }
}

enum CommandMessageBusinessType : Int {
    case onAir = 6
    case livePlace = 7
}

enum CommandMessageSubBusinessType : Int {
    case onAirStart = 9
    case onAirEnd = 10
    case onAirMissed = 11
    case onAirLineBusy = 12
}

class WatchToIphoneCommonDataTransferClass : NSObject  {
    @objc var dataType = WatchToIphoneCommonDataType.normal.rawValue    /// 数据类型
    @objc var params : Dictionary<String, Any>?   /// 必要参数
    @objc var content : Any? ///返回内容
}


