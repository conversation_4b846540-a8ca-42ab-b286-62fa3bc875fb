//
//  UIImage+Extension.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/23.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import UIKit

extension UIImage {
    func resizeImage(targetSize: CGSize) -> UIImage? {
        let size = self.size
        
        let widthRatio  = targetSize.width  / size.width
        let heightRatio = targetSize.height / size.height
        
        // Figure out what our orientation is, and use that to form the rectangle
        var newSize: CGSize
        if(widthRatio > heightRatio) {
            newSize = CGSize(width: size.width * heightRatio, height: size.height * heightRatio)
        } else {
            newSize = CGSize(width: size.width * widthRatio, height: size.height * widthRatio)
        }
        
        // This is the rect that we've calculated out and this is what is actually used below
        let rect = CGRect(origin: .zero, size: newSize)
        
        // Actually do the resizing to the rect using the ImageContext stuff
        UIGraphicsBeginImageContextWithOptions(newSize, false, 2.0)
        self.draw(in: rect)
        let newImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        
        return newImage
    }
    
    func generateAlphaImage() -> UIImage? {
        guard let cgImage = self.cgImage else {
            return nil
        }
        
        let size = CGSize(width: cgImage.width, height: cgImage.height)
        let bytesPerPixel = 4  // RGBA
        let bytesPerRow = bytesPerPixel * Int(size.width)
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        
        guard let context = CGContext(data: nil,
                                      width: Int(size.width),
                                      height: Int(size.height),
                                      bitsPerComponent: 8,
                                      bytesPerRow: bytesPerRow,
                                      space: colorSpace,
                                      bitmapInfo: CGImageAlphaInfo.premultipliedLast.rawValue) else {
            return nil
        }
        
        let rect = CGRect(origin: .zero, size: size)
        context.draw(cgImage, in: rect)
        
        guard let data = context.data else {
            return nil
        }
        
        // Generate alpha image based on the grayscale value
        for i in 0..<(Int(size.height) * Int(size.width)) {
            let alphaIndex = i * 4 + 3
            let redIndex = i * 4
            let greenIndex = i * 4 + 1
            let blueIndex = i * 4 + 2
            let redColor = data.load(fromByteOffset: redIndex, as: UInt8.self)
            let greenColor = data.load(fromByteOffset: greenIndex, as: UInt8.self)
            let blueColor = data.load(fromByteOffset: blueIndex, as: UInt8.self)
            // Calculate grayscale value
            let grayscale = UInt8(min(255, (Int(redColor) + Int(greenColor) + Int(blueColor)) / 3))
            
            data.storeBytes(of: grayscale, toByteOffset: alphaIndex, as: UInt8.self)
        }
        
        if let alphaImage = context.makeImage() {
            return UIImage(cgImage: alphaImage)
        }
        
        return nil
    }
}
