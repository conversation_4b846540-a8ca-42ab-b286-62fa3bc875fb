//
//  AudioPlay.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/7/20.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import AVFoundation
import BaseTool
import TekiPlayer


class AudioPlay : NSObject{
    
    var player: AVPlayer?
    var playerItem:AVPlayerItem?
    fileprivate let seekDuration: Float64 = 10
    
    var playerQueue : AVQueuePlayer?
    
    var isPlaying = false
    
    static let shared = AudioPlay()
    private var playerItemContext = 0
    
    private(set) lazy var tekiplayer: TekiPlayer = {
        var config = Teki.Configuration.shared
        config.setupAudioSession = false
        config.handleInterruption = false
        let player = TekiPlayer.init(configuration: config)
        return player
    }()
    
    override init() {
        super.init()
        ILog.debug(tag: "watchos", "AudioPlay init")
    }
    
    func playTekiplayer(){
        
        // Set up the session.
        let session = AVAudioSession.sharedInstance()


        do {
            try session.setCategory(AVAudioSession.Category.playback,
                                    mode: .default,
                                    policy: AVAudioSession.RouteSharingPolicy.longFormAudio,
                                    options: [])
        } catch let error {
            NSLog("output =  Unable to set up the audio session: \(error.localizedDescription) ***")
        }
        
        
        // Activate and request the route.
        session.activate(options: []) { (success, error) in
            guard error == nil else {
                NSLog("output =  An error occurred: \(error!.localizedDescription) ***")
                // Handle the error here.
                return
            }

            NSLog("output =  是否播放成功 ： \(success)")

            if success {
                playRemoteUrl()
            }
        }
        
        func playRemoteUrl(){
            
//        https://cdnus101-buz.183im.com/1/2/06381798dda25e9d7d6655bf18d4bfd8/m/4733e270f37745abbe610a07d55f0acd.opus
                        
            let fileName = "music.opus"

            let mainBundle = Bundle.main
            
            var url0 = mainBundle.url(forResource: fileName, withExtension: nil)!
            
            url0 = URL.init(string: "https://cdnus101-buz.183im.com/2/2/db22af2cd680cbecee45d92bef586255/m/ce230a447f7347b58da3a2d256d62894/AE025687-3C5A-45B1-98AA-5080BD83B51C.opus") ?? url0
        
            var url = mainBundle.url(forResource: fileName, withExtension: nil)!
            
            url = URL.init(string: "https://cdnus101-buz.183im.com/1/2/dc294e3a94b448873a7a6d7aeef0ea63/m/87b9dff4880e4e88b32b561dcc23e81c.opus") ?? url
            
            var url1 = mainBundle.url(forResource: fileName, withExtension: nil)!
            
            url1 = URL.init(string: "https://cdnus101-buz.183im.com/1/2/dc294e3a94b448873a7a6d7aeef0ea63/m/a1ea1a115b0840c6b1d8e835c26e0682.opus") ?? url
            
            var url2 = mainBundle.url(forResource: fileName, withExtension: nil)!
            
            url2 = URL.init(string: "https://cdnus101-buz.183im.com/1/2/dc294e3a94b448873a7a6d7aeef0ea63/m/87b9dff4880e4e88b32b561dcc23e81c.opus") ?? url
            
            var url3 = mainBundle.url(forResource: fileName, withExtension: nil)!
            
            url3 = URL.init(string: "https://cdnus101-buz.183im.com/1/2/dc294e3a94b448873a7a6d7aeef0ea63/m/a1ea1a115b0840c6b1d8e835c26e0682.opus") ?? url
            
            var item0 =  Teki.MediaItem.init(url: url0)
            var item =  Teki.MediaItem.init(url: url)
            var item1 =  Teki.MediaItem.init(url: url1)
            var item2 =  Teki.MediaItem.init(url: url2)
            var item3 =  Teki.MediaItem.init(url: url3)
            
                        
            self.tekiplayer.addMediaItems([item0, item, item1, item2, item3 ,item0 ,item, item1, item2, item3])
            
            
            self.tekiplayer.volume = 0.1
            
            self.tekiplayer.play()
        }
        
    }
    
    func playAudioWithUrl(url : URL){
        
        let playerItem:AVPlayerItem = AVPlayerItem(url: url)
        player = AVPlayer(playerItem: playerItem)
        player?.play()
        player?.volume = 1.0//Float(SettingManager.shared.volumeValue)
        let duration : CMTime = playerItem.asset.duration
        
        BuzWatchInfoLog.debug(msg:"duration = \(duration)")
        
        let session = AVAudioSession.sharedInstance()
        
        do {
            try session.setCategory(AVAudioSession.Category.playback,
                                    mode: .default,
                                    policy: AVAudioSession.RouteSharingPolicy.default,
                                    options: [])
        } catch let error {
            fatalError("*** Unable to set up the audio session: \(error.localizedDescription) ***")
        }
        
        player?.addPeriodicTimeObserver(forInterval: CMTime(value: 1, timescale: 1), queue: DispatchQueue.main) { time in
            if self.player?.currentItem?.status == .readyToPlay {
                let nowDuration = CMTimeGetSeconds(duration)
                let currentTime = Int(time.seconds)
                
                let text = "\(currentTime)/\(nowDuration)\""
                let progress = Float(currentTime) / Float(nowDuration)
                BuzWatchInfoLog.debug(msg:"text:\(text) progress:\(progress)")
            }
            let playbackLikelyToKeepUp = self.player?.currentItem?.isPlaybackLikelyToKeepUp
            if playbackLikelyToKeepUp == false{
                BuzWatchInfoLog.debug(msg:"IsBuffering")
            } else {
                BuzWatchInfoLog.debug(msg:"Buffering completed")
            }
        }
        
        playerItem.addObserver(self, forKeyPath: #keyPath(AVPlayerItem.status), options: [.old, .new], context: &self.playerItemContext)
        
        NotificationCenter.default.addObserver(self, selector: #selector(self.finishedPlaying(_:)), name: NSNotification.Name.AVPlayerItemDidPlayToEndTime, object: playerItem)
        
    }
    
    func playWithUrl(fileUrl : String){
        
        var url = URL.init(fileURLWithPath: fileUrl)
        
        if fileUrl.hasPrefix("http") {
            url = URL.init(string: fileUrl) ?? URL.init(fileURLWithPath: fileUrl)
        }
        
        let playerItem:AVPlayerItem = AVPlayerItem(url: url)
        player = AVPlayer(playerItem: playerItem)
        player?.play()
        let duration : CMTime = playerItem.asset.duration
        
        BuzWatchInfoLog.debug(msg:"duration = \(duration)")
        
        let session = AVAudioSession.sharedInstance()
        
        do {
            try session.setCategory(AVAudioSession.Category.playback,
                                    mode: .default,
                                    policy: AVAudioSession.RouteSharingPolicy.default,
                                    options: [])
        } catch let error {
            fatalError("*** Unable to set up the audio session: \(error.localizedDescription) ***")
        }
        
        player?.addPeriodicTimeObserver(forInterval: CMTime(value: 1, timescale: 1), queue: DispatchQueue.main) { time in
            if self.player?.currentItem?.status == .readyToPlay {
                let nowDuration = CMTimeGetSeconds(duration)
                let currentTime = Int(time.seconds)
                
                let text = "\(currentTime)/\(nowDuration)\""
                let progress = Float(currentTime) / Float(nowDuration)
                BuzWatchInfoLog.debug(msg:"text:\(text) progress:\(progress)")
            }
            let playbackLikelyToKeepUp = self.player?.currentItem?.isPlaybackLikelyToKeepUp
            if playbackLikelyToKeepUp == false{
                BuzWatchInfoLog.debug(msg:"IsBuffering")
            } else {
                BuzWatchInfoLog.debug(msg:"Buffering completed")
            }
        }
        
        playerItem.addObserver(self, forKeyPath: #keyPath(AVPlayerItem.status), options: [.old, .new], context: &self.playerItemContext)
        
        NotificationCenter.default.addObserver(self, selector: #selector(self.finishedPlaying(_:)), name: NSNotification.Name.AVPlayerItemDidPlayToEndTime, object: playerItem)
    }
    
    
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        guard context == &playerItemContext else {
            super.observeValue(forKeyPath: keyPath, of: object, change: change, context: context)
            return
        }
        
        if keyPath == #keyPath(AVPlayerItem.status) {
            let status: AVPlayerItem.Status
            if let statusNumber = change?[.newKey] as? NSNumber {
                status = AVPlayerItem.Status(rawValue: statusNumber.intValue)!
            } else {
                status = .unknown
            }
            
            // Switch over status value
            switch status {
            case .readyToPlay:
                self.player?.play()
                BuzWatchInfoLog.debug(msg:".readyToPlay")
            case .failed:
                BuzWatchInfoLog.debug(msg:".failed")
            case .unknown:
                BuzWatchInfoLog.debug(msg:".unknown")
            @unknown default:
                BuzWatchInfoLog.debug(msg:"@unknown default")
            }
        }
    }
    
    func stop(){
        player?.pause()
    }
    
    func replay(){
        let seekTime = CMTime(value: CMTimeValue(0), timescale: 1)
        player?.seek(to: seekTime, completionHandler: { _ in
                                
        })
        player?.play()
    }
    
    func seek(){
        let playTime = 10
        let seekTime = CMTime(value: CMTimeValue(playTime), timescale: 1)
        player?.seek(to: seekTime, completionHandler: { _ in
                                
        })
    }
    
    @objc func finishedPlaying( _ myNotification:NSNotification) {
        BuzWatchInfoLog.debug(msg:"finishedPlaying")
    }
       
    func playButton(_ sender: Any) {
        BuzWatchInfoLog.debug(msg:"play Button")
       if player?.rate == 0
       {
           player?.play()
       } else {
           player?.pause()
       }
    }
    
    func playQueue(){
        
        DispatchQueue.main.async {
            
            if self.isPlaying {
                
                self.playerQueue?.pause()
                
                self.isPlaying = false
                
                self.playerQueue = nil
                
                NSLog("output = 结束播放")
                
                return ;
                
            }
            
            NSLog("output = 开发播放")
            
            // Set up the session.
            let session = AVAudioSession.sharedInstance()


            do {
                try session.setCategory(AVAudioSession.Category.playback,
                                        mode: .default,
                                        policy: AVAudioSession.RouteSharingPolicy.longFormAudio,
                                        options: [])
            } catch let error {
                NSLog("output =  Unable to set up the audio session: \(error.localizedDescription) ***")
            }


//            // Set up the player.
//            let player: AVAudioPlayer
//            do {
//                player = try AVAudioPlayer(data: audioData)
//            } catch let error {
//                print("*** Unable to set up the audio player: \(error.localizedDescription) ***")
//                // Handle the error here.
//                return
//            }


            // Activate and request the route.
            session.activate(options: []) { (success, error) in
                guard error == nil else {
                    NSLog("output =  An error occurred: \(error!.localizedDescription) ***")
                    // Handle the error here.
                    return
                }
                
                NSLog("output =  是否播放成功 ： \(success)")
                
                if success {
                    playAudio(isSpeak: false)
                }else{
                    
                    NSLog("output =  其他方式")
                    do {
                        try session.setCategory(AVAudioSession.Category.playback,
                                                mode: .default,
                                                policy: AVAudioSession.RouteSharingPolicy.default,
                                                options: [])
                    } catch let error {
                        NSLog("output =  Unable to set up the audio session: \(error.localizedDescription) ***")
                    }
                    
                    playAudio(isSpeak: false)
                }
            }
            
//            let session = AVAudioSession.sharedInstance()
//
//            for output in session.currentRoute.outputs {
//                if output.portType == AVAudioSession.Port.headphones || output.portType == AVAudioSession.Port.lineOut {
//
//                }
//                NSLog("output = \(output.portType)")
//            }
//
//
//            session.activate { isFinished, error in
//                NSLog("output =  set activate the audio session: isFinished = \(isFinished) , \(error?.localizedDescription) ***")
//                if isFinished {
//                    playAudio(isSpeak: false)
//                }else{
//                    playAudio(isSpeak: true)
//                }
//            }
            

            func playAudio(isSpeak : Bool){
//                do {
//
//                    try session.setCategory(AVAudioSession.Category.playback,
//                                            mode: .default,
//                                            policy: isSpeak ?  .default : .longFormAudio,
//                                            options: [])
//
//
//                } catch let error {
//
//                    NSLog("output = Unable to set setCategory the audio session: \(error.localizedDescription) ***")
//
//                }
                
                NSLog("output =  执行播放")
                
                self.isPlaying = true

                let string1 =  "https://cdnus101-buz.183im.com/2/2/db22af2cd680cbecee45d92bef586255/m/7bd0b68850b640ce86a0253349be682b/1b0bde74-ac0d-4732-8eb9-5a5f3bfc8e68.mp3?vxCostTime=2693&vxFileSize=106560&vxStorageType=s3"

                let string2 =  "https://cdnus101-buz.183im.com/2/2/db22af2cd680cbecee45d92bef586255/m/aed5dcdeddb148548471a64409d287e5/3702D00F-3C4F-452F-A18B-BDF1B0C71EFE.aac"

                let string3 = "https://cdnus101-buz.183im.com/2/2/db22af2cd680cbecee45d92bef586255/m/9251917abb754e0683ba880d0025f283/5A7342A9-0B32-44C0-8A37-B7983D71AD48.m4a"
                

                if let url1 = URL(string:string1) ,let url2 = URL(string:string2) , let url3 = URL(string:string3){
                    
                    let playerItem1 : AVPlayerItem = AVPlayerItem(url: url1)
                    let playerItem2 : AVPlayerItem = AVPlayerItem(url: url2)
                    let playerItem3 : AVPlayerItem = AVPlayerItem(url: url3)
                    let playerItem4 : AVPlayerItem = AVPlayerItem(url: url1)
                    let playerItem5 : AVPlayerItem = AVPlayerItem(url: url2)
                    let playerItem6 : AVPlayerItem = AVPlayerItem(url: url3)
                    let playerItem7 : AVPlayerItem = AVPlayerItem(url: url1)
                    let playerItem8 : AVPlayerItem = AVPlayerItem(url: url2)
                    let playerItem9 : AVPlayerItem = AVPlayerItem(url: url3)

                    self.playerQueue = AVQueuePlayer(items: [playerItem1 , playerItem2 , playerItem3,playerItem4 , playerItem5 , playerItem6,playerItem7 , playerItem8 , playerItem9])

                    self.playerQueue?.play()

                    self.isPlaying = true

                    self.playerQueue?.addObserver(self, forKeyPath: #keyPath(AVPlayerItem.status), options: [.old, .new], context: &self.playerItemContext)

                    NotificationCenter.default.addObserver(self, selector: #selector(self.finishedPlaying(_:)), name: NSNotification.Name.AVPlayerItemDidPlayToEndTime, object: self.playerItem)

                    self.playerQueue?.addPeriodicTimeObserver(forInterval: CMTime(value: 1, timescale: 1), queue: DispatchQueue.main) { time in
        //                if self.player?.currentItem?.status == .readyToPlay {
        //                    let nowDuration = CMTimeGetSeconds(duration)
        //                    let currentTime = Int(time.seconds)
        //
        //                    let text = "\(currentTime)/\(nowDuration)\""
        //                    let progress = Float(currentTime) / Float(nowDuration)
        //                    BuzWatchInfoLog.debug(msg:"text:\(text) progress:\(progress)")
        //                }
                        let playbackLikelyToKeepUp = self.playerQueue?.currentItem?.isPlaybackLikelyToKeepUp
                        if playbackLikelyToKeepUp == false{
                            BuzWatchInfoLog.debug(msg:"IsBuffering")
                        } else {
                            BuzWatchInfoLog.debug(msg:"Buffering completed")
                        }
                    }

                }
            }
            }
        
                
                
        //        let fileName = "sample6.mp3"

                // 获取当前应用的主Bundle
        //        let mainBundle = Bundle.main

                // 获取文件的路径
        //        let url = mainBundle.url(forResource: fileName, withExtension: nil)
                
        //        let url = URL(string: "https://argaamplus.s3.amazonaws.com/eb2fa654-bcf9-41de-829c-4d47c5648352.mp3")
        //        let url = URL(string: "https://cdnus101-buz.183im.com/2/2/db22af2cd680cbecee45d92bef586255/m/e980a928d8a54cd18e1dd85b9e6509da/aae77122-65e7-4d4a-b03a-370ec7389106.opus")
                
        //    https://cdnus101-buz.183im.com/2/2/db22af2cd680cbecee45d92bef586255/m/8e7e9e64100a4b80a7aa47476ecdbe48/6ff4cba2-d60a-41d5-8625-791fafb4aadf.aac
                
        //    https://cdnus101-buz.183im.com/2/2/db22af2cd680cbecee45d92bef586255/m/9251917abb754e0683ba880d0025f283/5A7342A9-0B32-44C0-8A37-B7983D71AD48.m4a 188KB 54s
                
        //    https://cdnus101-buz.183im.com/2/2/db22af2cd680cbecee45d92bef586255/m/7bd0b68850b640ce86a0253349be682b/1b0bde74-ac0d-4732-8eb9-5a5f3bfc8e68.mp3?vxCostTime=2693&vxFileSize=106560&vxStorageType=s3  107KB 26s
                
        //    https://cdnus101-buz.183im.com/2/2/db22af2cd680cbecee45d92bef586255/m/aed5dcdeddb148548471a64409d287e5/3702D00F-3C4F-452F-A18B-BDF1B0C71EFE.aac  125KB 73s
                
        ///
        ///longFormAudio
        ///
        ///policy: AVAudioSession.RouteSharingPolicy.longFormAudio
        ///1、需要用户连接外接耳机，否则无法后台播放
        ///2、进入后台无法接受iPhone实时data数据、可以接受后台消息(后台消息不保证实时性)
        ///policy: AVAudioSession.RouteSharingPolicy.default
        ///1、进入后台无法播放
    }
}
