//
//  AudioRecrod.swift
//  com.interfun.buz.watch Watch App
//
//  Created by l<PERSON>hi on 2023/7/18.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import AVFoundation
import WatchConnectivity
import WatchKit

enum AudioRecorderEndType {
    case normal
    case overMaxDuraion
    case interruption
}

typealias AudioRecorderCompletionBlock = (_ successfullyflag : Bool , _ duration : Double , _ fileUrl : URL? , _ audioRecorderEnd : AudioRecorderEndType) -> Void
typealias AudioRecordStartFinishCompletionBlock = (_ isSuccess: Bool , _ error : Error?) -> Void
typealias AudioRecordCurrentDurationBlock = (_ duration: Int8) -> Void

class AudioRecorder: NSObject , AVAudioRecorderDelegate {
    
    static let shared = AudioRecorder()
    var isRecording = false
    var audioRecorder: AVAudioRecorder?
    var timer : Timer?
    var duration = 0.0
    var downCount = Int8(0)
    var completionBlock : AudioRecorderCompletionBlock?
    var recordDurationBlock : AudioRecordCurrentDurationBlock?
    
    private var audioRecorderEndType = AudioRecorderEndType.normal
    
    init(isRecording: Bool = false, audioRecorder: AVAudioRecorder? = nil, timer: Timer? = nil, duration: Double = 0.0, downCount: Int8 = Int8(0), completionBlock: AudioRecorderCompletionBlock? = nil, recordDurationBlock: AudioRecordCurrentDurationBlock? = nil, audioRecorderEndType: AudioRecorderEndType = .normal) {
        self.isRecording = isRecording
        self.audioRecorder = audioRecorder
        self.timer = timer
        self.duration = duration
        self.downCount = downCount
        self.completionBlock = completionBlock
        self.recordDurationBlock = recordDurationBlock
        self.audioRecorderEndType = audioRecorderEndType
        super.init()
        self.addInterruptionNotification()
    }
    
    func addInterruptionNotification() {
        NotificationCenter.default.addObserver(self, selector: #selector(self.interruptionNotification(notification:)),
                                               name: NSNotification.Name(rawValue: AVAudioSession.interruptionNotification.rawValue),
                                               object: nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    @objc
    func interruptionNotification(notification : Notification) {
        BuzWatchInfoLog.debug(msg:"WatchAudioPlayManager interruptionNotification")
        self.stopRecord(audioRecorderEndType: .interruption)
        self.removeTimer()
        return
    }
    
    func startRecording(recordMaxDuration : Int8 , recordStartFinishCompletion : AudioRecordStartFinishCompletionBlock , currentDurationBlock : @escaping AudioRecordCurrentDurationBlock , audioRecorderCompletionBlock : AudioRecorderCompletionBlock?) {
        self.completionBlock = audioRecorderCompletionBlock
        self.recordDurationBlock = currentDurationBlock
        let settings = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 16000,
            AVNumberOfChannelsKey: 1,
            AVEncoderAudioQualityKey: AVAudioQuality.low.rawValue
        ]
        
        audioRecorder?.stop()
        audioRecorder = nil
        BuzWatchInfoLog.debug(msg: "start recording : loading")
        
        guard AVAudioSession.sharedInstance().recordPermission == .granted  else {
            BuzWatchInfoLog.debug(msg: "没有权限")
            if let block = completionBlock {
                block(false , 0, nil  , .normal)
            }
            return ;
        }

        do {
            try AVAudioSession.sharedInstance().setCategory(AVAudioSession.Category.record,
                                    mode: .default,
                                    policy: AVAudioSession.RouteSharingPolicy.default,
                                    options: [])
            audioRecorder = try AVAudioRecorder(url: self.getAudioFileUrl(), settings: settings)
            audioRecorder?.delegate = self
            audioRecorder?.prepareToRecord()
            audioRecorder?.record()
            isRecording = true
            BuzWatchInfoLog.debug(msg: "start recording : sucess")
            recordStartFinishCompletion(true , nil)
        } catch {
            BuzWatchInfoLog.debug(msg: "start recording : Failed")
            recordStartFinishCompletion(false , error)
        }
        
        self.removeTimer()
        
        self.timer = Timer(timeInterval: 1.0, repeats: true, block: { timer in
            if self.downCount >= recordMaxDuration {
                BuzWatchInfoLog.debug(msg: "timer stopRecording")
                self.stopRecord(audioRecorderEndType: .overMaxDuraion)
                self.removeTimer()
                return
            }
            self.downCount = self.downCount + 1
            if let block = self.recordDurationBlock{
                block(self.downCount)
            }
            BuzWatchInfoLog.debug(msg: "recording.downCount = \(self.downCount) currentTime = \(String(describing: self.audioRecorder?.currentTime))")
        })
        
        self.timer?.fire()
        RunLoop.current.add(timer!, forMode: .common)
    }
    
    func stopRecord(audioRecorderEndType : AudioRecorderEndType = .normal){
        
        if audioRecorder?.isRecording == true{
            self.audioRecorderEndType = audioRecorderEndType
            self.duration = self.audioRecorder?.currentTime ?? 0.0
            audioRecorder?.stop()
            isRecording = false
            removeTimer()
            self.activePlayerSession()
        }
    }
    
    
    func activePlayerSession() {
        let session = AVAudioSession.sharedInstance()
        do {
            try session.setCategory(.playback,
                                    mode: .default,
                                    policy: .default,
                                    options: [])
            try session.setActive(true)
        } catch let error {
            fatalError("WatchAudioPlayManager.*** Unable to set up the audio session: \(error.localizedDescription) ***")
        }
    }
    
    private func removeTimer(){
        self.downCount = 0
        self.timer?.invalidate()
        self.timer = nil
    }
    
    private func getDocumentsDirectory() -> URL {
        FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
    }
    
    private func getAudioFileUrl() -> URL {
        let fileName = Int(Date.timeIntervalSinceReferenceDate)
        let audioFilename = getDocumentsDirectory().appendingPathComponent("\(fileName).aac")
        return audioFilename
    }
    
    func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        BuzWatchInfoLog.debug(msg: "audioRecorderDidFinishRecording = \(flag) recorder.currentTime = \(self.duration)")
        if flag{
            do {
                if let block = completionBlock {
                    block(flag , self.duration, recorder.url , self.audioRecorderEndType)
                }
//                let data = try Data(contentsOf: fileUrl)
//                // 使用从文件加载的NSData进行进一步处理
//
//                NSLog("data = \(data)")
//                WCSession.default.sendMessage(["key" : "拉取app"]) { value in
//
//                    NSLog("拉取app = \(value)")
//                    Connectivity.shared.sendFile(url: fileUrl , duration: self.duration)
//                } errorHandler: { error in
//
//                }
            } catch {
                BuzWatchInfoLog.debug(msg: "Failed to load data from file: \(error)")
                if let block = completionBlock {
                    block(false , 0, nil , .normal)
                }
            }
        }else{
            if let block = completionBlock {
                block(false , 0, nil  , .normal)
            }
        }
    }
    
    func audioRecorderEncodeErrorDidOccur(_ recorder: AVAudioRecorder, error: Error?) {
        BuzWatchInfoLog.debug(msg: "error = \(String(describing: error)) \(#function) " )
    }
}
