//
//  StringExtension.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/24.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation

extension String {
    func toDict() -> [String : Any]?{

        if let data = data(using: String.Encoding.utf8) ,
           let dict = try? JSONSerialization.jsonObject(with: data, options: .mutableContainers) as? [String : Any]
        {
            return dict
        }
        return nil
    }
}

extension NSString{
    //       注：该id为appGroup生成文件
    func toThumbnail(size : Int) -> NSString {
        let range = self.range(of: ".", options: .backwards)
        
        if range.location == NSNotFound {
            return self
        }
        
        do {
            let re = try NSRegularExpression(pattern: "_[1-9]0+x[1-9]0+", options: .caseInsensitive)
            
            let match = re.matches(in: self as String,
                                      options: .reportProgress,
                                      range: NSRange.init(location: 0, length: self.length))
            if let item = match.last {
                return self.replacingCharacters(in: item.range, with: "_\(size)x\(size)") as NSString
            }
        }catch {
            
        }
        
        let substr = "_\(size)x\(size)." + self.pathExtension
        
        if self.hasSuffix(substr) {
            return self
        }
        
        let prefix = self.substring(to: range.location)
        let suffix = self.substring(from: range.location)
        
        return prefix + "_\(size)x\(size)" + suffix as NSString
    }
}
