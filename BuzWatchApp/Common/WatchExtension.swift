//
//  WatchExtension.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/8/22.
//  Copyright © 2023 lizhi. All rights reserved.
//

import SwiftUI

extension WatchHomeListModel  {

    var getQuietModeColor : Color {
        get{
            if onlineState{
                if self.userType == 1{
                    return .black
                }else if quietMode == 1 {
                    return Color.watchPrimaryColor()
                }else if quietMode == 2 {
                    return Color.watchPurpleColor()
                }else {
                    return .black
                }
            }else{
                return .black
            }
        }
    }
}
