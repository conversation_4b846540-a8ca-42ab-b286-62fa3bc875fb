//
//  WatchNotification.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/18.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation

//protocol WatchNotificationProtocol {
//    func watchNotification(notification : WatchNotification)
//}
//
//struct WatchNotification {
//    let name : String
//    let userInfo : Any?
//}
//
//class WatchNotificationCenter {
//    static let shared = WatchNotificationCenter()
//    var weakTable : [String : NSHashTable<WatchNotificationObserve>]  = [:]
//
//    func register(notifyName : String, observe : WatchNotificationObserve) {
//        if self.weakTable[notifyName] == nil  {
//            self.weakTable[notifyName] = NSHashTable.weakObjects()
//        }
//
//        self.weakTable[notifyName]?.add(observe)
//    }
//
//    func post(notifyName : String, userInfo : Any? = nil) {
//        if let ls = self.weakTable[notifyName] {
//            let obj = WatchNotification.init(name: notifyName, userInfo: userInfo)
//
//            ls.allObjects.forEach { observe in
//                observe.callback(obj)
//            }
//        }
//    }
//}

protocol WatchNotificationProtocol {
    func onNotification(notification : Notification)
}

class WatchNotificationObserve {
    var delegate :  WatchNotificationProtocol?
    let name : String
    
    init(name : String, delegate : WatchNotificationProtocol) {
        self.delegate = delegate
        self.name = name
        
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(self.onNotification(notification:)),
                                               name: NSNotification.Name(name),
                                               object: nil)
    }
    
    init(name : String) {
        self.name = name
        
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(self.onNotification(notification:)),
                                               name: NSNotification.Name(name),
                                               object: nil)
    }
    
    @objc
    func onNotification(notification : Notification) {
        self.delegate?.onNotification(notification: notification)
    }
}
