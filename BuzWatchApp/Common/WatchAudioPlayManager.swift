//
//  WatchAudioPlayManager.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/8/25.
//  Copyright © 2023 lizhi. All rights reserved.
//

import AVFoundation
import BaseTool
import E2EEHelper
import Foundation
import TekiPlayer
import WatchKit

enum WatchAudioPlayManagerAssetPlayState {
    case idle
    case loading
    case buffing
    case playing
    case ended
    case fail
}

class WatchAudioPlayManagerAsset: NSObject {
    var url: String
    var targetId: Int64?
    var msgId: Int64?
    var serverMsgId: Int64?
    var convType: Int?
    var traceId: String?
    var fileExtension: String?
    var playState: WatchAudioPlayManagerAssetPlayState = .idle
    var player: Any?
    let cryptKey: String?
    let cryptIV: String?
    let voicemoji: String?
    let senderNickName: String?

    init(url: String, targetId: Int64? = nil, msgId: Int64? = nil, serverMsgId: Int64? = nil, convType: Int? = nil, fileExtension: String? = nil, traceId: String?, cryptKey: String?, cryptIV: String? , voicemoji: String?,senderNickName: String? ) {
        self.url = url
        self.targetId = targetId
        self.msgId = msgId
        self.convType = convType
        self.fileExtension = fileExtension
        self.serverMsgId = serverMsgId
        self.traceId = traceId
        self.cryptKey = cryptKey
        self.cryptIV = cryptIV
        self.voicemoji = voicemoji
        self.senderNickName = senderNickName
    }
}

class WatchAudioPlayManager: NSObject, ObservableObject {
    @Published var currentAsset: WatchAudioPlayManagerAsset?
    @Published var playerState: WatchAudioPlayManagerAssetPlayState = .idle
    @Published var isPlaying = false

    private var assetList: Array<WatchAudioPlayManagerAsset> = Array<WatchAudioPlayManagerAsset>()
    private var currentIndex: Int?
    private var playerItemContext = 0

    init(currentAsset: WatchAudioPlayManagerAsset? = nil,
         playerState: WatchAudioPlayManagerAssetPlayState = .idle,
         isPlaying: Bool = false,
         assetList: Array<WatchAudioPlayManagerAsset> = Array<WatchAudioPlayManagerAsset>(),
         currentIndex: Int? = nil,
         playerItemContext: Int = 0) {
        self.currentAsset = currentAsset
        self.playerState = playerState
        self.isPlaying = isPlaying
        self.assetList = assetList
        self.currentIndex = currentIndex
        self.playerItemContext = playerItemContext
        super.init()
        addVolumeChangeNotification()
    }

    func addVolumeChangeNotification() {
        NotificationCenter.default.addObserver(self, selector: #selector(volumeChangeNotification(notification:)),
                                               name: NSNotification.Name(rawValue: NSNotificationName.volumeChangeNotification.rawValue),
                                               object: nil)
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    @objc
    func volumeChangeNotification(notification: Notification) {
        if let volume = notification.object as? Float {
            set(volume: volume)
        }
    }

    func playWithUrl(url: String,
                     targetId: Int64?,
                     msgId: Int64?,
                     serverMsgId: Int64?,
                     convType: Int?,
                     traceId: String,
                     cryptKey: String?,
                     cryptIV: String? , voicemoji: String? , senderNickName: String?) {
        if url.count > 0 {
            let asset = WatchAudioPlayManagerAsset(url: url, targetId: targetId, msgId: msgId, serverMsgId: serverMsgId, convType: convType, traceId: traceId, cryptKey: cryptKey, cryptIV: cryptIV, voicemoji: voicemoji,senderNickName:senderNickName )
            assetList.append(asset)
            if !isPlaying {
                currentIndex = 0
                play()
            }
        }
        BuzWatchInfoLog.error(msg: "WatchAudioPlayManager = insert: url = \(String(describing: url)) , targetId = \(String(describing: targetId)) , msgId = \(String(describing: msgId)) , serverMsgId = \(String(describing: serverMsgId)) convType = \(String(describing: convType)) traceId = \(traceId) isEncrypted:\(cryptKey != nil)")
    }

    func set(volume: Float) {
        if isPlaying {
            if let index = currentIndex {
                if let avPlayer = assetList[index].player as? AVPlayer {
                    avPlayer.volume = Float(volume)
                    BuzWatchInfoLog.error(msg: "WatchAudioPlayManager = avPlayer volume : \(volume)")
                } else if let tekiPlayer = assetList[index].player as? TekiPlayer {
                    tekiPlayer.volume = volume
                    BuzWatchInfoLog.error(msg: "WatchAudioPlayManager = tekiPlayer volume : \(volume)")
                }
            }
        }
    }

    func stop(completion: (() -> Void)?) {
        if isPlaying {
            if let index = currentIndex {
                if let avPlayer = assetList[index].player as? AVPlayer {
                    avPlayer.pause()
                    avPlayer.currentItem?.removeObserver(self, forKeyPath: #keyPath(AVPlayerItem.status), context: &playerItemContext)
                    BuzWatchInfoLog.error(msg: "WatchAudioPlayManager = avPlayer.stop")
                } else if let tekiPlayer = assetList[index].player as? TekiPlayer {
                    tekiPlayer.delegate = nil
                    tekiPlayer.stop()
                    BuzWatchInfoLog.error(msg: "WatchAudioPlayManager = tekiPlayer.stop")
                }
            }
            if let index = currentIndex, index < assetList.count {
                let assst = assetList[index]
                assst.playState = .ended
            }
            cleanReset()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5, execute: DispatchWorkItem(block: {
            if let block = completion {
                block()
            }
        }))
    }

    private func cleanReset() {
        playerState = .ended
        isPlaying = false
        currentIndex = nil
        currentAsset = nil
        assetList.removeAll()
        BuzWatchInfoLog.error(msg: "WatchAudioPlayManager = cleanReset")
    }

    private func playerPlayListFinished() {
        BuzWatchInfoLog.error(msg: "WatchAudioPlayManager = ListFinished")
        cleanReset()
    }

    private func playerStateChange(playState: WatchAudioPlayManagerAssetPlayState) {
        if let index = currentIndex, index < assetList.count {
            let assst = assetList[index]
            assst.playState = playState
            playerState = playState
        }
        NotificationCenter.default.post(name: NSNotification.Name(NSNotificationName.audioPlayStateChanged.rawValue), object: self)
        BuzWatchInfoLog.error(msg: "WatchAudioPlayManager.state = \(playState)  msgId: \(String(describing: currentAsset?.msgId))")
    }

    private func playerAssetChanged() {
        if let index = currentIndex, index < assetList.count {
            let assst = assetList[index]
            currentAsset = assst
            BuzWatchInfoLog.error(msg: "WatchAudioPlayManager playerAssetChanged = \(String(describing: currentAsset?.msgId))")
        }
    }

    private func playNext() {
        if WKExtension.shared().applicationState != .active {
            playerPlayListFinished()
            return
        }

        BuzWatchInfoLog.error(msg: "WatchAudioPlayManager  playNext")
        NotificationCenter.default.removeObserver(self)
        addVolumeChangeNotification()
        currentIndex = (currentIndex ?? 0) + 1
        play()
    }

    private func play() {
        if let index = currentIndex {
            if index >= assetList.count {
                playerPlayListFinished()
            } else {
                setAVAudioSession()
                playerAssetChanged()
                let asset = assetList[index]
                isPlaying = true
                playerStateChange(playState: .loading)
                play(asset: asset)
            }
        } else {
            playerPlayListFinished()
            BuzWatchInfoLog.error(msg: "WatchAudioPlayManager index 异常")
        }
    }

    private func play(asset: WatchAudioPlayManagerAsset) {
        getUrlWithAsset(asset: asset) { [weak self] url in
            guard let self else {
                return
            }
            guard let path = url else {
                self.playNext()
                return
            }
            let pathExtension = path.pathExtension
            if pathExtension == "mp3" {
                // voice from AI will use this format
                if asset.cryptKey != nil {
                    BuzWatchInfoLog.error(msg: "Not supported mp3 with encrypted data!")
                    self.playNext()
                    return
                }

                let avPlayer = self.avPlayer(url: path)
                avPlayer.volume = 1.0
                asset.player = avPlayer
                asset.fileExtension = pathExtension
                avPlayer.play()
                if let playerItem = avPlayer.currentItem {
                    playerItem.addObserver(self, forKeyPath: #keyPath(AVPlayerItem.status),
                                           options: [.old, .new],
                                           context: &self.playerItemContext)
                    NotificationCenter.default.addObserver(self,
                                                           selector: #selector(self.finishedPlaying(_:)),
                                                           name: NSNotification.Name.AVPlayerItemDidPlayToEndTime,
                                                           object: playerItem)
                    avPlayer.addPeriodicTimeObserver(forInterval: CMTime(value: 1, timescale: 1), queue: DispatchQueue.main) { _ in
                        if playerItem.isPlaybackLikelyToKeepUp == false {
                            self.playerStateChange(playState: .buffing)
                        }
                    }
                }
            } else {
                let tekiPlayer: TekiPlayer
                // only remoteURL content need decrypt
                if path.isFileURL {
                    tekiPlayer = self.tekiPlayer(url: path,
                                                 traceId: asset.traceId ?? "",
                                                 cryptKey: nil,
                                                 cryptIV: nil)
                } else {
                    tekiPlayer = self.tekiPlayer(url: path,
                                                 traceId: asset.traceId ?? "",
                                                 cryptKey: asset.cryptKey,
                                                 cryptIV: asset.cryptIV)
                }
                asset.player = tekiPlayer
                asset.fileExtension = pathExtension
                tekiPlayer.play()
                tekiPlayer.volume = 1.0
                tekiPlayer.delegate = self
            }
        }
    }

    private func tekiPlayer(url: URL,
                            traceId: String,
                            cryptKey: String?,
                            cryptIV: String?) -> TekiPlayer {
        var config = Teki.Configuration.shared
        config.setupAudioSession = false
        config.handleInterruption = false
        config.enableRdsReport = true
        let player = TekiPlayer(configuration: config)
        var item = Teki.MediaItem(url: url)

        var extraData: [Teki.MediaItem.ExtraDataKey: Any] = [:]
        extraData[.kMsgTraceID] = traceId
        if let key = cryptKey, let iv = cryptIV {
            extraData[.kAesKey] = key
            extraData[.kAesIV] = iv
        }
        item.extraData = extraData

        player.addMediaItem(item)
        return player
    }

    private func avPlayer(url: URL) -> AVPlayer {
        let playerItem: AVPlayerItem = AVPlayerItem(url: url)
        let avPlayer = AVPlayer(playerItem: playerItem)
        return avPlayer
    }

    private func setAVAudioSession() {
        let session = AVAudioSession.sharedInstance()
        do {
            try session.setCategory(AVAudioSession.Category.playback,
                                    mode: .default,
                                    policy: AVAudioSession.RouteSharingPolicy.default,
                                    options: [])
        } catch let error {
            fatalError("WatchAudioPlayManager.*** Unable to set up the audio session: \(error.localizedDescription) ***")
        }
    }
}

extension WatchAudioPlayManager {
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey: Any]?, context: UnsafeMutableRawPointer?) {
        guard context == &playerItemContext else {
            super.observeValue(forKeyPath: keyPath, of: object, change: change, context: context)
            return
        }

        if keyPath == #keyPath(AVPlayerItem.status) {
            let status: AVPlayerItem.Status
            if let statusNumber = change?[.newKey] as? NSNumber {
                status = AVPlayerItem.Status(rawValue: statusNumber.intValue)!
            } else {
                status = .unknown
            }
            switch status {
            case .readyToPlay:
                self.playerStateChange(playState: .playing)
            case .failed:
                self.playerStateChange(playState: .fail)
                self.playNext()
                BuzWatchInfoLog.error(msg: "WatchAudioPlayManager..failed")
            case .unknown:
                self.playNext()
                BuzWatchInfoLog.error(msg: "WatchAudioPlayManager..unknown")
            @unknown default:
                BuzWatchInfoLog.error(msg: "WatchAudioPlayManager.@unknown default")
            }
        }
    }

    @objc func finishedPlaying(_ notification: NSNotification) {
        playerStateChange(playState: .ended)
        playNext()
    }
}

extension WatchAudioPlayManager: TekiPlayerDelegate {
    func audioTranscoderSuccessSaveFile(fileType: String, path: String, oriUrl: String, contentLen: Int) {
    }

    func audioTranscoderEnd(isNormal: Bool, oriUrl: String) {
    }

    func playControlSuccessSaveFile(fileType: String, path: String, oriUrl: String, contentLen: Int) {
    }

    /// 播放器错误回调
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - error: 错误类型
    func playerErrorOccurred(_ player: TekiPlayer, error: Teki.PlayerError) {
        playNext()
    }

    /// 播放器状态变更回调
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - state: 状态
    func playerPlayStateDidChanged(_ player: TekiPlayer, state: Teki.State) {
        if state == Teki.State.buffering {
            playerStateChange(playState: .buffing)
        } else if state == Teki.State.playing {
            playerStateChange(playState: .playing)
        } else if state == Teki.State.ended {
            playerStateChange(playState: .ended)
            playNext()
        }
    }

    /// 播放器列表当前播放位置变化回调
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - position: 播放位置
    func playerPlayListCurrentChanged(_ player: TekiPlayer, position: Int) {
    }

    /// 播放器列表当前播放被移除回调
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - reason: 被移除原因
    func playerPlayListCurrentRemoved(_ player: TekiPlayer, reason: PlayListChangeReason) {
    }

    /// 播放器播放列表变化
    /// - Parameters:
    ///   - player: 播放器实例
    ///   - reason: 列表变化
    func playerPlayListChanged(_ player: TekiPlayer, reason: PlayListChangeReason) {
    }

    /// 播放器播放音质切换成功
    /// - Parameters:
    ///   - player: 播放器实例
    func playerChangeAudioQualitySuccess(_ player: TekiPlayer) {
    }

    /// 定时功能，剩余播放时长回调
    /// - Parameters:
    ///   - playControl: 播放器实例
    ///   - remainingTime: 剩余时长（单位：s）
    func playerTimeRemainingUpdate(_ player: TekiPlayer, remainingTime: TimeInterval) {
    }

    /// 定时功能，剩余播放节目数量回调
    /// - Parameters:
    ///   - playControl: 播放器实例
    ///   - remainingItem: 剩余播放节目数量
    func playerItemRemainingUpdate(_ player: TekiPlayer, remainingItem: Int) {
    }

    /// 播放列表结束，没有下一个节目（只有顺序播放会回调）
    /// - Parameter player: 播放器实例
    func playerPlayListFinished(_ player: TekiPlayer) {
    }

    /// 当前节目缓冲进度，间隔1s回调
    /// - Parameters:
    ///   - playControl: 播放器实例
    ///   - bufferProgress: 缓冲进度(0~1.0)
    func playControlBufferProgressUpdate(_ player: TekiPlayer, bufferProgress: Float) {
    }

    /// 当前节目播放进度，回调间隔通过Configuration.progressTimeInterval 设置
    /// - Parameters:
    ///   - playControl: 播放器实例
    ///   - playbackPosition: 播放进度
    func playControlPlaybackPositionUpdate(_ player: TekiPlayer, playbackPosition: Teki.PlaybackPosition) {
    }

    func playControlRdsReportData(_ event: String, label: [String: Any]) {
        WatchDataSourceManager.shared.sendEventTrackingToRDS(event: event, label: label)
    }
}

// MARK: pre-download encrypted data

extension WatchAudioPlayManager {
    private func download(from url: URL) async throws -> URL {
        let session = URLSession(configuration: .default)
        let (location, _) = try await session.download(from: url)
        let destination = NSTemporaryDirectory() + url.path.md5 + "." + url.pathExtension
        let destinationURL = URL(fileURLWithPath: destination)
        try FileManager.default.moveItem(at: location, to: destinationURL)
        return destinationURL
    }
}

extension WatchAudioPlayManager {
    private func getUrlWithAsset(asset: WatchAudioPlayManagerAsset, completion: @escaping (URL?) -> Void) {
        if asset.url.hasPrefix("http") || asset.url.hasPrefix("https") { /// remote file
            if let url = URL(string: asset.url) {
                completion(url)
            } else {
                BuzWatchInfoLog.error(msg: "WatchAudioPlayManager: Invalid url \(asset.url)")
                completion(nil)
            }
            BuzWatchInfoLog.error(msg: "WatchAudioPlayManager：有远程链接 = \(asset.url)")
        } else { 
            /// local file
            BuzWatchInfoLog.error(msg: "WatchAudioPlayManager：请求数据链接msgId = \(String(describing: asset.msgId)) convType = \(String(describing: asset.convType))")
            ////拉取远程地址
            WatchDataSourceManager.shared.getAudioRemoteUrl(msgId: asset.msgId ?? 0, convType: asset.convType ?? 1) { url, isSucceed in
                BuzWatchInfoLog.error(msg: "WatchAudioPlayManager：获取远程链接 = \(url) isSucceed = \(isSucceed)")
                if isSucceed {
                    if let url = URL(string: url) {
                        completion(url)
                    } else {
                        completion(nil)
                        BuzWatchInfoLog.error(msg: "WatchAudioPlayManager: Invalid url \(asset.url)")
                    }
                } else {
                    BuzWatchInfoLog.error(msg: "WatchAudioPlayManager：拉取本地文件")
                    var filePath = asset.url
                    if asset.url.hasPrefix("file:///") {
                        filePath = filePath.replacingOccurrences(of: "file://", with: "")
                    }
                    LocalFileRequest(targetId: asset.targetId ?? 0, msgId: asset.msgId ?? 0, filePath: filePath) { isSucceed, message, error in
                        BuzWatchInfoLog.error(msg: "WatchAudioPlayManager 点击播放拉取文件 : isSucceed = \(isSucceed) error = \(error) message = \(String(describing: message))")
                        if isSucceed {
                            if let filePath = message?["filePath"] as? String {
                                let localFileUrl = URL(fileURLWithPath: filePath)
                                completion(localFileUrl)
                            } else {
                                let params = message?["params"] as? Dictionary<String, Any>
                                if let filePath = params?["filePath"] as? String {
                                    let localFileUrl = URL(fileURLWithPath: filePath)
                                    completion(localFileUrl)
                                } else {
                                    completion(nil)
                                }
                            }
                        } else {
                            completion(nil)
                        }
                    }.execute()
                }
            }
        }
    }
}
