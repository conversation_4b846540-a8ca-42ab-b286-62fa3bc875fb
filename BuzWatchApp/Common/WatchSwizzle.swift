//
//  WatchSwizzle.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/16.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation

@objcMembers
class WatchSwizzle : NSObject {
    static func exchange(src : Selector, dest : Selector, instanceClass : AnyClass?) {
        let originalMethod = class_getInstanceMethod(instanceClass, src)
        let swizzledMethod = class_getInstanceMethod(instanceClass, dest)
        if let originalMethod = originalMethod, let swizzledMethod = swizzledMethod {
          method_exchangeImplementations(originalMethod, swizzledMethod)
        }
    }
}
