//
//  WatchFont.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/18.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import UIKit
import SwiftUI

extension Font {
    //MARK: - roboto字体
    static func watch_bold(size : CGFloat) -> Font
    {
        return Font.init(UIFont.boldSystemFont(ofSize: size))
    }
    
    static func watch_medium(size : CGFloat) -> Font
    {
        return Font.init(UIFont.systemFont(ofSize: size))
    }

    static func watch_regular(size : CGFloat) -> Font
    {
        return Font.init(UIFont.systemFont(ofSize: size))
    }

    static func watch_light(size : CGFloat) -> Font
    {
        return Font.init(UIFont.systemFont(ofSize: size))
    }

}
