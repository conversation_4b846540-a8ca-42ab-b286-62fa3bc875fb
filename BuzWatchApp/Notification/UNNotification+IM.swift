//
//  UNNotification+IM.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/25.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import UserNotifications
import BuzAPNsCenter

extension UNNotification {
    func userInfo() -> [AnyHashable : Any] {
        return self.request.content.userInfo
    }
    
    static func getActionData(userInfo : [AnyHashable : Any]) -> [String : Any]? {
        var action : [String : Any]?
        
        if let apsDict = userInfo[BuzPushInfoKey.aps] as? [String : Any],
           let alertDict = apsDict[BuzPushInfoKey.alert] as? [String : Any],
           let keyString = alertDict[BuzPushInfoKey.key] as? String ,
           let dict = keyString.toDict(),
           let taction = dict[BuzPushInfoKey.action] as? [String : Any]
        {
            action = taction
        } else if let taction = userInfo[BuzPushInfoKey.action] as? [String : Any] {
            action = taction
        }
        
        return action
    }
    
    static func messageType(userInfo : [AnyHashable : Any]) -> MessageType {
        if let action = UNNotification.getActionData(userInfo: userInfo) {
            if let im5 = action["IM5"] as? [String : Any] {
                if let type = im5["msgType"] as? Int {
                    return MessageType.init(rawValue: type) ?? .unknow
                }
            } else if let appDataString = action["appData"] as? String, let appData = appDataString.toDict() {
                if let typeValue = appData[BuzPushInfoKey.type] as? Int32 ,
                    let type = BuzAPNs.PushType.init(rawValue: typeValue) {
                    if type == .privateChat {
                        if let im5 = appData[BuzPushInfoKey.pushExtra] as? [String : Any] {
                            if let type = im5[BuzPushInfoKey.imMsgType] as? Int {
                                return MessageType.init(rawValue: type) ?? .unknow
                            }
                        }
                    } else if type == .groupChat {
                        if let im5 = appData[BuzPushInfoKey.pushExtra] as? [String : Any] {
                            if let type = im5[BuzPushInfoKey.imMsgType] as? Int {
                                return MessageType.init(rawValue: type) ?? .unknow
                            }
                        }
                    }
                }
            }
        }
        
        return .unknow
    }
    
    static func targetId(userInfo : [AnyHashable : Any]) -> Int64? {
        if let userInfo = self.messageInfo(userInfo: userInfo) {
//            NSLog("userInfo:::::\(userInfo)")
            if let convType = userInfo["convType"] as? Int {
                if convType == 3 {
                    if let targetId = userInfo["groupId"] as? Int64 {
                        return targetId
                    }
                }else if convType == 1 {
                    if let userId = userInfo["userId"] as? Int64 {
                        return userId
                    }
                }
            }
        }
        
        return nil
    }
    
    static func messageInfo(userInfo : [AnyHashable : Any]) -> [String : Any]? {
        if let action = UNNotification.getActionData(userInfo: userInfo) {
            if let appDataString = action["appData"] as? String, let appData = appDataString.toDict() {
                var result : [String : Any] = [:]
                
                if let sendUserInfo = appData["senderUserInfo"] as? [String : Any] {
                    result.merge(sendUserInfo) { (current, _) in current }
                }
                
                if let groupInfo = appData[BuzPushInfoKey.pushExtra] as? [String : Any] {
                    if let groupId = groupInfo["groupId"] as? Int64 {
                        result["groupId"] = groupId
                    }
                }
                
                if let im5 = action["IM5"] as? [String : Any] {
                    result["targetId"] = im5["targetId"]
                    result["svrMsgId"] = im5["svrMsgId"]
                    result["convType"] = im5["convType"]
                }
                
                return result
            }
        }
        
        return nil
    }
    
    static func voiceInfo(userInfo : [AnyHashable : Any]) -> [String : Any]? {
        if let action = UNNotification.getActionData(userInfo: userInfo) {
            var result : [String : Any] = [:]
            
            if let im5 = action["IM5"] as? [String : Any] {
                result["targetId"] = im5["targetId"]
                result["svrMsgId"] = im5["svrMsgId"]
                result["convType"] = im5["convType"]
            }
            
            return result
        }
        
        return nil
    }
}
