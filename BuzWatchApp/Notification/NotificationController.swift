//
//  NotificationController.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/8/4.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import WatchKit
import SwiftUI
import UserNotifications
import BuzConfig
import Localizable

enum NotificationActionType : Int {
    case view = 1
    case sendMessage = 2
    case playVoice = 3
}

final class NotificationController: WKUserNotificationHostingController<NotificationView> {
    
    static let category : String = BuzConfig.notificationCategoryIdentifier
    
    var  content : UNNotificationContent?
    var date : Date?
    var title : String?
    var contentBody : String?
    
    var isInteractive : Bool {
        get {
            return true
        }
    }
    
    override var body: NotificationView {
        return NotificationView(date: date,
                                title: self.title ?? "Unknown",
                                contentBody: self.contentBody ?? "Sent you a message")
    }

    override func didReceive(_ notification: UNNotification) {
        Localizable.setGroupId(groupId: BuzConfig.groupStoreId)
        content = notification.request.content
        date = notification.date
        self.title = content?.title
        contentBody = content?.body
        let type = UNNotification.messageType(userInfo: notification.userInfo())
        self.notificationActions = self.getNotificationActions()
        
        if type == .text {

        } else if type == .voiceText || type == .voice || type == .newVoiceText || type.isVoiceEmoji() {

            self.notificationActions = self.getVoiceNotificationActions()
        } else if type == .image {
        } else {
            self.notificationActions = []
        }
    }
    
    func getNotificationActions() -> [UNNotificationAction]{
        return  [
            UNNotificationAction.init(identifier: "\(NotificationActionType.view.rawValue)", title: Localizable.notification_view_message,
                                      options: .foreground),
            UNNotificationAction.init(identifier: "\(NotificationActionType.sendMessage.rawValue)", title: Localizable.notification_reply_message, options: .foreground)
        ]
    }
    
    func getVoiceNotificationActions() -> [UNNotificationAction]{
        return  [
            UNNotificationAction.init(identifier: "\(NotificationActionType.playVoice.rawValue)", title: Localizable.notification_play_voice_message,
                                      options: .foreground),
            UNNotificationAction.init(identifier: "\(NotificationActionType.sendMessage.rawValue)", title: Localizable.notification_reply_message, options: .foreground)
        ]
    }
    
    

    
    override func didReceive(_ notification:
                             UNNotification, withCompletion
                             completionHandler: @escaping (WKUserNotificationInterfaceType) -> Void) {
        if UNNotification.messageType(userInfo: notification.userInfo()) == .unknow {
            completionHandler(.default)
        } else {
            completionHandler(.custom)
        }
    }
}

///dynamic long look
struct NotificationView: View {
    var date : Date?
    var title : String
    var contentBody : String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0, content: {
            Text(self.title)
                .font(.system(size: 17, weight: .regular, design: .default))
            Text(self.contentBody)
                .font(.system(size: 17, weight: .regular, design: .default))
        }).background(Color.clear)
    }
}
