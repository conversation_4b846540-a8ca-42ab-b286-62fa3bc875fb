//
//  WatchHistoryListObserver.swift
//  BuzWatchApp
//
//  Created by l<PERSON>hi on 2023/7/26.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import Combine
import SwiftUI
import BuzConfig

class WatchHistoryListObserver : ObservableObject {
    
    var echo = PassthroughSubject<WatchHistoryListModel, Never>()
    var subscription: AnyCancellable? = nil
    var model : WatchHomeListModel
    
    @Published var isLoadData = false
    @Published var isScroll = false
    @Published var isFirstLoadData = true
    @Published var isHaveMoreHistory = false
    @Published var isScroolToTop = false
    @Published var isLoadSucceed = false
    
    @Published var messageList = Array<WatchHistoryListModel>()
    
    init(model : WatchHomeListModel = WatchHomeListModel()){
        self.model = model
        WatchDataSourceManager.shared.weakWatchHistoryListObserver.append(self)
    }
    
    
    func subscribe() {
        subscription = echo.debounce(for: .seconds(1), scheduler: DispatchQueue.main)
            .sink(receiveValue: { [weak self] message in
                self?.messageList.append(message)
            })
    }
    
    func sendLastMessage(_ message: WatchHistoryListModel) {
        messageList.append(message)
    }
    
    func send(_ message: WatchHistoryListModel) {
        messageList.append(message)
    }
    
    func echo(_ message: WatchHistoryListModel) {
        echo.send(message)
    }
    
    func receiveMessage(_ messages : [WatchHistoryListModel]){
        var list = Array<WatchHistoryListModel>()
        messages.forEach { obj in
            if obj.target == Int64(self.model.userId ?? "0") {
                list.append(obj)
            }
        }
        if list.count > 0{
            self.messageList = self.messageList + list
        }
        self.isScroolToTop = false
        self.isScroll.toggle()
    }
    
    func updateMessage(_ messages : [WatchHistoryListModel]){
        messages.forEach { item in
            self.messageList.forEach { obj in
                if item.msgId == obj.msgId{
                    obj.content = item.content
                    obj.voiceTextType = item.voiceTextType
                    obj.voiceTextContent = item.voiceTextContent
                }
            }
        }
        self.isScroolToTop = false
        self.isScroll.toggle()
    }
    
    func deleteMessage(_ messages : [WatchHistoryListModel]){
        messages.forEach { item in
            self.messageList.removeAll { obj in
                item.msgId == obj.msgId
            }
        }
        self.isScroolToTop = false
        self.isScroll.toggle()
    }
    
   private func getHistoryList(boundaryMsgId : Int32 , completionBlock : (() -> Void)?){
              
       WatchDataSourceManager.shared.getHistoryList(targetId: model.userId ?? "0",
                                                    isGroup: model.isGroup,
                                                    boundaryMsgId: boundaryMsgId) { isSucceed, array, maxImMsgHistory in
           if isSucceed , let list = array {
               if boundaryMsgId > 0{
                   self.isScroolToTop = true
                   self.messageList = list + self.messageList
               }else{
                   self.isScroolToTop = false
                   self.messageList = list
               }
               
               self.isHaveMoreHistory = list.count >= BuzConfig.defaultMessagePageCount && self.messageList.count < maxImMsgHistory
               BuzWatchInfoLog.info(msg: "WatchHistoryListObserver   maxImMsgHistory:\(maxImMsgHistory)")
               if let block = completionBlock{
                   block()
               }
               
               DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                   self.isScroll.toggle()
               }
               WatchDataSourceManager.shared.eventTracking(label: ["exclusive_id" : "RB2023081404","page_business_type" : self.model.isGroup ? "group" : "chat","is_success" : isSucceed])
           }else{
               if let block = completionBlock{
                   block()
               }
           }
           self.isLoadSucceed = isSucceed
           self.isFirstLoadData = false
           self.isLoadData = false
        }
    }
    
    func getNestData(completionBlock : @escaping () -> Void){
        self.isLoadData = true
        self.isFirstLoadData = true
        self.getHistoryList(boundaryMsgId: 0) {
            completionBlock()
        }
    }
    
    func getPreviousData(completionBlock :  @escaping () -> Void){
        self.isLoadData = true
        DispatchQueue.main.asyncAfter(deadline: .now() + 3.0, execute: DispatchWorkItem.init(block: {
            self.getHistoryList(boundaryMsgId: Int32(self.messageList.first?.msgId ?? 0)) {
                completionBlock()
            }
        }))
    }
    
    deinit {
        WatchDataSourceManager.shared.weakWatchHistoryListObserver.removeAll { WatchHistoryListObserver in
            WatchHistoryListObserver === self
        }
        subscription?.cancel()
    }
    
}
