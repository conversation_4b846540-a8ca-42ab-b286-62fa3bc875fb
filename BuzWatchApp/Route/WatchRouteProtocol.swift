//
//  WatchRouteProtocol.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/23.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import SwiftUI
import BuzConfig

class WatchRouteItem {
    var presents : [String : Binding<PresentationMode>] = [:]
    var keys : [String] = []
    var data : Any?
    var url : String?
    var identityNumber : Int64 = 0
    var previousUrl : String?
    var previousUrlSetTs : TimeInterval?
    
    var identity : String {
        get {
            return "\(identityNumber)"
        }
    }
    
    func appendPresentMode(key : String, present : Binding<PresentationMode>) {
        presents.removeValue(forKey: key)
        keys.insert(key, at: 0)
        presents[key] = present
    }
    
    func removeAllPresentMode() {
        keys.removeAll()
        presents.removeAll()
    }
    
    func getAndIncreaseIdentity() -> String {
        if let ts = self.previousUrlSetTs, self.url == nil {
            if Date().timeIntervalSince1970 - ts < 0.01 {
                self.url = self.previousUrl
            }
        }
        
        identityNumber = identityNumber + 1
        return self.identity
    }
    
    func clearUrl() {
        self.previousUrlSetTs = Date().timeIntervalSince1970
        self.previousUrl = self.url
        self.url = nil
    }
    
    func getUrl() -> String? {
        if let ts = self.previousUrlSetTs, self.url == nil {
            if Date().timeIntervalSince1970 - ts < 0.01 {
                return self.previousUrl
            }
        }
        
        return self.url
    }
    
    init(data: Any? = nil, url: String?) {
        self.data = data
        self.url = url
    }
}

struct WatchRouteProtocol {
    static func newViewDeallocObject() -> ViewDeallocObject {
        let deallocObj = ViewDeallocObject.init(identity: WatchAppDelegate.sharedRoute.getAndIncreaseIdentity())
        deallocObj.dealloc = { identity in
            if WatchAppDelegate.sharedRoute.identity == identity {
                WatchAppDelegate.sharedRoute.clearUrl()
            }
        }
        return deallocObj
    }
    
    @ViewBuilder
    static func routeView(item : WatchRouteItem) -> some View {
        if item.getUrl() == "watch_mode://" {
            WatchSettingVoiceModeView(mode: item.data as? PersonalStatusType ?? .available,
                                      deallocObj: newViewDeallocObject()).id("watch_mode://")
        } else if item.getUrl() == "watch_imView://" {
            let model = item.data as? WatchHomeListModel ?? WatchHomeListModel()
            WatchHistoryContentView(viewModel: WatchHistoryListObserver(model: model), model: model,
                                    deallocObj: newViewDeallocObject()).id("watch_imView://")
        }
    }
}
