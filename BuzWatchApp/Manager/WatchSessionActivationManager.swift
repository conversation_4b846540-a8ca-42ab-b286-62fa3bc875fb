//
//  Connectivity.swift
//  com.interfun.buz.watch Watch App
//
//  Created by lizhi on 2023/7/18.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import WatchConnectivity
import WatchKit
import WidgetKit

class WatchSessionRequestBlock : NSObject{
    var block : (() -> Void)?
    var date : Date?
    var isExecute = false
}
 
final class WatchSessionActivationManager: NSObject, WCSessionDelegate  {
    
    static let shared = WatchSessionActivationManager()
    
    var sessionActivationState: WCSessionActivationState { wcSession.activationState }
    var weakActivatedArray = Array<WatchSessionRequestBlock>()
    var weakGetLocalFiledArray = Array<WatchRequest>()
    let wcSession: WCSession
    var activationState = WCSessionActivationState.notActivated
    var appActive = true
    
    override init() {
        self.wcSession = WCSession.default
        super.init()
        self.wcSession.delegate = self
    }
    
    func activate(completionHandler:(() -> Void)?){
        BuzWatchInfoLog.info(msg:"WatchSessionActivationManager 执行activate操作")
        if sessionActivationState == .activated && self.wcSession.isReachable{
            if let block = completionHandler{
                block()
            }
        }else{
            self.wcSession.activate()
            if let block = completionHandler{
                let obj = WatchSessionRequestBlock()
                obj.block = block
                obj.date = Date()
                self.weakActivatedArray.append(obj)
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 5.0, execute: DispatchWorkItem.init(block: {
                    if !obj.isExecute{
                        obj.isExecute = true
                        if let block = obj.block{
                            block()
                        }
                        BuzWatchInfoLog.info(msg:"WatchSessionActivationManager 执行超时操作")
                    }
                }))
            }
        }
    }
    
    func sessionCompanionAppInstalledDidChange(_ session: WCSession) {
        BuzWatchInfoLog.debug(msg:"WatchSessionActivationManager sessionCompanionAppInstalledDidChange")
    }
    
    func session(_ session: WCSession, activationDidCompleteWith activationState: WCSessionActivationState, error: Error?) {
        BuzWatchInfoLog.debug(msg:"WatchSessionActivationManager WCSessionActivationState = \(activationState.rawValue)")
        self.activationState = activationState
        self.execCompletionBlock()
    }
    
    func sessionReachabilityDidChange(_ session: WCSession) {
        BuzWatchInfoLog.debug(msg:"WatchSessionActivationManager session.isReachable = \(session.isReachable)")
        self.execCompletionBlock()
    }
    
    func execCompletionBlock(){
        if self.wcSession.activationState == .activated && (self.wcSession.isReachable){
            self.weakActivatedArray.forEach { obj in
                if !obj.isExecute{
                    obj.isExecute = true
                    if let block = obj.block{
                        block()
                    }
                }
            }
        }
    }
}


extension WatchSessionActivationManager {
    
    ///发送IM消息
    func sendIMWithVoiceFile(url : URL , duration : Double , isGroup : Bool , targetId : String , traceId : String , ntpTime : Int64 ,completion : ((Bool) -> ())?){
        BuzWatchInfoLog.info(msg:"发送IM消息")
//        let data = WatchToIphoneCommonDataTransferClass()
//        data.dataType = WatchToIphoneCommonDataType.activateApp.rawValue
//        let json = data.yy_modelToJSONObject()
//        if let dic = json as? Dictionary<String, Any> {
//            WCSession.default.sendMessage(dic) { value in
//                WatchSessionActivationManager.shared.activate {
//                    let dic = ["fileName": url.absoluteURL.lastPathComponent ,
//                               "duration" : "\(duration)",
//                               "isGroup" : isGroup,
//                               "targetId" : targetId ,
//                               "traceId" : traceId ,
//                               "ntpTime" : ntpTime ,
//                    ] as [String : Any]
//                    WCSession.default.transferFile(url, metadata: dic)
//                }
//                if let block = completion {
//                    block(true)
//                }
//                BuzWatchInfoLog.debug(msg:"sendFile: value = \(value)")
//            } errorHandler: { error in  ///  session.isReachable = false 无法发送实时消息，改为直接发送后台消息
//                let dic = ["fileName": url.absoluteURL.lastPathComponent ,
//                           "duration" : "\(duration)",
//                           "isGroup" : isGroup,
//                           "targetId" : targetId
//                ] as [String : Any]
//                WCSession.default.transferFile(url, metadata: dic)
//                if let block = completion {
//                    block(true)
//                }
//                BuzWatchInfoLog.debug(msg:"sendFile: error = \(error)")
//            }
//        }
        
        let dic = ["fileName": url.absoluteURL.lastPathComponent ,
                   "duration" : "\(duration)",
                   "isGroup" : isGroup,
                   "targetId" : targetId ,
                   "traceId" : traceId ,
                   "ntpTime" : ntpTime ,
        ] as [String : Any]
        WCSession.default.transferFile(url, metadata: dic)
        if let block = completion {
            block(true)
        }
        BuzWatchInfoLog.debug(msg:"WatchSessionActivationManager sendFile: value = \(dic)")
    }
    
    ///发送文件消息
    func sendFile(url : URL ,params : Dictionary<String, Any>){
        BuzWatchInfoLog.info(msg:"发送文件消息: url = \(url.absoluteString) parms = \(params)")
//        let data = WatchToIphoneCommonDataTransferClass()
//        data.dataType = WatchToIphoneCommonDataType.activateApp.rawValue
//        let json = data.yy_modelToJSONObject()
//        if let dic = json as? Dictionary<String, Any> {
//            WCSession.default.sendMessage(dic) { value in
//                WatchSessionActivationManager.shared.activate {
//                    WCSession.default.transferFile(url, metadata: params)
//                }
//                BuzWatchInfoLog.debug(msg:"sendFile: value = \(value)")
//            } errorHandler: { error in
//                BuzWatchInfoLog.debug(msg:"sendFile: error = \(error)")
//            }
//        }
//        WatchSessionActivationManager.shared.activate {
//            WCSession.default.transferFile(url, metadata: params)
//        }
        WCSession.default.transferFile(url, metadata: params)
    }
    
    ///发送文件状态
    func session(_ session: WCSession, didFinish userInfoTransfer: WCSessionUserInfoTransfer, error: Error?) {
        BuzWatchInfoLog.debug(msg:"WatchSessionActivationManager isTransferring：\(userInfoTransfer.isTransferring) , userInfoTransfer = \(userInfoTransfer.userInfo)")
    }
    
    ///发送实时消息
    func request(isForceActive : Bool = false, params : Dictionary<String, Any> , completionHandler:((Bool , Dictionary<String, Any>? , Error?) -> Void)? = nil){
        BuzWatchInfoLog.info(msg:"WatchSessionActivationManager 开启实时数据请求数据、激活session = \(params) appActive = \(appActive)")
        if self.appActive || isForceActive{
            WatchSessionActivationManager.shared.activate {
                BuzWatchInfoLog.info(msg:"发送请求数据 = \(params)")
                WCSession.default.sendMessage(params) { responseValue in
                    if let block = completionHandler{
                        block(true , responseValue , nil)
                    }
                    BuzWatchInfoLog.info(msg:"请求数据response: = \(responseValue)")
                } errorHandler: { error in
                    if let block = completionHandler{
                        block(false , nil , nil)
                    }
                    BuzWatchInfoLog.info(msg:"请求数据error：\(error)")
                }
            }
        }else{
            if let block = completionHandler{
                block(false , nil , nil)
            }
        }
    }
    
    ///发送Dictionary[实时传输,需要保证watch激活状态]
    func sendMessageWithDic(dic : Dictionary<String, Any>){
        BuzWatchInfoLog.info(msg:"WatchSessionActivationManager 发送dic数据")
        WCSession.default.sendMessage(dic) { value in
            BuzWatchInfoLog.debug(msg:"收到dic成功回调: = \(value)")
        } errorHandler: { error in
            BuzWatchInfoLog.debug(msg:"收到dic失败error：\(error)")
        }
    }
    
    ///发送Data[实时传输,需要保证watch激活状态]
    func sendMessageWithData(data : Data){
        BuzWatchInfoLog.info(msg:"WatchSessionActivationManager 发送data数据")
        WCSession.default.sendMessageData(data) { data in
            BuzWatchInfoLog.debug(msg:"发送data成功回调：\(data)")
        } errorHandler: { error in
            BuzWatchInfoLog.debug(msg:"收到data失败error：\(error)")
        }
    }
}


extension WatchSessionActivationManager {
    ///收到文件
    func session(_ session: WCSession, didReceive file: WCSessionFile) {
        BuzWatchInfoLog.debug(msg:"收到文件消息: = \(String(describing: file.metadata))")
        WatchDataSourceManager.shared.didReceive(didReceive: file)
        self.weakGetLocalFiledArray.forEach { block in
            block.onReceive(didReceive: file)
        }
    }
    
    ///收到交互消息Dic
    func session(_ session: WCSession, didReceiveMessage message: [String : Any], replyHandler: @escaping ([String : Any]) -> Void) {
        BuzWatchInfoLog.debug(msg:"收到交互消息dic replyHandler = \(message)")
        let dataSource = WatchToIphoneCommonDataTransferClass.yy_model(with: message)
        if dataSource?.dataType == WatchToIphoneCommonDataType.getWatchIsCanSpeaking.rawValue {
            let data = WatchToIphoneCommonDataTransferClass()
            data.dataType = WatchToIphoneCommonDataType.getWatchIsCanSpeaking.rawValue
            data.params = ["isCanSpeaking" : WatchDataSourceManager.shared.isCanSpeaking]
            let json = data.yy_modelToJSONObject()
            if let dic = json as? Dictionary<String, Any> {
                wcSession.sendMessage(dic) { message in
                    replyHandler(dic)
                } errorHandler: { error in
                    replyHandler(dic)
                }
            }else{
                replyHandler(message)
            }
        }else{
            WatchDataSourceManager.shared.didReceiveMessage(message: message)
            replyHandler(message)
        }
    }
    
    ///收到交互消息data
    func session(_ session: WCSession, didReceiveMessageData messageData: Data, replyHandler: @escaping (Data) -> Void) {
        BuzWatchInfoLog.debug(msg:"收到交互消息data replyHandler = \(messageData)")
        replyHandler(messageData)
    }
    
    ///收到交互消息didReceiveUserInfo
    func session(_ session: WCSession, didReceiveUserInfo userInfo: [String : Any] = [:]) {
        BuzWatchInfoLog.info(msg:"收到iphone后台.didReceiveUserInfo消息 = \(userInfo)")
        WatchDataSourceManager.shared.didReceiveUserInfo(didReceiveUserInfo: userInfo)
//        if #available(watchOS 9.0, *) {
//            WatchDataSourceManager.shared.complicationUserInfo = userInfo
//
//            WatchDataSourceManager.shared.saveUsr(userId: 112)
//
//            WidgetCenter.shared.reloadAllTimelines()
//
//        } else {
//            // Fallback on earlier versions
//        }
//        wcSession.transferUserInfo(userInfo)
    }
    
    func session(_ session: WCSession, didReceiveApplicationContext applicationContext: [String : Any]) {
        BuzWatchInfoLog.info(msg:"收到iphone后台.applicationContext消息 = \(applicationContext)")
        WatchDataSourceManager.shared.didReceiveUserInfo(didReceiveUserInfo: applicationContext)
    }
    
    func session(_ session: WCSession, didFinish fileTransfer: WCSessionFileTransfer, error: Error?) {
        BuzWatchInfoLog.debug(msg:"收到iphone后台fileTransfer消息 = \(fileTransfer)")
    }
    
    ///收到交互消息didReceiveMessage
    func session(_ session: WCSession, didReceiveMessage message: [String : Any]) {
        BuzWatchInfoLog.debug(msg:"收到iphone交互消息 = \(message)")
    }
}
