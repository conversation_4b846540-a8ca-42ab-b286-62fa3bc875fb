//
//  WatchRequest.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/3.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import WatchConnectivity
//
//protocol WatchRequestProtocol {
//    ///收到文件
//    func session(_ session: WCSession, didReceive file: WCSessionFile) -> Void
//}

class WatchRequest : NSObject {
    let traceId = UUID().uuidString
    var requestType : WatchToIphoneCommonDataType
    var timeout : Int = 3
    var completionHandler : ((Bool , Dictionary<String, Any>? , Error?) -> Void)? = nil
    
    init(requestType : WatchToIphoneCommonDataType) {
        self.requestType = requestType
    }
    
    @objc
    func onTimeout(data : WatchRequest) {
        self.onFailure(error: nil)
    }
    
    func execute() -> Void {
        self.perform(#selector(onTimeout), with: self, afterDelay: TimeInterval(self.timeout))
    }
    
    func onFailure(error : Error?) {
        if let block = completionHandler {
            block(false , nil , error)
        }
        
        self.completionHandler = nil
    }
    
    func onReceive(didReceive file: WCSessionFile) -> Void {
    }
    
    func transferToProtocol(params : [String : Any]) -> [String:Any] {
        return ["dataType":self.requestType.rawValue, "params" : params]
    }
}

//extension WatchRequest {
//    ///收到文件
//    func session(_ session: WCSession, didReceive file: WCSessionFile) -> Void {
//        self.onReceive(didReceive: file)
//    }
//}
