//
//  SettingManager.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/18.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import Localizable

class SettingManager {
    var volumeValue: Float  {
        get {
            if UserDefaults.standard.object(forKey: "volume") == nil {
                UserDefaults.standard.set(1.0, forKey: "volume")
                return 1.0
            }
            
            return UserDefaults.standard.float(forKey: "volume")
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "volume")
        }
    }
    
    var activeComplications: String  {
        get {
            if UserDefaults.standard.object(forKey: "complications") == nil {
                return ""
            }
            
            return UserDefaults.standard.string(forKey: "complications") ?? ""
        }
        set {
            UserDefaults.standard.set(newValue, forKey: "complications")
        }
    }
    
    var mode: PersonalStatusType = .available {
        didSet  {
            WatchDataSourceManager.shared.saveMode(mode: self.mode)
            
            if oldValue != self.mode {
                NotificationCenter.default.post(name: NSNotification.Name(NSNotificationName.modeChangeNotification.rawValue),
                                                object: self.mode)
            }
        }
    }
    
    var extendData : [AnyHashable : Any]?
    
    static let shared = SettingManager.init()
    
    func registerCenter() {
        NotificationCenter.default.addObserver(self, selector: #selector(onStatusUpdate),
                                               name: NSNotification.Name(NSNotificationName.updatePersonalStatueType.rawValue),
                                               object: nil)
    }
    
    @objc
    func onStatusUpdate() {
        self.startLoad {}
    }
    
    func startLoad(callback : @escaping () -> Void) {
        WatchDataSourceManager.shared.getSetting { isSuccess, mode, volume, language in
            DispatchQueue.main.async {
                self.mode = PersonalStatusType.init(rawValue: mode) ?? .available
                
                if Localizable.currentLanguage != language && language != "" {
                    Localizable.changeTo(language: language)
                    NotificationCenter.default.post(name: NSNotification.Name(NSNotificationName.languageChangeNotification.rawValue),
                                                    object: language)
                }
                
                callback()
            }
        }
    }
}
