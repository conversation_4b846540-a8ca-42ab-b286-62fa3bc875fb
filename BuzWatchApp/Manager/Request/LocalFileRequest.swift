//
//  LocalFileRequest.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/3.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import WatchConnectivity

class LocalFileRequest : WatchRequest {
    var completionFileHandler : ((Bool , Dictionary<String, Any>? , Error?) -> Void)? = nil
    var localFilePath : String
    var targetId : Int64
    var msgId : Int64
    var filePath : String
    
    init(targetId: Int64, msgId: Int64, filePath: String, completionFileHandler: ((Bool, Dictionary<String, Any>?, Error?) -> Void)? = nil) {
        self.completionFileHandler = completionFileHandler
        self.targetId = targetId
        self.msgId = msgId
        self.filePath = filePath
        let md5File = NSTemporaryDirectory() + self.filePath.md5 + "." + (self.filePath as NSString).pathExtension
        self.localFilePath = md5File
        super.init(requestType: .getLocalFile)
    }
    
    override func onFailure(error : Error?) {
        if let block = completionFileHandler {
            block(false , nil , error)
        }
        
        self.completionFileHandler = nil
        WatchSessionActivationManager.shared.weakGetLocalFiledArray.removeAll { request in
            return request === self
        }
    }
    
    override func execute() {
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.getLocalFile.rawValue
        data.params = ["targetId" : targetId ,
                       "msgId" : msgId ,
                       "filePath" :  filePath]
        
        let params : [String : Any] = ["filePath":filePath, "targetId":targetId, "msgId":msgId, "traceId":traceId]
        let md5File = self.localFilePath
        self.localFilePath = md5File
        
        if FileManager.default.fileExists(atPath: md5File) {
            let param = NSMutableDictionary.init(dictionary: params)
            param["filePath"] = md5File
            if let block = completionFileHandler {
                block(true , param as? Dictionary<String, Any> , nil)
            }
            
            self.completionFileHandler = nil
            return
        } else if FileManager.default.fileExists(atPath: self.filePath) {
            if let block = completionFileHandler {
                block(true , params, nil)
            }
            
            self.completionFileHandler = nil
            return
        }
        
        BuzWatchInfoLog.debug(msg:"开启请求数据file = \(params)")
        super.execute()
        
        WatchSessionActivationManager.shared.activate {
            WatchSessionActivationManager.shared.weakGetLocalFiledArray.append(self)
            WCSession.default.sendMessage(self.transferToProtocol(params: params)) { responseValue in
                BuzWatchInfoLog.debug(msg:"请求数据file response: = \(responseValue)")
            } errorHandler: { error in
                DispatchQueue.main.async {
                    NSObject.cancelPreviousPerformRequests(withTarget: self)
                    self.onFailure(error: error)
                    BuzWatchInfoLog.debug(msg:"请求数据file error：\(error)")
                }
            }
        }
    }
    
    override func onReceive(didReceive file: WCSessionFile) {
        guard let metadata = file.metadata else {
            return
        }
        
        if let traceId = metadata["traceId"] as? String , traceId == self.traceId {
            DispatchQueue.main.async {
                NSObject.cancelPreviousPerformRequests(withTarget: self)
                BuzWatchInfoLog.debug(msg:"LocalFileRequest 收到文件消息: = \(String(describing: file.metadata))")
                WatchDataSourceManager.shared.didReceive(didReceive: file)
                
                do {
                    try FileManager.default.moveItem(atPath: file.fileURL.path, toPath: self.localFilePath)
                    let params : [String : Any] = ["targetId":self.targetId, "msgId":self.msgId, "traceId":traceId, "filePath" : self.localFilePath]
                    
                    if let block = self.completionFileHandler {
                        block(true , params, nil)
                    }
                }catch{
                    BuzWatchInfoLog.debug(msg : "文件保存错误! = \(error)")
                }
                
                self.completionFileHandler = nil
                WatchSessionActivationManager.shared.weakGetLocalFiledArray.removeAll { request in
                    return request === self
                }
            }
        }
    }
}
