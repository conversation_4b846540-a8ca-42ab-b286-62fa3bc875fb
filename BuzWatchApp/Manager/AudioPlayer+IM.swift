//
//  AudioPlayer+IM.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/25.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation


extension AudioPlay {
    func tryPlayVoice(targetId : Int64, svrMsgId : Int64, convType : Int, retryCount : Int = 0, limitDelay : Int64 = 120) {
        if retryCount >= 3 {
            return
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            WatchDataSourceManager.shared.getVoiceUrl(msgId: svrMsgId, convType: convType) { url, createTime, localUrl in
                if url.isEmpty && localUrl.isEmpty {
                    self.tryPlayVoice(targetId: targetId, svrMsgId: svrMsgId, convType: convType, retryCount : retryCount + 1, limitDelay: limitDelay)
                    return
                }
                
                DispatchQueue.main.async {
                    if Int64(Date().timeIntervalSince1970 * 1000) > limitDelay * 1000 + createTime {
                        return
                    }
                    
                    if localUrl != "" {
                        WatchHomeView.sharedView?.playAudio(target: targetId, msgId: svrMsgId, content: localUrl)
                    } else {
                        WatchHomeView.sharedView?.playAudio(target: targetId, msgId: svrMsgId, content: url)
                    }
                }
            }
        }
    }
}


