//
//  WatchDataRequest.swift
//  buz
//
//  Created by l<PERSON>hi on 2023/7/31.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import WatchKit
import BuzConfig
import Localizable

extension WatchDataSourceManager{
    func saveRecentUser(userId : String?, icon : String) {
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return
        }
        
        if userId == nil {
            userDefaults.removeObject(forKey: "UserDefaultsKey_Recent")
            userDefaults.synchronize()
        } else {
            userDefaults.set(["id" : userId ?? "", "icon" : icon] as [String : Any], forKey: "UserDefaultsKey_Recent")
            userDefaults.synchronize()
        }
    }
    
    func getRecentUser() -> [String : Any]? {
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return nil
        }
        
        return userDefaults.value(forKey: "UserDefaultsKey_Recent") as? [String : Any]
    }
    
    func saveIsLogin(isLogin : Bool) {
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return
        }
        
        userDefaults.set(isLogin, forKey: "UserDefaultsKey_isLogin")
        userDefaults.synchronize()
    }
    
    func getIsLogin() -> Bool {
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return false
        }
        
        return userDefaults.bool(forKey: "UserDefaultsKey_isLogin")
    }
    
    func saveMode(mode : PersonalStatusType) {
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return
        }
        
        userDefaults.set(mode.rawValue, forKey: "UserDefaultsKey_Mode")
        userDefaults.synchronize()
    }
    
    func getMode() -> PersonalStatusType {
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return PersonalStatusType.available
        }
        
        return PersonalStatusType.init(rawValue: Int32(userDefaults.integer(forKey: "UserDefaultsKey_Mode"))) ?? .available
    }
    
    func saveUsr(userId : Int64?) {
        
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return
        }
        
        userDefaults.set(userId, forKey: "UserDefaultsKey_UserId")
        userDefaults.synchronize()
    }
    
    func getUser() -> Int64?{
        
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return nil
        }
        
        if let userId =   userDefaults.value(forKey: "UserDefaultsKey_UserId") as? Int64 {
            return userId
        }
        return nil
    }
    
    func saveUseFirst(isUseFirst : Bool) {
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return
        }
        userDefaults.set(true, forKey: "UserDefaultsKey_isUseFirst")
        userDefaults.synchronize()
    }
    
    func getUseFirst() -> Bool{
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return false
        }
        if let isBool =  userDefaults.value(forKey: "UserDefaultsKey_isUseFirst") as? Bool {
            return isBool
        }
        return false
    }
    
    func saveDeviceId(deviceId : String?) {
        
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return
        }
        
        userDefaults.set(deviceId, forKey: "UserDefaultsKey_deviceId")
        userDefaults.synchronize()
    }
    
    func getDeviceId() -> String?{
        
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return nil
        }
        
        if let userId =   userDefaults.value(forKey: "UserDefaultsKey_deviceId") as? String {
            return userId
        }
        return nil
    }
    ///请求设备信息
    func getDeviceId(completionHandler:((Bool , String) -> Void)? = nil){
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.getDeviceId.rawValue
        let json = data.yy_modelToJSONObject()
        if let deviceId = getDeviceId() , deviceId.count > 0 {
            if let block = completionHandler {
                block(true , deviceId)
            }
        }else{
            if let dic = json as? Dictionary<String, Any> {
                WatchSessionActivationManager.shared.request(params: dic) { isSucceed , message , error  in
                    if isSucceed , let m = message{
                        BuzWatchInfoLog.info(msg:"请求数据message.response: = \(message)")
                        if let data = WatchToIphoneCommonDataTransferClass.yy_model(with: m) , let content = data.content as? Dictionary<String, Any> {
                            if let deviceId = content["deviceId"] as? String{
                                self.saveDeviceId(deviceId: deviceId)
                            }
                            if let uid = content["uid"] as? Int64{
                                self.saveUsr(userId:uid)
                            }
                            if let block = completionHandler {
                                block(isSucceed ,self.getDeviceId() ?? "")
                            }
                        }
                    }else{
                        if let block = completionHandler {
                            block(isSucceed ,"")
                        }
                    }
                }
            }
        }
    }
    
    
    ///请求登录信息
    func getLoginStatus(completionHandler:((Bool , Bool) -> Void)? = nil){
        BuzWatchInfoLog.info(msg: "请求登录信息")
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.loginStatus.rawValue
        let json = data.yy_modelToJSONObject()
        if let userId = getUser() , userId > 0 {
            if let block = completionHandler {
                self.uid = userId
                block(true , self.uid > 0)
            }
            BuzWatchInfoLog.info(msg: "请求登录信息：本地有数据:userId = \(userId)")
        }else{
            BuzWatchInfoLog.info(msg: "请求登录信息：没有本地有数据")
            if let dic = json as? Dictionary<String, Any> {
                WatchSessionActivationManager.shared.request(params: dic) { isSucceed , message , error  in
                    BuzWatchInfoLog.info(msg: "请求登录信息：isSucceed = \(isSucceed) message = \(message)")
                    if isSucceed , let m = message{
                        if let data = WatchToIphoneCommonDataTransferClass.yy_model(with: m) , let content = data.content as? Dictionary<String, Any> ,let block = completionHandler {
                            if let uid = content["uid"] as? Int64{
                                self.saveUsr(userId: uid)
                                self.uid = uid
                            }
                            BuzWatchInfoLog.info(msg: "请求登录信息：isSucceed = \(isSucceed) message = \(message) uid = \(self.uid)")
                            block(isSucceed ,self.uid > 0)
                        }else{
                            if let block = completionHandler {
                                block(false , false)
                            }
                            BuzWatchInfoLog.info(msg: "请求登录信息：isSucceed = \(isSucceed) 解析失败！")
                        }
                    }else{
                        if let block = completionHandler {
                            block(isSucceed ,self.uid > 0)
                        }
                        BuzWatchInfoLog.info(msg: "请求登录信息：isSucceed 222 = \(isSucceed) 解析失败！")
                    }
                }
            }
        }
    }
    
    func outPutLog(log: String , completionHandler:((Bool) -> Void)? = nil){
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.outPutLog.rawValue
        data.params = ["log" : log]
        let json = data.yy_modelToJSONObject()
        if let dic = json as? Dictionary<String, Any> {
//            WatchSessionActivationManager.shared.request(params: dic) { isSucceed , message , error  in
//                if isSucceed{
//                    if let block = completionHandler {
//                        block(true)
//                    }
//                }else{
//                    if let block = completionHandler {
//                        block(false)
//                    }
//                }
//            }
            do {
                try WatchSessionActivationManager.shared.wcSession.updateApplicationContext(dic)
            } catch {

            }
//            WatchSessionActivationManager.shared.wcSession.transferUserInfo(dic)
        }
    }
    
    ///请求首页列表
    func getHomeList(completionHandler:((_ isSucceed : Bool ,_ list : Array<WatchHomeListModel>) -> Void)? = nil){

        BuzWatchInfoLog.info(msg: "请求好友列表信息")
        
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.getHomeList.rawValue
        
        let json = data.yy_modelToJSONObject()
        if let dic = json as? Dictionary<String, Any> {
            WatchSessionActivationManager.shared.request(params: dic) { isSucceed , message , error  in
                if isSucceed , let m = message{
                    let commonDataSourceModel = WatchToIphoneCommonDataTransferClass.yy_model(with: m)
                    let array = NSArray.yy_modelArray(with: WatchHomeListModel.self, json: commonDataSourceModel?.content ?? Array<Any>())
                    if let list =  array as? Array<WatchHomeListModel> , let block = completionHandler{
                        block(isSucceed, list)
                    }else{
                        if let block = completionHandler{
                            block(false ,[])
                        }
                        BuzWatchInfoLog.info(msg: "请求好友列表信息:收到好友列表信息isSucceed: = \(isSucceed) 解析失败！")
                    }
                    BuzWatchInfoLog.info(msg: "请求好友列表信息:收到好友列表信息isSucceed: = \(isSucceed) list = \(message)")
                }else{
                    if let block = completionHandler{
                        block(isSucceed ,[])
                    }
                    BuzWatchInfoLog.info(msg: "请求好友列表信息:收到好友列表信息isSucceed: = \(isSucceed) 解析失败！")
                }
                BuzWatchInfoLog.info(msg: "请求好友列表信息:收到好友列表信息isSucceed: = \(isSucceed) message \(error)")
            }
        }
    }
    
    ///请求历史消息
    func getHistoryList(targetId : String ,isGroup : Bool, boundaryMsgId : Int32 , completionHandler:((Bool , Array<WatchHistoryListModel>?, Int32) -> Void)? = nil){
        BuzWatchInfoLog.info(msg: "请求历史列表信息boundaryMsgId: = \(boundaryMsgId)")
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.getHistoryList.rawValue
        data.params = ["targetId" : targetId , "boundaryMsgId" : boundaryMsgId , "isGroup" :  isGroup]
    
        let json = data.yy_modelToJSONObject()
        if let dic = json as? Dictionary<String, Any> {
            WatchSessionActivationManager.shared.request(params: dic) { isSucceed , message , error  in
                BuzWatchInfoLog.info(msg: "收到历史列表信息:isSucceed = \(isSucceed)  message = \(message) error = \(error)")
                if isSucceed , let m = message{
                    let commonDataSourceModel = WatchToIphoneCommonDataTransferClass.yy_model(with: m)
                    
                    if let content = commonDataSourceModel?.content as? [String : Any] {
                        if let items = content["items"] as? Array<Any> , let maxImMsgHistory = content["maxImMsgHistory"] as? Int32{
                            let array = NSArray.yy_modelArray(with: WatchHistoryListModel.self, json: items)
                            if let list =  array as? Array<WatchHistoryListModel> , let block = completionHandler{
                                block(isSucceed , list, maxImMsgHistory)
                            }
                            return
                        }
                    }
                }
                
                completionHandler?(isSucceed, nil, 0)
            }
        }
    }
    
    func getSetting(completionHandler:((_ isSuccess : Bool, _ mode : Int32, _ volume : Double, _ language : String) -> Void)?) {
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.getWatchSetting.rawValue
        data.params = [:]
    
        let json = data.yy_modelToJSONObject()
        if let dic = json as? Dictionary<String, Any> {
            WatchSessionActivationManager.shared.request(params: dic) { isSucceed , message , error  in
                if isSucceed , let m = message{
                    let commonDataSourceModel = WatchToIphoneCommonDataTransferClass.yy_model(with: m)
                    
                    if let content = commonDataSourceModel?.content as? [String : Any] {
                        if let mode = content["mode"] as? Int32 ,
                            let volume = content["volume"] as? Double ,
                           let language = content["language"] as? String ,
                            let block = completionHandler{
                            block(true, mode , volume, language)
                            return
                        }
                    }
                }
                
                if let block = completionHandler{
                    block(true, 1, 0.5, "")
                }
            }
        }
    }
    
    func setPersonalStatusType(mode:PersonalStatusType, completionHandler:((_ isSuccess : Bool) -> Void)?) {
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.setPersonalStatusType.rawValue
        data.params = ["mode":mode.rawValue, "element_business_content" : self.elementBusinessType()]
    
        let json = data.yy_modelToJSONObject()
        if let dic = json as? Dictionary<String, Any> {
            WatchSessionActivationManager.shared.request(params: dic) { isSucceed , message , error  in
                DispatchQueue.main.async {
                    completionHandler?(isSucceed)
                }
            }
        }
    }
    
    func watchTrackingSettingEvent(mode:PersonalStatusType, volume : Double, completionHandler:((_ isSuccess : Bool) -> Void)?) {
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.watchSettingTracking.rawValue
        data.params = ["mode":mode.rawValue, "volume" : volume]
    
        let json = data.yy_modelToJSONObject()
        if let dic = json as? Dictionary<String, Any> {
            WatchSessionActivationManager.shared.request(params: dic) { isSucceed , message , error  in
                completionHandler?(isSucceed)
            }
        }
    }
    
    
    func watchTrackingActiveComplications(complications : String, completionHandler:((_ isSuccess : Bool) -> Void)?) {
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.watchActiveComplicationsTracking.rawValue
        data.params = ["complications":complications]
    
        let json = data.yy_modelToJSONObject()
        if let dic = json as? Dictionary<String, Any> {
            WatchSessionActivationManager.shared.request(params: dic) { isSucceed , message , error  in
                completionHandler?(isSucceed)
            }
        }
    }
    
    func getVoiceUrl(msgId:Int64, convType : Int, completionHandler:((_ url : String,
                                                                      _ createTime : Int64,
                                                                      _ localUrl : String) -> Void)?) {
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.getAudioFileByMsgInfo.rawValue
        data.params = ["msgId":msgId, "convType" : convType]
        
        SettingManager.shared.extendData?.forEach({ (key: AnyHashable, value: Any) in
            if let key = key as? String {
                data.params?[key] = value
            }
        })
        
        let json = data.yy_modelToJSONObject()
        if let dic = json as? Dictionary<String, Any> {
            WatchSessionActivationManager.shared.request(params: dic) { isSucceed , message , error  in
                if isSucceed , let m = message {
                    if let result = WatchToIphoneCommonDataTransferClass.yy_model(with: m)?.content as? [String:Any] {
                        var remoteUrl = ""
                        var localUrl = ""
                        
                        if let turl = result["url"] as? String {
                            remoteUrl = turl
                        }
                        
                        if let tlocalUrl = result["localUrl"] as? String {
                            localUrl = tlocalUrl
                        }
                        
                        if (localUrl != "" || remoteUrl != ""), let createTime = result["createTime"] as? Int64 {
                            completionHandler?(remoteUrl, createTime, localUrl)
                            return
                        }
                    }
                }
                
                completionHandler?(message?["url"] as? String ?? "", 0, "")
            }
        }
    }
    
    
    func setIMManageRead(msgId : Int64 , convType : Int , serverMsgId : Int64 , targetId : Int64 ){
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.IMMessageSetRead.rawValue
        data.params = ["msgId" : msgId, "convType" : convType ,"serverMsgId" : serverMsgId ,"targetId" : targetId]
    
        let json = data.yy_modelToJSONObject()
        if let dic = json as? Dictionary<String, Any> {
            WatchSessionActivationManager.shared.request(params: dic) { isSucceed , message , error  in
                BuzWatchInfoLog.info(msg:"设置消息已读结果：\(isSucceed) , error = \(error)")
            }
        }
    }
    
    func sendEventTrackingToRDS( event : String, label : [String: Any]){
        var params = label
        params["systemVersion"] = WKInterfaceDevice.current().systemVersion
        params["systemName"] = WKInterfaceDevice.current().systemName
        params["model"] = WKInterfaceDevice.current().model
        params["name"] = WKInterfaceDevice.current().name
        var dic = Dictionary<String , Any>()
        dic["event"] = event
        dic["label"] = params
        
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.tekiplayerEventTrackingToRDS.rawValue
        data.params = dic
        let json = data.yy_modelToJSONObject()
        if let dic = json as? Dictionary<String, Any> {
            WatchSessionActivationManager.shared.request(params: dic) { isSucceed , message , error  in
                BuzWatchInfoLog.info(msg:"设置sendEventTrackingToRDS结果：\(isSucceed) , error = \(error)")
            }
        }
    }
    
    func elementBusinessType() -> String {
        let systemVersion = WKInterfaceDevice.current().systemVersion
        let systemName = WKInterfaceDevice.current().systemName
        let model = WKInterfaceDevice.current().model
//        let localizedModel = WKInterfaceDevice.current().localizedModel
//        let name = WKInterfaceDevice.current().name
        let screenBounds = WKInterfaceDevice.current().screenBounds
        
        return model + "#" + systemName  + "#" + systemVersion + "#" + "\(screenBounds.size.width)"  + "#" + "\(screenBounds.size.height)"
    }
    
    ///watch打点
    func eventTracking(label : [String: Any]){
        var dic = label
        dic["element_business_type"] = self.elementBusinessType()
        let data = WatchToIphoneCommonDataTransferClass()
        data.dataType = WatchToIphoneCommonDataType.watchEventTracking.rawValue
        data.params = dic
        let json = data.yy_modelToJSONObject()
        if let dic = json as? Dictionary<String, Any> {
            WatchSessionActivationManager.shared.request(isForceActive: true, params: dic) { isSucceed , message , error  in
                BuzWatchInfoLog.info(msg:"设置eventTracking结果：\(isSucceed) , error = \(error)")
            }
        }
    }
    
    
    ///获取远程链接
    func getAudioRemoteUrl(msgId:Int64, convType : Int, completionHandler:((_ url : String,
                                                                            _ isSucceed : Bool) -> Void)?) {
          let data = WatchToIphoneCommonDataTransferClass()
          data.dataType = WatchToIphoneCommonDataType.getAudioRemoteURL.rawValue
          data.params = ["msgId":msgId, "convType" : convType]
    
          let json = data.yy_modelToJSONObject()
          if let dic = json as? Dictionary<String, Any> {
              WatchSessionActivationManager.shared.request(params: dic) { isSucceed , message , error  in
                  if isSucceed , let m = message{
                      if let data = WatchToIphoneCommonDataTransferClass.yy_model(with: m) ,
                            let content = data.content as? Dictionary<String, Any> {
                          BuzWatchInfoLog.info(msg:"WatchAudioPlayManager：content = \(content)")
                          if let remoteUrl = content["remoteURL"] as? String , let isSucessed = content["isSucessed"] as? Bool {
                              completionHandler?(remoteUrl , isSucessed)
                          }else{
                              completionHandler?( "", false)
                          }
                      }else{
                          completionHandler?( "", false)
                      }
                  }else{
                      completionHandler?( "", false)
                  }
              }
          }
    }
}


