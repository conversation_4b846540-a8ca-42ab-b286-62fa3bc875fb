//
//  WatchDataSourceManager.swift
//  BuzWatchApp
//
//  Created by l<PERSON>hi on 2023/7/20.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import WatchConnectivity
import YYModel
import SwiftUI

enum NSNotificationName : String {
    case userLogout = "userLogout"
    case userLogin = "userLogin"
    case sendIMReceipt = "sendIMReceipt"
    case updateConversation = "updateConversation"
    case userSpeaking = "userSpeaking"
    case updateConversationList = "updateConversationList"
    case forceNavigationBack = "forceNavigationBack"
    case updatePersonalStatueType = "updatePersonalStatueType"
    case volumeChangeNotification = "volumeChangeNotification"
    case languageChangeNotification = "languageChangeNotification"
    case modeChangeNotification = "modeChangeNotification"
    case audioPlayStateChanged = "audioPlayStateChanged"
    case audioPlayAssetChanged = "audioPlayAssetChanged"
}

class WatchDataSourceManager : NSObject {
    
    static let shared = WatchDataSourceManager()
    var weakWatchHistoryListObserver = [WatchHistoryListObserver]()
    var uid : Int64 = 0
    init(weakWatchHistoryListObserver: [WatchHistoryListObserver] = [WatchHistoryListObserver]()) {
        super.init()
        self.weakWatchHistoryListObserver = weakWatchHistoryListObserver
    }
    var complicationUserInfo : [String : Any]?
    var isCanSpeaking = false ///是否能播放
}

///发送消息到iphone
extension WatchDataSourceManager{
    ///发送文件消息
    func sendFile(url : URL ,params : Dictionary<String, Any>){
        WatchSessionActivationManager.shared.sendFile(url: url, params: params)
    }
    
    ///发送IM消息
    func sendIMWithVoiceFile(url : URL , duration : Double , isGroup : Bool , targetId : String , traceId : String , ntpTime : Int64 , completion : ((Bool) -> ())?){
        WatchSessionActivationManager.shared.sendIMWithVoiceFile(url: url, duration: duration, isGroup: isGroup, targetId: targetId , traceId: traceId, ntpTime: ntpTime ,  completion: completion)
    }
    
    ///发送Dictionary[实时传输,需要保证watch激活状态]
    func sendMessageWithDic(dic : Dictionary<String, Any>){
        WatchSessionActivationManager.shared.sendMessageWithDic(dic: dic)
        
    }
    
    ///发送Data[实时传输,需要保证watch激活状态]
    func sendMessageWithData(data : Data){
        WatchSessionActivationManager.shared.sendMessageWithData(data: data)
    }
    
    ///发送实时消息
    func requestWithDic(dic : Dictionary<String, Any> , completionHandler:((Bool , Dictionary<String, Any>? , Error?) -> Void)? = nil){
        WatchSessionActivationManager.shared.request(params: dic, completionHandler: completionHandler)
    }
}


///收到iphone消息
extension WatchDataSourceManager{
    
    ///收到交互消息
    func didReceiveMessage(message : [String : Any]){
        self.didReceiveUserInfo(didReceiveUserInfo: message)
    }
    
    ///收到后台消息
    func didReceiveUserInfo(didReceiveUserInfo userInfo: [String : Any] = [:]) {
        let dataSource = WatchToIphoneCommonDataTransferClass.yy_model(with: userInfo)
        if dataSource?.dataType == WatchToIphoneCommonDataType.receiveIM.rawValue {
            let array = NSArray.yy_modelArray(with: WatchHistoryListModel.self, json: dataSource?.content ?? Array<Any>())
            if let list =  array as? Array<WatchHistoryListModel> , list.count > 0{
                DispatchQueue.main.async(execute: DispatchWorkItem.init(block: {
                    self.weakWatchHistoryListObserver.forEach { WatchHistoryListObserver in
                        WatchHistoryListObserver.receiveMessage(list)
                    }
                }))
            }
        }else if dataSource?.dataType == WatchToIphoneCommonDataType.updateIMMessage.rawValue {
            let array = NSArray.yy_modelArray(with: WatchHistoryListModel.self, json: dataSource?.content ?? Array<Any>())
            if let list =  array as? Array<WatchHistoryListModel> , list.count > 0{
                DispatchQueue.main.async(execute: DispatchWorkItem.init(block: {
                    self.weakWatchHistoryListObserver.forEach { WatchHistoryListObserver in
                        WatchHistoryListObserver.updateMessage(list)
                    }
                }))
            }
        }else if dataSource?.dataType == WatchToIphoneCommonDataType.deleteIMMessage.rawValue {
            let array = NSArray.yy_modelArray(with: WatchHistoryListModel.self, json: dataSource?.content ?? Array<Any>())
            if let list =  array as? Array<WatchHistoryListModel> , list.count > 0{
                DispatchQueue.main.async(execute: DispatchWorkItem.init(block: {
                    self.weakWatchHistoryListObserver.forEach { WatchHistoryListObserver in
                        WatchHistoryListObserver.deleteMessage(list)
                    }
                }))
            }
        }else if dataSource?.dataType == WatchToIphoneCommonDataType.logout.rawValue {
            self.uid = 0
            WatchDataSourceManager.shared.saveUsr(userId: 0)
            NotificationCenter.default.post(name: NSNotification.Name(NSNotificationName.userLogout.rawValue), object: userInfo)
        }else if dataSource?.dataType == WatchToIphoneCommonDataType.login.rawValue {
            if let dic = dataSource?.content as? Dictionary<String, Any> , let uid = dic["uid"] as? Int64 {
                self.uid = uid
                WatchDataSourceManager.shared.saveUsr(userId: uid)
            }
            NotificationCenter.default.post(name: NSNotification.Name(NSNotificationName.userLogin.rawValue), object: userInfo)
        }else if dataSource?.dataType == WatchToIphoneCommonDataType.sendIMReceipt.rawValue {
            NotificationCenter.default.post(name: NSNotification.Name(NSNotificationName.sendIMReceipt.rawValue), object: userInfo)
        }else if dataSource?.dataType == WatchToIphoneCommonDataType.updateConversation.rawValue {
            NotificationCenter.default.post(name: NSNotification.Name(NSNotificationName.updateConversation.rawValue), object: userInfo)
        }else if dataSource?.dataType == WatchToIphoneCommonDataType.userSpeaking.rawValue {
            NotificationCenter.default.post(name: NSNotification.Name(NSNotificationName.userSpeaking.rawValue), object: userInfo)
        }else if dataSource?.dataType == WatchToIphoneCommonDataType.updateConversationList.rawValue {
            NotificationCenter.default.post(name: NSNotification.Name(NSNotificationName.updateConversationList.rawValue), object: userInfo)
        } else if dataSource?.dataType == WatchToIphoneCommonDataType.watchSettingChangeType.rawValue {
            NotificationCenter.default.post(name: NSNotification.Name(NSNotificationName.updatePersonalStatueType.rawValue), object: userInfo)
        }
    }
    
    ///收到文件
    func didReceive(didReceive file: WCSessionFile) {
        BuzWatchInfoLog.debug(msg:"收到文件: = \(String(describing: file.metadata))")
    }
}
