//
//  TraceTool.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/9/5.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import BaseTool

class MessageTraceIdTool: NSObject {

    static func generateNewestTraceId() -> String {
        let senderUid = WatchDataSourceManager.shared.uid
        let currentTimestamp = Int64(Date().timeIntervalSince1970 * 1000)
        let traceId = "\(senderUid)\(currentTimestamp)".md5
        return traceId
    }
    
    static func assertTraceIdNotNull(_ traceId : String)
    {
        assert(traceId.utf16.count > 0, "traceId should not null")
    }
    
}


class NTPTimeTool : NSObject {
    
    static func nowTimestamp() -> Int64 {
        var currentTs : Int64 = 0
        if NTPTime.now > 0 {
            currentTs = Int64(NTPTime.now * 1000)
        }else{
            currentTs = Int64(Date().timeIntervalSince1970 * 1000)
        }
        return currentTs
    }
}
