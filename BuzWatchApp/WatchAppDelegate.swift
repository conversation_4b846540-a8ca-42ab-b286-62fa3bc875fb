//
//  AppDelegate.swift
//  WatchApp1 Watch App
//
//  Created by ellzu gu on 2023/7/25.
//

import Foundation
import WatchKit
import WatchConnectivity
import BaseTool
import UserNotifications
import Intents
import SwiftUI
import Logz
import ClockKit
import WidgetKit
import BuzConfig
import Localizable
//import BuglyWatchOS

extension WKApplication {
    static var sharedAppDelegate: WatchAppDelegate? { Self.shared().appDelegate }
    var appDelegate: WatchAppDelegate? { self.delegate as? WatchAppDelegate }
}

class WatchAppDelegate: NSObject, WKApplicationDelegate , WKExtensionDelegate, WKExtendedRuntimeSessionDelegate {
    var hadSetup : Bool = false
    static var sharedRoute : WatchRouteItem = WatchRouteItem(url: nil)
    
    func extendedRuntimeSession(_ extendedRuntimeSession: WKExtendedRuntimeSession, didInvalidateWith reason: WKExtendedRuntimeSessionInvalidationReason, error: Error?) {
        BuzWatchInfoLog.debug(msg: "extendedRuntimeSession")
    }
    
    func extendedRuntimeSessionDidStart(_ extendedRuntimeSession: WKExtendedRuntimeSession) {
        BuzWatchInfoLog.debug(msg: "extendedRuntimeSessionDidStart")
    }
    
    func extendedRuntimeSessionWillExpire(_ extendedRuntimeSession: WKExtendedRuntimeSession) {
        BuzWatchInfoLog.debug(msg: "extendedRuntimeSessionWillExpire")
    }
    
    func applicationDidFinishLaunching() {
        ILog.info("applicationDidFinishLaunching")
        Localizable.setGroupId(groupId: BuzConfig.groupStoreId)
        UNUserNotificationCenter.current().delegate = self
//        WKExtension.shared().isAutorotating = true
    }
    
    func applicationWillEnterForeground() {
        ILog.info("applicationWillEnterForeground")
    }
    
    func applicationDidBecomeActive() {
        ILog.info("applicationDidBecomeActive")
        self.checkOnce()
    }
    
    func applicationWillResignActive() {
        ILog.info("applicationWillResignActive")
        self.reloadWidget()
    }
    
    func applicationDidEnterBackground() {
        ILog.info("applicationDidEnterBackground")
    }
    
    func reloadWidget() {
        if #available(watchOS 9.0, *) {
            WidgetCenter.shared.getCurrentConfigurations { result in
                switch result {
                case .success(let success):
                    success.forEach { info in
                        WidgetCenter.shared.reloadTimelines(ofKind: info.kind)
                    }
                case .failure(_):
                    return
                }
            }
        } else {
            let complicationServer = CLKComplicationServer.sharedInstance()
            complicationServer.activeComplications?.forEach({ complication in
                complicationServer.reloadTimeline(for: complication)
            })
        }
    }
}

extension WatchAppDelegate {
    func reportComplication() {
        var reportComplications : [String] = []
        
        let complicationsHandleIdentity : ((String) -> Void) = { identity in
            if identity == BuzConfig.appComplicationIdentity {
                reportComplications.append("default_icon")
            } else if identity == BuzConfig.userComplicationIdentity {
                reportComplications.append("recent_contacts")
            } else if identity == BuzConfig.modeComplicationIdentity {
                reportComplications.append("mode_display")
            }
        }
        
        let checkAndReport : (() -> Void) = {
            if reportComplications.isEmpty {
                return
            }
            
            WatchDataSourceManager.shared.watchTrackingActiveComplications(complications: reportComplications.joined(separator: ",")) { isSuccess in
            }
        }
        
        if #available(watchOS 9.0, *) {
            WidgetCenter.shared.getCurrentConfigurations { result in
                switch result {
                case .success(let success):
                    success.forEach { info in
                        complicationsHandleIdentity(info.kind)
                    }
                    
                    checkAndReport()
                case .failure(_):
                    return
                }
            }
        } else {
            let complicationServer = CLKComplicationServer.sharedInstance()
            complicationServer.activeComplications?.forEach({ complication in
                complicationsHandleIdentity(complication.identifier)
            })
            
            checkAndReport()
        }
    }
    
    func checkOnce() {
        if hadSetup {
            return
        }
        
        hadSetup = true
        self.reportComplication()
        SettingManager.shared.registerCenter()
        SettingManager.shared.startLoad {}
    }
}

extension WatchAppDelegate : UNUserNotificationCenterDelegate {
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void){
        BuzWatchInfoLog.debug(msg: "didReceive response")
        let userInfo = response.notification.request.content.userInfo
        let title = response.notification.request.content.title
        if let action = response.actionIdentifier as String? {
            handleCustomAction(withIdentifier: action, title : title, forRemoteNotification: userInfo)
        }
        completionHandler()
    }
    
    func handleCustomAction(withIdentifier identifier: String?,
                            title title: String?,
                            forRemoteNotification remoteNotification: [AnyHashable : Any]) {
        if !WatchDataSourceManager.shared.getIsLogin() {
            BuzWatchInfoLog.info(msg:"查询到历史私聊::::::::unlogin")
            return
        }
        
        BuzWatchInfoLog.info(msg:"查询到历史私聊::::::::messageInfo :  \(remoteNotification)")
        if identifier == "\(NotificationActionType.view.rawValue)"
            || identifier == "\(NotificationActionType.playVoice.rawValue)" {
            if let info = UNNotification.messageInfo(userInfo: remoteNotification) {
                if let convType = info["convType"] as? Int,
                   let targetId = UNNotification.targetId(userInfo: remoteNotification) {
                    if WatchAppDelegate.sharedRoute.url != nil {
                        return
                    }
                    
                    WatchAppDelegate.sharedRoute.url = "watch_imView://"
                    let model = WatchHomeListModel.init()
                    model.userId = "\(targetId)"
                    
//                    if let portrait = info["portrait"] as? String {
//                        model.portrait = portrait
//                    } else {
                        model.portrait = ""
//                    }
                    
//                    if let name = info["userName"] as? String {
//                        model.name = name
//                    } else {
                        model.name = title
//                    }
                    
                    model.conversationType = convType
                          
                    if identifier == "\(NotificationActionType.playVoice.rawValue)" {
                        if let tsvrMsgId = info["svrMsgId"] as? String,
                            let svrMsgId = Int64(tsvrMsgId) {
                            model.autoPlayMsgId = svrMsgId
                        }
                    }
                    
                    SettingManager.shared.extendData = remoteNotification
                    WatchAppDelegate.sharedRoute.data = model
                    WatchHomeView.sharedView?.pushRoute()
                }
            }
            
        } else if identifier == "\(NotificationActionType.sendMessage.rawValue)" {
            if let userId = UNNotification.targetId(userInfo: remoteNotification) {
                WatchHomeView.sharedView?.gotoTargetId(targetId: userId)
            }
        }
//        else if identifier == "\(NotificationActionType.playVoice.rawValue)" {
//            if let info = UNNotification.voiceInfo(userInfo: remoteNotification) {
//                if let ttargetId = info["targetId"] as? String,
//                    let tsvrMsgId = info["svrMsgId"] as? String,
//                    let convType = info["convType"] as? Int,
//                    let targetId = Int64(ttargetId),
//                    let svrMsgId = Int64(tsvrMsgId) {
//                    self.tryPlayVoice(targetId: targetId, svrMsgId: svrMsgId, convType: convType, retryCount: 0)
//                }
//            }
//        }
    }
    func handle(_ userActivity: NSUserActivity) {
        BuzWatchInfoLog.debug(msg:"userActivity: \(userActivity.userInfo?["WGWidgetUserInfoKeyKind"] ?? "error")")
        
        if !WatchDataSourceManager.shared.getIsLogin() {
            BuzWatchInfoLog.info(msg:"userActivity::::::::unlogin")
            return
        }
        
        if let kind = userActivity.userInfo?["WGWidgetUserInfoKeyKind"] as? String {
            if kind == BuzConfig.modeComplicationIdentity {
//                SettingManager.shared.startLoad {
                WatchAppDelegate.sharedRoute.url = "watch_mode://"
                WatchAppDelegate.sharedRoute.data = PersonalStatusType.none
                WatchHomeView.sharedView?.pushRoute()
//                }
            }
        }
    }
}
