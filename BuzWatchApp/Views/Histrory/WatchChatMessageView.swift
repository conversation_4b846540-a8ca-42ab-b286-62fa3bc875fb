//
//  WatchChatMessageView.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/7/22.
//  Copyright © 2023 lizhi. All rights reserved.
//

import BuzLocalizable
import E2EEHelper
import Foundation
import Localizable
import SDWebImageSwiftUI
import SwiftUI
import WatchKit

struct ChatMessageView: View {
    @Binding var messsage: WatchHistoryListModel
    var tapBlock: ((WatchHistoryListModel) -> Void)?
    var body: some View {
        HStack(alignment: .top, spacing: 0) {
            VStack(alignment: .leading, spacing: 5) {
                if messsage.isShowTime {
                    MessageTime(messsage: messsage).frame(width: .infinity)
                }
                if !messsage.isSelf && messsage.isGroup && messsage.type != MessageType.command.rawValue {
                    MessageName(messsage: messsage).padding([.trailing], 20)
                }
                if messsage.type == MessageType.command.rawValue {
                    MessageCommand(messsage: messsage)
                } else if messsage.type == MessageType.image.rawValue {
                    MessageImage(messsage: messsage).padding([.trailing], 20)
                } else if messsage.type == MessageType.text.rawValue {
                    MessageText(messsage: messsage, text: messsage.content, leadingAndTrailingPadding: 10, topAndTrailingBottom: 14)
                        .background(!messsage.isSelf ? Color.watchWhiteColor(alpha: 0.12) : Color.watchPrimaryColor())
                        .clipShape(RoundedRectangle(cornerRadius: 10)).padding([.trailing], 20)
                } else if messsage.type == MessageType.leaveVoice.rawValue {
                    HStack {
                        MessageVoice(messsage: messsage, tapBlock: self.tapBlock)
                            .background(!messsage.isSelf ? Color.watchWhiteColor(alpha: 0.12) : Color.watchPrimaryColor())
                            .clipShape(RoundedRectangle(cornerRadius: 10))
                        HStack {
                            Circle().frame(width: 8, height: 8, alignment: .center)
                                .foregroundColor(!messsage.isRead && !messsage.isSelf ? Color.watchSecondaryErrorColor() : .clear).id(messsage.isRead)
                        }.frame(width: 20)
                    }

                } else if messsage.type == MessageType.voiceText.rawValue  || messsage.type == MessageType.newVoiceText.rawValue{
                    if messsage.voiceTextType == 1 { /// 成功
                        MessageVoiceText(messsage: messsage, tapBlock: self.tapBlock)
                            .padding([.trailing], 20)
                    } else if messsage.voiceTextType == 0 { /// 失败
                        MessageVoice(messsage: messsage, tapBlock: self.tapBlock)
                            .background(!messsage.isSelf ? Color.watchWhiteColor(alpha: 0.12) : Color.watchPrimaryColor())
                            .clipShape(RoundedRectangle(cornerRadius: 10))
                            .padding([.trailing], 20)
                        MessageSendFail(text: Localizable.common_failure_translate)
                            .padding([.trailing], 20)
                    } else if messsage.voiceTextType == 2 { /// 转换中
                        MessageVoice(messsage: messsage, tapBlock: self.tapBlock)
                            .background(!messsage.isSelf ? Color.watchWhiteColor(alpha: 0.12) : Color.watchPrimaryColor())
                            .clipShape(RoundedRectangle(cornerRadius: 10))
                            .padding([.trailing], 20)
                        MessageTranscribing(text: "...").padding([.trailing], 20)
                    }
                } else if messsage.type == MessageType.robotThinking.rawValue {
                    MessageRobotThinking().padding([.trailing], 20)
                } else if messsage.type == MessageType.decryptFailed.rawValue {
                    if messsage.isSelf {
                        DecryptFailedMessage(text: Localizable.sender_decrypt_failed, selfSend: messsage.isSelf)
                    } else {
                        DecryptFailedMessage(text: Localizable.receiver_decrypt_failed, selfSend: messsage.isSelf)
                    }
                } else if messsage.type == MessageType.voicEmoji.rawValue || messsage.type == MessageType.compatibleVoiceEmoji.rawValue {
                    VoicemojiView(messsage: messsage, text: messsage.content, leadingAndTrailingPadding: 10, topAndTrailingBottom: 14,tapBlock: self.tapBlock)
                }else {
                    MessageUnsupport(messsage: messsage).padding([.trailing], 20)
                }
                if messsage.isSendFail {
                    MessageSendFail(text: Localizable.FailToSent).padding([.trailing], 20)
                }
            }
            Spacer()
        }.environment(\.layoutDirection, !messsage.isSelf ? .leftToRight : .rightToLeft)
    }
}

struct MessageTranscribing: View {
    let text: String
    var body: some View {
        HStack {
            Spacer()
            if #available(watchOS 11.0, *) {
                EmptyView()
            } else {
                ProgressView().frame(width: 14.5, height: 14.5, alignment: .center)
            }
            
            Text(text)
                .font(.system(size: 12.0))
                .foregroundColor(Color.watchWhiteColor())
        }.environment(\.layoutDirection, .leftToRight)
    }
}


struct VoicemojiView : View {
    
    let messsage: WatchHistoryListModel
    let text: String
    let leadingAndTrailingPadding: CGFloat
    let topAndTrailingBottom: CGFloat
    var tapBlock: ((WatchHistoryListModel) -> Void)?
    var body: some View {
        HStack {
            Button(action: {
                if let block = self.tapBlock {
                    block(messsage)
                }
            }, label: {
                HStack(alignment: .top) {
                    Text(text)
                        .font(.system(size: 56.0))
                        .padding([.leading], leadingAndTrailingPadding)
                        .environment(\.layoutDirection, .leftToRight)
                    

                    ZStack(alignment:.topTrailing) {
                        Circle()
                            .fill(Color.watchVoiceHowColor())
                            .frame(width: 8.0, height: 8.0)
                            .position(x: messsage.isSelf ? 6 : 7 , y:messsage.isSelf ? 16:  16)
                            .scaleEffect(x: messsage.isSelf ? -1 : 1, y: 1)
                        
                        Circle()
                            .fill(Color.watchVoiceHowColor())
                            .frame(width: 20.0, height: 20.0)
                            .scaleEffect(x: messsage.isSelf ? -1 : 1, y: 1)
                            .overlay {
                                if messsage.playerState == 2 {
                                    LottieAnimationContentView(filename:  "Playing_primary")
                                        .frame(width: 16.0, height: 16.0)
                                        .clipShape(Circle())
                                        .scaleEffect(x: messsage.isSelf ? -1 : 1, y: 1)
                                }else {
                                    
                                        Image("voicemoji_thinking")
                                        .frame(width: 16.0, height: 16.0)
                                        .scaleEffect(x: messsage.isSelf ? -1 : 1, y: 1)
                                            
                                }
                                
                            }
                        
                    
                        Circle()
                            .fill(Color.watchVoiceHowColor())
                            .frame(width: 4.0, height: 4.0)
                            .position(x: 2, y: 21)
                            .scaleEffect(x: messsage.isSelf ? -1 : 1, y: 1)
                        
                            
                    }.frame(width: 23.0, height: 23.0)
                        
                        .scaleEffect(x: messsage.isSelf ? -1 : 1, y: 1)
                    
                        .padding(.leading , messsage.isSelf ? -15 : -8)
                        
                }
                
            }).buttonStyle(.borderless)
        }
    }
    
    
}

struct MessageText: View {
    let messsage: WatchHistoryListModel
    let text: String
    let leadingAndTrailingPadding: CGFloat
    let topAndTrailingBottom: CGFloat
    var body: some View {
        HStack(alignment: .center, spacing: 0) {
            Text(text)
                .foregroundColor(!messsage.isSelf ? Color.white : Color.black)
                .padding([.top, .bottom], topAndTrailingBottom)
                .padding([.leading, .trailing], leadingAndTrailingPadding)
                .environment(\.layoutDirection, .leftToRight)
        }
    }
}

struct DecryptFailedMessage: View {
    let text: String
    let selfSend: Bool
    var body: some View {
        HStack(alignment: .center, spacing: 0) {
            Image(systemName: "exclamationmark.circle.fill")
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 14.0, height: 14.0, alignment: .center)
                .foregroundColor(Color.gray)
            Text(text)
                .foregroundColor(selfSend ? Color.black : Color.white)
                .padding([.top, .bottom], 14)
                .padding([.leading, .trailing], 10)
                .environment(\.layoutDirection, .leftToRight)
                .background(selfSend ? Color.watchPrimaryColor() : Color.watchWhiteColor(alpha: 0.12))
                .clipShape(RoundedRectangle(cornerRadius: 10)).padding([.trailing], 20)
        }
    }
}

struct MessageVoiceTranslateText: View {
    let messsage: WatchHistoryListModel
    let text: String
    let leadingAndTrailingPadding: CGFloat
    let topAndTrailingBottom: CGFloat
    var body: some View {
        HStack(alignment: .center, spacing: 0) {
            Text(text)
                .foregroundColor(!messsage.isSelf ? Color.white : Color.black)
                .padding([.top, .bottom], topAndTrailingBottom)
                .padding([.leading, .trailing], leadingAndTrailingPadding)
                .frame(maxWidth: .infinity, alignment: .leading)
                .environment(\.layoutDirection, .leftToRight)
        }
    }
}

struct MessageTime: View {
    let messsage: WatchHistoryListModel
    var body: some View {
        HStack {
            Spacer()
            Text(messsage.sendTime)
                .foregroundColor(Color.watchWhiteColor(alpha: 0.4))
                .font(.system(size: 14.0))
            Spacer()
        }
    }
}

struct MessageCommand: View {
    let messsage: WatchHistoryListModel
    var body: some View {
        HStack {
            Spacer()
            let textColor = (messsage.commandType == 5 && (messsage.subBusinessType == 5 || messsage.subBusinessType == 6 || messsage.subBusinessType == 7)) ? Color.watchWhiteColor(alpha: 0.6) : Color.watchWhiteColor()
            Text(messsage.content)
                .foregroundColor(textColor)
                .font(.system(size: 14.0))
                .multilineTextAlignment(.center)
            Spacer()
        }.padding([.bottom], 5)
    }
}

struct MessageName: View {
    let messsage: WatchHistoryListModel
    var body: some View {
        HStack {
            if !messsage.isSelf {
                Text(messsage.sendName)
                    .foregroundColor(Color.watchWhiteColor())
                    .font(.system(size: 14.0))
                Spacer()
            } else {
                Spacer()
                Text(messsage.sendName)
                    .foregroundColor(Color.watchWhiteColor())
                    .font(.system(size: 14.0))
            }
        }
    }
}

struct MessageVoice: View {
    let messsage: WatchHistoryListModel
    var tapBlock: ((WatchHistoryListModel) -> Void)?
    var body: some View {
        HStack {
            Text(transformDurationStr(seconds: Int(messsage.duration)))
                .padding([.leading], 10)
                .foregroundColor(!messsage.isSelf ? Color.white : Color.black)
            Spacer()
            Button(action: {
                if let block = self.tapBlock {
                    block(messsage)
                }
            }, label: {
                if messsage.playerState == 0 {
                    Image(messsage.isSelf ? "Checkmark_right" : "Checkmark_left")
                } else if messsage.playerState == 1 {
                    
                    if #available(watchOS 11.0, *) {
                        EmptyView()
                    } else {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: messsage.isSelf ? Color.black : Color.white))
                    }
                    
                    
                    
                } else if messsage.playerState == 2 {
                    LottieAnimationContentView(filename: messsage.isSelf ? "Playing_black" : "Playing_primary")
                        .frame(width: 24.0, height: 24.0)
                        .clipShape(Circle())
                }
            }).frame(width: 32.0, height: 32.0)
                .padding([.trailing], 8)
                .buttonStyle(.borderless)
        }.frame(height: 48.0)
            .environment(\.layoutDirection, .leftToRight)
            .clipShape(RoundedRectangle(cornerRadius: 10)).id(messsage.playerState)
    }

    private func transformDurationStr(seconds: Int) -> String {
        let minutes = Int(seconds / 60)
        let remainingSeconds = Int(seconds) % 60

        let formattedMinutes = String(format: "%d", minutes)
        let formattedSeconds = String(format: "%02d", remainingSeconds)

        return "\(formattedMinutes):\(formattedSeconds)"
    }
}

struct MessageVoiceText: View {
    let messsage: WatchHistoryListModel
    var tapBlock: ((WatchHistoryListModel) -> Void)?
    @State var isPresented = false
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            MessageVoice(messsage: messsage, tapBlock: self.tapBlock)
                .background(Color.clear)
                .clipShape(RoundedRectangle(cornerRadius: 10))

            HStack(alignment: .center, spacing: 0) {
                MessageVoiceTranslateText(messsage: messsage, text: messsage.voiceTextContent, leadingAndTrailingPadding: 8, topAndTrailingBottom: 8)
            }.background(!messsage.isSelf ? Color.watchBlackColor(alpha: 0.4) : Color.watchBlackColor(alpha: 0.14))
                .clipShape(RoundedRectangle(cornerRadius: 10))
                .padding([.leading, .trailing, .bottom], 2)
        }.background(!messsage.isSelf ? Color.watchWhiteColor(alpha: 0.12) : Color.watchPrimaryColor())
            .clipShape(RoundedRectangle(cornerRadius: 10)).frame(width: .infinity)
    }
}

struct MessageRobotThinking: View {
    var body: some View {
        HStack {
            if #available(watchOS 11.0, *) {
                EmptyView()
            } else {
                ProgressView().frame(width: 14.5, height: 14.5, alignment: .center)
                    .foregroundColor(Color.white)
            }
            
            Text("...".localized)
                .foregroundColor(Color.white)
                .frame(height: 48.0)
        }.padding([.leading, .trailing], 10)
            .background(Color.watchWhiteColor(alpha: 0.12))
            .clipShape(RoundedRectangle(cornerRadius: 10))
            .environment(\.layoutDirection, .leftToRight)
    }
}

struct MessageImage: View {
    let messsage: WatchHistoryListModel
    @State var isPresented = false
    var body: some View {
        WebImage(url: URL(string: messsage.content),
                 context: webImageContext(for: messsage))
            .placeholder(Image("icon_placeholder"))
            .resizable()
            .indicator { _, _ in
                if #available(watchOS 11.0, *) {
                    EmptyView()
                } else {
                    ProgressView()
                }
                
            }
            .aspectRatio(contentMode: .fill)
            .frame(width: self.imageWidth(imageSizeType: messsage.imageSizeType, messageWidth: messsage.imageSize.width, messageHeight: messsage.imageSize.height), height: self.imageHeight(imageSizeType: messsage.imageSizeType, messageWidth: messsage.imageSize.width, messageHeight: messsage.imageSize.height))
            .cornerRadius(5)
            .fullScreenCover(isPresented: $isPresented) {
                MessageImageView(messsage: messsage)
            }.onTapGesture {
                self.isPresented = true
            }
    }

    typealias SDWebImageContext = [SDWebImageContextOption: Any]
    func webImageContext(for message: WatchHistoryListModel) -> SDWebImageContext? {
        // only remote URL need to decrypt
        guard message.content.hasPrefix("http") else {
            BuzWatchInfoLog.error(msg: "content:\(message.content) is not remote URL")
            return nil
        }

        if let key = message.cryptKey,
           let iv = message.cryptIV,
           let decryptor = E2EEDecryptorForSDWebImage(key: key, iv: iv) {
            return [.downloadDecryptor: decryptor]
        } else {
            return nil
        }
    }

    func imageWidth(imageSizeType: Int, messageWidth: CGFloat, messageHeight: CGFloat) -> CGFloat {
        let maxWidth = WKInterfaceDevice.current().screenBounds.width - 40.0
        if imageSizeType == 2 {
            return maxWidth
        } else if imageSizeType == 1 {
            return messageWidth > maxWidth ? maxWidth - 20.0 : messageWidth
        }
        return messageWidth > maxWidth ? maxWidth - 20.0 : messageWidth
    }

    func imageHeight(imageSizeType: Int, messageWidth: CGFloat, messageHeight: CGFloat) -> CGFloat {
        let maxWidth = WKInterfaceDevice.current().screenBounds.width - 40.0
        if imageSizeType == 2 {
            return (maxWidth) * 0.75
        } else if imageSizeType == 1 {
            return messageHeight
        }
        return messageWidth > maxWidth ? maxWidth - 20.0 : messageHeight
    }
}

struct MessageUnsupport: View {
    let messsage: WatchHistoryListModel
    @State var isPresented = false
    var body: some View {
        HStack {
            Image(systemName: "exclamationmark.circle.fill")
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 14.0, height: 14.0, alignment: .center)
                .foregroundColor(Color.gray)
            Text("This is an unsupported message type")
                .font(.system(size: 17.0))
                .foregroundColor(Color.watchWhiteColor(alpha: 0.4))
        }.padding([.leading, .trailing], 10)
            .padding([.top, .bottom], 14)
            .background(Color.watchWhiteColor(alpha: 0.12))
            .cornerRadius(10)
            .environment(\.layoutDirection, .leftToRight)
    }
}

struct MessageImageView: View {
    let messsage: WatchHistoryListModel
    @State var scale = Double(1)
    var body: some View {
        VStack(alignment: .center, spacing: 0) {
            Spacer()
            HStack(alignment: .center, spacing: 0) {
                Spacer()
                WebImage(url: URL(string: messsage.content))
                    .placeholder(Image("icon_placeholder"))
                    .resizable().aspectRatio(contentMode: .fit)
                Spacer()
            }
            Spacer()
        }.frame(width: messsage.imageSize.width, height: messsage.imageSize.height, alignment: .center)
            .scaleEffect(CGSize(width: scale, height: scale), anchor: .center)
            .focusable(true) /// 表冠监听
            .digitalCrownRotation($scale, from: 1, through: 2, by: 0.1, sensitivity: .low, isContinuous: false, isHapticFeedbackEnabled: true)
    }
}

class E2EEDecryptorForSDWebImage: NSObject, SDWebImageDownloaderDecryptorProtocol {
    let aesKey: Data
    let aesIV: Data
    required init?(key: String, iv: String) {
        guard let keyData = key.data(using: .utf8),
              let ivData = iv.data(using: .utf8) else {
            BuzWatchInfoLog.error(msg: "Invalid key:\(key) or IV:\(iv)")
            return nil
        }
        guard let aesKey = Data(base64Encoded: keyData),
              let aesIV = Data(base64Encoded: ivData) else {
            BuzWatchInfoLog.error(msg: "Invalid base64 key:\(key) or IV:\(iv)")
            return nil
        }
        self.aesKey = aesKey
        self.aesIV = aesIV
        BuzWatchInfoLog.error(msg: "DEBUG>>> aesKey:\(aesKey), aesIV:\(aesIV)")
        super.init()
    }

    func decryptedData(with data: Data, response: URLResponse?) -> Data? {
        do {
            BuzWatchInfoLog.error(msg: "DEBUG>>> dcrypt")

            return try E2EEHelper_AES_CBC.decryptData(data,
                                                      withKey: aesKey,
                                                      withIV: aesIV)
        } catch {
            BuzWatchInfoLog.error(msg: "decrypt data failed:\(error)")
            return nil
        }
    }
}
