//
//  ContentView.swift
//  com.interfun.buz.watch Watch App
//
//  Created by lizhi on 2023/7/18.
//  Copyright © 2023 lizhi. All rights reserved.
//

import BuzConfig
import BuzLocalizable
import Localizable
import SDWebImageSwiftUI
import SwiftUI
import WatchConnectivity

struct WatchHistoryContentView: View {
    @Environment(\.presentationMode) var presentationMode
    @StateObject var viewModel: WatchHistoryListObserver
    @StateObject var player = WatchAudioPlayManager()
    @Environment(\.scenePhase) private var scenePhase
    var playBackBlock: ((Bool) -> Void)?

    @State var model: WatchHomeListModel
    @State var playingModel: WatchHistoryListModel?
    @State var isAppear = false
    @State var isFristLoad = true
    @State var pushView: (any View)? = nil
    @State var isRefresh = false
    @State var isPresented = false
    @State var isRefreshMessage = false
    @State var hasSetupTargetId: Bool = false
    @State var hasSetupTargetTimer: Bool = false
    @State var notificationObserve: NSObjectProtocol?

    var deallocObj: ViewDeallocObject?

    var body: some View {
        GeometryReader { geometryProxy in
//            CustomBackButton(title: model.name ?? "")
//                .edgesIgnoringSafeArea(.top)
//                .zIndex(1)
            ScrollViewReader { scrollView in
                ScrollView(.vertical, showsIndicators: false) {
                    if self.viewModel.isFirstLoadData {
                        loadingView()
                            .frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
                    } else {
                        if $viewModel.messageList.count <= 0 {
                            Text(viewModel.isLoadSucceed ? Localizable.setting_watch_no_chats : Localizable.sendrequestfail())
                                .frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
                        } else {
                            contentView(scrollView: scrollView, size: CGSize(width: geometryProxy.size.width, height: geometryProxy.size.height))
                                .onAppear {
                                    self.scrollToTarget(scrollView: scrollView)
                                }
                        }
                    }
                }.enableRefresh().onAppear { requestData() }
            }.onAppear {
                WatchAppDelegate.sharedRoute.appendPresentMode(key: BuzConfig.watchHistoryContentRouteKey, present: self.presentationMode)
                WatchDataSourceManager.shared.eventTracking(label: ["exclusive_id": model.isGroup ? "AVS2022091407" : "AVS2022091406", "page_business_id": self.model.userId ?? "0"])
            }
            .zIndex(0)
        }.navigationTitle(model.name ?? "")
            .navigationBarTitleDisplayMode(.inline)
//            .navigationBarHidden(true)
    }
}

extension WatchHistoryContentView {
    func getTargetId() -> Int64 {
        if let playId = model.autoPlayMsgId {
            BuzWatchInfoLog.debug(msg: "isScrol: autoplay msgId = \(viewModel.messageList.last?.msgId ?? -1)")

            if let playModel = viewModel.messageList.first(where: { model in
                model.serverMsgId == playId
            }) {
                return playModel.msgId
            }
        }

        BuzWatchInfoLog.debug(msg: "isScrol:msgId = \(viewModel.messageList.last?.msgId ?? -1)")
        if viewModel.isScroolToTop {
            return viewModel.messageList.first?.msgId ?? 0
        } else {
            return viewModel.messageList.last?.msgId ?? 0
        }
    }

    func scrollToTarget(scrollView: ScrollViewProxy) {
        let targetId = getTargetId()

        if let playId = model.autoPlayMsgId {
            BuzWatchInfoLog.debug(msg: "isScrol: autoplay msgId = \(viewModel.messageList.last?.msgId ?? -1)")

            if let playModel = viewModel.messageList.first(where: { model in
                model.serverMsgId == playId
            }) {
                playAudio(model: playModel)
            }

            model.autoPlayMsgId = nil
        }

        scrollView.scrollTo(targetId, anchor: .bottom)

        if !hasSetupTargetId && !hasSetupTargetTimer {
            hasSetupTargetTimer = true
            DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
                self.hasSetupTargetId = true
            }
        }
    }

    @ViewBuilder
    private func contentView(scrollView: ScrollViewProxy, size: CGSize) -> some View {
        if viewModel.isHaveMoreHistory {
            RefreshHeader(refreshing: .constant(viewModel.isLoadData)) {
                loadData()
            } label: { _ in
                if self.viewModel.isLoadData {
                    if #available(watchOS 11.0, *) {
                        EmptyView()
                    } else {
                        ProgressView().progressViewStyle(CircularProgressViewStyle(tint: Color.white))
                    }
                    
                } else {
                    if #available(watchOS 11.0, *) {
                        EmptyView()
                    } else {
                        ProgressView().progressViewStyle(CircularProgressViewStyle(tint: Color.white))
                    }
                    
                    
                }
            }.frame(width: size.width, height: 40, alignment: .center)
        }
        ForEach($viewModel.messageList, id: \.msgId) { item in
            ChatMessageView(messsage: item) { obj in
                BuzWatchInfoLog.debug(msg: "WatchAudioPlayManager 点击")
                if self.player.isPlaying {
                    self.resetCurrentPlyingStatus()
                    BuzWatchInfoLog.debug(msg: "WatchAudioPlayManager 点击->结束播放")
                    if let msgId = self.player.currentAsset?.msgId as? Int64, let lastId = item.msgId.wrappedValue as? Int64, msgId == lastId, msgId > 0 {
                        self.player.stop {
                            BuzWatchInfoLog.debug(msg: "WatchAudioPlayManager 点击->开始播放 , 相同暂停")
                        }
                    } else {
                        self.player.stop {
                            BuzWatchInfoLog.debug(msg: "WatchAudioPlayManager 点击->开始播放 , 不同开始其他")
                            self.playAudio(model: obj)
                        }
                    }
                } else {
                    BuzWatchInfoLog.debug(msg: "WatchAudioPlayManager 开始播放")
                    self.playAudio(model: obj)
                }
            }.opacity((isRefresh || isRefreshMessage) ? 1.0 : 1.0)
                .onAppear {
                    if !self.hasSetupTargetId {
                        let targetId = self.getTargetId()

                        if targetId == item.msgId.wrappedValue {
                            self.hasSetupTargetId = true
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                scrollView.scrollTo(targetId, anchor: .bottom)
                            }
                        }
                    }
                }
        }.onAppear {
        }.alert("当前不支持播放", isPresented: $isPresented) {
        }.onChange(of: player.currentAsset) { newValue in
            BuzWatchInfoLog.debug(msg: "player.currentAsset  = \(newValue) *** msgId = \(newValue?.msgId)")
            self.resetCurrentPlyingStatus()
            if newValue != nil {
                self.viewModel.messageList.forEach { obj in
                    if obj.msgId == newValue?.msgId {
                        self.playingModel = obj
                    }
                }
            }
            self.isRefreshMessage.toggle()
        }.onChange(of: player.playerState) { newValue in
            BuzWatchInfoLog.debug(msg: "player.playerState = \(newValue) *** msgId = \(self.playingModel?.msgId ?? -1)")
            if newValue == .ended {
                self.playingModel?.playerState = 0
            } else if newValue == .buffing || newValue == .loading {
                self.playingModel?.playerState = 1
                self.playingModel?.isRead = true
                BuzWatchInfoLog.debug(msg: "self.player.currentAsset?.msgId = \(self.player.currentAsset?.msgId) convType = \(self.player.currentAsset?.convType)")
                if let model = self.player.currentAsset {
                    WatchDataSourceManager.shared.setIMManageRead(msgId: model.msgId ?? 0, convType: model.convType ?? 1, serverMsgId: model.serverMsgId ?? 0, targetId: model.targetId ?? 0)
                }
            } else if newValue == .playing {
                self.playingModel?.playerState = 2
                self.playingModel?.isRead = true
                BuzWatchInfoLog.debug(msg: "self.player.currentAsset?.msgId = \(self.player.currentAsset?.msgId) convType = \(self.player.currentAsset?.convType)")
                if let model = self.player.currentAsset {
                    WatchDataSourceManager.shared.setIMManageRead(msgId: model.msgId ?? 0, convType: model.convType ?? 1, serverMsgId: model.serverMsgId ?? 0, targetId: model.targetId ?? 0)
                }
            }
            self.isRefreshMessage.toggle()
        }.onChange(of: player.isPlaying) { newValue in
            if let block = self.playBackBlock {
                BuzWatchInfoLog.debug(msg: "self.isPlayering = \(newValue)")
                block(newValue)
            }
        }.onDisappear {
            self.stopPlaying()
        }.onChange(of: viewModel.isScroll) { _ in
            self.scrollToTarget(scrollView: scrollView)
        }.onChange(of: scenePhase) { newScenePhase in
            switch newScenePhase {
            case .active:
                BuzWatchInfoLog.debug(msg: "App active")
            case .inactive:
//                self.stopPlaying()
                BuzWatchInfoLog.debug(msg: "App inactive")
            case .background:
//                self.stopPlaying()
                BuzWatchInfoLog.debug(msg: "App background")
            @unknown default:
                NSLog("Others")
            }
        }.clipped()
    }

    @ViewBuilder
    private func loadingView() -> some View {
        GeometryReader { geometryProxy in
            VStack {
                
                if #available(watchOS 11.0, *) {
                    EmptyView()
                } else {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .frame(width: 26.0, height: 30.0, alignment: .center)
                }
                
                Text(Localizable.common_loading)
                    .font(.system(size: 17.0))
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
        }
    }

    /// 播放音频
    func playAudio(model: WatchHistoryListModel) {
        for item in viewModel.messageList { /// 防止其他地方播放的
            if item.playerState != 0 {
                item.playerState = 0
            }
        }

        WatchDataSourceManager.shared.eventTracking(label: ["exclusive_id": model.isGroup ? "AC2022091421" : "AC2022091414"])
        if model.isRead || model.isSelf || model.isAIbot { /// 自己或者已播放，播放当前
            player.playWithUrl(url: model.content,
                               targetId: model.target,
                               msgId: model.msgId,
                               serverMsgId: model.serverMsgId,
                               convType: model.convType,
                               traceId: "",
                               cryptKey: model.cryptKey,
                               cryptIV: model.cryptIV, voicemoji: model.voicemoji , senderNickName: model.senderNickName)
        } else {
            let firstIndex = viewModel.messageList.firstIndex { item in
                item.msgId == model.msgId
            }
            if let index = firstIndex {
                let fromIndex = viewModel.messageList.count - index
                let datas = viewModel.messageList.suffix(fromIndex)
                for obj in datas {
                    if obj.type == MessageType.leaveVoice.rawValue && !obj.isRead && !obj.isSelf { /// 不是我的且不是已读的
                        player.playWithUrl(url: obj.content,
                                           targetId: obj.target,
                                           msgId: obj.msgId,
                                           serverMsgId: model.serverMsgId,
                                           convType: model.convType,
                                           traceId: "",
                                           cryptKey: model.cryptKey,
                                           cryptIV: model.cryptIV, voicemoji: model.voicemoji, senderNickName: model.senderNickName)
                    }
                }
            }
        }
    }

    /// 重置状态
    func resetCurrentPlyingStatus() {
        playingModel?.playerState = 0
        playingModel = nil
    }

    func stopPlaying() {
        if player.isPlaying {
            player.stop {
            }
            resetCurrentPlyingStatus()
            isRefresh.toggle()
        }
    }
}

extension WatchHistoryContentView {
    private func requestData() {
        if !isAppear {
            isAppear = true
            viewModel.model = model
            viewModel.getNestData {
                self.isFristLoad = true
            }
            registerNotification()
        }
    }

    func loadData() {
        if viewModel.isLoadData {
            return
        }
        viewModel.getPreviousData {
            self.isFristLoad = false
        }
    }

    func audioPlayStateChanged(_ notification: Notification) {
        if let player = notification.object as? WatchAudioPlayManager {
            BuzWatchInfoLog.debug(msg: "notification.audioPlayStateChanged = \(player.playerState) *** msgId = \(player.currentAsset?.msgId)")
            for item in viewModel.messageList {
                if item.msgId == player.currentAsset?.msgId {
                    item.isRead = true
                    if player.playerState == .ended {
                        item.playerState = 0
                    } else if player.playerState == .buffing || player.playerState == .loading {
                        item.playerState = 1
                    } else if player.playerState == .playing {
                        item.playerState = 2
                    }
                }
            }
        }
    }

    func reloadState() {
        if playingModel != nil {
            let newValue = player.playerState
            BuzWatchInfoLog.debug(msg: "notification.audioPlayStateChanged = \(newValue) *** msgId = \(player.currentAsset?.msgId)")
            if newValue == .ended {
                playingModel?.playerState = 0
            } else if newValue == .buffing || newValue == .loading {
                playingModel?.playerState = 1
                playingModel?.isRead = true
            } else if newValue == .playing {
                playingModel?.playerState = 2
                playingModel?.isRead = true
            }
        }
    }
}

extension WatchHistoryContentView {
    private func registerNotification() {
        NotificationCenter.default.addObserver(forName: NSNotification.Name(NSNotificationName.sendIMReceipt.rawValue), object: nil, queue: nil, using: sendIMReceipt)
        NotificationCenter.default.addObserver(forName: NSNotification.Name(NSNotificationName.userSpeaking.rawValue), object: nil, queue: nil, using: userSpeaking)
        NotificationCenter.default.addObserver(forName: NSNotification.Name(NSNotificationName.audioPlayStateChanged.rawValue), object: nil, queue: nil, using: audioPlayStateChanged)
    }

    func sendIMReceipt(_ notification: Notification) {
        BuzWatchInfoLog.debug(msg: "发送消息 isSucessed")
        if let obj = notification.object as? Dictionary<String, Any>, let content = obj["content"] as? Dictionary<String, Any> {
            if let isSucessed = content["isSucessed"] as? Bool, let msgId = content["msgId"] as? Int64 {
                viewModel.messageList.forEach { obj in
                    if obj.msgId == msgId {
                        obj.isSendFail = !isSucessed
                        self.isRefresh.toggle()
                        BuzWatchInfoLog.debug(msg: "发送消息：更新数据")
                    }
                }
                BuzWatchInfoLog.debug(msg: "发送消息 isSucessed = \(isSucessed) , msgId = \(msgId)")
            }
        }
    }

    func userSpeaking(_ notification: Notification) {
        BuzWatchInfoLog.debug(msg: "收到实时说话消息 = \(String(describing: notification.object))")
        if let obj = notification.object as? Dictionary<String, Any>, let model = WatchSpeakingModel.yy_model(withJSON: obj["content"] as Any) {
        }
    }
}
