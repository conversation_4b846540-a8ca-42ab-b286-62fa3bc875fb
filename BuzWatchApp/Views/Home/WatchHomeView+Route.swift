//
//  WatchHomeView+Route.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/9/2.
//  Copyright © 2023 lizhi. All rights reserved.
//

import SwiftUI
import BuzConfig

extension WatchHomeView {
    @ViewBuilder
    func routeView() -> some View {
        WatchRouteProtocol.routeView(item: WatchAppDelegate.sharedRoute)
    }
    
    func pushRoute() {
        if Self.sharedView == nil {
            return
        }
        if WatchAppDelegate.sharedRoute.url != nil {
            if WatchAppDelegate.sharedRoute.keys.count != 0 {
                var animateTime : Double = 0
                WatchAppDelegate.sharedRoute.keys.forEach { key in
                    if let presentMode = WatchAppDelegate.sharedRoute.presents[key] {
                        DispatchQueue.main.asyncAfter(deadline: .now() + animateTime) {
                            if presentMode.wrappedValue.isPresented {
                                presentMode.wrappedValue.dismiss()
                            }
                        }
                    }
                    
                    animateTime = animateTime + BuzConfig.navigationBackAnimateDelay
                }
             
                WatchAppDelegate.sharedRoute.removeAllPresentMode()
                
                DispatchQueue.main.asyncAfter(deadline: .now() + animateTime + BuzConfig.navigationBackAnimateDelay) {
                    if WatchAppDelegate.sharedRoute.url != nil {
                        isPushAutomaticly = true // Automatically activate the NavigationLink after 2 seconds
                    }
                }
            } else {
                DispatchQueue.main.asyncAfter(deadline: .now() + BuzConfig.pushRouteDelay) {
                    if WatchAppDelegate.sharedRoute.url != nil {
                        isPushAutomaticly = true // Automatically activate the NavigationLink after 2 seconds
                    }
                }
            }
        }
    }
    
    func gotoTargetId(targetId : Int64) {
        if let index = self.datas.firstIndex(where: { model in
            return model.userId == "\(targetId)"
        }) {
            self.activePageIndex = index
        } else {
            self.targetId = targetId
        }
    }
}
