//
//  WatchHomeView+Complications.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/9/2.
//  Copyright © 2023 lizhi. All rights reserved.
//

import SwiftUI
import SDWebImageSwiftUI
import BuzConfig

extension WatchHomeView {
    func saveRecentUser() {
        if self.datas.isEmpty {
            BuzWatchInfoLog.info(msg:"saveRecentUser  empty")
            WatchDataSourceManager.shared.saveRecentUser(userId: nil, icon: "")
        } else {
            BuzWatchInfoLog.info(msg:"saveRecentUser  count of list \(self.datas.count)")
            if let item = self.datas.first {
                if let url = URL.init(string: ((item.portrait ?? "") as NSString).toThumbnail(size: 100) as String) {
                    BuzWatchInfoLog.info(msg:"saveRecentUser  first item of list \(url)")
                    if let groupsPath = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: BuzConfig.groupStoreId)?.path {
                        //4.拼接图片的路径
                        let path = groupsPath + "/" + url.absoluteString.md5 + ".jpg"
                        
                        if FileManager.default.fileExists(atPath: path) {
                            WatchDataSourceManager.shared.saveRecentUser(userId: item.userId, icon: url.absoluteString)
                            return
                        }
                        
                        if let sdImageManager = self.sdImageManager {
                            sdImageManager.cancel()
                        }
                        
                        self.sdImageManager = ImageManager()
                        self.sdImageManager?.setOnSuccess { p, data, type in
                            if let data = data {
                                do {
                                    let img : UIImage? = UIImage(data: data)
                                    try img?.pngData()?.write(to: URL.init(fileURLWithPath: path))
                                    WatchDataSourceManager.shared.saveRecentUser(userId: item.userId, icon: url.absoluteString)
                                    BuzWatchInfoLog.info(msg:"saveRecentUser  download success \(item.userId ?? "-1")  \(url)")
                                }catch{
                                    BuzWatchInfoLog.info(msg:"saveRecentUser  download fail exception \(item.userId ?? "-1")  \(url)")
                                    BuzWatchInfoLog.debug(msg: "get resourceValues error = \(error)")
                                }
                            } else {
                                BuzWatchInfoLog.info(msg:"saveRecentUser  download fail \(item.userId ?? "-1")  \(url)")
                            }
                            
                            self.sdImageManager = nil
                        }
                        
                        self.sdImageManager?.setOnFailure(perform: { error in
                            self.sdImageManager = nil
                        })
                        
                        self.sdImageManager?.load(url: url)
                    }
                }
            }
        }
    }
}
