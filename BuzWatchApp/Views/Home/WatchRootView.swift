//
//  WatchHomeList.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/7/20.
//  Copyright © 2023 lizhi. All rights reserved.
//

import SwiftUI
import UserNotifications
import WatchConnectivity
import Localizable
import BuzLocalizable
import AVKit
import AVFoundation

struct WatchRootView: View{
    @State var isLogin : Bool? = nil {
        didSet {
            WatchDataSourceManager.shared.saveIsLogin(isLogin: self.isLogin ?? false)
        }
    }
    
    @State var isAppear = false
    @State var isSucceed = false
    @State var isLoading : Bool?
    @State var isUseFirst = WatchDataSourceManager.shared.getUseFirst()
    @State var isShowAlert = false
    @State var activationState = WCSession.default.activationState
    @State var customPlayer : AVPlayer? = {
        let bundlePath = Bundle.main.path(forResource: "leaveVoice", ofType: "wav")!
        return AVPlayer.init(url: URL.init(fileURLWithPath: bundlePath))
    }()
    
    var body: some View {
        GeometryReader { geometryProxy in
            ZStack {
                VStack {
                    if self.isSucceed == true{
                        if self.isLogin == true{
                            if !isUseFirst{
                                guideView()
                            }else{
                                WatchHomeView()
                            }
                        }else if self.isLogin == false{
                            logOutView()
                        }
                    }else{
                        if self.isLoading == true {
                            loadingView()
                        }else if self.isLoading == false {
                            networkErrorView()
                        }
                    }
                }
                if isShowAlert{
                    notificationGuideView()
                }
                VideoPlayer(player: self.customPlayer).focusable(false).opacity(0.0).frame(height: 0.01).hidden().id("leaveVoice")
            }.onAppear {
                DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 0.5, execute: DispatchWorkItem.init(block: { ///  app activation
                    loadData()
                }))
                requestNotification()
            }.onChange(of: WatchSessionActivationManager.shared.activationState) { newValue in
                self.activationState = newValue
                BuzWatchInfoLog.debug(msg: "self.activationState ****** = \(self.activationState)")
            }
        }.toolbar {
//            ToolbarItem(placement: .cancellationAction) {
//                if self.activationState != .activated{
//                    Image(systemName: "network")
//                        .resizable()
//                        .aspectRatio(contentMode: .fill)
//                        .frame(width: 18.0, height: 18.0)
//                        .foregroundColor(Color.watchSecondaryErrorColor())
//                }
//            }
        }
    }
    
    @ViewBuilder
    private func logOutView() -> some View{
        GeometryReader { geometryProxy in
            VStack {
                Image("logout")
                Text(Localizable.login_please_on_iphone_tip)
                    .font(.system(size: 17.0))
                    .multilineTextAlignment(.center)
                    .padding([.leading , .trailing] , 20)
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
        }
    }
    
    @ViewBuilder
    private func loadingView() -> some View{
        GeometryReader { geometryProxy in
            VStack {
                if #available(watchOS 11.0, *) {
                    EmptyView()
                } else {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .frame(width: 26.0, height: 30.0, alignment: .center)
                }
                
                Text(Localizable.loadingXXX())
                    .font(.system(size: 17.0))
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
        }
    }
    
    @ViewBuilder
    private func guideView() -> some View{
        GeometryReader { geometryProxy in
            ScrollView {
                LottieAnimationContentView(filename: "Lottie_NewUserGuide")
                    .padding([.top] , -3)
                    .frame(width: 164.0, height: 100.0, alignment: .center)
                Text(Localizable.loginintobuz())
                    .font(.system(size: 22.5).bold())
                    .foregroundColor(Color.watchPrimaryColor())
                    .padding([.leading , .trailing] , 20)
                    .padding([.top] , -8)
                Text(Localizable.setting_watch_record_introduce)
                    .font(.system(size: 14.0))
                    .multilineTextAlignment(.center)
                    .padding([.leading , .trailing] , 20)
                    .padding([.top] , 1)
                Spacer(minLength: 12)
                Button {
                    WatchDataSourceManager.shared.saveUseFirst(isUseFirst: true)
                    self.isUseFirst = true
                } label: {
                    Text(Localizable.ok())
                        .foregroundColor(.black)
                }.frame(width: geometryProxy.size.width - 20.0, height: 50.0, alignment: .center)
                    .background(Color.watchPrimaryColor())
                    .cornerRadius(25.0)
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
        }
    }
    
    @ViewBuilder
    private func networkErrorView() -> some View{
        GeometryReader { geometryProxy in
            WatchLoadingErrorView {
                self.isAppear = false
                self.loadData()
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
        }
    }
    
    @ViewBuilder
    private func notificationGuideView() -> some View{
        GeometryReader { geometryProxy in
            ScrollView {
                VStack {
                    Text(Localizable.setting_watch_authorization.localized)
                        .font(.system(size: 25.0))
                        .foregroundColor(Color.watchPrimaryColor())
                    Text(Localizable.system_please_allow_access_mic)
                        .font(.system(size: 17.0))
                        .multilineTextAlignment(.leading)
                        .padding([.top] , 4)
                    Button {
                        let options: UNAuthorizationOptions = [.sound, .alert, .badge]
                        UNUserNotificationCenter.current().requestAuthorization(options: options) { (granted, error) in
                            WatchDataSourceManager.shared.eventTracking(label: ["exclusive_id" : "RB2022123001" , "enable" : granted ? "enable" : "disable"])
                        }
                        self.isShowAlert.toggle()
                    } label: {
                        Text(Localizable.Allowaccess())
                            .foregroundColor(.black)
                    }.frame(width: geometryProxy.size.width  - 20, height: 50.0, alignment: .center)
                        .background(Color.watchPrimaryColor())
                        .cornerRadius(25.0)
                        .padding([.top] , 20)
                    Button {
                        self.isShowAlert.toggle()
                    } label: {
                        Text(Localizable.setting_watch_not_now)
                            .foregroundColor(.white)
                    }.frame(width: geometryProxy.size.width  - 20, height: 50.0, alignment: .center)
                        .background(Color.watchWhiteColor(alpha: 0.12))
                        .cornerRadius(25.0)
                        .padding([.top] , 5)
                        .buttonStyle(PlainButtonStyle())
                    
                    
                    
                }.padding([.leading , .trailing] , 20)
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
                .background(.black)
        }
    }

}

extension WatchRootView {
    private func loadData(){
        if !isAppear{
            isAppear = true
            isLoading = true
            WatchDataSourceManager.shared.getLoginStatus { isSucceed , isLogin in
                if Thread.isMainThread {
                    self.isSucceed = isSucceed
                    self.isLogin = isLogin
                    self.isLoading = false
                } else {
                    DispatchQueue.main.async {
                        self.isSucceed = isSucceed
                        self.isLogin = isLogin
                        self.isLoading = false
                    }
                }
                BuzWatchInfoLog.info(msg: "getLoginStatus.isSucceed = \(isSucceed) isLogin = \(isLogin)")
            }
            self.registerNotification()
        }
    }
    
    private func requestNotification(){
        let current = UNUserNotificationCenter.current()
         current.getNotificationSettings { (settings:UNNotificationSettings) in
             if settings.authorizationStatus == .notDetermined {
                 self.isShowAlert.toggle()
             }
             BuzWatchInfoLog.debug(msg: "UIDevice.current.settings = \(settings.authorizationStatus)")
        }
    }
    
    ///退出
    func logout(_ notification: Notification){
        BuzWatchInfoLog.debug(msg: "用户退出")
        DispatchQueue.main.async {
            self.isLogin = false
        }
    }
    
    ///用户登录
    func login(_ notification: Notification){
        BuzWatchInfoLog.debug(msg: "用户登录")
        DispatchQueue.main.async {
            self.isLogin = true
        }
    }
    
    private func registerNotification(){
        NotificationCenter.default.addObserver(forName: NSNotification.Name(NSNotificationName.userLogout.rawValue), object: nil, queue: nil, using: self.logout)
        NotificationCenter.default.addObserver(forName: NSNotification.Name(NSNotificationName.userLogin.rawValue), object: nil, queue: nil, using: self.login)
    }
}


