//
//  WatchHomeView+Extension.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/9/3.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import BuzConfig

extension WatchHomeView {
    func cacheDataList(data : Array<WatchHomeListModel>) {
        if let groupsPath = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: BuzConfig.groupStoreId)?.path {
            //4.拼接图片的路径
            let path = groupsPath + "/homePageCache.json"
            let datas = data as NSArray
            do {
                try datas.yy_modelToJSONString()?.write(toFile: path, atomically: true, encoding: .utf8)
            }catch{
                BuzWatchInfoLog.debug(msg: "get resourceValues error = \(error)")
            }
        }
    }
    
    func readCacheData() -> Array<WatchHomeListModel> {
        if let groupsPath = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: BuzConfig.groupStoreId)?.path {
            //4.拼接图片的路径
            let path = groupsPath + "/homePageCache.json"
            do {
                let data = try String.init(contentsOfFile: path)
                if let array : Array<WatchHomeListModel> = (NSArray.yy_modelArray(with: WatchHomeListModel.self, json: data) ?? Array()) as? Array<WatchHomeListModel> {
                    return array
                }
            }catch{
                BuzWatchInfoLog.debug(msg: "get resourceValues error = \(error)")
            }
        }
        
        return Array<WatchHomeListModel>()
    }
}

extension WatchHomeView {
    func sendIMReceipt(_ notification: Notification){
        BuzWatchInfoLog.debug(msg: "发送消息成功回执 = \(notification.object)")
        if !self.isRecording && self.isShowView{ ///录制不展示
            if let object = notification.object as? Dictionary<String, Any> , let content = object["content"] as? Dictionary<String, Any> ,
               let isSucessed = content["isSucessed"] as? Bool, isSucessed{
                DispatchQueue.main.async {
                    self.isSendMessageSucceed = true
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0, execute: DispatchWorkItem.init(block: {
                        self.isSendMessageSucceed = false
                    }))
                }
            }else{
                self.isShowSendFail = true
            }
        }
    }
    
    func updateConversation(_ notification: Notification){
        BuzWatchInfoLog.debug(msg: "收到实时更新会话消息 = \(String(describing: notification.object))")
        if let obj = notification.object as? Dictionary<String , Any>, let model = WatchHomeListModel.yy_model(withJSON: obj["content"] as Any){
            DispatchQueue.main.async {
                if let item = self.datas.first(where: { titem in
                    return titem.userId == model.userId
                }) {
                    if !item.equalInformation(model) {
                        item.quietMode = model.quietMode
                        item.unReadCount = model.unReadCount
                        item.onlineState = model.onlineState
                        item.portrait = model.portrait
                        item.name = model.name
                        item.isThinking = model.isThinking
                        BuzWatchInfoLog.debug(msg: "收到实时更新会话消息 portrait = \(model.portrait) self.activePageIndex  =\(self.activePageIndex)")
                        self.isRefresh.toggle()
                        self.saveRecentUser()
                    }
                }
            }
        }
    }
    
    func userSpeaking(_ notification: Notification){
        BuzWatchInfoLog.debug(msg: "收到实时更新说话消息: isRecording = \(isRecording) userinfo = \(String(describing: notification.object))")
        if let obj = notification.object as? Dictionary<String , Any>, let model = WatchSpeakingModel.yy_model(withJSON: obj["content"] as Any){
            if self.isRecording { ///播放中缓存
                self.speakingCache.append(model)
            }else{
                player.playWithUrl(url: model.url, 
                                   targetId: Int64(model.sourceId ?? "0") ,
                                   msgId: model.msgId ,
                                   serverMsgId: model.serverMsgId ,
                                   convType: model.convType ,
                                   traceId: model.traceId,
                                   cryptKey: model.cryptKey,
                                   cryptIV: model.cryptIV, voicemoji: model.voicemoji , senderNickName: model.senderNickName)
            }
        }
    }
    
    func updateConversationList(_ notification: Notification){
        BuzWatchInfoLog.debug(msg: "收到实时更新列表消息 1 = \(String(describing: notification.object))")
        if let obj = notification.object as? Dictionary<String , Any>, let array = NSArray.yy_modelArray(with: WatchHomeListModel.self, json: obj["content"] ?? Array<Any>()){
            if let list =  array as? Array<WatchHomeListModel>{
                if self.isShowView && !self.isRecording{
                    self.datas = list
                    BuzWatchInfoLog.debug(msg: "收到实时更新列表 2 = target \(self.player.currentAsset?.targetId)")
                    self.scrollToIndex(userId: self.player.currentAsset?.targetId ?? 0)
                }else{
                    self.tempDatas = list
                }
            }
            self.saveRecentUser()
        }
    }
    
    func modeChangeNotification(_ notification: Notification){
        BuzWatchInfoLog.debug(msg: "modeChangeNotification  = \(notification.object)")
        if let mode = notification.object as? PersonalStatusType , mode == .quiet{
            self.player.stop {
                
            }
        }
    }
    
    func registerNotification(){
        BuzWatchInfoLog.debug(msg: "注册通知")
        NotificationCenter.default.addObserver(forName: NSNotification.Name(NSNotificationName.sendIMReceipt.rawValue), object: nil, queue: nil, using: self.sendIMReceipt)
        NotificationCenter.default.addObserver(forName: NSNotification.Name(NSNotificationName.updateConversation.rawValue), object: nil, queue: nil, using: self.updateConversation)
        NotificationCenter.default.addObserver(forName: NSNotification.Name(NSNotificationName.userSpeaking.rawValue), object: nil, queue: nil, using: self.userSpeaking)
        NotificationCenter.default.addObserver(forName: NSNotification.Name(NSNotificationName.updateConversationList.rawValue), object: nil, queue: nil, using: self.updateConversationList)
        NotificationCenter.default.addObserver(forName: NSNotification.Name(NSNotificationName.modeChangeNotification.rawValue), object: nil, queue: nil, using: self.modeChangeNotification)
    }
    
    func playAudio(target : Int64 , msgId : Int64 , content : String){
        
        BuzWatchInfoLog.debug(msg: "收到实时说话消息 : target = \(target) msgId = \(msgId) content = \(content)")
        
        guard let url = URL(string: content) else { return }
        var path = content
    
        ///local file modify
        if !content.hasPrefix("http") {
            path = url.relativePath
            
            LocalFileRequest.init(targetId: target, msgId: msgId, filePath: path) {
                isSucceed , message , error in
                BuzWatchInfoLog.debug(msg: "收到实时说话消息.拉取文件 : isSucceed = \(isSucceed) error = \(error) message = \(message)")
                
                if !isSucceed {
                    if  url.pathExtension != "opus"{
                        AudioPlay.shared.playWithUrl(fileUrl: content)
                    } else {
                        
                    }
                } else {
                    AudioPlay.shared.playWithUrl(fileUrl: message?["filePath"] as? String ?? "")
                }
                self.isSpeaking = false
            }.execute()
        } else {
            AudioPlay.shared.playWithUrl(fileUrl: content)
        }
    }
}

extension WatchHomeView {
    func reportTrackStartRecord(model : WatchHomeListModel , traceId : String , ntpTime : Int64){
        let targetId = Int64(model.userId ?? "0") ?? 0
        let isSendToSelf = (targetId == WatchDataSourceManager.shared.uid)
        MessageTraceIdTool.assertTraceIdNotNull(traceId)
        WatchDataSourceManager.shared.eventTracking(label: [
            "exclusive_id" : "EVENT_BUZ_RTP_RECORD_START",
            "isGroup" : model.isGroup ,
            "targetId" : targetId,
            "isSendtoSelf" : isSendToSelf ? "Y" : "N",
            "timestamp" : Date.init().timeIntervalSince1970 * 1000 ,
            "traceId" : traceId,
            "ntpTime" : ntpTime,
        ])
    }
    
    func reportTrackStartRecordResult(model : WatchHomeListModel , traceId : String , result : Bool , errorCode : Int64){
        MessageTraceIdTool.assertTraceIdNotNull(traceId)
        WatchDataSourceManager.shared.eventTracking(label: [
            "exclusive_id" : "EVENT_BUZ_RTP_VOICE_RECORD_RESULT",
            "isGroup" : model.isGroup ,
            "targetId" : Int64(model.userId ?? "0") ?? 0 ,
            "traceId" : traceId,
            "result" : result,
            "errorCode" : errorCode,
        ])
    }
    
    func reportTrackPlayResult( targetId : Int64? , isGroup : Bool , traceId : String? , isSuccess : Bool){
        WatchDataSourceManager.shared.eventTracking(label: [
            "exclusive_id" : "RB2023090601",
            "page_business_type" : isGroup ? "group" : targetId == 12345 ? "robot" : "private" ,
            "page_business_id" : targetId ?? 0,
            "content_id" : traceId ?? "",
            "log_time" : NTPTimeTool.nowTimestamp(),
            "is_success" : isSuccess
        ])
    }
}
