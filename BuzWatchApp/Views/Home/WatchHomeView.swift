//
//  WatchHomeView.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/8/16.
//  Copyright © 2023 lizhi. All rights reserved.
//

import SwiftUI
import SDWeb<PERSON>mageSwiftUI
import Localizable
import BuzLocalizable
import AVFoundation
import BuzConfig

struct WatchHomeView: View{
    @Environment(\.scenePhase) private var scenePhase
    static var sharedView : WatchHomeView? = WatchHomeView()
    var audioRecord = AudioRecorder.shared
    @StateObject var player = WatchAudioPlayManager()
    var audioRecordTipsDuration = 50
    
    @State var speakingCache = Array<WatchSpeakingModel>()
    @State var sdImageManager : ImageManager?
    @State var isPushAutomaticly = false
    
    @State var isScrollOffset = Double(0)
    @State var isStartRecord = false
    @State var isRecording = false
    @State var isAppear = false
    @State var isSpeaking = false
    ///data
    @State var selectModel : WatchHomeListModel?
    @State var activePageIndex: Int = 0
    @State var datas = Array<WatchHomeListModel>() {
        didSet {
            self.cacheDataList(data: self.datas)
        }
    }
    @State var tempDatas : Array<WatchHomeListModel>?
    @State var recordDuration = Int8(0)
    @State var isSendMessageSucceed = false
    @State var isRefresh = false
    @State var isShowAlert = false
    @State var isLoading = false
    @State var isRequestSucessed = false
    @State var isShowView = false
    @State var isShowSendFail = false
    @State var isShowRecordDurationAlert = false
    
    private var stateText: String {
        get {
            var text = ""
            
            if player.currentAsset?.convType == 3 {
                if let emoji =  player.currentAsset?.voicemoji , emoji.count > 0 {
                    text = "\(player.currentAsset?.senderNickName ?? "") [\(Localizable.ve_coicemoji_updated)]"
                }else {
                    text = "\(player.currentAsset?.senderNickName ?? "") \("speaking".localized)"
                }
            }else {
                if let emoji =  player.currentAsset?.voicemoji , emoji.count > 0 {
                    text = "[\(Localizable.ve_coicemoji_updated)]"
                }else {
                    text = "speaking".localized
                }
            }
            
            
            return text
        }
    }
    
    var padding = 6.0
    let functionItemMaxSize = 36.0
    @State var targetId : Int64?

    var body: some View {
        GeometryReader { GeometryProxy in
            if self.isLoading{
                loadingView()
            }else{
                ZStack(alignment: .top) {
                    if isRequestSucessed{
                        if self.datas.count <= 0{
                            addFriendTipsView()
                        }else{
                            contentView()
                        }
                    }else{
                        WatchLoadingErrorView {
                            self.isAppear = false
                            self.loadData()
                        }
                    }
                }.onDisappear(perform: {
                    self.isShowView = false
                }).onAppear {
                    self.isShowView = true
                    self.loadData()
                    Self.sharedView = self
                    self.pushRoute()
                    WatchDataSourceManager.shared.eventTracking(label: ["exclusive_id" : "AVS2022091401"])
                }.alert(isPresented: $isShowAlert) {
                    return Alert(title: Text(""), message: Text(AVAudioSession.sharedInstance().recordPermission != .granted ? Localizable.system_please_allow_access_mic : Localizable.speaking()), dismissButton:
                            .default(Text(Localizable.system_set_on_iphone), action: {
                                self.isShowAlert = false
                            }))
                }.onChange(of: self.activePageIndex) { newValue in
                    BuzWatchInfoLog.debug(msg: "activePageIndex update = \(Int(newValue))")
                    if self.activePageIndex != Int(isScrollOffset){
                        self.isScrollOffset = Double(self.activePageIndex)
                        self.playShake(.start)
                    }
                }.onChange(of: isScrollOffset) { newValue in
                    BuzWatchInfoLog.debug(msg: "isScrollOffset update = \(Int(newValue))")
                    if player.isPlaying {
                        return ;
                    }
                    if !self.isRecording{
                        if self.activePageIndex != Int(newValue) && Int(newValue) < self.datas.count{
                            withAnimation(Animation.easeInOut(duration: 0.3)) {
                                self.activePageIndex = Int(newValue)
                            }
                        }
                    }
                }.onChange(of: player.playerState) { newValue in
                    if newValue == .ended {
                        self.isSpeaking = false
                    }else if newValue == .buffing || newValue == .loading{
                        self.isSpeaking = true
                        BuzWatchInfoLog.info(msg: "self.player.currentAsset?.msgId = \(self.player.currentAsset?.msgId) convType = \(self.player.currentAsset?.convType)")
                        if let model = self.player.currentAsset{
                            BuzWatchInfoLog.info(msg: "self.player.currentAsset?.msgId setIMManageRead")
                            self.scrollToIndex(userId: model.targetId ?? 0)
                            WatchDataSourceManager.shared.setIMManageRead(msgId: model.msgId ?? 0, convType: model.convType ?? 1, serverMsgId: model.serverMsgId ?? 0, targetId: model.targetId ?? 0)
                        }
                    }else if newValue == .playing{
                        if let model = self.player.currentAsset{
                            self.reportTrackPlayResult(targetId: model.targetId, isGroup: model.convType == 3, traceId: model.traceId, isSuccess: true)
                        }
                    }else if newValue == .fail{
                        if let model = self.player.currentAsset{
                            self.reportTrackPlayResult(targetId: model.targetId, isGroup: model.convType == 3, traceId: model.traceId, isSuccess: false)
                        }
                    }
                    BuzWatchInfoLog.debug(msg: "player.playerState = \(newValue) msgId = \(player.currentAsset?.msgId)")
                }.onChange(of: player.currentAsset) { newValue in
//                    NotificationCenter.default.post(name: NSNotification.Name(NSNotificationName.audioPlayAssetChanged.rawValue), object: self.player)
                    let targetId = newValue?.targetId ?? 0
                    self.scrollToIndex(userId: targetId)
                }.onChange(of: scenePhase) { newScenePhase in
                    switch newScenePhase {
                    case .active :
                        loadDataAppActive()
                        BuzWatchInfoLog.debug(msg: "App active")
                    case .inactive :
//                        self.player.stop {
//                            
//                        }
                        BuzWatchInfoLog.debug(msg: "App inactive")
                    case .background :
                        BuzWatchInfoLog.debug(msg: "App background")
                        stopRecord()
                    @unknown default :
                        NSLog("Others")
                    }
                }.onChange(of: self.isShowView) { newValue in
                    if newValue == true{
                        if let list = self.tempDatas{
                            self.datas = list
                            if self.player.isPlaying ,let model = self.player.currentAsset { ///正在播放
                                self.scrollToIndex(userId: model.targetId ?? 0)
                            }else{
                                self.activePageIndex = 0
                            }
                            self.tempDatas = nil
                        }else{
                            if self.player.isPlaying ,let model = self.player.currentAsset { ///正在播放
                                self.scrollToIndex(userId: model.targetId ?? 0)
                            }
                        }
                    }
                }
            }
        }.ignoresSafeArea(edges: .bottom)
            .onAppear {
                WatchAppDelegate.sharedRoute.removeAllPresentMode()
            }
        
        if isPushAutomaticly {
            NavigationLink(
                destination: self.routeView(),
                isActive: $isPushAutomaticly) {
                    EmptyView()
                }.buttonStyle(PlainButtonStyle())
        }
    }
}

extension WatchHomeView {
    func realLoadData(complete : ((Bool, Array<WatchHomeListModel>) -> Void)?) {
        BuzWatchInfoLog.info(msg: "getHomeList 1")
        DispatchQueue.main.asyncAfter(deadline: .now(), execute: DispatchWorkItem.init(block: {
            BuzWatchInfoLog.info(msg: "getHomeList 2")
            WatchDataSourceManager.shared.getHomeList { isSucceed, list in
                DispatchQueue.main.async {
                    BuzWatchInfoLog.info(msg: "getHomeList 3")
                    complete?(isSucceed, list)
                    self.saveRecentUser()
                    
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                        if let targetId = self.targetId {
                            self.gotoTargetId(targetId: targetId)
                            self.targetId = nil
                        }
                    }
                }
                WatchDataSourceManager.shared.eventTracking(label: ["exclusive_id" : "RB2023081404","page_business_type" : "home","is_success" : isSucceed])
                BuzWatchInfoLog.info(msg: "getHomeList.isSucceed = \(isSucceed) list = \(list.count)")
            }
        }))
    }
    
    private func loadData(){
        BuzWatchInfoLog.info(msg: "getHomeList 0")
        if !isAppear{
            self.isAppear = true
            self.datas = self.readCacheData()
            
            if self.datas.count != 0 {
                isRequestSucessed = true
                self.realLoadData { isSuccess, list in
                    if isSuccess{
                        datas = list
                        WatchDataSourceManager.shared.isCanSpeaking = true
                    }
                }
            } else {
                self.isLoading = true
                self.realLoadData { isSuccess, list in
                    if isSuccess{
                        datas = list
                        WatchDataSourceManager.shared.isCanSpeaking = true
                    }
                    self.isLoading = false
                    self.isRequestSucessed = isSuccess
                }
            }
            
            self.registerNotification()
        }
    }
    
    private func loadDataAppActive(){
        WatchDataSourceManager.shared.getHomeList { isSucceed, list in
            DispatchQueue.main.async {
                if isSucceed{
                    self.datas = list
                }
                self.saveRecentUser()
            }
        }
    }
}


extension WatchHomeView {
    
    @ViewBuilder func contentView() -> some View{
        friendListView()
        if self.isRecording{
            recordMaskView()
        }
        sendSucessedView()
        recordingView()
        speakingView()
        functionView()
        userInfoView()
        if isShowSendFail{
            sendFailView()
        }
        if isShowRecordDurationAlert{
            WatchAlertRecordDurationShortView(text: Localizable.chatvoicetooshorttosend()) {
                self.isShowRecordDurationAlert = false
            }
        }
    }
    
    @ViewBuilder
    private func loadingView() -> some View{
        GeometryReader { geometryProxy in
            VStack {
                
                if #available(watchOS 11.0, *) {
                    EmptyView()
                } else {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .frame(width: 26.0, height: 30.0, alignment: .center)
                }
                
                Text(Localizable.common_loading)
                    .font(.system(size: 17.0))
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
        }
    }
    
    @ViewBuilder
    private func recordMaskView() -> some View{
        WatchLinearGradientAnimationView()
    }
    
    @ViewBuilder
    private func circleView() -> some View{
        Circle()
            .strokeBorder(Color.white.opacity(0.16),lineWidth: isMinScreen() ? 2 : 3)
            .background(Circle().foregroundColor(Color.clear))
            .frame(width: isMinScreen() ? 81.0 : screenScaleSize(114), height: isMinScreen() ? 81.0 : screenScaleSize(114))
    }
    
    @ViewBuilder
    private func friendListView() -> some View{
        GeometryReader { geometryProxy in
            HStack(alignment: .center, spacing: 0) {
                ZStack {
                    pagingScrollView().allowsHitTesting(self.isSpeaking ? false : true)
                    WatchShadowView(width: (WKInterfaceDevice.current().screenBounds.width - screenScaleSize(96)) * 0.4)
                    circleView().opacity((self.isSpeaking || self.isRecording) ? 0.0 : 1.0)
                }
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
        }
    }
    
    @ViewBuilder
    private func pagingScrollView() -> some View {
        GeometryReader { geometryProxy in
            ZStack{
                VStack{
                    PagingView(config: .init(margin: 40, spacing: isMinScreen() ? 30 : 20), page: $activePageIndex) {
                        ForEach(0 ..< self.datas.count, id: \.self) { index in
                            let item = datas[index]
                            let isCurrent = (self.activePageIndex == index)
                            ZStack {
                                Circle().fill(Color.watchWhiteColor(alpha: 0.01))
                                    .frame(width: screenScaleSize(96) * 0.4, height: screenScaleSize(96) * 0.4, alignment: .center)
                                    .allowsHitTesting(isCurrent)
                                    .gesture(
                                        TapGesture().onEnded({ Void in
                                            self.startRecord()
                                        })
                                    ).gesture(
                                        LongPressGesture(minimumDuration: 0.5).onEnded({ isStart in
                                            self.startRecord()
                                        })
                                    )
                                webImageView(portrait: item.portrait ?? "", isCurrent: isCurrent).id(item.portrait ?? "")
                            }
                        }
                    }
                }
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
        }
    }
    
    @ViewBuilder func webImageView(portrait : String , isCurrent : Bool) -> some View{
        WebImage(url: URL(string: portrait))
            .resizable().placeholder(Image("icon_placeholder"))
            .frame(width: isMinScreen() ? 71.0 : screenScaleSize(96), height: isMinScreen() ? 71.0 : screenScaleSize(96))
            .clipShape(Circle())
            .contentShape(Circle())
            .background(Color.watchWhiteColor(alpha: 0.1)).opacity(isRecording ? 0.0 : 1.0).clipShape(Circle()).allowsHitTesting(false)
    }
    
    @ViewBuilder func userInfoView() ->some View{
        if self.datas.count > 0 && self.activePageIndex < self.datas.count{
            VStack(alignment: .center, spacing: 0) {
                HStack(spacing:3) {
                    if self.activePageIndex < self.datas.count {
                        if datas[self.activePageIndex].getQuietModeColor != .black{
                            Circle()
                                .fill(datas[self.activePageIndex].getQuietModeColor)
                                .frame(width: 7, height: 7).opacity(isRefresh ? 1.0 : 1.0)
                        }
                        Text(datas[self.activePageIndex].name ?? "")
                            .lineLimit(1)
                            .font(.system(size: isMinScreen() ? 13.0 : screenScaleSize(17.0)).bold())
                            .foregroundColor(.white)
                    }
                }
                if self.isSpeaking{
                    Text(stateText)
                        .font(.system(size: isMinScreen() ? 13.0 : screenScaleSize(14.0)))
                        .foregroundColor(Color.watchPrimaryColor())
                }else{
                    Text(datas[self.activePageIndex].isThinking ? Localizable.setting_watch_thinking :  (self.isRecording ? Localizable.record_tap_to_send : Localizable.record_tap_to_start))
                        .font(.system(size: isMinScreen() ? 13.0 :  screenScaleSize(14.0)))
                        .foregroundColor(.white)
                        .opacity(isRefresh ? 0.5 : 0.5)
                }
                Spacer()
            }.focusable(true) /// 表冠监听
                .digitalCrownRotation($isScrollOffset, from: 0, through: Double(self.datas.count), by: 0.3, sensitivity: .low, isContinuous: false, isHapticFeedbackEnabled: true).padding([.top] ,isMinScreen() ? 0 : -3)
        }
    }
    
    @ViewBuilder
    private func functionView() -> some View{
        if self.activePageIndex < self.datas.count{
            let m = datas[self.activePageIndex]
            VStack(alignment: .leading, spacing: 0) {
                Spacer()
                HStack {
                    functionSettingView(icon: "ellipsis")
                    Spacer()
                    functionItemView(icon: "message.fill", model: m).opacity(self.isRefresh ? 1.0 : 1.0)
                }.padding([.leading , .trailing , .bottom] , padding)
            }.allowsHitTesting(!self.isRecording)
                .opacity(self.isRecording ? 0 : 1.0)
        }
    }
    
    @ViewBuilder
    private func recordingView() -> some View{
        GeometryReader { geometryProxy in
            if self.isRecording{
                ZStack {
                    LottieAnimationContentView(filename: "Lottie_TalktoFriend")
                        .frame(width: isMinScreen() ? 89.0 : screenScaleSize(116.0), height: isMinScreen() ? 89.0 : screenScaleSize(116.0))
                        .clipShape(Circle())
                    ZStack {
                        Circle()
                            .fill(Color.watchPrimaryColor())
                            .frame(width: isMinScreen() ? 60.0 : screenScaleSize(80.0), height: isMinScreen() ? 60.0 : screenScaleSize(80.0))
                            .onTapGesture {
                                self.playShake(.stop)
                                self.stopRecord()
                            }
                        Image("sendMessage").frame(width: isMinScreen() ? 24 : 32, height:  isMinScreen() ? 24 : 32)
                    }
                    VStack {
                        Spacer()
                        countDownView()
                    }.padding([.bottom] , padding)
                }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
            }
        }
    }
    
    @ViewBuilder
    private func countDownView() -> some View{
        HStack {
//            Image(systemName: "mic.fill").foregroundColor(Color.watchYellowColor())
            if self.recordDuration > audioRecordTipsDuration {
                Text("\(60 - self.recordDuration)s left")
                    .font(.system(size: 14.0))
            }else{
                Text("0:\(String(format:"%02d",self.recordDuration))")
                    .font(.system(size: 14.0))
            }
        }.onChange(of: self.recordDuration) { newValue in
//            if newValue == audioRecordTipsDuration {
//                self.playShake(.directionDown)
//            }
        }
    }
    
    @ViewBuilder
    private func speakingView() -> some View{
        if isSpeaking{
            GeometryReader { geometryProxy in
                withAnimation {
                    ZStack {
                        
                        if let emoji =  player.currentAsset?.voicemoji , emoji.count > 0  {
                            ZStack {
                                Circle()
                                    .fill(Color.watchBlackColor())
                                    .frame(width: screenScaleSize(120.0), height: screenScaleSize(120.0))
                               
                            }
                            
                        }
                        LottieAnimationContentView(filename: "Lottie_FriendSpeaking12")
                            .frame(width: screenScaleSize(120.0), height: screenScaleSize(120.0))
                            .clipShape(Circle())
                        
                        if let emoji =  player.currentAsset?.voicemoji , emoji.count > 0  {
                            ZStack {
                                Text(emoji)
                                    .font(.system(size: 70))
                            }
                            
                        }
                        
                    }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
                }
            }.opacity(isSpeaking ? 1.0 : 0.0)
        }
    }
    
    @ViewBuilder
    private func functionSettingView(icon : String) -> some View{
        NavigationLink {
            WatchSettingView()
        } label: {
            Image(systemName: icon)
        }.frame(width: functionItemMaxSize, height: functionItemMaxSize, alignment: .center)
            .cornerRadius(functionItemMaxSize * 0.5)
    }
    
    @ViewBuilder
    private func functionItemView(icon : String ,model : WatchHomeListModel) -> some View{
        WatchFunctionView(icon: icon, model: model , width : functionItemMaxSize) { isPlayering in
            if isPlayering{
                self.player.stop {
                    
                }
            }
        }
    }
    
    @ViewBuilder
    private func sendSucessedView() -> some View{
        withAnimation {
            VStack {
                Spacer()
                HStack(spacing: 0, content: {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(Color.watchPrimaryColor())
                    Text(Localizable.record_has_sent)
                        .font(.system(size: 14.0))
                        .foregroundColor(Color.watchPrimaryColor())
                })
            }.padding([.bottom] , padding)
        }.opacity(isSendMessageSucceed && !self.isRecording ? 1 : 0)
    }
    
    @ViewBuilder
    private func sendFailView() -> some View{
        GeometryReader { geometryProxy in
            VStack {
                Image(systemName: "exclamationmark.circle.fill")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 36.0, height: 36.0)
                    .foregroundColor(Color.watchSecondaryErrorColor())
                    .padding([.top] , 10)
                Text(Localizable.chat_failed_to_send_message)
                    .font(.system(size: 14.0))
                    .foregroundColor(Color.watchWhiteColor(alpha: 0.8))
                    .padding([.bottom] , 20)
                Button {
                    self.isShowSendFail = false
                } label: {
                    Text(Localizable.ok())
                }.frame(width: 163, height: 50, alignment: .center)
                    .cornerRadius(25.0)
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
                .background(Color.watchBlackColor(alpha: 0.9))
        }
    }
    
    @ViewBuilder
    private func paddingMaskView() -> some View{
        GeometryReader { geometryProxy in
            let width = (WKInterfaceDevice.current().screenBounds.width - screenScaleSize(96) - 30.0) * 0.5
            HStack {
                Rectangle()
                    .frame(width: width , height: screenScaleSize(96))
                    .background(.black).opacity(0.2)
                Spacer()
                Rectangle()
                    .frame(width: width , height: screenScaleSize(96))
                    .background(.black).opacity(0.2)
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
        }
    }
    
    @ViewBuilder
    private func addFriendTipsView() -> some View{
        GeometryReader { geometryProxy in
            VStack {
                Image("logout")
                    .resizable()
                    .frame(width: 50, height: 50, alignment: .center)
                Text(Localizable.friend_add_anyone_before_chat)
                    .font(.system(size: 17.0))
                    .multilineTextAlignment(.center)
                    .padding([.leading , .trailing] , 20)
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
        }
    }
    
    private func screenScaleSize(_ size : Float) -> CGFloat {
        CGFloat(size * Float((WKInterfaceDevice.current().screenBounds.width - 88)) / 96.0)
    }
    
    private func isMinScreen() -> Bool {
        return WKInterfaceDevice.current().screenBounds.width <= 136
    }
}


extension WatchHomeView {
    func startRecord(){
        let model = self.datas[self.activePageIndex]
        DispatchQueue.main.async {
            if isStartRecord {
                return ;
            }
            guard AVAudioSession.sharedInstance().recordPermission == .granted else{ ///没有权限
                self.isShowAlert = true
                return ;
            }
            guard model.isSpeaking == false else{ ///正在说话
//                self.isShowAlert = true
                return ;
            }
            
            self.selectModel = model
            let traceId = MessageTraceIdTool.generateNewestTraceId()
            let ntpTime = NTPTimeTool.nowTimestamp()
            self.reportTrackStartRecord(model: model , traceId: traceId , ntpTime: ntpTime)
            BuzWatchInfoLog.debug(msg: "开始录制")
            showRecordingView(isShow: true)
            audioRecord.startRecording(recordMaxDuration: 60) { isStartSuccess , error in
                BuzWatchInfoLog.debug(msg: "开启录制状态 = \(isStartSuccess)")
                if !isStartSuccess {
                    showRecordingView(isShow: false)
                    if let er = error as? NSError{
                        self.reportTrackStartRecordResult(model: model, traceId: traceId , result: isStartSuccess , errorCode: Int64(er.code))
                    }
                }
            } currentDurationBlock: { duration in
                self.recordDuration = duration
                if duration == 51 || duration == 57 || duration == 58 || duration == 59 { ///UI need
                    if duration == 51 || duration == 59{
                        self.playShake(.success)
                    }else{
                        self.playShake(.directionDown)
                    }
                }
            } audioRecorderCompletionBlock: {  successfullyflag, duration, fileUrl, audioRecorderEndType in
                if duration > 0.25{
                    self.reportTrackStartRecordResult(model: model, traceId: traceId , result: true , errorCode: 0)
                    if audioRecorderEndType == .interruption{
                        
                    }else{
                        if successfullyflag , let url = fileUrl{
                            WatchSessionActivationManager.shared.sendIMWithVoiceFile(url: url , duration: duration , isGroup: model.isGroup, targetId: model.userId ?? "" , traceId: traceId , ntpTime: ntpTime) { isSucessed in
                                if !isSucessed{
                                    self.isShowSendFail = true
                                }
                            }
                        }
                    }
                }else{
                    self.reportTrackStartRecordResult(model: model, traceId: traceId , result: false , errorCode: 1)
                    self.isShowRecordDurationAlert = true
                }
                BuzWatchInfoLog.debug(msg: "successfullyflag = \(successfullyflag) , duration = \(duration) , audioRecorderEndType = \(audioRecorderEndType)")
                showRecordingView(isShow: false)
            }
        }
    }
    
    func stopRecord(){
        DispatchQueue.main.async {
            if self.isRecording{
                BuzWatchInfoLog.debug(msg: "结束录制")
                showRecordingView(isShow: false)
                audioRecord.stopRecord()
                
                if self.speakingCache.count > 0 {  ///播放录音过程的缓存
                    self.speakingCache.forEach { model in
                        player.playWithUrl(url: model.url, 
                                           targetId: Int64(model.sourceId ?? "0") ,
                                           msgId: model.msgId ,
                                           serverMsgId: model.serverMsgId ,
                                           convType: model.convType ,
                                           traceId: model.traceId,
                                           cryptKey: model.cryptKey,
                                           cryptIV: model.cryptIV, voicemoji: model.voicemoji, senderNickName: model.senderNickName)
                    }
                    self.speakingCache.removeAll { obj in
                        return true
                    }
                }
            }
        }
    }
    
    func showRecordingView(isShow : Bool) {
        self.isRecording = isShow
        self.isStartRecord = isShow
        if isShow{
            self.playShake(.directionDown)
        }
    }
    
    func playShake(_ type: WKHapticType){
        WKInterfaceDevice.current().play(type)
    }
    
    func scrollToIndex(userId : Int64){
        if self.isShowView{
            if self.player.isPlaying{
                if let index = self.datas.firstIndex(where: {$0.userId == String(userId)}){
                    self.activePageIndex  = index
                }
            }else{
                self.activePageIndex = 0
            }
        }
    }
}


