//
//  WatchVoiceModeSettingView.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/18.
//  Copyright © 2023 lizhi. All rights reserved.
//

import SwiftUI
import BuzLocalizable
import Localizable
import BuzConfig

struct WatchSettingVoiceModeView: View {
    @Environment(\.presentationMode) var presentationMode
    @State var mode: PersonalStatusType {
        didSet {
            NotificationCenter.default.post(name: WatchNotificationNameCenter.quietMode, object: self.mode)
        }
    }
    var deallocObj : ViewDeallocObject?
    
    @State var isLoading = false
    @State var didLoadData = false {
        didSet {
            if !oldValue && self.didLoadData {
                self.isLoading = true
                
                SettingManager.shared.startLoad {
                    if SettingManager.shared.mode == .none {
                        self.mode = .available
                    } else {
                        self.mode = SettingManager.shared.mode
                    }
                    
                    self.isLoading = false
                }
            }
        }
    }
    
    @ViewBuilder
    private func contentView() -> some View{
        VStack(alignment: .leading) {
            let availableImage = self.mode == .quiet ? UIImage(named: "modeAvailableWhite") : UIImage(named: "modeAvailableBlack")
            let quietImage = UIImage(named: "modeQuietWhite")
            
//            CustomBackButton(title: "Mode")
            Text(Localizable.selectmode()).font(Font.watch_regular(size: 14)).foregroundColor(Color.watchWhiteColor(alpha: 0.4))
            Spacer().frame(height: 4)
            Button.init {
                WatchDataSourceManager.shared.setPersonalStatusType(mode: .available) { isSuccess in
                    self.mode = .available
                    SettingManager.shared.mode = .available
                    WatchAppDelegate.sharedRoute.url = nil
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        presentationMode.wrappedValue.dismiss()
                    }
                    WKInterfaceDevice.current().play(.directionDown)
                }
            } label: {
                HStack {
                    Spacer().frame(width: 20)
                    Text(Localizable.available()).foregroundColor(self.mode == .quiet ? .white : .black)
                    Spacer()
                    Image(uiImage: availableImage ?? UIImage())
                        .frame(width: 24, height: 24)
                    Spacer().frame(width: 20)
                }.frame(height: 60.0)
                    .background(self.mode == .available ? Color.watchAvailableColor() : Color.watchBgColor(alpha: 0.14))
                    .cornerRadius(30)
            }.buttonStyle(PlainButtonStyle()).ignoresSafeArea()
            Spacer().frame(height: 4)
            Button.init {
                WatchDataSourceManager.shared.setPersonalStatusType(mode: .quiet) { isSuccess in
                    self.mode = .quiet
                    SettingManager.shared.mode = .quiet
                    WatchAppDelegate.sharedRoute.url = nil
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        presentationMode.wrappedValue.dismiss()
                    }
                    WKInterfaceDevice.current().play(.directionDown)
                }
            } label: {
                HStack {
                    Spacer().frame(width: 20)
                    Text(Localizable.quiet_mode)
                    Spacer()
                    Image(uiImage: quietImage ?? UIImage())
                        .frame(width: 24, height: 24)
                        .aspectRatio(contentMode: .fit)
                    Spacer().frame(width: 20)
                }.frame(height: 60.0)
                    .background(self.mode == .quiet ? Color.watchQuietColor() : Color.watchBgColor(alpha: 0.14))
                    .cornerRadius(30)
            }.buttonStyle(PlainButtonStyle()).ignoresSafeArea()
        }
        .navigationBarTitle(Localizable.complications_mode_switch)
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            WatchAppDelegate.sharedRoute.appendPresentMode(key: BuzConfig.watchSettingVoiceModeRouteKey, present: self.presentationMode)
        }
//        .navigationBarHidden(true)
//        .edgesIgnoringSafeArea(.top)
//        .navigationBarItems(leading: CustomBackButton())
    }
    
    @ViewBuilder
    private func loadingView() -> some View{
        GeometryReader { geometryProxy in
            VStack {
                if #available(watchOS 11.0, *) {
                    EmptyView()
                }else {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .frame(width: 26.0, height: 30.0, alignment: .center)
                }
                
                Text(Localizable.common_loading)
                    .font(.system(size: 17.0))
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
        }
    }
    
    var body: some View {
        if (self.isLoading || !self.didLoadData) && self.mode == .none {
            loadingView().onAppear {
                self.didLoadData = true
            }
        } else {
            contentView()
        }
    }
}
