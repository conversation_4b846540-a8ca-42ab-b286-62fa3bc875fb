//
//  WatchSettingView.swift
//  BuzWatchApp
//
//  Created by lizhifm on 2023/8/17.
//  Copyright © 2023 lizhi. All rights reserved.
//

import SwiftUI
import BuzLocalizable
import Localizable
import BuzConfig
import WatchKit
import AVFoundation

struct MyProgressViewStyle: ProgressViewStyle {
    let defaultColor : Color
    let highlightColor : Color
    
    func makeBody(configuration: Configuration) -> some View {
        GeometryReader.init { reader in
            let height = reader.size.height
            let width = reader.size.width
            let value = configuration.fractionCompleted ?? 0
            
            Rectangle()
                .cornerRadius(height / 2.0)
                .foregroundColor(defaultColor) // Set the color of the progress
                .frame(width: width)
                .overlay {
                    HStack{
                        Rectangle()
                            .cornerRadius(height / 2.0)
                            .foregroundColor(highlightColor) // Set the color of the progress
                            .frame(width: (width) * value)
                        Spacer(minLength: 0)
                    }
                }
        }
    }
}

struct VolumeView: WKInterfaceObjectRepresentable {
    typealias WKInterfaceObjectType = WKInterfaceVolumeControl

    func makeWKInterfaceObject(context: Self.Context) -> WKInterfaceVolumeControl {
        let view = WKInterfaceVolumeControl(origin: .local)
        view.setTintColor(.red)
        Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { [weak view] timer in
            if let view = view {
                view.focus()
            } else {
                timer.invalidate()
            }
        }
        DispatchQueue.main.async {
            view.focus()
        }
        return view
    }
    func updateWKInterfaceObject(_ wkInterfaceObject: WKInterfaceVolumeControl, context: WKInterfaceObjectRepresentableContext<VolumeView>) {
    }
}

struct WatchSettingView: View {
    @Environment(\.presentationMode) var presentationMode
    @State private var volumeValue: Float = AVAudioSession.sharedInstance().outputVolume
    @State private var previousVolumeValue: Float?
    @State private var mode: PersonalStatusType = .available
    @State var isLoading = false
    @State var didLoadData = false {
        didSet {
            if !oldValue && self.didLoadData {
                self.isLoading = true
                
                SettingManager.shared.startLoad {
                    self.isLoading = false
                    self.mode = SettingManager.shared.mode
                }
            }
        }
    }
    
    @State var notificationObserve : NSObjectProtocol?
    @State private var volume: Double?
    @State var volumeObserve : NSObject?
    @Environment(\.scenePhase) private var scenePhase
    
    @ViewBuilder
    private func loadingView() -> some View{
        GeometryReader { geometryProxy in
            VStack {
                if #available(watchOS 11.0, *) {
                    EmptyView()
                } else {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        .frame(width: 26.0, height: 30.0, alignment: .center)
                }
                
                Text(Localizable.common_loading)
                    .font(.system(size: 17.0))
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
        }
    }
    
    @ViewBuilder
    private func contentView() -> some View{
        VStack(alignment: .leading) {
            let bgColor = Color.watchBgColor(alpha: 0.14)
            Text(Localizable.setting_watch_volume).font(Font.watch_regular(size: 14)).foregroundColor(Color.watchWhiteColor(alpha: 0.4))
            Spacer().frame(height: 4)
            HStack {
                Button.init {
//                    self.volumeValue = max(0, self.volumeValue - 0.1)
                } label: {
                    Color.clear.ignoresSafeArea().overlay {
                        Image("VolumeIncrease")
                    }
                }.buttonStyle(PlainButtonStyle())
                    .frame(width: 42.0, height: 44.0)
                Spacer(minLength: 1)
                Color.clear.ignoresSafeArea().overlay {
                    let edge = EdgeInsets.init(top: 19, leading: 6, bottom: 19, trailing: 20)
                    if #available(watchOS 11.0, *) {
                        EmptyView()
                    } else {
                        ProgressView(value: volumeValue).progressViewStyle(MyProgressViewStyle.init(defaultColor: Color.watchWhiteColor(alpha: 0.14),
                                                                                                    highlightColor: Color.watchPrimaryColor())).padding(edge)
                    }
                    
                }
//                Spacer(minLength: 1)
//                Button.init {
////                    self.volumeValue = min(1.0, self.volumeValue + 0.1)
//                } label: {
//                    bgColor.ignoresSafeArea().overlay {
//                        Image("VolumeIncrease")
//                    }
//                }.buttonStyle(PlainButtonStyle())
//                    .frame(width: 42.0, height: 44.0)
            }.background(bgColor).frame(height: 44.0).cornerRadius(9.0)
            Spacer().frame(height: 10)
            Text(Localizable.complications_mode_switch).font(Font.watch_regular(size: 14)).foregroundColor(Color.watchWhiteColor(alpha: 0.4))
            Spacer().frame(height: 4)
            NavigationLink {
                WatchSettingVoiceModeView.init(mode: self.mode)
            } label: {
                let edge = EdgeInsets.init(top: 12, leading: 10, bottom: 12, trailing: 10)
                HStack {
                    Text("").frame(width: 8.0, height: 8.0)
                        .background(self.mode == .quiet ? Color.watchQuietColor() : Color.watchAvailableColor())
                        .clipShape(Circle())
                    
                    if self.mode == .available {
                        Text(Localizable.available())
                    } else {
                        Text(Localizable.quiet_mode)
                    }
                    
                    Spacer()
                    Image("RightArrow")
                }.padding(edge)
                    .contentShape(Rectangle())
            }.buttonStyle(PlainButtonStyle()).background(bgColor).frame(height: 44.0).cornerRadius(9.0)
            VolumeView().frame(height: 0.0).opacity(0).id("VolumeView")
        }
        .navigationBarTitle(Localizable.settings())
        .navigationBarTitleDisplayMode(.inline)
        .onAppear {
            self.previousVolumeValue = self.volumeValue
            self.mode = SettingManager.shared.mode
            WatchAppDelegate.sharedRoute.appendPresentMode(key: BuzConfig.watchSettingRouteKey, present: self.presentationMode)
            try? AVAudioSession.sharedInstance().setActive(true)
            self.volumeObserve = AVAudioSession.sharedInstance().observe(\.outputVolume) { session, _ in
                self.volumeValue = session.outputVolume
            }
        }.onDisappear {
            self.volumeObserve = nil
            
            if let previousVolumeValue = self.previousVolumeValue, previousVolumeValue != self.volumeValue {
                self.previousVolumeValue = self.volumeValue
                WatchDataSourceManager.shared.watchTrackingSettingEvent(mode: self.mode,
                                                                        volume: Double(self.volumeValue)) { isSuccess in
                }
            }
        }.onChange(of: scenePhase) { newScenePhase in
            switch newScenePhase {
            case .background, .inactive:
                break
            case .active:
                try? AVAudioSession.sharedInstance().setActive(true)
            @unknown default:
                try? AVAudioSession.sharedInstance().setActive(true)
            }
        }
    }
    
    func forceBack(notification : Notification) {
        self.presentationMode.wrappedValue.dismiss()
    }
    
    var body: some View {
        if self.isLoading || !self.didLoadData {
            loadingView().onAppear {
                self.didLoadData = true
            }
        } else {
            contentView()
        }
    }
}
