//
//  WatchAlertView.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/8/31.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import SwiftUI
import Localizable
import BuzLocalizable

struct MessageSendFail : View {
    let text : String
    var body: some View {
        HStack {
            Spacer()
            Image(systemName: "exclamationmark.circle.fill")
                .resizable()
                .aspectRatio(contentMode: .fill)
                .frame(width: 14.0, height: 14.0, alignment: .center)
                .foregroundColor(Color.watchSecondaryErrorColor())
            Text(text)
                .font(.system(size: 12.0))
                .foregroundColor(Color.watchSecondaryErrorColor())
        }.environment(\.layoutDirection, .leftToRight)
    }
}


struct WatchAlertRecordDurationShortView : View {
    let text : String
    var dissMissBlock : (() ->())?
    var body: some View {
        GeometryReader { geometryProxy in
            VStack {
                Image(systemName: "exclamationmark.circle.fill")
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 36.0, height: 36.0)
                    .foregroundColor(Color.watchWhiteColor(alpha: 0.8))
                    .padding([.top] , 10)
                Text(Localizable.chatvoicetooshorttosend())
                    .font(.system(size: 14.0))
                    .foregroundColor(Color.watchWhiteColor(alpha: 0.8))
                    .padding([.top] , 10)
            }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
                .background(Color.watchBlackColor(alpha: 0.9))
                .onAppear {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                        if let block = self.dissMissBlock{
                            block()
                        }
                    }
                }
        }.environment(\.layoutDirection, .leftToRight)
    }
}

