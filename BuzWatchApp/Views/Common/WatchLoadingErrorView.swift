//
//  WatchLoadingError.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/8/23.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import SwiftUI
import Localizable
import BuzLocalizable

struct WatchLoadingErrorView : View{
    
    var tapBlock : (() ->())?
    
    var body: some View {
            GeometryReader { geometryProxy in
                ScrollView {
                    VStack {
                        Image(systemName: "wifi.exclamationmark")
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 36.0, height: 36.0)
                            .foregroundColor(Color.watchSecondaryErrorColor())
                        Text(Localizable.setting_watch_network_error)
                            .padding([.top] , 10)
                            .multilineTextAlignment(.center)
                            .font(.system(size: 14.0))
                            .padding([.leading , .trailing] , 27.0)
                        Button {
                            if let block = self.tapBlock{
                                block()
                            }
                        } label: {
                            Text(Localizable.setting_watch_retry)
                                .foregroundColor(.white)
                        }.frame(width: 163.0, height: 50.0, alignment: .center)
                            .background(Color.watchWhiteColor(alpha: 0.12))
                            .cornerRadius(25.0)
                            .padding([.top] , 20)
                            .buttonStyle(.borderless)
                    }
                }.frame(width: geometryProxy.size.width, height: geometryProxy.size.height, alignment: .center)
        }
    }
}


