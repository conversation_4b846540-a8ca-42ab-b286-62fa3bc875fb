//
//  LottieAnimationContentView.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/8/26.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import SwiftUI

struct LottieAnimationContentView: View {
    
    @StateObject var viewModel: LottieViewModel = .init()
    
    var filename : String
    var body: some View {
        Image(uiImage: viewModel.image)
            .resizable()
            .scaledToFit()
            .onAppear {
                self.viewModel.loadAnimationFromFile(filename: filename)
            }.onDisappear(perform: {
                self.viewModel.pause()
            })
    }
}
