//
//  WatchCommonView.swift
//  BuzWatchApp
//
//  Created by lizhi on 2023/9/7.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import SwiftUI
import Localizable
import BuzLocalizable

struct WatchLinearGradientAnimationView : View {
    @State private var isAnimating = true /// 控制动画的状态变量
    let duration = 2.6
    let timer = Timer.publish(every: 0.6, on: .main, in: .common).autoconnect()
    var body: some View {
        GeometryReader { geometryProxy in
            VStack {
                let colors = [Color.init(UIColor.init(hexString: "#B9FB65" , alpha: 0)), Color.init(UIColor.init(hexString: "#B9FB65" , alpha: 0.03))]
                let colors1 = [Color.init(UIColor.init(hexString: "#B9FB65" , alpha: 0)), Color.init(UIColor.init(hexString: "#B9FB65" , alpha: 0.12))]
                if self.isAnimating{
                    LinearGradient(gradient: Gradient(colors: colors), startPoint: .top, endPoint: .bottom).animation(.linear(duration: duration))
                }else{
                    LinearGradient(gradient: Gradient(colors: colors1), startPoint: .top, endPoint: .bottom).animation(.linear(duration: duration))
                }
            }.onReceive(timer) { firedDate in
                withAnimation {
                    self.isAnimating.toggle()
                }
            }
        }
    }
}


struct WatchShadowView : View {
    var width = 24.0
    var body: some View {
        GeometryReader { geometryProxy in
            HStack(alignment: .center, spacing: 0) {
                let colors = [Color.watchBlackColor(alpha: 1), Color.watchBlackColor(alpha: 0)]
                LinearGradient(gradient: Gradient(colors: colors), startPoint: .leading, endPoint: .trailing).frame(width: width)
                Spacer()
                let colors1 = [Color.watchBlackColor(alpha: 0), Color.watchBlackColor(alpha: 1)]
                LinearGradient(gradient: Gradient(colors: colors1), startPoint: .leading, endPoint: .trailing).frame(width: width)
            }.allowsHitTesting(false)
                .padding(-3)
        }
    }
}


struct WatchFunctionView : View {
    let icon : String
    let model : WatchHomeListModel
    let width : CGFloat
    let playBackBlock : ((Bool) -> Void)?
    
    var body: some View {
        NavigationLink {///高有最小限制，宽没有限制
            WatchHistoryContentView(viewModel: WatchHistoryListObserver(model: model), playBackBlock: { isPlayering in
                playBackBlock?(isPlayering)
            }, model: model)
        } label: {
            if model.unReadCount > 0 {
                Text(model.unReadCount > 99 ? "99+" : "\(model.unReadCount)")
                    .foregroundColor(.black)
                    .frame(width: width , height: width)
                    .background(.white)
            }else{
                Image(systemName: icon)
            }
        }.frame(width: width, height: width, alignment: .center)
            .cornerRadius(width * 0.5)
    }
}


struct LazyView<Content: View>: View {
    let build: () -> Content
    
    init(_ build: @autoclosure @escaping () -> Content) {
        self.build = build
    }
    
    var body: Content {
        build()
    }
}
