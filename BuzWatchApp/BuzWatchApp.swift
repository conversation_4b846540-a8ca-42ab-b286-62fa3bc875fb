//
//  com_interfun_buz_watchApp.swift
//  com.interfun.buz.watch Watch App
//
//  Created by lizhi on 2023/7/18.
//  Copyright © 2023 lizhi. All rights reserved.
//

import SwiftUI
import SDWeb<PERSON>mageSwiftUI
import WidgetKit
import UIKit
import BuzConfig
import Logz

@main
@available(watchOS 8.0, *)
struct WatchApp: App {
    @WKExtensionDelegateAdaptor(WatchAppDelegate.self) var extensionDelegate
    @WKApplicationDelegateAdaptor var appDelegate: WatchAppDelegate
    @Environment(\.scenePhase) private var scenePhase
    
    @State var isPresented = false
    @State var onAppear = false
    
    var body: some Scene {
        WindowGroup {
            GeometryReader { geometryProxy in
                NavigationView{
                    WatchRootView()
                }
            }.ignoresSafeArea()
        }.onChange(of: scenePhase) { newScenePhase in
            switch newScenePhase {
            case .active :
                WatchSessionActivationManager.shared.appActive = true
                self.onViewAppear()
                BuzWatchInfoLog.debug(msg: "WatchSessionActivationManager: App active")
            case .inactive :
                WatchSessionActivationManager.shared.appActive = true
                BuzWatchInfoLog.debug(msg: "WatchSessionActivationManager: App inactive")
            case .background :
                WatchSessionActivationManager.shared.appActive = false
                BuzWatchInfoLog.debug(msg: "WatchSessionActivationManager: App background")
            @unknown default :
                BuzWatchInfoLog.debug(msg: "WatchSessionActivationManager: Others")
            }
        }
//        .backgroundTask(.appRefresh("test")) {
//            await updateWeatherData()
//        }
        
        ///custom notification
        WKNotificationScene(
              controller: NotificationController.self,
              category: NotificationController.category
            )
    }
    
    func updateWeatherData() async {
        BuzWatchInfoLog.debug(msg: "updateWeatherData")
        WatchDataSourceManager.shared.saveUsr(userId: 1222)
//        WidgetCenter.shared.reloadAllTimelines()
    }
    
    func onViewAppear(){
        if !self.onAppear{
            self.onAppear = true
            if  WatchSessionActivationManager.shared.appActive{
                BuzWatchInfoLog.error(msg: "WatchSessionActivationManager activate = 1")
                WatchSessionActivationManager.shared.activate(completionHandler: nil)
                WatchDataSourceManager.shared.eventTracking(label: ["exclusive_id" : "EVENT_ACTIVE_USERS"])
                WatchDataSourceManager.shared.getDeviceId { isSucessed, deviceId in
                    if deviceId.count > 0 {
                        let config : LogzConfig = LogzConfig.init(appId: "87075309", deviceId: deviceId)
                        config.uidBlock = {
                            // 应用用户id
                            return WatchDataSourceManager.shared.getUser() ?? 0
                        }
                        Logz.startup(with: config)
                    }
                }
            }
        }
    }
}


struct TileView: View {
    let icon: String
    
    var body: some View {
        RoundedRectangle(cornerRadius: 20).fill(Color.gray)
            .overlay(Image(systemName: icon).imageScale(.large))
            .frame(height: 128)
    }
}
