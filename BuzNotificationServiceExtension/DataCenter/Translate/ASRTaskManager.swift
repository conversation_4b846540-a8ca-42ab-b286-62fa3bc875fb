//
//  ASRTaskManager .swift
//  buz
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/8/23.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation
import BuzConfig
import BuzLog

typealias ASRTranscriptionTask = ASRTaskManager.Task

class ASRTaskManager {
    typealias TaskCallback = (Result<String, Error>) -> Void
    typealias TaskExecution = (@escaping (Result<String, Error>) -> Void) -> Void

    class Task {
        let taskId: Int64
        let timeout: TimeInterval
        let callback: TaskCallback
        var isCompleted = false
        
        // 以下字段用于埋点
        var pageBusinessType: String
        var pageBusinessId: String
        var elementBusinessType: String
        var elementBusinessId: String

        init(taskId: Int64, 
             timeout: TimeInterval,
             pageBusinessType: String,
             pageBusinessId: String,
             elementBusinessType: String,
             elementBusinessId: String,
             callback: @escaping TaskCallback) {
            self.taskId = taskId
            self.timeout = timeout
            self.callback = callback
            self.pageBusinessType = pageBusinessType
            self.pageBusinessId = pageBusinessId
            self.elementBusinessType = elementBusinessType
            self.elementBusinessId = elementBusinessId
        }
    }

    private var tasks: [Int64: Task] = [:]

    // 添加并执行任务
    func addTask(_ task: Task) {
        guard tasks[task.taskId] == nil else {
            print("Task with id \(task.taskId) already exists.")
            return
        }
        
        tasks[task.taskId] = task
        executeTask(task)
    }

    // 执行任务
    private func executeTask(_ task: Task) {
        let timeout = task.timeout
        let callback = task.callback
        
        let timeoutWorkItem = DispatchWorkItem {
            if !task.isCompleted {
                task.isCompleted = true
                callback(.failure(NSError(domain: "ASRTaskManager", code: 1, userInfo: [NSLocalizedDescriptionKey: "Task timed out"])))
                self.removeTask(task)
            }
        }
        
        // 定时器
        DispatchQueue.global().asyncAfter(deadline: .now() + timeout, execute: timeoutWorkItem)
        
        getAsrText(task: task) { result in
            DispatchQueue.main.async {
                if !task.isCompleted {
                    task.isCompleted = true
                    timeoutWorkItem.cancel() // 取消超时任务
                    callback(result)
                    self.removeTask(task)
                }
            }
        }
    }

    // 移除任务
    private func removeTask(_ task: Task) {
        tasks[task.taskId] = nil
    }
    
    // 请求asr转义结果
    func getAsrText(task: Task, completion: @escaping (Result<String, Error>) -> Void) {
        let msgId = task.taskId
        APNsNetworker.getAsrText(msgId: msgId, pageBusinessType: task.pageBusinessType, pageBusinessId: task.pageBusinessId, elementBusinessType: task.elementBusinessType) { resp in
            if let asrText = resp?.asrText, asrText.count > 0 {
                completion(.success(asrText))
            } else {
                completion(.failure(NSError(domain: "ASRTaskManager", code: 2, userInfo: [NSLocalizedDescriptionKey: "getAsrText failed"])))
            }
        }
    }
    
    /// 通知栏是否展示ASR预览
    func isEnableNotificationTranscribePreview() -> Bool {
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return false
        }
        
        let nseAsrSwitch = userDefaults.bool(forKey: "BuzShareStore_nseAsrSwitch")
        let nseAsrEnabled = userDefaults.bool(forKey: "BuzShareStore_nseAsrEnabled")
        
        // 1.先判断服务器开关
        guard nseAsrSwitch else {
            BuzLog.ServiceExtension.log("nseAsrCheck 1, nseAsrSwitch = \(nseAsrSwitch)")
            // 服务器没开
            return false
        }
        
        // 2.再判断ASR预览是否生效（app内部会根据配置开关和AB实验自动决策后存入缓存）
        BuzLog.ServiceExtension.log("nseAsrCheck 2, nseAsrEnabled = \(nseAsrEnabled)")
        return nseAsrEnabled
    }
    
    func nseAsrTimeout() -> TimeInterval {
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return 5.0
        }
        
        let seconds = userDefaults.integer(forKey: "BuzShareStore_nseAsrTimeout")
        return seconds > 0 ? TimeInterval(seconds) : 5.0
    }
}
