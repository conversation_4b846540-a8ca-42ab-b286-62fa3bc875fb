//
//  BizPushService.swift
//  BuzNotificationServiceExtension
//
//  Created by st.chio on 2024/12/20.
//  Copyright © 2024 lizhi. All rights reserved.
//

import BuzAPNsCenter
import Localizable
import BuzLocalizable
import BuzDataShareKit
import BuzLog

// MARK: ---BuzAPNsIMControlerable--------------
public protocol BuzAPNsBizControlerable: BuzAPNsControlerable { }

// MARK: ---extension BuzAPNsIMControlerable--------------
extension BuzAPNsBizControlerable {
    
}

public extension BuzAPNs {
    class BizPushController: BuzAPNsBizControlerable {
        public var ctx: BuzAPNs.Context?
        public required init(ctx: BuzAPNs.Context) {
            self.ctx = ctx
            self.ctx?.content.badge = self.updateBadge(fromId: nil, conversationType: nil)
            self.bizPushHandler()
        }
    }
}

extension BuzAPNs.BizPushController {
    
    func bizPushHandler() {
        if let pushType = self.ctx?.payload.appData?.pushType {
            switch pushType {
            case .friendApply:
                self.friendApplyPush()
            case .friendApplyPassed:
                self.communicationPush()
            case .privateLivePlaceInvite, .groupLivePlaceInvite, .privateLivePlaceOpen, .groupLivePlaceOpen, .privateLivePlaceKnock:
                self.livePlacePush()
            default:
                self.defaultPush()
            }
        }
    }
}

// MARK: ---defaultPush---
extension BuzAPNs.BizPushController {
    
    func defaultPush() {
        guard let content = self.ctx?.content else { return }
        if let appData = self.ctx?.payload.appData {
            if let title = appData.titleReplace() {
                content.title = title
            }
            if let pushContent = appData.getPushContent() {
                content.body = pushContent
            }
        }
        
        self.showNotification(content)
    }
}

// MARK: ---friendApply、feedbackPush---
extension BuzAPNs.BizPushController {
    
    func friendApplyPush() {
        if let lastApplyTime = self.ctx?.payload.appData?.pushExtra?.lastApplyTime {
            let lastApplyTs = PushShareDataTool.lastApplyFriendTs()
            BuzLog.ServiceExtension.log("friendApply offline notification, lastApplyTime:\(lastApplyTime) previousTs:\(lastApplyTs)")
            if lastApplyTime > lastApplyTs {
                let totalUnreadCount = PushShareDataTool.totalUnreadCount() + 1
                PushShareDataTool.savetotalUnreadCount(totalUnreadCount)
                PushShareDataTool.saveLastApplyFriendTs(lastApplyTime)
                self.ctx?.content.badge = NSNumber(value: totalUnreadCount)
            }
        }
        
        self.communicationPush()
    }
    
    func communicationPush() {
        guard let content = self.ctx?.content else { return }
        guard let appData = self.ctx?.payload.appData else {
            self.showNotification(content)
            return
        }
        
        if let title = appData.senderInfo?.userName, title.count > 0 {
            content.title = title
        }
        if let pushContent = appData.pushContent, pushContent.count > 0 {
            content.body = pushContent
        }
        
        guard let portrait = appData.senderInfo?.portrait, portrait.count > 0 else {
            self.showNotification(content)
            return
        }
        
        let params = BuzAPNs.ContentParams.init(content: content,
                                                pushType: appData.pushType,
                                                senderName: content.title,
                                                iconUrl: portrait)
        BuzAPNsCenter.communicationContent(params: params) { content in
            self.showNotification(content)
        }
    }
    
    func livePlacePush() {
        guard let content = self.ctx?.content else { return }
        guard let appData = self.ctx?.payload.appData else {
            self.showNotification(content)
            return
        }
       
        let pushType = appData.pushType
        var isShowGreenCircle = false
        
        ///消息发送者
        var sendName : String = ""
        var title : String?
        var body  : String?
        var portrait : String?
        
        let isGroup = (pushType == .groupLivePlaceOpen || pushType == .groupLivePlaceInvite)
        
        if var userName = appData.senderInfo?.userName, userName.count > 0 {
            var groupName : String = ""
            if isGroup {
                if let groupInfo = appData.groupInfo{
                    let localGroupInfoMap: [Int64: [String: String]] = PushShareDataTool.loadGroupsMap()
                    if let groupId = groupInfo.groupId,
                       let groupInfoDict = localGroupInfoMap[groupId]{
                        groupName = groupInfoDict[PushShareDataKey.groupInfoDisplayNameKey.rawValue] ?? groupInfo.name ?? ""
                        portrait = groupInfoDict[PushShareDataKey.groupInfoPortraitKey.rawValue]
                    }else{
                        groupName = groupInfo.name ?? ""
                    }
                }
            }else {
                portrait = appData.senderInfo?.portrait
            }
            
            let contactsNameMap: [Int64: String] = PushShareDataTool.loadContactsMap()
            if let userId = appData.senderInfo?.userId,
               let name = contactsNameMap[userId], name.count > 0 {
                userName = name
            }
            
            body = String.init(format: Localizable.live_place_join_topic, (appData.pushExtra?.topic ?? ""))
            
            switch pushType {
            case .groupLivePlaceInvite:
                isShowGreenCircle = true
                sendName = groupName
                title = String.init(format: Localizable.live_place_xxx_invites_you, userName)
                content.sound = UNNotificationSound(named: UNNotificationSoundName(AppAudioCues.livePlactInviteTojoin.rawValue))
            case .groupLivePlaceOpen:
                isShowGreenCircle = true
                sendName = groupName
                title = String.init(format: Localizable.live_place_opened, userName)
                content.sound = UNNotificationSound(named: UNNotificationSoundName(AppAudioCues.livePlaceOpen.rawValue))
            case .privateLivePlaceInvite:
                isShowGreenCircle = true
                sendName = userName
                title = Localizable.live_place_invites_you
                content.sound = UNNotificationSound(named: UNNotificationSoundName(AppAudioCues.livePlactInviteTojoin.rawValue))
            case .privateLivePlaceOpen:
                isShowGreenCircle = true
                sendName = String.init(format: Localizable.live_place_opened, userName)
                content.sound = UNNotificationSound(named: UNNotificationSoundName(AppAudioCues.livePlaceOpen.rawValue))
            case .privateLivePlaceKnock:
                sendName = Localizable.knock_knock
                title = String(format: Localizable.live_place_want_chat, userName)
                body = Localizable.live_place_lets_open
            default:
                sendName = userName
            }
        }
        content.body = body ?? appData.getPushContent() ?? ""
       
        guard let portrait = portrait, portrait.count > 0 else {
            self.showNotification(content)
            return
        }
        
        let params = BuzAPNs.ContentParams.init(content: content,
                                                pushType: appData.pushType,
                                                senderName: sendName,
                                                iconUrl: portrait,
                                                groupName: title,
                                                isShowGreenCircle: isShowGreenCircle)
        BuzAPNsCenter.communicationContent(params: params) { content in
            self.showNotification(content)
        }
    }
}

// MARK: ---feedback---
extension BuzAPNs.BizPushController {
    
    func feedbackPush() {
        guard let content = self.ctx?.content else { return }
        PushShareDataTool.saveFeedbackNotificationUserInfo(content.userInfo)
        self.showNotification(content)
    }
}
