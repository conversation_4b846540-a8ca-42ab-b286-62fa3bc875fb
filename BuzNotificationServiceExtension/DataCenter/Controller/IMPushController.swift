//
//  BuzIMMessageController.swift
//  buz
//
//  Created by st.chio on 2024/12/23.
//  Copyright © 2024 lizhi. All rights reserved.
//

import BuzLog
import BuzAPNsCenter
import Localizable
import BuzLocalizable
import BuzDataShareKit
import Voder<PERSON><PERSON><PERSON>mpotentNSE

// MARK: ---BuzAPNsIMControlerable--------------
public protocol BuzAPNsIMControlerable: BuzAPNsControlerable { }

// MARK: ---extension BuzAPNsIMControlerable--------------
extension BuzAPNsIMControlerable {
    
    public var pushType: BuzAPNs.PushType {
        if let pushType = self.ctx?.payload.appData?.pushType {
            switch pushType {
            case .privateChat, .groupChat:
                return pushType
            case .quickReactVoicemoji:
                guard let scheme = self.ctx?.payload.appData?.router?.scheme as? String else { return .none }
                if scheme == "chat/private" {
                    return .privateChat
                }else if scheme == "chat/group" {
                    return .groupChat
                }
                return .none
            default:
                return .none
            }
        }
        return .none
    }
    
    public var messageType: BuzAPNs.ImMessageType {
        if let type = self.ctx?.payload.appData?.imInfo?.msgType {
            return type
        }
        return .unknow
    }
    
    public var businessType: BuzAPNs.CommandBusinessType {
        if let type = self.ctx?.payload.appData?.pushExtra?.businessType {
            return type
        }
        return .none
    }
    
    public var subBusinessType: BuzAPNs.CommandSubBusinessType {
        if let type = self.ctx?.payload.appData?.pushExtra?.subBusinessType {
            return type
        }
        return .none
    }
}

public extension BuzAPNs {
    class IMPushControler: BuzAPNsIMControlerable {
        public var ctx: BuzAPNs.Context?
        
        let contentParams: BuzAPNs.ContentParams
        
        public required init(ctx: BuzAPNs.Context) {
            self.ctx = ctx
            self.contentParams = .init(content: ctx.content, pushType: ctx.payload.appData?.pushType ?? .none)
            self.filterIMMessage()
            self.updateData()
            self.notification()
        }
    }
}

// MARK: ---filterIMMessage---------
extension BuzAPNs.IMPushControler {
    // 撤回
    func filterIMMessage() {
        if let im = self.ctx?.payload.appData?.imInfo {
            if let messageIdempotent = self.ctx?.idempotent {
                // 先判断是否为重复推送
                if let svrMsgId = im.svrMsgId, let identifier = messageIdempotent.getDisplayedIdentifier(for: svrMsgId) {
                    BuzLog.ServiceExtension.log("message:\(svrMsgId) already displayed, remove it:\(identifier)")
                    self.removeNotification(by: identifier)
                }
                
                if im.isRecallMessage, let content = self.ctx?.content {
                    // 处理撤回
                    if let orgSvrMsgId = im.orgSvrMsgId,
                       let identifier = messageIdempotent.getDisplayedIdentifier(for: orgSvrMsgId) {
                        BuzLog.ServiceExtension.log("remove identifier:\(identifier) for recall message:\(orgSvrMsgId)")
                        self.removeNotification(by: identifier)
                        recallNotification(content)
                    } else {
                        BuzLog.ServiceExtension.log("no identifier for recall message:\(String(describing: self.ctx?.payload.appData?.receivedRawData))")
                    }
                } else {
                    // 否则登记消息 ID <-> APNS identifier
                    if let svrMsgId = im.svrMsgId, let request = self.ctx?.request {
                        messageIdempotent.messageDisplayed(msgId: svrMsgId, identifier: request.identifier)
                        BuzLog.ServiceExtension.log("save identifier:\(request.identifier) for message:\(svrMsgId)")
                    }
                }
            }
        }
    }
    
    func removeNotification(by identifier: String) {
        let center = UNUserNotificationCenter.current()
        center.removeDeliveredNotifications(withIdentifiers: [identifier])
        center.removePendingNotificationRequests(withIdentifiers: [identifier])
    }

    func recallNotification(_ content: UNMutableNotificationContent) {
        if let myIdentifier = self.ctx?.request.identifier {
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                BuzLog.ServiceExtension.log("remove myself:\(myIdentifier)")
                self.removeNotification(by: myIdentifier)
            }
        }
    }
}

// MARK: ---updateData---------
extension BuzAPNs.IMPushControler {
    
    func updateData() {
        //--senderName-----
        self.updateSenderName()
        //--title-----
        self.updateTitle()
        //--body-----
        self.updateBody()
        //--icon-----
        self.updateIcon()
        //--threadIdentifier-----
        self.updateThreadIdentifier()
        //--badge-----
        self.updateBadge()
    }
    
    func updateSenderName() {
        var senderName = self.senderName()
        if self.pushType == .groupChat {
            var isAtMe = false
            let myId = PushShareDataTool.loadSessionUserId()
            if let pushExtra = self.ctx?.payload.appData?.pushExtra,
               let result = pushExtra.mentionedUsers?.contains("\(myId)"),
                result == true {
                isAtMe = true
            } else if let pushExtra = self.ctx?.payload.appData?.pushExtra, let mentionMap = pushExtra.mentionMap {
                
                mentionMap.forEach { (key: String, value: String) in
                    if isAtMe {
                        return
                    }
                    
                    // Support Mention All [$groupId-ALL]
                    if key.hasSuffix("-ALL]") {
                        isAtMe = true
                    } else {
                        let result = BuzAPNs.ActionPayload.AppDataV2.parseMentionKey(key)
                        isAtMe = result.userId == myId
                    }
                }
            }
            
            if isAtMe {
                senderName += " \(Localizable.mentioned_you)"
            }
        }
        
        self.contentParams.senderName = senderName
    }
    
    func updateTitle() {
        if self.pushType == .groupChat {
            var groupName = self.groupName()
            
            if let title = self.ctx?.payload.appData?.titleReplace() {
                groupName = title
            }
            
            self.ctx?.content.title = groupName
            self.contentParams.groupName = groupName
        }
        
        if self.pushType == .privateChat {
            self.ctx?.content.title = self.contentParams.senderName
        }
    }
    
    func updateBody() {
        var content = ""
        if let text = self.ctx?.payload.appData?.getPushContent() {
            if self.pushType == .groupChat {
                if #available(iOS 15.0, *) {
                    content = text
                } else {
                    content = self.senderName() + " : " + text
                }
            }
            if self.pushType == .privateChat {
                content = text
            }
        }
        if self.ctx?.payload.appData?.contentReplaceInfo == nil, let text = self.localPushContent() {
            content = text
        }
        
        self.ctx?.content.body = content
    }
    
    func updateIcon() {
        if self.pushType == .groupChat {
            self.contentParams.iconUrl = self.ctx?.payload.appData?.groupInfo?.portrait
        }
        if self.pushType == .privateChat {
            self.contentParams.iconUrl = self.ctx?.payload.appData?.senderInfo?.portrait
        }
    }
    
    func updateThreadIdentifier() {
        guard let targetId = self.routerTargetId() else { return }
        self.ctx?.content.threadIdentifier = targetId
        self.contentParams.identifier = targetId
    }
    
    func updateBadge() {
        guard let targetId = self.routerTargetId() else { return }
        if self.pushType == .groupChat {
            self.ctx?.content.badge = self.updateBadge(fromId: targetId, conversationType: .group)
        }
        if self.pushType == .privateChat {
            self.ctx?.content.badge = self.updateBadge(fromId: targetId, conversationType: .peer)
        }
    }
}

// MARK: ---data handler---------
extension BuzAPNs.IMPushControler {
    func notification() {
        guard let content = self.ctx?.content else { return }
        self.contentParams.content = content
        
        if let _ = self.routerTargetId() {
            BuzAPNsCenter.communicationContent(params: self.contentParams) { content in
                if let im = self.ctx?.payload.appData?.imInfo, im.isRecallMessage {
                    content.sound = nil
                }
                self.finalDisplayImPushContent(content)
            }
        } else {
            BuzLog.ServiceExtension.log("is not IM message")
            self.showNotification(content)
        }
    }
    
    // IM消息最终展示通知消息
    func finalDisplayImPushContent(_ content: UNMutableNotificationContent) {
        guard let appData = self.ctx?.payload.appData, let im = appData.imInfo, im.isWalkieTalkieMsgType else {
            // 其他消息
            self.showNotification(content)
            return
        }
        
        // ------ 以下是语音消息
        
        let taskManager = ASRTaskManager()
        var isVoiceFilter = false
        var voiceFilterName: String?
        if let pushExtra = self.ctx?.payload.appData?.pushExtra, let filterId = pushExtra.voiceFilterId {
            isVoiceFilter = true
            voiceFilterName = PushShareDataTool.getVoiceFilterName(filterId)
        }
        
        guard taskManager.isEnableNotificationTranscribePreview() else {
            // 通知栏不用显示ASR
            if self.ctx?.payload.appData?.pushExtra?.voiceFilterId ?? 0 > 0 {
                isVoiceFilter = true
                content.body = Localizable.send_voice_with_vf_tip
            }
            self.showNotification(content)
            return
        }
        
        // 通知栏要显示ASR预览
        let nseAsrTimeout = taskManager.nseAsrTimeout()
        let task = ASRTranscriptionTask(taskId: im.svrMsgId ?? 0,
                                        timeout: nseAsrTimeout,
                                        pageBusinessType: im.convType == 3 ? "group" : "private",
                                        pageBusinessId: String(appData.targetId ?? 0),
                                        elementBusinessType: String(im.msgType.rawValue),
                                        elementBusinessId: String(im.svrMsgId ?? 0),
                                        callback: { result in
            switch result {
            case .success(let asrText):
                if isVoiceFilter {
                    if let name = voiceFilterName {
                        content.body = "[\(name)]" + " " + asrText
                    }else{
                        content.body = Localizable.vf_enhanced + " " + asrText
                    }
                } else {
                    content.body = Localizable.notification_tile_voice + " " + asrText
                }
                
                self.showNotification(content)
            case .failure(_):
                if isVoiceFilter {
                    content.body = Localizable.send_voice_with_vf_tip
                }
                self.showNotification(content)
            }
        })
        taskManager.addTask(task)
    }
}

// MARK: ---data handler---------
extension BuzAPNs.IMPushControler {
    
    func groupName() -> String {
        guard self.pushType == .groupChat else {
            return ""
        }
        if let groupName = self.ctx?.payload.appData?.groupInfo?.name, groupName.count > 0 {
            BuzLog.debug("groupNameSourceDebug - 通过推送数据带过来的群名  -> \(String(describing: groupName))")
            
            return groupName
        }
        
        let localGroupInfoMap: [Int64: [String: String]] = PushShareDataTool.loadGroupsMap()
        if let groupId = self.ctx?.payload.appData?.groupInfo?.groupId,
           let groupInfoDict = localGroupInfoMap[groupId],
           let groupName = groupInfoDict[PushShareDataKey.groupInfoDisplayNameKey.rawValue], groupName.count > 0 {
            BuzLog.debug("groupNameSourceDebug - 通过本地信息取出群名 -> \(String(describing: groupName))")
            
            return groupName
        }
        
        return Localizable.groupchat()
    }
    
    func routerTargetId() ->String? {
        if self.pushType == .groupChat {
            if let extraData = self.ctx?.payload.appData?.router?.extraData {
                var id: String?
                if let fidStr = extraData[BuzPushInfoKey.targetId] as? String {
                    id = fidStr
                } else if let fidNumber = extraData[BuzPushInfoKey.targetId] as? Int64 {
                    id = "\(fidNumber)"
                }
                return id
            }
        }
        if self.pushType == .privateChat {
            if let extraData = self.ctx?.payload.appData?.router?.extraData {
                let isOnAirMessage = self.messageType == .command && self.businessType == .onAir
                var id: String?
                if isOnAirMessage {
                    if let targetId = extraData[BuzPushInfoKey.targetId] as? Int64 {
                        id = String(targetId)
                    }else if let targetId = extraData[BuzPushInfoKey.targetId] as? String {
                        id = targetId
                    }
                }else{
                    if let fromId = extraData[BuzPushInfoKey.fromId] as? String {
                        id = fromId
                    }
                }
                return id
            }
        }
        
        return nil
    }
}


// MARK: ---local data---------
extension BuzAPNs.IMPushControler {
    
    func localPushContent() -> String? {
        guard let appData = self.ctx?.payload.appData else {
            return nil
        }
        
        if let im = self.ctx?.payload.appData?.imInfo, im.isRecallMessage {
            return Localizable.message_recalled
        }
        
        var pushContent = appData.pushContent
        if self.contentParams.mode == .remote {
            pushContent = self.localConentForRemoteApns()
        }
        
        if self.messageType == .command{
            if self.businessType == .onAir {
                let onAirContent = self.onAirContent()
                if self.subBusinessType == .onAirStart {
                    let senderName = self.senderName()
                    pushContent = String.init(format: onAirContent ?? "", senderName)
                }else{
                    pushContent = onAirContent ?? ""
                }
            }else if self.businessType == .livePlace{
                let onLivePlaceContent = self.onLivePlaceContent()
                pushContent = onLivePlaceContent ?? ""
            }
        } else if self.messageType == .livePlaceShareCard {
            let topic = self.ctx?.payload.appData?.pushExtra?.topic ?? ""
            pushContent = "[\(Localizable.live_place)] " + String.init(format: Localizable.live_place_join_topic, topic)
        } else if self.messageType == .reactionChanged {
            if let emoji = appData.pushExtra?.reactionEmoji {
                pushContent = String(format: Localizable.qr_notify_tip, emoji)
            }
        }
        
        return pushContent
    }
    
    func localConentForRemoteApns() -> String? {
        guard let appData = self.ctx?.payload.appData else {
            return nil
        }
        // 离线通知多语言处理
        switch self.messageType {
        case .text:
            // 文本消息不处理
            return nil
        case .fileAttachment:
            return String.init(format: Localizable.chat_pop_msg_file_tag, appData.pushExtra?.fileName ?? "")
        case .voice, .leaveVoice:
            return Localizable.imsendaprivatevoiceleavemessage()
        case .image:
            return Localizable.notification_tile_photo
        case .video:
            return Localizable.notification_tile_video
        case .location:
            return Localizable.notification_tile_location
        case.voicEmoji:
            if let voicemojiIcon = appData.pushExtra?.voicemojiIcon as? String {
                return Localizable.notification_tile_voicemoji_updated + " " + voicemojiIcon
            }
        case .compatibleVoiceEmoji:
            return Localizable.notification_tile_voicemoji_updated
        case .voicegif:
            return Localizable.voice_gif_tag
        case .reactionChanged:
            if let reactionEmoji = appData.pushExtra?.reactionEmoji {
                return String(format: Localizable.qr_notify_tip, reactionEmoji)
            }
        case .unknow:
            return Localizable.chat_pop_msg_unsupported_tag
        default:
            return nil
        }
        return nil
    }
    
    func onAirContent() -> String? {
        if self.businessType == .onAir {
            if self.subBusinessType == .onAirStart {
                return Localizable.air_somebody_started_onair
            }else if self.subBusinessType == .onAirEnd {
                return Localizable.air_air_end
            }else if self.subBusinessType == .onAirMissed {
                return Localizable.air_miss_air_invited
            }else if self.subBusinessType == .onAirLineBusy {
                return Localizable.line_busy
            }
        }
        
        return nil
    }
    
    func onLivePlaceContent() -> String? {
        if self.businessType == .livePlace {
            if self.subBusinessType == .LivePlaceMissed {
                return Localizable.liveplace_missed_group;
            }else if self.subBusinessType == .LivePlaceLineBusy {
                return Localizable.line_busy;
            }else if self.subBusinessType == .LivePlaceEnd{
                return Localizable.live_place_closed;
            }
        }
        return nil
    }
}
