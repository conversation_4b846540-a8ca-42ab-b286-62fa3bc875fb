//
//  BuzAPNsControlerable.swift
//  buz
//
//  Created by st.chio on 2024/12/23.
//  Copyright © 2024 lizhi. All rights reserved.
//

import BuzDataShareKit
import BuzLog
import BuzAPNsCenter
import Localizable
import BuzLocalizable
import Voder<PERSON>IdempotentNSE

// MARK: ---BuzAPNs.Context--------------
public extension BuzAPNs {
    typealias ContentHandler = ((UNNotificationContent) -> Void)
    typealias TimeWillExpire = (() -> Void)?
    
    class Context {
        var request: UNNotificationRequest
        var content: UNMutableNotificationContent
        var payload: BuzAPNs.ActionPayload
        var idempotent: MessageIdempotent?
        var contentHandler: ContentHandler?
        var timeWillExpire: TimeWillExpire?
        
        init(request: UNNotificationRequest,
             content: UNMutableNotificationContent,
             payload: BuzAPNs.ActionPayload,
             idempotent: MessageIdempotent?,
             contentHandler: @escaping ContentHandler,
             timeWillExpire: TimeWillExpire? = nil) {
            
            self.request = request
            self.content = content
            self.payload = payload
            self.idempotent = idempotent
            self.contentHandler = contentHandler
            self.timeWillExpire = timeWillExpire
        }
    }
}

// MARK: ---BuzAPNsControlerable--------------
public protocol BuzAPNsControlerable {
    var ctx: BuzAPNs.Context?{ get }
    var pushType: BuzAPNs.PushType{ get }
    init(ctx: BuzAPNs.Context)
}

// MARK: ---extension BuzAPNsControlerable--------------
extension BuzAPNsControlerable {
    public var pushType: BuzAPNs.PushType {
        return self.ctx?.payload.appData?.pushType ?? .none
    }
    
    func senderName() -> String {
        var senderName: String = ""
        if let senderInfo = self.ctx?.payload.appData?.senderInfo {
            senderName = senderInfo.userName ?? ""
        }
        
        let contactsNameMap: [Int64: String] = PushShareDataTool.loadContactsMap()
        if let userId = self.ctx?.payload.appData?.senderInfo?.userId,
           let name = contactsNameMap[userId], name.count > 0 {
            senderName = name
        }
        
        return senderName
    }
}


extension BuzAPNsControlerable {
    
    func showNotification(_ content: UNMutableNotificationContent) {
        if let contentHandler = self.ctx?.contentHandler {
            APNsReporter.pushExposure(content, contentHandler: contentHandler)
        }
    }
    
    func updateBadge(fromId: String?, conversationType: ConversationType?) -> NSNumber? {
        if PushShareDataTool.neverShowBadgeFor(userId: fromId ?? "0") {
            return nil
        }
        var totalUnreadCount = PushShareDataTool.totalUnreadCount()

        if let fromId, let conversationType { // IM消息则未读数+1
            totalUnreadCount += 1
            BuzLog.ServiceExtension.log("updateBadge increased：\(fromId), \(conversationType)")
            PushShareDataTool.savetotalUnreadCount(totalUnreadCount)
        }
        return NSNumber(value: totalUnreadCount)
    }
}
