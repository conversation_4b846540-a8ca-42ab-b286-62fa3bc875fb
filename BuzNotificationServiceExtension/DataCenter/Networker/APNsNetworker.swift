//
//  APNsNetworker.swift
//  buz
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2024/8/22.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation
import BuzConfig
import BuzLog

enum NSERequestMethodType {
    case get
    case post
}

struct NSERequestStringResult: Codable {
    let msg: String?
    let data: String?
    let rcode: Int
    
    enum CodingKeys: String, CodingKey {
        case msg
        case data
        case rcode
    }
    
    init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        
        msg = try values.decodeIfPresent(String.self, forKey: .msg)
        data = try values.decodeIfPresent(String.self, forKey: .data)
        rcode = try values.decode(Int.self, forKey: .rcode)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encodeIfPresent(msg, forKey: .msg)
        try container.encodeIfPresent(data, forKey: .data)
        try container.encode(rcode, forKey: .rcode)
    }
}

struct NSERequestJsonResult: Codable {
    let msg: String?
    let data: [String : Any]?
    let rcode: Int
    
    enum CodingKeys: String, CodingKey {
        case msg
        case data
        case rcode
    }
    
    init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        
        msg = try values.decodeIfPresent(String.self, forKey: .msg)
        data = try values.decodeIfPresent([String: Any].self, forKey: .data)
        rcode = try values.decode(Int.self, forKey: .rcode)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        
        try container.encodeIfPresent(msg, forKey: .msg)
        try container.encodeIfPresent(data, forKey: .data)
        try container.encode(rcode, forKey: .rcode)
    }
}

class NSERequest {
    let path: String
    var method: NSERequestMethodType = .get
    var params: [String: Any] = [:]
    var allowRetry = true
    var retryCount: Int = 0
    var maxRetryCount = 1
    var useDomainConfig = false // 是否使用域名配置
    var needCheckStatusCode = true
    var isDebug = false

    init(path: String) {
        self.path = path
    }
    
    deinit {
        print("")
    }
}

struct NSEStringResponse: Codable {
    let data: String?
}

struct NSEAsrTextResponse: Codable {
    let msgId: Int64?
    let asrText: String?
}

class APNsNetworker {
    
    enum NSEErrorDomain : String {
        case httpError = "com.NSENetwork.error.http"    // http错误
        case buzError = "com.NSENetwork.error.buz"      // 业务错误
        case parseError = "com.NSENetwork.error.parse"  // 解析错误
    }
    
    enum NotificationServiceRequestPath : String {
        case getWebToken = "/customer/getWebToken"
    }
    
    static func baseURL(useDomainConfig: Bool) -> String {
        // 兜底域名
        #if DEBUG
        var baseURL = "http://buz.yfxn.lzpsap1.com"
        #else
        var baseURL = "https://api.buz-app.com"
        #endif
        
        guard useDomainConfig else {
            return baseURL
        }
        
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            return baseURL
        }
        
        let env = userDefaults.integer(forKey: "BuzShareStore_ENV")
        var key = ""
        if env == 0 { // 正式环境
            key = "BuzShareStore_nseDomain_product"
        } else if env == 1 { // 预发布环境
            key = "BuzShareStore_nseDomain_pre"
        } else { // 灯塔环境
            key = "BuzShareStore_nseDomain_tower"
        }
        if let domain = userDefaults.string(forKey: key), domain.count > 0 {
            #if DEBUG
            baseURL = "http://" + domain
            #else
            baseURL = "https://" + domain
            #endif
        }
        return baseURL
    }
    
    static func createURL(path: String, parameters: [String: Any], useDomainConfig: Bool = false) -> URL? {
    
        let baseURL = baseURL(useDomainConfig: useDomainConfig)
        var urlComponents = URLComponents(string: baseURL)
        urlComponents?.path = path
        var queryItems = [URLQueryItem]()
        
        for (key, value) in parameters {
            // 根据参数类型处理不同的值
            if let stringValue = value as? String {
                queryItems.append(URLQueryItem(name: key, value: stringValue))
            } else if let numberValue = value as? NSNumber {
                queryItems.append(URLQueryItem(name: key, value: numberValue.stringValue))
            } else if let arrayValue = value as? [Any] {
                for item in arrayValue {
                    if let itemString = item as? String {
                        queryItems.append(URLQueryItem(name: "\(key)[]", value: itemString))
                    } else if let itemNumber = item as? NSNumber {
                        queryItems.append(URLQueryItem(name: "\(key)[]", value: itemNumber.stringValue))
                    }
                }
            } else if let dictValue = value as? [String: Any] {
                for (dictKey, dictValue) in dictValue {
                    if let dictStringValue = dictValue as? String {
                        queryItems.append(URLQueryItem(name: "\(key)[\(dictKey)]", value: dictStringValue))
                    } else if let dictNumberValue = dictValue as? NSNumber {
                        queryItems.append(URLQueryItem(name: "\(key)[\(dictKey)]", value: dictNumberValue.stringValue))
                    }
                }
            }
        }
        
        urlComponents?.queryItems = queryItems
        return urlComponents?.url
    }
    
    static func _sendRequest(_ req: NSERequest, completion: @escaping (_ jsonData: Data?, _ error: NSError?) -> Void) {
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId), let token = userDefaults.string(forKey: "BuzShareStore_webToken") else {
            return
        }
        
        guard let url = createURL(path: req.path, parameters: req.params, useDomainConfig: req.useDomainConfig) else {
            return
        }
        
        var request = URLRequest(url: url)
        switch req.method {
        case .get:
            request.httpMethod = "GET"
        case .post:
            request.httpMethod = "POST"
        }
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(token, forHTTPHeaderField: "token")

        let callback: ((Data?, NSError?) -> Void) = { jsonData, error in
            DispatchQueue.main.async {
                completion(jsonData, error)
            }
        }
        
        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            guard error == nil else {
                // http 网络层错误
                if let err = error as? NSError {
                    let err = NSError(domain: NSEErrorDomain.httpError.rawValue,
                                      code: err.code,
                                      userInfo: [NSLocalizedDescriptionKey: err.localizedDescription])
                    callback(nil, err)
                    BuzLog.ServiceExtension.log("request error: url = \(String(describing: request.url)), response error: \(err)")
                } else {
                    let err = NSError(domain: NSEErrorDomain.httpError.rawValue,
                                      code: -4,
                                      userInfo: [NSLocalizedDescriptionKey: "unknown error"])
                    callback(nil, err)
                    BuzLog.ServiceExtension.log("request error: url = \(String(describing: request.url)), error: \(err)")
                }
                return
            }
            
            guard let resp = response as? HTTPURLResponse else {
                // http 网络层错误
                let err = NSError(domain: NSEErrorDomain.httpError.rawValue,
                                  code: -3,
                                  userInfo: [NSLocalizedDescriptionKey: "this is not http request"])
                callback(nil, err)
                BuzLog.ServiceExtension.log("request error: url = \(String(describing: request.url)), error: \(err)")
                return
            }
            
            if req.needCheckStatusCode || req.isDebug {
                guard resp.statusCode == 200 else {
                    // http 网络层错误
                    let statusCode = resp.statusCode
                    var errMsg = "unknown error"
                    if let data = data {
                        errMsg = String(data: data, encoding: .utf8) ?? HTTPURLResponse.localizedString(forStatusCode: statusCode)
                        
                    } else {
                        errMsg = "http response data is nil"
                    }
                    
                    let err = NSError(domain: NSEErrorDomain.httpError.rawValue,
                                      code: statusCode,
                                      userInfo: [NSLocalizedDescriptionKey: errMsg])
                    callback(nil, err)
                    BuzLog.ServiceExtension.log("request error: url = \(String(describing: request.url)), http status code error:\(err)")
                    return
                }
            }
            
            guard let data = data else {
                // 没有http响应数据
                let err = NSError(domain: NSEErrorDomain.httpError.rawValue,
                                  code: -3,
                                  userInfo: [NSLocalizedDescriptionKey: "http response data is nil"])
                callback(nil, err)
                BuzLog.ServiceExtension.log("request error: \(err)")
                return
            }
                
            // 0. 以下是 http 成功 (statusCode == 200)
            
            let failureCallback: ((Any) -> Void) = { r in
                var rcode = 0
                var errorMsg = ""
                
                if let result = r as? NSERequestJsonResult {
                    rcode = result.rcode
                    errorMsg = result.msg ?? "unknown error"
                } else if let result = r as? NSERequestStringResult {
                    rcode = result.rcode
                    errorMsg = result.msg ?? "unknown error"
                }
                
                if rcode != 0 {
                    // 业务错误
                    let err = NSError(domain: NSEErrorDomain.buzError.rawValue,
                                      code: rcode,
                                      userInfo: [NSLocalizedDescriptionKey: errorMsg])
                    callback(nil, err)
                    BuzLog.ServiceExtension.log("request error: \(err)")
                } else {
                    // 业务data为空
                    let err = NSError(domain: NSEErrorDomain.buzError.rawValue,
                                      code: -1,
                                      userInfo: [NSLocalizedDescriptionKey: "result data is nil"])
                    callback(nil, err)
                    BuzLog.ServiceExtension.log("request error: \(err)")
                }
            }
            
            // 0. 控制台打印请求响应
            let responseJson = try? JSONSerialization.jsonObject(with: data, options: [])
            BuzLog.ServiceExtension.log("http response, path: \(req.path), params: \(req.params), token:\(request.allHTTPHeaderFields?["token"] ?? "") responseJson: \(String(describing: responseJson))")
            
            // 1.以下是数据解析流程
            do {
                // 1.1 先用 json 结构解析
                let result = try JSONDecoder().decode(NSERequestJsonResult.self, from: data)
                BuzLog.ServiceExtension.log("request success, path: \(req.path), params: \(req.params), result: \(result)")
                
                if let rData = result.data, result.rcode == 0 {
                    // 业务成功
                    do {
                        let jsonData = try JSONSerialization.data(withJSONObject: rData, options: [])
                        callback(jsonData, nil)
                    } catch let err as NSError {
                        callback(nil, err)
                        BuzLog.ServiceExtension.log("request error: response data parse failed, error: \(err)")
                    }
                } else {
                    // 业务失败
                    failureCallback(result)
                }
            } catch let err as NSError {
                
                // 1.2 再用 string 结构解析
                do {
                    let result = try JSONDecoder().decode(NSERequestStringResult.self, from: data)
                    BuzLog.ServiceExtension.log("request success, path: \(req.path), params: \(req.params), result: \(result)")
                    
                    if let rData = result.data, result.rcode == 0 {
                        // 业务成功
                        let dict = ["data" : rData]
                        do {
                            let jsonData = try JSONSerialization.data(withJSONObject: dict, options: [])
                            callback(jsonData, nil)
                        } catch let err as NSError {
                            callback(nil, err)
                            BuzLog.ServiceExtension.log("request error: response data parse failed, error: \(err)")
                        }
                    } else {
                        // 业务失败
                        failureCallback(result)
                    }
                } catch let err as NSError {
                    callback(nil, err)
                    BuzLog.ServiceExtension.log("request error: response data parse failed, error: \(err)")
                }
            }
        }
        
        task.resume()
    }
    
    static func sendRequest(_ req: NSERequest, completion: @escaping (_ jsonData: Data?, _ error: NSError?) -> Void) {
        _sendRequest(req) { jsonData, error in
            if let err = error {
                // token 过期
                let isTokenExpired = err.code == 409
                if err.domain == NSEErrorDomain.buzError.rawValue, isTokenExpired {
                    // 达到最大重试次数
                    if req.retryCount >= req.maxRetryCount {
                        completion(jsonData, error)
                        return
                    }
                    // getWebToken 如果也409不重试，避免死循环
                    if req.path == NotificationServiceRequestPath.getWebToken.rawValue {
                        completion(jsonData, error)
                        return
                    }
                    
                    // 刷新token
                    getWebToken { isSucceed in
                        // 重试请求
                        if isSucceed {
                            req.retryCount = req.retryCount + 1
                            Self.sendRequest(req, completion: completion)
                        }
                    }
                } else {
                    completion(jsonData, error)
                }
            } else {
                // 成功
                completion(jsonData, error)
            }
        }
    }
    
    static func getWebToken(_ completion: @escaping (Bool) -> Void) {
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId), let _ = userDefaults.string(forKey: "BuzShareStore_webToken") else {
            return
        }
        
        let request = NSERequest(path: NotificationServiceRequestPath.getWebToken.rawValue)
        request.method = .get
        
        sendRequest(request) { jsonData, error in
            if let data = jsonData, let resp = try? JSONDecoder().decode(NSEStringResponse.self, from: data) {
                if let token = resp.data {
                    userDefaults.set(token, forKey: "BuzShareStore_webToken")
                    BuzLog.ServiceExtension.log("getWebToken success")
                    completion(true)
                } else {
                    BuzLog.ServiceExtension.log("getWebToken fail")
                    completion(false)
                }
            } else {
                completion(false)
            }
        }
    }
    
    static func getAsrText(msgId: Int64, pageBusinessType: String, pageBusinessId: String, elementBusinessType: String, _ completion: @escaping (NSEAsrTextResponse?) -> Void) {
        
        let params = [
            "msgId" : String(msgId),
            "pageBusinessType" : pageBusinessType,
            "pageBusinessId" : pageBusinessId,
            "elementBusinessType" : elementBusinessType,
        ]
        
        let request = NSERequest(path: "/getAsrLongPoll")
        request.method = .get
        request.params = params
        request.useDomainConfig = true
//        request.needCheckStatusCode = false
//        request.isDebug = true
        
        sendRequest(request) { jsonData, error in
            
            if let data = jsonData, let resp = try? JSONDecoder().decode(NSEAsrTextResponse.self, from: data) {
                BuzLog.ServiceExtension.log("getAsrText success: \(String(describing: resp))")
                completion(resp)
            } else if let err = error {
                BuzLog.ServiceExtension.log("getAsrText error: \(err)")
                // 如果是网络错误，进行一次 asr 转义重试，业务错误则忽略
                if err.domain == NSEErrorDomain.httpError.rawValue {
                    retryGetAsrText(request: request, completion)
                } else {
                    completion(nil)
                }
            } else {
                completion(nil)
            }

        }
    }
    
    static func retryGetAsrText(request: NSERequest, _ completion: @escaping (NSEAsrTextResponse?) -> Void) {
//        request.isDebug = false
        sendRequest(request) { jsonData, error in
            if let data = jsonData, let resp = try? JSONDecoder().decode(NSEAsrTextResponse.self, from: data) {
                BuzLog.ServiceExtension.log("retry getAsrText success: \(String(describing: resp))")
                BuzLog.ServiceExtension.log("\(String(describing: resp))")
                completion(resp)
            } else {
                BuzLog.ServiceExtension.log("retry getAsrText error: \(String(describing: error))")
                completion(nil)
            }

        }
    }
}
