//
//  NotificationService.swift
//  BuzNotificationServiceExtension
//
//  Created by liuyufeng on 2022/8/18.
//  Copyright © 2022 lizhi. All rights reserved.
//

import BuzLocalizable
import Intents
import Localizable
import SDWebImage
import UIKit
import UserNotifications
import VoderXIdempotentNSE
import BuzDataShareKit
import BuzAPNsCenter
import BuzConfig
import BuzLog

class NotificationService: UNNotificationServiceExtension {
    var request: UNNotificationRequest!
    var contentHandler: ((UNNotificationContent) -> Void)?
    var content: UNMutableNotificationContent?
    var controller: BuzAPNsControlerable?

    let messageIdempotent: MessageIdempotent? = {
        guard let userId = NSEShareConfig.sharedUserId else {
            return nil
        }
        let config = MessageIdempotentConfig(userId: userId, appGroups: NSEShareConfig.appGroups)
        return MessageIdempotent(config: config)
    }()

    override func didReceive(_ request: UNNotificationRequest, withContentHandler contentHandler: @escaping (UNNotificationContent) -> Void) {
        Localizable.setGroupId(groupId: BuzConfig.groupStoreId)
        
        self.request = request
        self.contentHandler = contentHandler
        self.content = (request.content.mutableCopy() as? UNMutableNotificationContent)
        self.content?.sound = UNNotificationSound(named: UNNotificationSoundName(AppAudioCues.offLineNotification.rawValue))

        BuzLog.ServiceExtension.setupLogz()

        let content = request.content
        BuzLog.ServiceExtension.log("didReceive, reuqest:\(request.identifier), title:\(content.title), \(content.subtitle), userInfo:\(content.userInfo)")

        Task {
            handleReceive()
        }
    }
    
    override func serviceExtensionTimeWillExpire() {
        BuzLog.ServiceExtension.log("time will expire")
        if let contentHandler = contentHandler, let content = content {
            contentHandler(content)
        }
    }

    func handleReceive() {
        guard let content = content, let contentHandler = contentHandler else {
            return
        }
        let userInfo = self.request.content.userInfo
        let apns = BuzAPNs.RemotePushPayload.init(received: userInfo)
        guard let alert = apns.aps?.alert,
              let key = alert.key else {
            return
        }
        let payload = BuzAPNs.ActionPayload.init(alert: alert, received: key)
        let ctx = BuzAPNs.Context.init(request: request,
                                       content: content,
                                       payload: payload,
                                       idempotent: self.messageIdempotent,
                                       contentHandler: contentHandler)
        
        if payload.isIMPush {
            // 处理im消息的推送
            self.controller = BuzAPNs.IMPushControler.init(ctx: ctx)
        }else {
            // 处理biz消息的推送
            self.controller = BuzAPNs.BizPushController.init(ctx: ctx)
        }
    }
}
