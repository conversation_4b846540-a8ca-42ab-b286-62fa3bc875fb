//
//  APNsReporter.swift
//  BuzNotificationServiceExtension
//
//  Created by liuyufeng on 2023/1/29.
//  Copyright © 2023 lizhi. All rights reserved.
//

import LZPush
import BuzLog

private enum CallBackURL: String {
    case release = "https://growthpushcb.buz-app.com"
    case debug = "http://pushcallbackout.lz225.com"
}

struct APNsReporter {
    
    static func pushExposure(_ content: UNMutableNotificationContent, contentHandler: @escaping (UNNotificationContent) -> Void) {
        // 创建配置信息, 并传入消息体实例
        let config = SEPushConfig(content: content)
        // 设置曝光上报域名URL
        #if DEBUG
        config.exposureReportBaseURL = URL(string: CallBackURL.debug.rawValue)
        #else
        config.exposureReportBaseURL = URL(string: CallBackURL.release.rawValue)
        #endif
        // 设置 push app id
        config.pushAppId = "buz"

        BuzLog.ServiceExtension.log("reportNotificationExposure")

        // 注册处理功能, 并在 completionHandler 中调用 self.contentHandler
        SEPush.register(with: config) { content in
            BuzLog.ServiceExtension.log("Exposure finished")
            contentHandler(content)
        }
    }
    
}
