//
//  SharePresenter.swift
//  BuzShareExtension
//
//  Created by 彭智鑫 on 2024/7/8.
//  Copyright © 2024 lizhi. All rights reserved.
//

import UIKit
import UniformTypeIdentifiers
import AVFoundation
import Photos
import Network
import BuzLocalizable
import Localizable

class SharePresenter: NSObject {
    
    weak var vc: ShareViewController?
    
    var contacts: [ShareExtentionContact] = [] {
        didSet {
            if contacts.isEmpty {
                self.vc?.setupEmptyContactView()
            } else {
                self.vc?.setupTableView()
            }
        }
    }
    
    var isLogin: Bool = false {
        didSet {
            if !isLogin {
                self.vc?.setupNeedLoginView()
            }
        }
    }
    
    var selectedContact: ShareExtentionContact?
    
    func setupData() {
        let userId = ShareExtentionDataTool.loadSessionUserId()
        self.isLogin = userId != 0
        
        guard self.isLogin else { return }
        
        ShareExtentionDataTool.clearShareDirectory()
        
        let contacts = ShareExtentionDataTool.loadContacts()
        
        self.checkIsShareMedia { hasMedia in
            self.contacts = contacts.filter { !hasMedia || $0.isSupportMedia }
        }
    }
    
    func openBuz() {
        if #available(iOS 18.0, *) {
            if let url = URL(string: "buzshare://") {
                var responder: UIResponder? = self.vc
                while responder != nil {
                    if let application = responder as? UIApplication {
                        application.open(url, options: [:], completionHandler: nil)
                        break
                    }
                    responder = responder?.next
                }
            }
        }else {
            if let url = URL(string: "buzshare://") {
                var responder = self.vc as UIResponder?
                let selectorOpenURL = sel_registerName("openURL:")
                while responder != nil {
                    if responder?.responds(to: selectorOpenURL) == true {
                        responder?.perform(selectorOpenURL, with: url)
                        break
                    }
                    responder = responder!.next
                }
            }
        }
        
    }
    
    func close() {
        self.vc?.extensionContext?.completeRequest(returningItems: [])
    }
    
    func send() {
        self.checkNetworkStatus { isConnected in
            if isConnected {
                guard let selectedContact = self.selectedContact else {
                    return
                }
                
                self.loadAttachments(contact: selectedContact) { attachments, inputItemsCount in
                    if attachments.count > 0 {
                        ShareExtentionDataTool.saveShareExtensionAttachments(attachments)
                        self.openBuz()
                        
                        if attachments.count == inputItemsCount {
                            self.close()
                        }
                    } else {
                        self.showAlert(message: Localizable.share_fail)
                    }
                }
            } else {
                self.showAlert(message: Localizable.network_error_try_again)
            }
        }
    }
    
    func checkIsShareMedia(completion: @escaping (_ hasMedia: Bool) -> Void) {
        guard let extensionItem = self.vc?.extensionContext?.inputItems.first as? NSExtensionItem, let attachments = extensionItem.attachments else {
            completion(false)
            return
        }
        
        var hasMedia = false
        let dispatchGroup = DispatchGroup()
        
        attachments.forEach { attachment in
            if attachment.hasItemConformingToTypeIdentifier(UTType.image.identifier) {
                dispatchGroup.enter()
                attachment.loadItem(forTypeIdentifier: UTType.image.identifier) { item, error in
                    if item is URL || item is UIImage {
                        hasMedia = true
                    }
                    dispatchGroup.leave()
                }
            }
            
            if attachment.hasItemConformingToTypeIdentifier(UTType.movie.identifier) {
                dispatchGroup.enter()
                attachment.loadItem(forTypeIdentifier: UTType.movie.identifier) { item, error in
                    if item is URL {
                        hasMedia = true
                    }
                    dispatchGroup.leave()
                }
            }
        }
        
        dispatchGroup.notify(queue: .main) {
            completion(hasMedia)
        }
    }
    
    func loadAttachments(contact: ShareExtentionContact, completion: @escaping ([ShareExtentionAttachment], _ inputItemsCount: Int) -> Void) {
        guard let extensionItem = self.vc?.extensionContext?.inputItems.first as? NSExtensionItem, let attachments = extensionItem.attachments else {
            completion([], 0)
            return
        }
        
        var items: [ShareExtentionAttachment] = []
        let dispatchGroup = DispatchGroup()
        
        attachments.forEach { attachment in
            if attachment.hasItemConformingToTypeIdentifier(UTType.plainText.identifier) {
                dispatchGroup.enter()
                attachment.loadItem(forTypeIdentifier: UTType.plainText.identifier) { item, error in
                    if let text = item as? String, self.checkTextIsValid(text) {
                        items.append(ShareExtentionAttachment(targetId: contact.sourceId, targetType: contact.convType, type: 1, content: text))
                    }
                    dispatchGroup.leave()
                }
            } else if attachment.hasItemConformingToTypeIdentifier(UTType.url.identifier) {
                dispatchGroup.enter()
                attachment.loadItem(forTypeIdentifier: UTType.url.identifier) { item, error in
                    if let urlPath = item as? URL {
                        if urlPath.isFileURL {
                            if let path = ShareExtentionDataTool.saveFileToShareDirectory(tempFileURL: urlPath) {
                                items.append(ShareExtentionAttachment(targetId: contact.sourceId, targetType: contact.convType, type: self.isImageFile(path.absoluteString) ? 2 : 3, content: path.absoluteString))
                            }
                        } else if self.checkTextIsValid(urlPath.absoluteString) {
                            items.append(ShareExtentionAttachment(targetId: contact.sourceId, targetType: contact.convType, type: 1, content: urlPath.absoluteString))
                        }
                    }
                    dispatchGroup.leave()
                }
            } else if attachment.hasItemConformingToTypeIdentifier(UTType.image.identifier) {
                dispatchGroup.enter()
                attachment.loadItem(forTypeIdentifier: UTType.image.identifier) { item, error in
                    if let imagePath = item as? URL, self.checkImageIsValid(at: imagePath), let path = ShareExtentionDataTool.saveFileToShareDirectory(tempFileURL: imagePath) {
                        items.append(ShareExtentionAttachment(targetId: contact.sourceId, targetType: contact.convType, type: 2, content: path.absoluteString))
                    } else if let image = item as? UIImage, let path = ShareExtentionDataTool.saveImageToShareDirectory(image: image), self.checkImageIsValid(at: path) {
                        items.append(ShareExtentionAttachment(targetId: contact.sourceId, targetType: contact.convType, type: 2, content: path.absoluteString))
                    }
                    dispatchGroup.leave()
                }
            } else if attachment.hasItemConformingToTypeIdentifier(UTType.movie.identifier) {
                dispatchGroup.enter()
                attachment.loadItem(forTypeIdentifier: UTType.movie.identifier) { item, error in
                    if let moviePath = item as? URL, self.checkMovieIsValid(at: moviePath), let path = ShareExtentionDataTool.saveFileToShareDirectory(tempFileURL: moviePath) {
                        items.append(ShareExtentionAttachment(targetId: contact.sourceId, targetType: contact.convType, type: 3, content: path.absoluteString))
                    }
                    dispatchGroup.leave()
                }
            }
        }
        
        dispatchGroup.notify(queue: .main) {
            completion(items, attachments.count)
        }
    }
    
    func isFileSizeExceeding1GB(at url: URL) -> Bool {
        do {
            let fileAttributes = try FileManager.default.attributesOfItem(atPath: url.path)
            
            if let fileSize = fileAttributes[FileAttributeKey.size] as? UInt64 {
                let oneGBInBytes = UInt64(1024 * 1024 * 1024)
                
                if fileSize > oneGBInBytes {
                    return true
                }
            }
        } catch {
            print("Error getting file attributes: \(error)")
            return false
        }
        
        return false
    }
    
    func isVideoDurationExceeding30Minutes(at url: URL) -> Bool {
        let asset = AVAsset(url: url)
        let duration = asset.duration
        let durationInSeconds = CMTimeGetSeconds(duration)
        let thirtyMinutesInSeconds: Double = 30 * 60
        
        return durationInSeconds > thirtyMinutesInSeconds
    }
    
    func checkTextIsValid(_ text: String) -> Bool {
        return text.count <= 1000
    }
    
    func checkImageIsValid(at url: URL) -> Bool {
        return !self.isFileSizeExceeding1GB(at: url)
    }
    
    func checkMovieIsValid(at url: URL) -> Bool {
        return !self.isFileSizeExceeding1GB(at: url) && !self.isVideoDurationExceeding30Minutes(at: url)
    }
    
    func showAlert(message: String) {
        DispatchQueue.main.async {
            let alert = ShareAlertView(message: message)
            
            if let window = self.vc?.view.window {
                window.addSubview(alert)
                alert.snp.makeConstraints { make in
                    make.edges.equalToSuperview()
                }
                
                alert.alpha = 0
                UIView.animate(withDuration: 0.25) {
                    alert.alpha = 1
                }
            }
        }
    }
    
    func checkNetworkStatus(completion: @escaping (Bool) -> Void) {
        let monitor = NWPathMonitor()
        let queue = DispatchQueue(label: "NetworkStatusMonitor")
        
        monitor.pathUpdateHandler = { path in
            if path.status == .satisfied {
                completion(true)
            } else {
                completion(false)
            }
            monitor.cancel()
        }
        
        monitor.start(queue: queue)
    }
    
    func isImageFile(_ filePath: String) -> Bool {
        // 支持的图片文件格式
        let imageExtensions: Set<String> = ["jpeg", "jpg", "png", "gif", "bmp", "tiff", "tif"]
        
        // 获取文件后缀名
        let fileExtension = (filePath as NSString).pathExtension.lowercased()
        
        // 判断文件后缀名是否在支持的图片文件格式中
        return imageExtensions.contains(fileExtension)
    }
}
