//
//  ShareTableViewCell.swift
//  BuzShareExtension
//
//  Created by 彭智鑫 on 2024/7/8.
//  Copyright © 2024 lizhi. All rights reserved.
//

import UIKit
import SDWebImage

class ShareTableViewCell: UITableViewCell {
    
    static let identifier = "ShareTableViewCell"

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
        self.selectionStyle = .none
        self.backgroundColor = .clear
        self.setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private lazy var portraitView: UIImageView = {
        let view = UIImageView()
        view.contentMode = .scaleAspectFill
        view.layer.cornerRadius = 25
        view.layer.masksToBounds = true
        view.backgroundColor = .overlay_grey20
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.overlay_white4.cgColor
        return view
    }()
    
    private lazy var containerView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var centerStack: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 2
        stack.alignment = .leading
        stack.translatesAutoresizingMaskIntoConstraints = false
        return stack
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.textColor = .text_white_main
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.numberOfLines = 2
        label.lineBreakMode = .byTruncatingTail
        return label
    }()
    
    private lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.textColor = .text_white_secondary
        label.font = .systemFont(ofSize: 14, weight: .regular)
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var selectIcon: UIImageView = {
        let view = UIImageView()
        view.image = .init(resource: .select)
        view.isHidden = false
        return view
    }()
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        super.setSelected(selected, animated: animated)
        
        // 自定义选中效果
        if selected {
            contentView.backgroundColor = .overlay_white4
        } else {
            contentView.backgroundColor = .clear
        }
    }//重写这个和下面的方法实现点击高亮
    
    override func setHighlighted(_ highlighted: Bool, animated: Bool) {
        super.setHighlighted(highlighted, animated: animated)
        // 自定义高亮效果
        if highlighted {
            contentView.backgroundColor = .overlay_white4
        } else {
            contentView.backgroundColor = .clear
        }
    }
    
    private func setupUI() {
        self.contentView.addSubview(portraitView)
        portraitView.snp.makeConstraints { make in
            make.size.equalTo(50)
            make.leading.equalToSuperview().offset(20)
            make.centerY.equalToSuperview()
            make.top.greaterThanOrEqualTo(7)
            make.bottom.greaterThanOrEqualTo(-7)
        }
        
        self.contentView.addSubview(containerView)
        containerView.addSubview(centerStack)
        centerStack.addArrangedSubview(nameLabel)
        centerStack.addArrangedSubview(usernameLabel)
        
        centerStack.snp.makeConstraints{ make in
            make.centerY.equalToSuperview()
            make.leading.trailing.equalToSuperview()
        }
        containerView.snp.makeConstraints { make in
            make.leading.equalTo(portraitView.snp.trailing).offset(16)
            make.centerY.equalToSuperview()
            make.top.greaterThanOrEqualTo(7)
            make.bottom.greaterThanOrEqualTo(-7)
            make.trailing.equalToSuperview().offset(-60)
        }
        
        usernameLabel.snp.makeConstraints { make in
            make.height.equalTo(21)
        }
        
        self.contentView.addSubview(selectIcon)
        selectIcon.snp.makeConstraints { make in
            make.width.equalTo(24)
            make.trailing.equalToSuperview().offset(-20)
            make.centerY.equalToSuperview()
        }
    }
    
    public func configure(_ model: ShareExtentionContact, isSelected: Bool) {
        portraitView.sd_setImage(with: URL(string: model.portrait), placeholderImage: .init(resource: .buzPortraitDefault))
        nameLabel.text = model.name
        usernameLabel.isHidden = model.buzId.isEmpty
        usernameLabel.text = "@\(model.buzId)"
        selectIcon.isHidden = !isSelected
        nameLabel.textColor = isSelected ? .token.color_foreground_highlight_default : .text_white_main
    }
}
