//
//  ShareAlertView.swift
//  BuzShareExtension
//
//  Created by 彭智鑫 on 2024/7/10.
//  Copyright © 2024 lizhi. All rights reserved.
//

import UIKit
import BuzLocalizable
import Localizable

class ShareAlertView: UIView {
    init(message: String) {
        super.init(frame: .zero)
        
        self.backgroundColor = .overlay_mask
        setupUI(message: message)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .overlay_grey10
        view.layer.cornerRadius = 20
        view.layer.masksToBounds = true
        return view
    }()
    
    private lazy var messageLabel: UILabel = {
        let label = UILabel()
        label.textColor = .text_white_main
        label.font = .systemFont(ofSize: 18, weight: .medium)
        label.textAlignment = .center
        return label
    }()
    
    private lazy var confirmButton: UIButton = {
        let button = UIButton()
        button.setTitle(Localizable.ok(), for: .normal)
        button.setBackgroundColor(.secondary_button_main, for: .normal)
        button.setBackgroundColor(.secondary_button_main.withAlphaComponent(0.6), for: .selected)
        button.setTitleColor(.text_black_main, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.layer.cornerRadius = 24
        button.layer.masksToBounds = true
        button.addTarget(self, action: #selector(onConfirmAction), for: .touchUpInside)
        return button
    }()
    
    private func setupUI(message: String) {
        self.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.size.equalTo(CGSize(width: 334, height: 182))
            make.center.equalToSuperview()
        }
        
        containerView.addSubview(confirmButton)
        confirmButton.snp.makeConstraints { make in
            make.bottom.trailing.equalToSuperview().offset(-20)
            make.leading.equalToSuperview().offset(20)
            make.height.equalTo(48)
        }
        
        containerView.addSubview(messageLabel)
        messageLabel.text = message
        messageLabel.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(20)
            make.trailing.equalToSuperview().offset(-20)
            make.top.equalToSuperview().offset(30)
            make.bottom.equalTo(confirmButton.snp.top).offset(-30)
        }
    }
    
    @objc
    func onConfirmAction() {
        UIView.animate(withDuration: 0.15, animations: {
            self.alpha = 0.0
        }) { _ in
            self.removeFromSuperview()
        }
    }
}
