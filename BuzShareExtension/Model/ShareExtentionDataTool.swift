//
//  ShareExtentionDataTool.swift
//  buz
//
//  Created by l<PERSON>hi on 2024/7/6.
//  Copyright © 2024 lizhi. All rights reserved.
//

import UIKit
import BuzConfig

struct ShareExtentionContact: Codable {
    let name: String
    let sourceId: Int64
    let isBot: Bool
    let portrait: String
    /// 1.peer , 2.group
    let convType: Int
    /// 是否支持发送媒体文件
    let isSupportMedia: Bool
    let buzId: String
}

struct ShareExtentionAttachment: Codable {
    let targetId: Int64
    let targetType: Int
    /// 1. text, 2. image, 3. video
    let type: Int
    let content: String
}

enum ShareExtentionDataKey : String {
    case Contacts = "ContactsKey"
    case SessionUid = "PushShareDataKey_session_uid"
    case ShareExtensionAttachments = "ShareExtensionAttachmentsKey"
}

class ShareExtentionDataTool {
    
    static let ShareGroupName = BuzConfig.groupStoreId
    
    static func saveContacts(_ contacts: [ShareExtentionContact]){
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        let encoder = JSONEncoder()
        if let encodeData = try? encoder.encode(contacts) {
            userDefaults.set(encodeData, forKey: ShareExtentionDataKey.Contacts.rawValue)
        }
    }
    
    static func loadContacts() -> [ShareExtentionContact]{
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return []
        }
        if let data = userDefaults.value(forKey: ShareExtentionDataKey.Contacts.rawValue) as? Data {
            let decoder = JSONDecoder()
            if let decoded = try? decoder.decode([ShareExtentionContact].self, from: data){
                return decoded
            } else {
                return []
            }
        } else {
            return []
        }
    }
    
    static func loadSessionUserId() -> Int64 {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName),
              let uid = userDefaults.object(forKey: ShareExtentionDataKey.SessionUid.rawValue) as? Int64
        else {
            return 0
        }
        
        return uid
    }
    
    static func saveShareExtensionAttachments(_ attachments: [ShareExtentionAttachment]) {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        
        let encoder = JSONEncoder()
        if let encodeData = try? encoder.encode(attachments) {
            userDefaults.set(encodeData, forKey: ShareExtentionDataKey.ShareExtensionAttachments.rawValue)
        }
    }
    
    static func loadShareExtensionAttachment() -> [ShareExtentionAttachment] {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return []
        }
        
        if let data = userDefaults.value(forKey: ShareExtentionDataKey.ShareExtensionAttachments.rawValue) as? Data {
            let decoder = JSONDecoder()
            if let decoded = try? decoder.decode([ShareExtentionAttachment].self, from: data){
                return decoded
            } else {
                return []
            }
        } else {
            return []
        }
    }
    
    static func clearShareExtensionAttachment() {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        
        userDefaults.removeObject(forKey: ShareExtentionDataKey.ShareExtensionAttachments.rawValue)
    }
    
    static func saveFileToShareDirectory(tempFileURL: URL) -> URL? {
        guard let shareDirectoryURL = getShareDirectory() else {
            print("Failed to get share directory URL")
            return nil
        }
        
        let fileNameWithExtension = tempFileURL.lastPathComponent
        let fileName = (fileNameWithExtension as NSString).deletingPathExtension
        let time = "\(Int64(Date().timeIntervalSince1970 * 1000))"
        
        let destURL = URL(fileURLWithPath: shareDirectoryURL.path).appendingPathComponent(fileName + "_" + time + "." + tempFileURL.pathExtension)
        
        do {
            if FileManager.default.fileExists(atPath: destURL.path) {
                try FileManager.default.removeItem(at: destURL)
            }
            try FileManager.default.copyItem(at: tempFileURL, to: destURL)
            return destURL
        } catch {
            print("Error saving file to App Group: \(error)")
            return nil
        }
    }
    
    static func saveImageToShareDirectory(image: UIImage) -> URL? {
        guard let shareDirectoryURL = getShareDirectory() else {
            print("Failed to get share directory URL")
            return nil
        }
        let time = "\(Int64(Date().timeIntervalSince1970 * 1000))"
        
        let destURL = URL(fileURLWithPath: shareDirectoryURL.path).appendingPathComponent(time + ".png")
        
        do {
            try image.pngData()?.write(to: destURL)
            return destURL
        } catch {
            print("Error saving file to App Group: \(error)")
            return nil
        }
    }
    
    static func getShareDirectory() -> URL? {
        if let groupURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: ShareGroupName) {
            let shareDirectory = groupURL.appendingPathComponent("share")
            do {
                try FileManager.default.createDirectory(at: shareDirectory, withIntermediateDirectories: true, attributes: nil)
            } catch {
                print("Error creating share directory: \(error)")
                return nil
            }
            return shareDirectory
        }
        return nil
    }
    
    static func clearShareDirectory() {
        guard let shareDirectoryURL = getShareDirectory() else {
            print("Failed to get share directory URL")
            return
        }
        
        do {
            let fileURLs = try FileManager.default.contentsOfDirectory(at: shareDirectoryURL, includingPropertiesForKeys: nil, options: [])
            for fileURL in fileURLs {
                try FileManager.default.removeItem(at: fileURL)
            }
        } catch {
            print("Error clearing share directory: \(error)")
        }
    }
}
