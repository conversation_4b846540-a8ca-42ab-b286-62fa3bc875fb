//
//  UIImageExtension.swift
//  BuzShareExtension
//
//  Created by 彭智鑫 on 2024/7/8.
//  Copyright © 2024 lizhi. All rights reserved.
//

import UIKit
import Localizable
import BuzLocalizable

extension UIImage {
    static func creatImage(color: UIColor) -> UIImage {
        let rect = CGRect(x: 0, y: 0, width: 1, height: 1)
        UIGraphicsBeginImageContext(rect.size)
        let context = UIGraphicsGetCurrentContext()
        context?.setFillColor(color.cgColor)
        context?.fill(rect)
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image ?? UIImage()
    }
    
    func mirroredWhenRTL() -> UIImage {
        if LocalizableStringManager.sharedInstance().isArabiaLanguage(), let cgImage = self.cgImage {
            return UIImage(cgImage: cgImage, scale: self.scale, orientation: .upMirrored)
        }
        
        return self
    }
}

