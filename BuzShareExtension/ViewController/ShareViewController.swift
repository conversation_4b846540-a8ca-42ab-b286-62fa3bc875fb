//
//  ShareViewController.swift
//  BuzShareExtension
//
//  Created by 彭智鑫 on 2024/7/8.
//  Copyright © 2024 lizhi. All rights reserved.
//

import UIKit
import SnapKit
import Buz<PERSON>Style
import Localizable
import BuzLocalizable
import BuzConfig

class ShareViewController: UIViewController {
    
    let presenter = SharePresenter()

    override func viewDidLoad() {
        super.viewDidLoad()
        
        Localizable.setGroupId(groupId: BuzConfig.groupStoreId)
        
        self.view.backgroundColor = .overlay_grey10
        self.setupNavBar()
        
        self.presenter.vc = self
        self.presenter.setupData()
    }
    
    private lazy var navBar: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var centerIndicatorButton: UIButton = {
        let button = UIButton()
        button.setImage(.init(resource: .indicator), for: .normal)
        return button
    }()
    
    private lazy var backButton: UIButton = {
        let button = UIButton()
        button.setImage(.init(resource: .back).mirroredWhenRTL(), for: .normal)
        button.addTarget(self, action: #selector(onBackAction), for: .touchUpInside)
        return button
    }()
    
    private lazy var actionButton: UIButton = {
        let button = UIButton()
        button.setTitle(Localizable.album_send, for: .normal)
        button.setTitleColor(.token.color_foreground_highlight_default, for: .normal)
        button.setTitleColor(.token.color_foreground_highlight_disable, for: .disabled)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.isEnabled = false
        button.addTarget(self, action: #selector(onSendAction), for: .touchUpInside)
        return button
    }()
    
    private lazy var centerTitleStack: UIStackView = {
        let stack = UIStackView()
        stack.axis = .vertical
        stack.spacing = 0
        stack.alignment = .center
        return stack
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "Buz"
        label.textColor = .text_white_main
        label.font = .systemFont(ofSize: 18, weight: .medium)
        return label
    }()
    
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = Localizable.forward_sendto
        label.textColor = .text_white_secondary
        label.font = .systemFont(ofSize: 14, weight: .regular)
        return label
    }()
    
    private lazy var tableView: UITableView = {
        let table = UITableView(frame: .zero, style: .plain)
        table.register(ShareTableViewCell.self, forCellReuseIdentifier: ShareTableViewCell.identifier)
        table.dataSource = self
        table.delegate = self
        table.backgroundColor = .clear
        table.showsVerticalScrollIndicator = false
        table.separatorStyle = .none
        table.estimatedRowHeight = UITableView.automaticDimension
        
        return table
    }()
    
    private lazy var loginButton: UIButton = {
        let button = UIButton()
        button.setTitleColor(.text_black_main, for: .normal)
        button.setTitle(Localizable.share_open_buz, for: .normal)
        button.layer.cornerRadius = 8.0
        button.layer.masksToBounds = true
        button.titleLabel?.textAlignment = .center
        button.titleLabel?.font = .boldSystemFont(ofSize: 16)
        button.setBackgroundColor(.token.color_foreground_highlight_default, for: .normal)
        button.setBackgroundColor(.token.color_foreground_highlight_pressed, for: .selected)
        button.addTarget(self, action: #selector(openBuz), for: .touchUpInside)
        return button
    }()
    
    private lazy var logo: UIImageView = {
        let view = UIImageView()
        view.image = .init(resource: .logo)
        return view
    }()
    
    private lazy var loginTip: UILabel = {
        let label = UILabel()
        label.text = Localizable.share_login_first
        label.textColor = .text_white_secondary
        label.font = .systemFont(ofSize: 16, weight: .regular)
        label.textAlignment = .center
        return label
    }()
    
    private lazy var emptyView: UIImageView = {
        let view = UIImageView()
        view.image = .init(resource: .empty)
        return view
    }()
    
    private lazy var emptyTip: UILabel = {
        let label = UILabel()
        label.text = Localizable.share_no_friends_yet
        label.textColor = .text_white_secondary
        label.textAlignment = .center
        label.font = .systemFont(ofSize: 16, weight: .regular)
        return label
    }()
    
    private func setupNavBar() {
        self.view.addSubview(navBar)
        navBar.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(106)
        }
        
        navBar.addSubview(centerIndicatorButton)
        centerIndicatorButton.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.height.equalTo(34)
        }
        
        navBar.addSubview(backButton)
        backButton.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(20)
            make.bottom.equalToSuperview().offset(-24)
            make.size.equalTo(24)
        }
        
        navBar.addSubview(actionButton)
        actionButton.snp.makeConstraints { make in
            make.centerY.equalTo(backButton)
            make.trailing.equalToSuperview().offset(-20)
        }
        
        navBar.addSubview(centerTitleStack)
        centerTitleStack.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalTo(backButton)
        }
        centerTitleStack.addArrangedSubview(titleLabel)
        centerTitleStack.addArrangedSubview(subtitleLabel)
    }
    
    public func setupNeedLoginView() {
        self.actionButton.isHidden = true
        self.subtitleLabel.isHidden = true
        
        self.view.addSubview(logo)
        logo.snp.makeConstraints { make in
            make.size.equalTo(100)
            make.centerX.equalToSuperview()
            make.top.equalTo(navBar.snp.bottom).offset(100)
        }
        
        self.view.addSubview(loginTip)
        loginTip.snp.makeConstraints { make in
            make.top.equalTo(logo.snp.bottom).offset(20)
            make.leading.equalToSuperview().offset(40)
            make.trailing.equalToSuperview().offset(-40)
        }
        
        self.view.addSubview(loginButton)
        loginButton.snp.makeConstraints { make in
            make.height.equalTo(56)
            make.leading.equalToSuperview().offset(40)
            make.trailing.equalToSuperview().offset(-40)
            make.bottom.equalTo(self.view.safeAreaLayoutGuide.snp.bottom).offset(-10)
        }
    }
    
    public func setupEmptyContactView() {
        self.actionButton.isHidden = true
        
        self.view.addSubview(emptyView)
        emptyView.snp.makeConstraints { make in
            make.size.equalTo(200)
            make.centerX.equalToSuperview()
            make.top.equalTo(navBar.snp.bottom).offset(120)
        }
        
        self.view.addSubview(emptyTip)
        emptyTip.snp.makeConstraints { make in
            make.leading.equalToSuperview().offset(40)
            make.trailing.equalToSuperview().offset(-40)
            make.top.equalTo(emptyView.snp.bottom).offset(20)
        }
    }
    
    public func setupTableView() {
        self.view.addSubview(tableView)
        tableView.snp.makeConstraints { make in
            make.top.equalTo(navBar.snp.bottom)
            make.leading.bottom.trailing.equalToSuperview()
        }
    }
    
    @objc
    private func openBuz() {
        self.presenter.openBuz()
    }
    
    @objc
    private func onBackAction() {
        self.extensionContext?.completeRequest(returningItems: [])
    }
    
    @objc
    private func onSendAction() {
        self.presenter.send()
    }
}

extension ShareViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return self.presenter.contacts.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if let cell = tableView.dequeueReusableCell(withIdentifier: ShareTableViewCell.identifier, for: indexPath) as? ShareTableViewCell {
            let contact = self.presenter.contacts[indexPath.row]
            cell.configure(contact, isSelected: contact.sourceId == self.presenter.selectedContact?.sourceId)
            
            return cell
        }
        return UITableViewCell()
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        self.actionButton.isEnabled = true
        self.presenter.selectedContact = self.presenter.contacts[indexPath.row]
        tableView.reloadData()
    }
}
