{"version": "8.3.1", "name": "ITNetLibrary", "id": "200001", "enableDemo": false, "subComponent": [{"name": "check", "id": "100071"}, {"name": "dispatchCenter", "id": "200087", "serverEnv": [{"env": "productEnv", "serverConfig": {"enable": 1, "resources": ["https://compass101.buz-app.com", "https://compass102.buz-app.com"], "defaultReqResps": ["https://httpproxy101.buz-app.com", "https://httpproxy102.buz-app.com"]}}, {"env": "preEnv", "serverConfig": {"enable": 1, "resources": ["https://httpproxypre.buz-app.com:4005"], "defaultReqResps": ["https://httpproxypre.buz-app.com:4005"]}}, {"env": "towerEnv", "serverConfig": {"enable": 1, "resources": ["http://************:7004"], "defaultReqResps": ["http://************:7004", "ws://************:7004/lthrift"]}}]}, {"name": "shortLink", "id": "200043", "serverEnv": [{"env": "productEnv", "serverConfig": {"shortLinkURLs": ["https://httpproxy101.buz-app.com", "https://httpproxy102.buz-app.com"]}}, {"env": "preEnv", "serverConfig": {"shortLinkURLs": []}}, {"env": "towerEnv", "serverConfig": {"shortLinkURLs": ["http://************:7004"]}}]}, {"name": "longLink", "id": "200044", "serverEnv": [{"env": "productEnv", "serverConfig": {"httpAppDnsURLs": [], "tcpAppDnsHosts": [], "tcpAppDnsPorts": [], "defaultProxys": []}}, {"env": "preEnv", "serverConfig": {"httpAppDnsURLs": [], "tcpAppDnsHosts": [], "tcpAppDnsPorts": [], "defaultProxys": []}}, {"env": "towerEnv", "serverConfig": {"httpAppDnsURLs": ["http://*************:8080/appdns"], "tcpAppDnsHosts": ["*************"], "tcpAppDnsPorts": [80], "defaultProxys": ""}}]}, {"name": "dns", "id": "200045", "serverEnv": [{"env": "productEnv", "serverConfig": {}}, {"env": "preEnv", "serverConfig": {}}, {"env": "towerEnv", "serverConfig": {}}]}, {"name": "httpupload", "id": "200052", "serverEnv": [{"env": "productEnv", "serverConfig": {"uploadURL": ""}}, {"env": "preEnv", "serverConfig": {"uploadURL": ""}}, {"env": "towerEnv", "serverConfig": {"uploadURL": ""}}]}], "extra": {"appid": "87075309"}, "serverEnv": [{"env": "productEnv", "serverHost": "", "serverConfig": {}}, {"env": "preEnv", "serverHost": "", "serverConfig": {}}, {"env": "towerEnv", "serverHost": "", "serverConfig": {}}]}