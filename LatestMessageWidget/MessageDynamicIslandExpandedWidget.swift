//
//  MessageDynamicIslandExpandedWidget.swift
//  buz
//
//  Created by 方煜逵 on 2023/8/28.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import WidgetKit
import SwiftUI

@available(iOS 16.2 , *)
struct MessageDynamicIslandExpandedWidget : View{
    let MessageArray : Array<LiveActivityConversationModel>
    let rightViewWidth = 76.0
    let widgetWidth = ScreenWidth - 24.0
    let widgetHeight = 160.0 - 12.0
    let itemMaxCount = 4
    let margin = 12.0

    var body: some View {
        VStack(alignment: .leading, spacing: 16.0 , content: {
            Text("RECENTS")
                .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.3))
                .font(.system(size: 11.0))
                .frame(height: 13.0)
                .padding(.leading , 27.0 - margin)
                .padding(.top , 15.0 - margin)
            HStack(alignment: .top , spacing: 0) {
                let count = min(MessageArray.count, itemMaxCount)
                let width = (widgetWidth) / Double(itemMaxCount)
                ForEach(0..<count , id : \.self){ index in
                    let model = MessageArray[index]
                    Link(destination: URL(string: "widget://contacts?userId=\(model.fromId)&isLiveActivity=1&sortId=\(index + 2)")!){
                        BuzDynamicIslandConversationWidget(width: width, conversation: model)
                    }
                }
                Spacer(minLength: 0)

            }
        }).frame(width: widgetWidth, height: widgetHeight , alignment: .topLeading)
    }
}




