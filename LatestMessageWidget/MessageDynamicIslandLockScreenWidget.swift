//
//  MessageDynamicIslandLockScreenWidget.swift
//  buz
//
//  Created by 方煜逵 on 2023/8/28.
//  Copyright © 2023 lizhi. All rights reserved.
//


import Foundation
import WidgetKit
import SwiftUI

struct LiveActivityConstant {
    
   static  let headerHeight = 40.0
    
   static  let widgetHeight = 160.0
    
   static  let widgetWidth = ScreenWidth
    
   static  let leftViewWidth = 106.0
    
   static let margin = 6.0
    
   static let  bubbleMargin = 82.0 + 15.0
    
    static let bubbleMaxWidth = widgetWidth - 88.0 - 8.0
   
    static let bubbleMaxHeight = 104.0
   
    static let bubbleMinWidth = 54.0
    
    static let bubbleMinEmojiWidth = 65.0
    
    static let bubbleMinHeight = 40.0
    
    static var bubbleContentWidth: CGFloat {
        get {
            if #available(iOS 17.0, *) {
                return LiveActivityConstant.bubbleMaxWidth - 59
            }else {
                return LiveActivityConstant.bubbleMaxWidth
            }
            
        }
    }
   
    
}

@available(iOS 16.2 , *)
struct MessageDynamicIslandLockScreenWidget : View {

    let conversationArray : Array<LiveActivityConversationModel>
    
    let messageModel: MessageLiveActivityModel

    var moreChatArray : Array<LiveActivityConversationModel>{
        let list = conversationArray.suffix(from: 1)
        return Array(list)
    }
    
    var actityConversation: LiveActivityConversationModel? {
        return conversationArray.first
    }
    

    var body: some View {
    
        ZStack(alignment: .topLeading , content: {
            
            if #available(iOS 17.0, *) {
                ButtonBlankView()
                    
            }
            
            HeadView(messageModel: messageModel)
            
            SpeakingAvatarView(speakingConversation: conversationArray.first, messageModel: messageModel)
            
            InfoView(speakingConversation: conversationArray.first, messageModel: messageModel)
            
            LastConversationListView(messageModel: messageModel, moreChatArray: moreChatArray)
            
            if messageModel.isEffectiveMessage {
                bubbleSupperView(messageModel: messageModel, speakingConversation: conversationArray.first)
            }
            
        
        })
        .frame(width : .infinity, height: LiveActivityConstant.widgetHeight)

    }
}






