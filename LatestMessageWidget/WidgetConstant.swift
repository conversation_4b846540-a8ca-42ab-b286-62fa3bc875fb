//
//  WidgetConstant.swift
//  buz
//
//  Created by 方煜逵 on 2023/8/4.
//  Copyright © 2023 lizhi. All rights reserved.
//

import UIKit
import Localizable
import BuzLocalizable

enum WidgetConstant {
    
    static let suiteName = "group.com.interfun.buz"
    
    static let quitKey = "quitKey"
    
    static let quitKind = "quitKind"
    
    static let LastMessageKind = "LastMessageKind"
    
    static let LastMessageKey = "LastMessageKey"
    
    static let messageImageDataKey = "messageImageDataKey"
    
    static let messageTranslationResultKey = "messageTranslationResultKey"
    
    static func getImageData() -> Data? {
        let suite =  UserDefaults(suiteName: WidgetConstant.suiteName)
        let data = suite?.data(forKey: WidgetConstant.messageImageDataKey)
        return data
    }
    static func setImageData(data: Data) {
        guard let suite =  UserDefaults(suiteName: WidgetConstant.suiteName) else {
            return
        }
        suite.set(data, forKey: WidgetConstant.messageImageDataKey)
        
    }
    
    static func isiOS17OrLater() -> <PERSON><PERSON> {
        let version = UIDevice.current.systemVersion
        let versionComponents = version.split(separator: ".").compactMap { Int($0) }

        if let majorVersion = versionComponents.first {
            return majorVersion >= 17
        }

        return false
    }
    
    static func enableLiveActivityLocaliable() {
        Localizable.setGroupId(groupId: "group.com.interfun.buz")
        
        
    }
    
}
