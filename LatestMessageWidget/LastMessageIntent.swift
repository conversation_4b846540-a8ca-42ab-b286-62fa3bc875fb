//
//  LastMessageIntent.swift
//  buz
//
//  Created by 方煜逵 on 2023/8/4.
//  Copyright © 2023 lizhi. All rights reserved.
//

import AppIntents

@available(iOS 17.0, *)
struct LastMessageIntent: LiveActivityIntent {
    
    static var title: LocalizedStringResource = "LastMessage"

    @Parameter(title: "IsLastMessage")
    var isLastMessage: Bool
    
    @Parameter(title: "isTapClose")
    var isTapClose: Bool
    
    @Parameter(title: "isTapPlay")
    var isTapPlay: Bool

    public init() {
        self.isTapClose = false
        self.isTapPlay = false
        self.isLastMessage = getLastMessage()
        
    }
    
    public init(isTapClose: Bool) {
        self.isTapClose = isTapClose
        self.isTapPlay = false
       
    }
    
    public init(isTapPlay: Bool) {
        self.isTapClose = false
        self.isTapPlay = isTapPlay
    
    }


    public func perform() async throws -> some IntentResult & ReturnsValue<Bool>  {
    
        #if MAIN_TARGET
        await  MessageLiveActivityManager.shared.openDetail(isTapClose: isTapClose, isTapPlay: isTapPlay)

        #endif
        
        return .result(value: true)
      
    }
}

@available(iOS 17.0, *)
extension LastMessageIntent {
    private func getLastMessage() -> Bool {
        let suite =  UserDefaults(suiteName: WidgetConstant.suiteName)
        let isLastMessage = suite?.bool(forKey: WidgetConstant.LastMessageKey) ?? false
        return isLastMessage
    }
    private func setLastMessage(isLastMessage: Bool) {
        guard let suite =  UserDefaults(suiteName: WidgetConstant.suiteName) else {
            return
        }
        suite.set(isLastMessage, forKey: WidgetConstant.LastMessageKey)
        
    }
}


