//
//  QuitIntent.swift
//  buz
//
//  Created by 方煜逵 on 2023/8/4.
//  Copyright © 2023 lizhi. All rights reserved.
//

import AppIntents
import UIKit

@available(iOS 17.0, *)
struct QuitIntent: LiveActivityIntent {
    
    static var title: LocalizedStringResource = "Quit"

    @Parameter(title: "IsQuit")
    var isQuit: Bool

    public init() {
    }


    public func perform() async throws -> some IntentResult & ReturnsValue<Bool>  {

        NotificationCenter.default.post(name: .PersonalStatusDidUpdate, object: nil)
        
        return .result(value: true)
      
    }
}

@available(iOS 17.0, *)
extension QuitIntent {
    private func getQuit() -> Bool {
        let suite =  UserDefaults(suiteName: WidgetConstant.suiteName)
        let isQuit = suite?.bool(forKey: WidgetConstant.quitKey) ?? false
        return isQuit
    }
    private func setQuit(isQuit: Bool) {
        guard let suite =  UserDefaults(suiteName: WidgetConstant.suiteName) else {
            return
        }
        suite.set(isQuit, forKey: WidgetConstant.quitKey)
        
    }
}


