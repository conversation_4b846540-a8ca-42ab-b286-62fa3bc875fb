//
//  MessageLiveActivity.swift
//  buz
//
//  Created by 方煜逵 on 2023/8/28.
//  Copyright © 2023 lizhi. All rights reserved.
//

import ActivityKit
import WidgetKit
import SwiftUI

enum MessageLiveActivityVoiceType: Int, Codable, Hashable {
    case defalut
    case text
    case emoji
    case unkown
  
}

enum MessageLiveActivityContentType: Int, Codable, Hashable {
    case voice
    case text
    case image
    case unkown
}

enum MessageLiveActivityVoiceStatus: Int, Codable, Hashable {
    case min // 最小化
    case max // 最大化
    
}

enum MessageLiveActivityPlayerStatus: Int, Codable, Hashable {
    case none
    case playing
    case played
}

struct MessageLiveActivityModel: Codable , Hashable {
    
    var voiceType: MessageLiveActivityVoiceType = .defalut
    
    let contentShow: Bool
    
    var isQuit: Bool
    
    var voiceText: String?
    
    var text: String?
    
    var contentType: MessageLiveActivityContentType = .unkown
    
    var isSpeaking: Bool
    
    var voiceStatus: MessageLiveActivityVoiceStatus
    
    var playStatus: MessageLiveActivityPlayerStatus
    
    var speechRecordEnable: Bool
    
    var rotateAngle: Double
    
    var isTranscribing: Bool
    
    let isEffectiveMessage: Bool
    
    
    
    
    public init(contentShow: Bool, isQuit: Bool, voiceText: String? , contentType:MessageLiveActivityContentType , text: String?, isSpeaking: Bool, voiceStatus: MessageLiveActivityVoiceStatus,speechRecordEnable: Bool , playStatus: MessageLiveActivityPlayerStatus,rotateAngle: Double,isTranscribing: Bool , isEffectiveMessage: Bool , voiceType: MessageLiveActivityVoiceType) {
      
        self.contentShow = contentShow
        self.isQuit = isQuit
        self.voiceText = voiceText
        self.contentType = contentType
        self.text = text
        self.isSpeaking = isSpeaking
        self.voiceStatus = voiceStatus
        self.speechRecordEnable = speechRecordEnable
        self.playStatus = playStatus
        self.rotateAngle = rotateAngle
        self.isTranscribing = isTranscribing
        self.isEffectiveMessage = isEffectiveMessage
        self.voiceType = voiceType
        
    }
}



struct MessageLiveActivityAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        // Dynamic stateful properties about your activity go here!
        ///会话数组，数据最多4个。前三个显示在界面上。
        var conversationArray : [LiveActivityConversationModel] = []
        
        var messageModel: MessageLiveActivityModel
    }

}
@available(iOS 16.2 , *)
struct MessageLiveActivity: Widget {
    
    init() {
        WidgetConstant.enableLiveActivityLocaliable()
    }
    
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: MessageLiveActivityAttributes.self) { context in
   
            
            if context.state.conversationArray.count > 0 {
                MessageDynamicIslandLockScreenWidget(conversationArray: context.state.conversationArray, messageModel: context.state.messageModel )
                    .activityBackgroundTint(Color.init(hex: 0x000000, alpha: 0.95))
                    
            }else{
                BuzDynamicIslandLockScreenDefaultWidget()
                    .activityBackgroundTint(Color.init(hex: 0x000000, alpha: 0.7))
            }
        } dynamicIsland: { context in
            DynamicIsland {
                
                DynamicIslandExpandedRegion(.leading) {
                    if context.state.conversationArray.count > 0 {
                        MessageDynamicIslandExpandedWidget(MessageArray: context.state.conversationArray)
                    }else{
                        BuzDynamicIslandExpendDefaultWidget()
                    }

                }
                DynamicIslandExpandedRegion(.trailing) {

                }
                DynamicIslandExpandedRegion(.bottom) {

                }
            } compactLeading: {

            } compactTrailing: {

            } minimal: {
                Image("buz_widget_minimal_logo")
                    .resizable()
                    .frame(width: 20.0 , height: 20.0)
            }.contentMargins(.all , 12.0 , for: .expanded)
            .widgetURL(URL(string: "http://www.apple.com"))
        }
    }
        
}

