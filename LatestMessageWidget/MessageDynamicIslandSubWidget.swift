//
//  MessageDynamicIslandSubWidget.swift
//  buz
//
//  Created by 方煜逵 on 2023/8/28.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import WidgetKit
import SwiftUI
import BuzLocalizable
import Localizable


@available(iOS 16.2 , *)
struct MessageDynamicIslandSpeakingWidget : View {
    
    let width : CGFloat
    
    let height : CGFloat
    
    let iconWidth: CGFloat
    
    let speakingConversation : LiveActivityConversationModel
    
    let messageModel: MessageLiveActivityModel
    
    var isSpeaking : Bool {
        get {
            speakingConversation.isSpeaking || messageModel.isSpeaking
        }
    }
    
    var body: some View {
        ZStack(alignment: .center, content: {
            if speakingConversation.isGroup {
                MessageDynamicIslandGroupPortraitWidget.init(portrait: speakingConversation.groupInfo?.portrait, conversation: speakingConversation, isSmallPattern: false)
                    .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.6))
                    .frame(width: iconWidth , height: iconWidth)
                    .background(Color(hex: 0xffffff, alpha: 0.1))
                    .cornerRadius(iconWidth * 0.5)

                    
            }else{
                BuzDynamicIslandPortraitWidget(portrait: speakingConversation.speakerUserInfo?.portrait)
                    .frame(width: iconWidth , height : iconWidth)
                    .cornerRadius(iconWidth * 0.5)
                    .animation(.easeInOut(duration: 0.5))
            }
            
            if #available(iOS 17.0, *) {
                if messageModel.playStatus == .played   {
                    ZStack(alignment: .center) {
                        Color.clear
                            .frame(maxWidth:.infinity,maxHeight: .infinity)
                    
                        Button(intent: LastMessageIntent(isTapPlay: true)) {
                            Text("")
                                .font(.iconfont(ofSize: 24))
                                .foregroundColor(.white)
                                .frame(maxWidth:.infinity,maxHeight: .infinity)
                        }
                        .frame(maxWidth:.infinity,maxHeight: .infinity)
                        .buttonStyle(PlainButtonStyle())
                        .background(Color.black.opacity(0.4))
                       
                        
                        
                    }
                    .frame(maxWidth:.infinity,maxHeight: .infinity)
                   
                }
            }
            
                 
        
            if speakingConversation.isOnLine {
                ZStack(alignment: .bottomTrailing) {
                    Color.clear
                        .frame(width: iconWidth , height: iconWidth)
                    ZStack(alignment: .center) {
                       Circle()
                           .fill(Color.black)
                           .frame(width:messageModel.voiceStatus == .min ? 24 :  21, height: messageModel.voiceStatus == .min ? 24:  21)
                       
                       Circle()
                            .fill(speakingConversation.isQuit == true ? Color.init(hex: 0x7660FF) : Color.init(hex: 0xB9FB65))
                            .frame(width: messageModel.voiceStatus == .min ? 12 : 9, height: messageModel.voiceStatus == .min ? 12 : 9)
                           
                    }
                    .padding(.trailing, messageModel.voiceStatus == .min ? -2.0 : -4.0)
                    .padding(.bottom,messageModel.voiceStatus == .min ? -2.0 : -4.0)
                }
                .frame(width: iconWidth , height: iconWidth)
                
                
            }
            
            
        })
        .frame(width: iconWidth , height: iconWidth)
//        .background(Color.red)
    }
}




@available(iOS 16.2 , *)
struct MessageDynamicIslandConversationWidget : View{
    let width : CGFloat
    let conversation : LiveActivityConversationModel?

    var body: some View {
        VStack(spacing: 0) {
            ZStack{
                if conversation == nil || conversation?.isGroup ?? false == true {
                    
                    MessageDynamicIslandGroupPortraitWidget.init(portrait: conversation?.groupInfo?.portrait, conversation: conversation, isSmallPattern: true)
                        .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.6))
                        .background(Color(hex: 0xffffff, alpha: 0.1))
                        .frame(width: 40.0 , height: 40.0)
                        .cornerRadius(40 * 0.5)
                        

                }else{
                    BuzDynamicIslandPortraitWidget(portrait: conversation?.speakerUserInfo?.portrait)
                        .frame(width: 40.0 , height: 40.0)
                        .cornerRadius(40 * 0.5)
                }
                
                
                if conversation?.isOnLine == true {
                    ZStack(alignment: .bottomTrailing) {
                        Color.clear
                            .frame(width: 40.0 , height: 40.0)
                        ZStack(alignment: .center) {
                           Circle()
                               .fill(Color.black)
                               .frame(width: 16, height: 16)
                           
                           Circle()
                                .fill(conversation?.isQuit == true ? Color.init(hex: 0x7660FF) : Color.init(hex: 0xB9FB65))
                               .frame(width: 8, height: 8)
                        }
                        .padding(.bottom , -4.0)
                        .padding(.trailing , -4.0)
                    }
                    .frame(width: 40.0, height: 40.0)
//                    .background(Color.red)
                    
                }
            }
            .frame(width: 40 , height: 40)
//            .cornerRadius(20.0)
//            .background(Color.blue)
            
            
        }
      
    }
}



struct CustomToggleStyle: ToggleStyle {
    func makeBody(configuration: ToggleStyleConfiguration) -> some View {
        HStack {
            Image(configuration.isOn ? "sound_switch" : "quit_switch")
                .resizable()
                .frame(width: 48, height: 30)
                .padding(0)
            configuration.label
        }
        .padding([.trailing,.top] , 8.0)
    }
}



extension SwiftUI.Font {
    static func iconfont(ofSize size: CGFloat) -> SwiftUI.Font {
        return SwiftUI.Font.custom("buz", size: size)
    }
}


@available(iOS 16.2 , *)
struct VoiceBubble: View {
    
    let speakingConversation : LiveActivityConversationModel
    
    let messageModel: MessageLiveActivityModel
    
    var body: some View {
        ZStack(alignment: .bottomLeading) {
            Color.clear
                .frame(width: messageModel.voiceStatus == .max ? .infinity : (messageModel.voiceType == .emoji ? LiveActivityConstant.bubbleMinEmojiWidth :  LiveActivityConstant.bubbleMinWidth ), height:  messageModel.voiceStatus == .max ? LiveActivityConstant.bubbleMaxHeight :  LiveActivityConstant.bubbleMinHeight)
                
            ZStack(alignment: .center, content: {
                if messageModel.voiceStatus == .max {
                    ZStack(alignment: .topLeading, content: {
                        if messageModel.contentType == .text {
                            MessageVoiceTextView(messageModel: messageModel, speakingConversation: speakingConversation, text: messageModel.text ?? "", isVoice: false)
                        }else if messageModel.contentType == .voice {
                            if (messageModel.voiceText?.count ?? 0) > 0 || messageModel.speechRecordEnable {
                                
                                if messageModel.voiceType == .emoji {
                                    MessageVoicemojiView(messageModel: messageModel, speakingConversation: speakingConversation, text: messageModel.voiceText ?? "")
                                }else {
                                    MessageVoiceTextView(messageModel: messageModel, speakingConversation: speakingConversation, text: messageModel.voiceText ?? "", isVoice: true)
                                }
                                
                            }else {
                                MessageSettingView()
                            }
                            
                        }else if messageModel.contentType == .image {
                            MessageImageView(speakingConversation: speakingConversation, messageModel: messageModel)
                        }
                        
                        
                        
                        
                        if #available(iOS 17.0, *) {
                            MessageOperationView(speakingConversation: speakingConversation)
                        }
                        
                    })
                    .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
                    
                }else {
                    if #available(iOS 17.0, *) {
                        
                        ZStack(alignment: .topLeading, content: {
                            Color.clear
                                .frame(width: (messageModel.voiceType == .emoji ? LiveActivityConstant.bubbleMinEmojiWidth :  LiveActivityConstant.bubbleMinWidth ), height: LiveActivityConstant.bubbleMinHeight)
                            ChatEntryView(messageModel: messageModel)
                        })
                        .frame(width: (messageModel.voiceType == .emoji ? LiveActivityConstant.bubbleMinEmojiWidth :  LiveActivityConstant.bubbleMinWidth ), height:  LiveActivityConstant.bubbleMinHeight)
                        
                    }

                }
                
            })
            .frame(width: messageModel.voiceStatus == .max ? .infinity :  (messageModel.voiceType == .emoji ? LiveActivityConstant.bubbleMinEmojiWidth :  LiveActivityConstant.bubbleMinWidth ), height:  messageModel.voiceStatus == .max ? LiveActivityConstant.bubbleMaxHeight :  LiveActivityConstant.bubbleMinHeight)
            .foregroundColor(.clear)
            .background(RoundedCorners(color: Color.init(hex: 0x333333, alpha: 1.0), tl: 20, tr: 20, bl: 0, br: 20))
            
            
            if messageModel.voiceStatus == .min && speakingConversation.unReadCount > 0 {
                ZStack(alignment: .topTrailing) {
                    Color.clear
                        .frame(width:(messageModel.voiceType == .emoji ? LiveActivityConstant.bubbleMinEmojiWidth :  LiveActivityConstant.bubbleMinWidth ) , height:LiveActivityConstant.bubbleMinHeight)
                        
                    Circle()
                        .foregroundColor(Color.init(hex: 0xEB4D3D))
                        .frame(width: 6.0, height: 6.0)
                        .padding(.top , 0)
                        .padding(.trailing , -6.0)
                }
                .frame(width:(messageModel.voiceType == .emoji ? LiveActivityConstant.bubbleMinEmojiWidth :  LiveActivityConstant.bubbleMinWidth ) , height:LiveActivityConstant.bubbleMinHeight)
            }
            
                        
        }

    }
}


@available(iOS 16.2 , *)
struct InfoView: View {
    let speakingConversation: LiveActivityConversationModel?
    
    let messageModel: MessageLiveActivityModel

    var body: some View {
        ZStack(alignment: .topLeading, content: {
            Color.clear
                .frame(width: .infinity , height: LiveActivityConstant.widgetHeight)
        
           if  let speakingConversation = speakingConversation  {
               if messageModel.isEffectiveMessage == false {
                   VStack(alignment: .leading, spacing: 4) {
                       VInfoView(speakingConversation: speakingConversation, messageModel: messageModel)

                   }
                   .padding(.leading,94.0)
                   .padding(.top,88.0)
                   .padding(.trailing,184.0)
                   .frame(width: .infinity)
                   
               }else if messageModel.voiceStatus == .max  {
                   HStack(alignment: .center, spacing: 4) {
                       HInfoView(speakingConversation: speakingConversation, messageModel: messageModel)
                       
                   }
                   .frame(height: 20.0)
                   .padding(.leading,88.0)
                   .padding(.top,23.0)
                   .padding(.trailing,58)
                   
                   
               }else if messageModel.voiceStatus == .min {
                   VStack(alignment: .leading, spacing: 4) {
                       VInfoView(speakingConversation: speakingConversation, messageModel: messageModel)

                   }
                   .padding(.leading,112.0)
                   .padding(.top,53.0)
                   .frame(width: .infinity)
               }
           } else {
               EmptyView()
           }
            
           
        })
        .padding(0)
        .frame(width: .infinity , height: LiveActivityConstant.widgetHeight)
    }
   

}


struct DotView: View {
    var body: some View {
        Circle()
            .foregroundColor(Color.init(hex:0xFFFFFF, alpha: 0.6))
            .frame(width: 3, height: 3)
            .overlay(
                Text("")
                    .frame(width: 3, height: 3)
            )
            .padding([.leading, .trailing], 1.0)
        
    }
}



@available(iOS 16.2 , *)
struct HInfoView: View {

    let speakingConversation: LiveActivityConversationModel
    
    let messageModel: MessageLiveActivityModel
    
    var body: some View {
        
        if speakingConversation.isGroup {
         
            BuzDynamicIslandPortraitWidget(portrait: speakingConversation.speakerUserInfo?.portrait)
                .frame(width: 20.0 , height : 20.0)
                .cornerRadius(20 * 0.5)
                .scaleEffect(speakingConversation.isSpeaking ? (20 * 0.91 / 20) : 1.0)
                .animation(.easeInOut(duration: 0.5))
            

            Text(speakingConversation.displayName)
                .font(.system(size: 12.0))
                .foregroundColor(Color.init(hex: 0xFFFFFF, alpha: 1.0))
                .padding(.leading , 1)
                .lineLimit(1)
                .bold()
            
                
            
            
            DotView()
            
            Text(speakingConversation.groupInfo?.name ?? "group")
                .font(.system(size: 12.0))
                .foregroundColor(Color.init(hex: 0xffffff, alpha: 1.0))
                .padding(0)
                .multilineTextAlignment(.center)
                .lineLimit(1)
                .bold()
            
            DotView()
            
            SpeakingAndTimeView(speakingConversation: speakingConversation)

            
            
            
        }else {
            Text(speakingConversation.displayName)
                .font(.system(size: 12.0))
                .bold()
                .foregroundColor(Color.init(hex: 0xffffff, alpha: 1.0))
                .padding(0)
                .lineLimit(1)
        
            if messageModel.voiceStatus != .min {
                DotView()
            }
            
            SpeakingAndTimeView(speakingConversation: speakingConversation)
        }
        
    }
    
}


@available(iOS 16.2 , *)
struct VInfoView: View {

    let speakingConversation: LiveActivityConversationModel
    
    let messageModel: MessageLiveActivityModel
    
    var body: some View {
        Text(speakingConversation.isGroup ? (speakingConversation.groupInfo?.name ?? "group") :  speakingConversation.displayName)
            .font(.system(size: 18.0))
            .bold()
            .foregroundColor(Color.init(hex: 0xffffff, alpha: 1.0))
            .padding(0)
            .lineLimit(1)
            .padding(.trailing, 12)
    
        SpeakingAndTimeView(speakingConversation: speakingConversation)
            .offset(y: messageModel.isEffectiveMessage == false ? 0 :  -4)
    }
    
}




@available(iOS 16.2 , *)
struct SpeakingAndTimeView: View {

    let speakingConversation: LiveActivityConversationModel
    
    var body: some View {
        
        if speakingConversation.msgTimestamp > 0 {
            Text("\(speakingConversation.timeString)")
                .font(.system(size: 12.0))
                .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.6))
                .padding(0)
                .lineLimit(1)
                

        }
        
        
//        if speakingConversation.isSpeaking {
//            Text("Speaking")
//                .font(.system(size: 11.0))
//                .bold()
//                .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.8))
//                .padding(0)
//                .multilineTextAlignment(.center)
//                .lineLimit(1)
//                
//        }else{
//            if speakingConversation.msgTimestamp > 0 {
//                Text("\(speakingConversation.timeString)")
//                    .font(.system(size: 12.0))
//                    .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.6))
//                    .padding(0)
//                    .multilineTextAlignment(.center)
//                    .lineLimit(1)
//                    
//            }
//
//        }
    }
    
}


@available(iOS 16.2 , *)
struct HeadView: View {
    
    let messageModel: MessageLiveActivityModel
    
    var body: some View {
        
        
        // 第一行 buz logo + 静音开关
        HStack(alignment: .bottom) {
           Image("buz_widget_logo_3")
               .resizable()
               .frame(width: 24.0 , height: 24.0)
               .padding(.leading , 14.0)
               .padding(.top,11.0)
            
            Spacer()

            if #available(iOS 17.0, *) {
                Button(intent: QuitIntent()) {
                    HStack {
                        Image(messageModel.isQuit == false ? "sound_switch" : "quit_switch")
                            .resizable()
                            .frame(width: 48, height: 30)
                            .padding(0)
                        
                    }
                    .padding([.trailing,.top] , 8.0)
                }
                .buttonStyle(PlainButtonStyle())
                
                
            }else {
                HStack(alignment: .center) {
                    Circle()
                        .foregroundColor(Color.init(hex: 0xFFFFFF,alpha: 0.1))
                        .frame(width: 30, height: 30)
                        .overlay(
                            Text(messageModel.isQuit ? "" : "")
                                .font(.iconfont(ofSize: 16))
                                .foregroundColor(messageModel.isQuit ?  Color.init(hex: 0x7660FF, alpha: 1.0):  Color.init(hex: 0xB9FB65, alpha: 1.0))
                        )
                    
                }
                .padding(.trailing,8)
                .padding(.top,8)
                
            }
            
            
        }
        .padding(0)
        .frame(width: .infinity , height: LiveActivityConstant.headerHeight)

    }
}



@available(iOS 16.2 , *)
struct SpeakingAvatarView: View {
    
    let speakingConversation: LiveActivityConversationModel?
    
    let messageModel: MessageLiveActivityModel
    
    var body: some View {
    
        ZStack(alignment: .bottomLeading, content: {
            if let currentSpeakingConversation = speakingConversation {
                
                Color.clear
                    .frame(width: .infinity , height: LiveActivityConstant.widgetHeight)
                
                // 头像
                if #available(iOS 17.0, *) , messageModel.playStatus != .none {
                    MessageDynamicIslandSpeakingWidget(width: LiveActivityConstant.leftViewWidth, height: 160, iconWidth: (messageModel.voiceStatus == .max || messageModel.isEffectiveMessage == false ) ? 62.0 : 80.0 , speakingConversation: currentSpeakingConversation, messageModel: messageModel)
                        .padding(.leading, (messageModel.voiceStatus == .min || messageModel.isEffectiveMessage == false) ?   20.0 : 12.0)
                        .padding(.bottom,(messageModel.voiceStatus == .min || messageModel.isEffectiveMessage == false) ?   24.0 : 12.0)
                    
                }else {
                    Link(destination: URL(string: "widget://contacts?userId=\(currentSpeakingConversation.fromId)&isLiveActivity=1&sortId=1")!) {
                        
                        MessageDynamicIslandSpeakingWidget(width: LiveActivityConstant.leftViewWidth, height: 160, iconWidth: (messageModel.voiceStatus == .max || messageModel.isEffectiveMessage == false ) ? 62.0 : 80.0 , speakingConversation: currentSpeakingConversation, messageModel: messageModel)
                            .padding(.leading, (messageModel.voiceStatus == .min || messageModel.isEffectiveMessage == false) ?   20.0 : 12.0)
                            .padding(.bottom,(messageModel.voiceStatus == .min || messageModel.isEffectiveMessage == false) ?   24.0 : 12.0)
                    }
                }
                
            }else {
                BuzDynamicIslandLogoWidget(height: LiveActivityConstant.widgetHeight , width: LiveActivityConstant.leftViewWidth , iconHeight: 25 , iconWidth: 40.0)
            }
        })
        .padding(0)
        .frame(width: .infinity , height: LiveActivityConstant.widgetHeight)
       
        
    }
}


@available(iOS 16.2 , *)
struct LastConversationListView: View {
    
    let messageModel: MessageLiveActivityModel
    
    var moreChatArray : Array<LiveActivityConversationModel>
    
    var body: some View {
    
        ZStack(alignment: .bottomTrailing, content: {
            Color.clear
                .frame(width: .infinity , height: LiveActivityConstant.widgetHeight)
            
            
            HStack(alignment: .bottom , spacing: 0) {
                  let count = 3
                  let margins = 12.0
                  let itmeWidth = 40.0
                
                  Spacer()
                  ForEach(0..<count  , id : \.self){ index in
                      if index < moreChatArray.count {
                          let model = moreChatArray[index]
                          Link(destination: URL(string: "widget://contacts?userId=\(model.fromId)&isLiveActivity=1&sortId=\(index + 2)")!){
                              MessageDynamicIslandConversationWidget(width: itmeWidth, conversation: model)
                                  .padding(.trailing, margins)
                              


                          }
                      }else{
                          MessageDynamicIslandConversationWidget(width: itmeWidth, conversation: nil)
                      }
                  }
            }
            .padding(.trailing,8)
            .padding(.bottom,24)
            
            
        })
        .padding(0)
        .frame(width: .infinity , height: LiveActivityConstant.widgetHeight)
        
               
    }
}


@available(iOS 16.2 , *)
struct bubbleSupperView: View {
    
    let messageModel: MessageLiveActivityModel
    
    let speakingConversation : LiveActivityConversationModel?
    
    var body: some View {
    
        ZStack(alignment: .bottomLeading, content: {
            Color.clear
                .frame(width: .infinity , height: LiveActivityConstant.widgetHeight)
        
            if let speakingConversation = speakingConversation {
                VoiceBubble(speakingConversation: speakingConversation, messageModel: messageModel)
                    .padding(.leading, messageModel.voiceStatus != .min ? 88: 110 )
                    .padding(.trailing,messageModel.voiceStatus == .min ? 0.0: 8.0)
                    .padding(.bottom,messageModel.voiceStatus != .min ? 8 : 24)
            }
            
            
        })
        .padding(0)
        .frame(width: .infinity , height: LiveActivityConstant.widgetHeight)
               
    }
}


///群头像
struct MessageDynamicIslandGroupPortraitWidget : View{
    let portrait : String?
    let conversation : LiveActivityConversationModel?
    let isSmallPattern: Bool
    var body: some View {
        if let imageUrl = portrait , imageUrl.count > 0, let image = WidgetImageLoader.syncLoadImage(urlStr: imageUrl){
            image
        }else if let conversation = conversation {
            let width = isSmallPattern ? 40 : 68.0
            let fontSize = isSmallPattern ? 12 : 17.5
            let maxHeight = isSmallPattern ? 40 : 63.0
            let padding = isSmallPattern ? 2.0 : 6.0
            Text(conversation.getDisplayGroupName(width: width, fontSize: fontSize , maxHeight : maxHeight))
                .font(.system(size: fontSize))
                .bold()
                .lineLimit(2)
                .lineSpacing(0)
                .multilineTextAlignment(.center)
                .padding(padding)
                .frame(maxWidth: .infinity , maxHeight: .infinity)
        }
        
        
    }
}



@available(iOS 16.2 , *)
struct MessageVoicemojiView: View {
    
    let messageModel: MessageLiveActivityModel
    
    let speakingConversation: LiveActivityConversationModel?
    
    let text: String
    
    var body: some View {
    
        ZStack(alignment: .topLeading, content: {
            Color.clear
                .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
            
            if #available(iOS 17.0, *) {

                Button(intent: LastMessageIntent(isTapPlay: true)) {
                    HStack(alignment: .top) {
                        Text(text)
                            .frame(width: 56, height: 56)
                            .font(.iconfont(ofSize: 56))
                            .foregroundColor(.white)
                            .padding([.leading],12)
                            .padding(.top , 13)
                            .lineSpacing(5.0)
                            .multilineTextAlignment(.leading)
                        
                        
                        Image("emoji_play_icon")
                            .resizable()
                            .frame(width: 25, height: 25)
                            .padding(.top, 13.0)
                            .padding(.leading, -5)
                            .foregroundColor(.init(hex: 0xFFFFFF , alpha: 0.6))
                    }
                }
                .buttonStyle(PlainButtonStyle())
                .foregroundColor(.clear)
                .background(.clear)
                
            }else {
                HStack(alignment: .top) {
                    Text(text)
                        .frame(width: 56, height: 56)
                        .font(.iconfont(ofSize: 56))
                        .foregroundColor(.white)
                        .padding([.leading],12)
                        .padding(.top , 13)
                        .lineSpacing(5.0)
                        .multilineTextAlignment(.leading)
                    
                    
                    Image("emoji_play_icon")
                        .resizable()
                        .frame(width: 25, height: 25)
                        .padding(.top, 13.0)
                        .padding(.leading, -5)
                        .foregroundColor(.init(hex: 0xFFFFFF , alpha: 0.6))
                }
            }
            
            
            
        
            
                        
        })
        
        .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
        
    }
}



@available(iOS 16.2 , *)
struct MessageVoiceTextView: View {
    
    let messageModel: MessageLiveActivityModel
    
    let speakingConversation: LiveActivityConversationModel?
    
    let text: String
    
    let isVoice: Bool
    
    var body: some View {
    
        ZStack(alignment: .topLeading, content: {
            Color.clear
                .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
            
            if #available(iOS 17.0, *) {
                ButtonDetailView()
            }else {
                LinkBlankView(speakingConversation: speakingConversation)
            }
            
            
            if isVoice {
                Image("transcribe_text_icon")
                    .resizable()
                    .frame(width: 20.0, height: 20.0)
                    .padding(.top, 13.0)
                    .padding(.leading, 14.0)
                    .foregroundColor(.init(hex: 0xFFFFFF , alpha: 0.6))
                
            }
            
            if messageModel.isTranscribing {
                Text("   " + Localizable.live_activity_Transcribing)
                    .font(.iconfont(ofSize: 15))
                    .foregroundColor(.init(hex: 0xFFFFFF , alpha: 0.6))
                    .padding(.top, 13)
                    .padding(.leading ,12)
                    .lineSpacing(8.0)
            }

            else {
                Text((isVoice ? "    " : "") + text )
                    .font(.iconfont(ofSize: 15))
                    .foregroundColor(.white)
                    .padding([.leading , .bottom],12)
                    .padding(.top , 13)
                    .padding(.trailing, WidgetConstant.isiOS17OrLater() ? 59 : 12)
                    .lineSpacing(5.0)
            }
            
            
    
            
        })
        
        .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
        
    }
}

@available(iOS 16.2 , *)
struct MessageImageView: View {
    
    let speakingConversation: LiveActivityConversationModel?
    
    let messageModel: MessageLiveActivityModel
    
    var body: some View {
        
        ZStack(alignment: .center, content: {
            Color.clear
                .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)

            
            if #available(iOS 17.0, *) {
                ButtonDetailView()
            }else {
                LinkBlankView(speakingConversation: speakingConversation)
                
            }
            
            if let image = WidgetImageLoader.syncChatLoadImage(){
                ZStack {
                    image
                        .resizable()
                        .frame(height: .infinity)
                        .blur(radius: 10)
          
                    image
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(height: .infinity)
                    
                }
                .cornerRadius(17.0)
                .padding([.leading , .top , .bottom],4)
                .padding(.trailing, WidgetConstant.isiOS17OrLater() ? 59 : 4 )
                
                
            }else{
                Image("buz_widget_logo")
            }
          
        })
        .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
       
      
    }
}

@available(iOS 16.2 , *)
struct MessageSettingView: View {
   
    let text: String = Localizable.live_activity_Allow_transcriptions

    var body: some View {
    
        ZStack(alignment: .topLeading, content: {
            Color.clear
                .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
            
            if #available(iOS 17.0, *) {
                ButtonDetailView()
            }
            
            Link(destination: URL(string: "widget://message?gotoSettings=1")!){
                VStack(alignment: .leading, content: {
                    Text("    " + text)
                        .font(.iconfont(ofSize: 15))
                        .foregroundColor(.white)
                        .opacity(0.6)
                        .padding([.leading , .top],12)
                        .padding(.trailing, WidgetConstant.isiOS17OrLater() ? 59 : 12)
                        .lineSpacing(6.0)
                    
                
                    
                    HStack(alignment: .center, content: {
                        Text(Localizable.seting_live_activity_go)
                            .font(.iconfont(ofSize: 15))
                            .foregroundColor(.white).opacity(1)
                            .padding(.top, 4.0)
                            .padding(.leading,12.0)
                            .padding(.trailing , 0.0 )
                        
                        
                        Text("")
                            .font(.iconfont(ofSize: 15))
                            .foregroundColor(.white).opacity(1)
                            .padding(.top, 8.0)
                            .padding(.leading,-4)
                            
                    })
                    
                    
                    
                    
                })

                                
            }
            
            
        
            Image("transcribe_text_icon")
                .resizable()
                .frame(width: 20.0, height: 20.0)
                .padding(.top, 12.0)
                .padding(.leading, 14.0)
                .foregroundColor(.init(hex: 0xFFFFFF , alpha: 0.6))
        })
        
        .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
        
    }
}






@available(iOS 17.0 , *)
struct MessageOperationView: View {
    
    let speakingConversation: LiveActivityConversationModel?
    
    var body: some View {
        ZStack(alignment: .topTrailing, content: {
            Color.clear
                .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
            
            Button(intent: LastMessageIntent(isTapClose: true)) {
                
                Circle()
                    .foregroundColor(Color.init(hex: 0xFFFFFF, alpha: 0.06))
                    .frame(width: 40, height: 40)
                    .overlay(
                        Image("close_icon")
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 16)
                        
                        

                    )
                    .padding([.top, .trailing] , 9.0)
                       
                
                    
            }
            .buttonStyle(PlainButtonStyle())
            .foregroundColor(.clear)
            .background(.clear)

            
        })
        
        .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
        
        
        
    
        ZStack(alignment: .bottomTrailing, content: {
            Color.clear
                .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
            
            Button(intent: ChatIntent()) {
                
                ZStack(alignment: .topTrailing) {
                    Color.clear
                        .frame(width: 40, height: 40)
                    
                    Circle()
                        .foregroundColor(Color.init(hex: 0xFFFFFF, alpha: 0.06))
                        .frame(width: 40, height: 40)
                        .overlay(
                            Image("chat_icon")
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 16)
                        )
                    
                    if let unReadCount = speakingConversation?.unReadCount, unReadCount > 0 {
                        Circle()
                            .foregroundColor(Color.init(hex: 0xEB4D3D, alpha: 1.0))
                            .frame(width: 18, height: 18)
                            
                            .overlay(
                                VStack(alignment: .center) {
                                    Text("\(unReadCount)")
                                        .font(.iconfont(ofSize: 12))
                                        .foregroundColor(.white)
                                        .baselineOffset(3)
                                }
                                
                            )
                            .padding(.top, -3)
                            .padding(.trailing , -3)
                    }
                    
                    
                    
                    
                }
                .frame(width: 40, height: 40)
                .padding([.bottom, .trailing] , 9.0)
                
            }
            .buttonStyle(PlainButtonStyle())
            .foregroundColor(.clear)
            .background(.clear)
            
            
            
        })
        .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
        
    }
}



@available(iOS 17.0 , *)
struct ButtonBlankView: View {
    var body: some View {
        Button(intent: BlankIntent()) {
            Color.clear
                .frame(width: .infinity, height: .infinity)
        }
        .buttonStyle(PlainButtonStyle())
        .foregroundColor(.clear)
        .background(.clear)
    }
}


@available(iOS 17.0 , *)
struct ButtonDetailView: View {
    var body: some View {
        Button(intent: BlankIntent()) {
            Color.clear
                .frame(width: .infinity, height: .infinity)
        }
        .buttonStyle(PlainButtonStyle())
        .foregroundColor(.clear)
        .background(.clear)
    }
}


@available(iOS 16.2 , *)
struct LinkBlankView: View {
    
    let speakingConversation: LiveActivityConversationModel?
    
    var body: some View {
        if let speakingConversation = speakingConversation {
        // 必须占位才能点击
            Link(destination: URL(string: "widget://message?gotoChat=1")!){
                Text("                                                                  ")
                    .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
                
            }
            .background(Color.clear)
            .frame(width: .infinity, height: LiveActivityConstant.bubbleMaxHeight)
        }
        
        
        
    }
}



@available(iOS 17.0 , *)
struct ChatEntryView: View {
    
    let messageModel: MessageLiveActivityModel
    
    var body: some View {
        Button(intent: LastMessageIntent()) {
            VStack(alignment: .center) {
                if messageModel.contentType == .text {
                    Image("text_icon")
                        .resizable()
                        .frame(width: 20, height: 20)
                    
                }else if messageModel.contentType == .voice {
                    
                    
                    if messageModel.voiceType == .emoji {
                        HStack {
                            
                            Spacer()
                            Text(messageModel.voiceText ?? "")
                                .font(.iconfont(ofSize: 24))
                                .foregroundColor(.white)
                                .padding(.all ,0)
                                .padding(.leading, 2)
                            Spacer(minLength: 0)
                            Image("emoji_play_icon")
                                .resizable()
                                .frame(width: 18, height: 18)
                                .foregroundColor(.init(hex: 0xFFFFFF , alpha: 0.6))
                                .padding(.all ,0)
                                .padding(.leading, -6)
                            
                            Spacer()
                        }
                    }else {
                        if messageModel.isQuit {
                            Image("transcribe_text_w_icon")
                                .resizable()
                                .frame(width: 20, height: 20)
                                .foregroundColor(.white)
                        }else {
                            Text("")
                                .font(.iconfont(ofSize: 20))
                                .foregroundColor(.white)
                        }
                    }
                    
                    
                    
                    
                    
                    
                }else if messageModel.contentType == .image {
                    if let image = WidgetImageLoader.syncChatLoadImage(){
                        image
                            .frame(width: 24.0, height: 24.0)
                            .cornerRadius(6.0)
                        
                        
                    }else{
                        Image("buz_widget_logo")
                    }
                }else {
                    Image("buz_widget_logo")
                }
            }
            .frame(width: (messageModel.voiceType == .emoji ? LiveActivityConstant.bubbleMinEmojiWidth :  LiveActivityConstant.bubbleMinWidth ), height:  LiveActivityConstant.bubbleMinHeight)
        }
        .frame(width: (messageModel.voiceType == .emoji ? LiveActivityConstant.bubbleMinEmojiWidth :  LiveActivityConstant.bubbleMinWidth ), height:  LiveActivityConstant.bubbleMinHeight)
        
        .buttonStyle(PlainButtonStyle())
        
    }
    
}


struct RoundedCorners: View {
    var color: Color = .blue
    var tl: CGFloat = 0.0
    var tr: CGFloat = 0.0
    var bl: CGFloat = 0.0
    var br: CGFloat = 0.0
 
    var body: some View {
        GeometryReader { geometry in
            Path { path in
 
                let w = geometry.size.width
                let h = geometry.size.height
 
                // Make sure we do not exceed the size of the rectangle
                let tr = min(min(self.tr, h/2), w/2)
                let tl = min(min(self.tl, h/2), w/2)
                let bl = min(min(self.bl, h/2), w/2)
                let br = min(min(self.br, h/2), w/2)
 
                path.move(to: CGPoint(x: w / 2.0, y: 0))
                path.addLine(to: CGPoint(x: w - tr, y: 0))
                path.addArc(center: CGPoint(x: w - tr, y: tr), radius: tr, startAngle: Angle(degrees: -90), endAngle: Angle(degrees: 0), clockwise: false)
                path.addLine(to: CGPoint(x: w, y: h - br))
                path.addArc(center: CGPoint(x: w - br, y: h - br), radius: br, startAngle: Angle(degrees: 0), endAngle: Angle(degrees: 90), clockwise: false)
                path.addLine(to: CGPoint(x: bl, y: h))
                path.addArc(center: CGPoint(x: bl, y: h - bl), radius: bl, startAngle: Angle(degrees: 90), endAngle: Angle(degrees: 180), clockwise: false)
                path.addLine(to: CGPoint(x: 0, y: tl))
                path.addArc(center: CGPoint(x: tl, y: tl), radius: tl, startAngle: Angle(degrees: 180), endAngle: Angle(degrees: 270), clockwise: false)
            }
            .fill(self.color)
        }
    }
}


