//
//  VoiceToText.swift
//  buz
//
//  Created by 方煜逵 on 2023/8/16.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import Speech
import AVFoundation
import BuzLog

class VoiceToText :  NSObject {
    
    var textBlock:((_ text: String?) -> Void)?
    
    var recordTask: SFSpeechRecognitionTask?///语音识别对象的结果
    
    fileprivate var recordRequest: SFSpeechURLRecognitionRequest?
    
    fileprivate var recognizer: SFSpeechRecognizer?
    
    fileprivate var path: String?
}


extension VoiceToText {
    //开始识别
    func startRecognize(url: URL , identifier: String){
        
        stopRecognize()
        
        if let path = path {
            LiveActivityTool.delete(filePath: path)
        }
       
        path = url.absoluteString
        
        recordRequest = SFSpeechURLRecognitionRequest(url: url)
        
        recognizer  = SFSpeechRecognizer(locale: Locale(identifier: identifier ))
    
        recordRequest?.shouldReportPartialResults = true
        
        //开始识别获取文字
        recordTask = recognizer?.recognitionTask(with: recordRequest!, resultHandler: { [self] (result, error) in
            
            guard error == nil else {
                BuzLiveActivityLog.info("语音识别发生错误：\(error!.localizedDescription)")
                textBlock?(nil)

                MessageLiveActivityReporter.reportVoiceToTextResultBack(isSuccess: false, failReason: error?.localizedDescription)
                if let path = path {
                    LiveActivityTool.delete(filePath: path)
                }
                
                return
            }
            
            guard let result = result else { return }
            let transcription = result.bestTranscription
            let recognizedText = transcription.formattedString
//            BuzLiveActivityLog.info("语音识别结果：\(recognizedText)")
            
            if result.isFinal == true {
                // 识别任务已经结束
                BuzLiveActivityLog.info("语音识别任务已结束 \(recognizedText)")
                textBlock?(recognizedText)
                MessageLiveActivityReporter.reportVoiceToTextResultBack(isSuccess: true)
                if let path = path {
                    LiveActivityTool.delete(filePath: path)
                }
            }
           
           
        })
    }
    
    //停止识别
    func stopRecognize(){
        if recordTask != nil {
            if #available(iOS 16.2, *) {
                if let message = MessageLiveActivityManager.shared.messages{
                    MessageLiveActivityManager.shared.voiceTranslateTextClouse?(message)
                }
            } else {
                // Fallback on earlier versions
            }
            recordTask?.cancel()
            recordTask = nil
            recognizer = nil
            
        }
    }
    
   
}

//extension VoiceToText: SFSpeechRecognizerDelegate{
//    //语音识别是否可用
//    func speechRecognizer(_ speechRecognizer: SFSpeechRecognizer, availabilityDidChange available: Bool) {
//
//    }
//}
