//
//  ChatIntent.swift
//  buz
//
//  Created by 方煜逵 on 2023/9/7.
//  Copyright © 2023 lizhi. All rights reserved.
//


import AppIntents
import UIKit

@available(iOS 17.0, *)
struct ChatIntent: LiveActivityIntent {
    
    static var title: LocalizedStringResource = "Chat"

    static var openAppWhenRun: Bool = true
    
    @Parameter(title: "IsQuit")
    var isQuit: Bool

    public init() {
    }

    public func perform() async throws -> some IntentResult & ReturnsValue<Bool>  {
#if MAIN_TARGET
        
        await MessageLiveActivityManager.shared.routeToChat()

#endif
        
        return .result(value: true)
      
    }
}




