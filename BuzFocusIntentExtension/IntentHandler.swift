//
//  IntentHandler.swift
//  FocusIntentExtension
//
//  Created by 彭智鑫 on 2024/7/31.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Intents
import BuzConfig

/// Consider implementing INSendMessageIntentHandling and INStartCallIntentHandling
/// to handle Share Sheet and Siri requests.
class IntentHandler: INExtension {
    var retryRequestCount = 0
    
    override func handler(for intent: INIntent) -> Any {
        return self
    }
}

// MARK: - INShareFocusStatusIntentHandling

@available(iOS 15.0, *)
extension IntentHandler: INShareFocusStatusIntentHandling {
    
    /**
     For this Intent to be handled, the following requirements must be met:
     FocusStatusCenter authorized for parent app (target).
     UserNotifications authorized for parent app (target).
     Communication Notifications capability (entitlement) added to the parent app (target).
     */
    func handle(intent: INShareFocusStatusIntent, completion: @escaping (INShareFocusStatusIntentResponse) -> Void) {
        let response = INShareFocusStatusIntentResponse(code: .success, userActivity: nil)
        if intent.focusStatus?.isFocused != nil {
            sendNotificationToServer()
        }
        completion(response)
    }
    
    func sendNotificationToServer() {
        ExtensionLog.log("BuzFoucsIntentExtension begin send to server")
        guard self.retryRequestCount < 5, let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId), let sessionKey = userDefaults.string(forKey: "BuzShareStore_webToken") else {
            ExtensionLog.log("BuzFoucsIntentExtension no web Token")
            return
        }
        
//        let url = "https://api.buz-app.com/common/dndModeModify"
        
#if DEBUG
        guard let userDefaults = UserDefaults(suiteName: BuzConfig.groupStoreId) else {
            ExtensionLog.log("BuzFoucsIntentExtension userDefaults")
            return
        }
        
        let env = userDefaults.integer(forKey: "BuzShareStore_ENV")
        var url = "https://api.buz-app.com/common/dndModeModify"
        if env == 0 { // 正式环境
            
        } else if env == 1 { // 预发布环境
            
        } else { // 灯塔环境
            url = "https://buz.yfxn.lzpsap1.com/common/dndModeModify"
        }
#else
        let url = "https://api.buz-app.com/common/dndModeModify"
#endif
        
        
        var request = URLRequest(url: URL(string: url)!)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue(sessionKey, forHTTPHeaderField: "token")

        let task = URLSession.shared.dataTask(with: request) { data, response, error in
            ExtensionLog.log("BuzFoucsIntentExtension request data:\(data), response:\(response), error:\(error)")
            if error != nil {
                self.retryRequestCount = self.retryRequestCount + 1
                DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                    self.sendNotificationToServer()
                }
            } else {
                self.retryRequestCount = 0
            }
        }
        
        task.resume()
    }
}
