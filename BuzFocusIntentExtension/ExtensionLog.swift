//
//  ExtensionLog.swift
//  BuzFocusIntentExtension
//
//  Created by 彭智鑫 on 2024/8/21.
//  Copyright © 2024 lizhi. All rights reserved.
//

import Foundation
import os.log

struct ExtensionLog {
    static let showLog: Bool = true
    static let logSystem = OSLog(subsystem: "BuzFocusIntentExtension", category: "NSE")
    static func log(_ msg: @autoclosure () -> String) {
        if showLog {
            let message = msg()
            os_log("%{public}@", log: logSystem, type: .default, message)
        }
    }
}
