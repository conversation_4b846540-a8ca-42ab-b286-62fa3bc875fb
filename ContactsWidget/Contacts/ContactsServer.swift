//
//  ContactsServer.swift
//  ContactsWidgetExtension
//
//  Created by yutao on 2022/7/28.
//  Copyright © 2022 lizhi. All rights reserved.
//

import Foundation

struct ContactsServer {
    
    static func previewEntry() -> SimpleEntry {
        
        let contacts = Contacts.contacts(json: Contacts.preview)
        return SimpleEntry(date: Date(), contacts: contacts)
    }
    
    static func updateEntrysFromApp() -> SimpleEntry {
        let contacts = WidgetShareData.loadContacts()
        if contacts.count == 0 {
            return previewEntry()
        }        
        return SimpleEntry(date: Date(), contacts: contacts)
    }
    
    // 填充至 4/8 个联系人。
    static func fillContacts(contacts: [ContactModel]) -> [ContactModel] {
        
        var filledContacts = contacts
        if filledContacts.count < 4 {
            for i in 0...3-contacts.count {
                filledContacts.append(ContactModel.defaultContact(userId: "com.interfun.buz.contacts.\(i)"))
            }
            return filledContacts
        }else if filledContacts.count < 8 && filledContacts.count > 4 {
            for i in 0...7-contacts.count {
                filledContacts.append(ContactModel.defaultContact(userId: "com.interfun.buz.contacts.\(i)"))
            }
        }
                
        return filledContacts
    }
}
