//
//  ContactsWidget.swift
//  ContactsWidget
//
//  Created by yutao on 2022/7/27.
//  Copyright © 2022 lizhi. All rights reserved.
//

import Foundation
import WidgetKit
import SwiftUI

struct ContactsWidget: Widget {
    let kind: String = "com.interfun.buz.contacts"

    var body: some WidgetConfiguration {
        let config = StaticConfiguration(kind: kind, provider: Provider()) { entry in
            ContactsWidgetEntryView(entry: entry)
        }
        .supportedFamilies([.systemMedium])
        .configurationDisplayName("Buz Widget")
        .description("Easily talk to the people you care about most.")
        
        if #available(iOSApplicationExtension 15.0, *) {
           _ =  config.contentMarginsDisabled()
        }
        
        return config
    }
}


// SWiftUI Preview
struct ContactsWidget_Previews: PreviewProvider {
    static var previews: some View {
        ContactsWidgetEntryView(entry: ContactsServer.previewEntry())
            .previewContext(WidgetPreviewContext(family: .systemMedium))
    }
}

struct Provider: TimelineProvider {
    
    // 窗口首次展示的时候，展示默认数据
    func placeholder(in context: Context) -> SimpleEntry {
        SizeInfo.widgetSize = context.displaySize
        return ContactsServer.previewEntry()
    }

    // 添加组件时的预览数据，在桌面滑动选择的时候展示数据
    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> ()) {
        SizeInfo.widgetSize = context.displaySize
        completion(ContactsServer.previewEntry())
    }

    // 时间线刷新策略控制逻辑（不需要主动更新）
    func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> ()) {
        SizeInfo.widgetSize = context.displaySize
        
        var entry = ContactsServer.updateEntrysFromApp()
        
        let portraitUrls = entry.contacts.compactMap { contact in
            contact.portrait
        }

        Network.asyncRequestImage(with: portraitUrls) { resultDatas in
            let contacts = entry.contacts.compactMap { contact -> ContactModel in
                var varContact = contact
                if contact.portrait.count > 0,
                   let data = resultDatas[contact.portrait] {
                    varContact.portraitData = data
                }
                return varContact
            }
            entry = SimpleEntry(date: Date(), contacts: contacts)
            let timeline = Timeline(entries: [entry], policy: .never)
            completion(timeline)
        }
    }
}

struct PortraitView: View {
        
    var item: ContactModel
    
    var body: some View {
        var userId = item.userId
        // 默认头像不带userId
        if item.isPlaceholder == true {
            userId = ""
        }
        return Link(destination: URL(string: "widget://contacts?userId=\(userId)")!) {
            if let data = item.portraitData, let image = UIImage.init(data: data) {
                // 需要防止超大尺寸头像时Widget崩溃
                Image(uiImage: image)
                    .resizable()
                    .clipShape(Circle())
            }else {
                Image(item.localPortrait)
                    .resizable()
                    .clipShape(Circle())
            }
        }
    }
    
    // TODO: - 改成异步逻辑
    // 注意这里无法异步刷新，暂时使用同步下载
    func syncDownloadImage(_ imgUrlString: String) -> UIImage? {
        
        guard let data = try? Data(contentsOf:URL(string: imgUrlString)!) else {
            return nil
        }
        return UIImage(data: data)
    }
}

struct BuzLogoView: View {
    
    var body: some View {
         HStack() {
            Image("buz_logo")
            Image("buz_friends")
         }
    }
}

struct ContactView: View {
    var item: ContactModel
    var body: some View {
        VStack {
            PortraitView(item: item)
                .clipShape(Circle())
                .aspectRatio(contentMode: .fit)
            Text(item.username)
                .lineLimit(1)
                .font(.custom("SFProText-Medium", size: 12))
                .foregroundColor(Color(hex: "80FFFFFF"))
         }
    }
}

struct ContactsWidgetEntryView : View {
    var entry: Provider.Entry
    
    var body: some View {
        
        if entry.contacts.count <= 4 {
            
            VStack(alignment: .leading) {
                BuzLogoView()
                    .padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 0))
                Spacer()
                HStack(spacing: SizeInfo.adaptive(width: 14)) {
                    ForEach(entry.contacts) { item in
                        ContactView(item: item)
                            .frame(width: SizeInfo.adaptive(width: 64),
                                   height: SizeInfo.adaptive(height: 64) + 22,
                                   alignment: .center)
                    }
                }
            }
            .padding(EdgeInsets(top: 18, leading: 0, bottom: 18, trailing: 0))
            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .top)
            .background(LinearGradient(gradient: Gradient(colors: [Color(hex: "000000"),
                                                                   Color(hex: "121212")]),
                                       startPoint: .top,
                                       endPoint: .bottom))
            .widgetBackground(Color.black)
            
        }else {
            
            VStack(alignment: .leading) {
                Spacer()
                HStack(spacing: SizeInfo.adaptive(width: 18)) {
                    ForEach(entry.contacts[0...3]) { item in
                        PortraitView(item: item)
                            .clipShape(Circle())
                            .frame(width: SizeInfo.adaptive(width: 56),
                                   height: SizeInfo.adaptive(height: 56),
                                   alignment: .center)
                            .aspectRatio(contentMode: .fit)
                    }
                }
                Spacer()
                HStack(spacing: SizeInfo.adaptive(width: 18)) {
                    ForEach(entry.contacts[4...7]) { item in
                        PortraitView(item: item)
                            .clipShape(Circle())
                            .frame(width: SizeInfo.adaptive(width: 56),
                                   height: SizeInfo.adaptive(height: 56),
                                   alignment: .center)
                            .aspectRatio(contentMode: .fit)
                    }
                }
                Spacer()
            }
            .frame(maxWidth: .infinity,
                   maxHeight: .infinity,
                   alignment: .top)
            .background(LinearGradient(gradient: Gradient(colors: [Color(hex: "000000"),
                                                                   Color(hex: "121212")]),
                                       startPoint: .top,
                                       endPoint: .bottom))
            .widgetBackground(Color.black)
        }
    }
}


struct SimpleEntry: TimelineEntry {
    var date: Date
    
    let contacts: [ContactModel]
    
    init(date: Date, contacts: [ContactModel]) {
        self.date = date
        self.contacts = ContactsServer.fillContacts(contacts:contacts)
    }
}



extension View {
    func widgetBackground(_ backgroundView: some View) -> some View {
        if #available(iOSApplicationExtension 17.0, *) {
            return containerBackground(for: .widget) {
                backgroundView
            }
        } else {
            return background(backgroundView)
        }
    }
}
