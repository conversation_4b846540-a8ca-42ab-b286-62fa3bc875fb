//
//  Contacts.swift
//  ContactsWidgetExtension
//
//  Created by yutao on 2022/7/28.
//  Copyright © 2022 lizhi. All rights reserved.
//

import Foundation

struct Contacts {
    
    /// 预览数据，内置 Assets 图片、用户名
    /// 默认图统一用 buz
    static let preview: [[String: Codable]] = [
        [
            "userId": "10000",
            "portrait": "",
            "localPortrait": "buz_portrait_default",
            "username": "Friend",
            "isPlaceholder": true
        ],[
            "userId": "10001",
            "portrait": "",
            "localPortrait": "buz_portrait_default",
            "username": "Friend",
            "isPlaceholder": true
        ],[
            "userId": "10002",
            "portrait": "",
            "localPortrait": "buz_portrait_default",
            "username": "Friend",
            "isPlaceholder": true
        ],[
            "userId": "10003",
            "portrait": "",
            "localPortrait": "buz_portrait_default",
            "username": "Friend",
            "isPlaceholder": true
        ]
    ]
    
    static func contacts(json: [[String: Codable]]) -> [ContactModel] {
        
        let contacts = json.compactMap { value -> ContactModel? in
            
            if let data = try? JSONSerialization.data(withJSONObject: value),
                let contact = try? JSONDecoder().decode(ContactModel.self, from: data)
            {
                return contact
            }
            return nil
        }
        
        return contacts
    }
}

struct ContactModel: Identifiable, Decodable, Encodable {
    var id: String {
        userId
    }
    
    var userId: String = ""
    
    var portrait: String = ""
    var username: String = ""
        
    var isPlaceholder = false
    var localPortrait: String = "buz_portrait_default"
    
    var portraitData: Data? = nil

    static func defaultContact(userId: String) -> ContactModel {
        ContactModel(userId: userId,
                     portrait: "",
                     username: "Friend",
                     isPlaceholder: true)
    }
}

extension ContactModel {
    
    enum CodingKeys: String, CodingKey {
        case userId
        case portrait
        case username
        case localPortrait
        case isPlaceholder
    }
    
    init(from decoder: Decoder) throws {
        let values = try decoder.container(keyedBy: CodingKeys.self)
        
        userId = try values.decode(String.self, forKey: .userId)
        portrait = try values.decode(String.self, forKey: .portrait)
        username = try values.decode(String.self, forKey: .username)
        localPortrait = try values.decode(String.self, forKey: .localPortrait)
        isPlaceholder = try values.decode(Bool.self, forKey: .isPlaceholder)
    }

}
