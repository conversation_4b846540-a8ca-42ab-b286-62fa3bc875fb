//
//  Network.swift
//  buz
//
//  Created by yutao on 2022/8/23.
//  Copyright © 2022 lizhi. All rights reserved.
//

import Foundation

struct Network {
    
    static func asyncRequestImage(with imageURLs: [String],
                                 completion: @escaping ([String: Data]) -> ()) {
                
        let requestGroup = DispatchGroup()
        var datas: [String: Data] = [:]
        for originalURLString in imageURLs {
            
            guard originalURLString.count > 0 else {
                continue
            }
            guard let imageURL = URL(string: originalURLString) else {
                continue
            }
            let resizeURL = URL.resizeImage(url: imageURL, width: 192, height: 192)

            requestGroup.enter()
            URLSession.shared.dataTask(with: resizeURL, completionHandler: {
                data, response, error in

                if let httpResponse = response as? HTTPURLResponse,
                   httpResponse.statusCode == 200,
                   data != nil {
                    datas[originalURLString] = data
                }

                requestGroup.leave()
            })
            .resume()
        }
        
        requestGroup.notify(queue: .main) {
            completion(datas)
        }
    }
}
