//
//  ContactsWidgetShareData.swift
//  buz
//
//  Created by lid<PERSON><PERSON> on 2022/8/3.
//  Copyright © 2022 lizhi. All rights reserved.
//

import UIKit
import WidgetKit

class WidgetShareData {
    static let MaxCount = 8
    static let ShareGroupName = "group.com.interfun.buz"
    static let WidgetKind = "com.interfun.buz.contacts"
    static let RootKey = "contacts"
    static let webImageFileDirName = "BuzWebImageFiles"

    static func loadContacts() -> [ContactModel] {        
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return []
        }
        if let data = userDefaults.value(forKey: RootKey) as? Data {
            let decoder = JSONDecoder()

            if let decoded = try? decoder.decode(Array.self, from: data) as [ContactModel] {
                return decoded
            } else {
                return []
            }
        } else {
            return []
        }
    }
        
    static func saveContactsAndReloadTimelinesIfNeed(_ data: [ContactModel]) -> <PERSON><PERSON>{
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return false
        }
        
        let oldData = userDefaults.value(forKey: RootKey) as? Data
        
        let encoder = JSONEncoder()
        if let encoded = try? encoder.encode(data), encoded != oldData {
            userDefaults.set(encoded, forKey: RootKey)
            DispatchQueue.main.async {
                self.notifyDataChanged()
            }
            return true
        }
        return false
    }

    static func saveContacts(_ data: [ContactModel]) {
        guard let userDefaults = UserDefaults(suiteName: ShareGroupName) else {
            return
        }
        let encoder = JSONEncoder()
        if let encoded = try? encoder.encode(data) {
            userDefaults.set(encoded, forKey: RootKey)
        }
    }
    static func notifyDataChanged() {
        if #available(iOS 14.0, *) {
            WidgetCenter.shared.reloadTimelines(ofKind: WidgetKind)
        } else {
            // Fallback on earlier versions
        }
    }
    
    
    static func appGroupWebImageFilePath(urlStr : String?) -> URL?
    {
        guard let destination = appGroupWebImageDirectory(),
              let urlStr = urlStr
        else { return nil }
        
        return destination.appendingPathComponent(urlStr.md5)
    }
    
    static func chatImageFilePath() -> URL?
    {
        guard let destination = appGroupWebImageDirectory() else { return nil }
        
        return destination.appendingPathComponent("buzImage")
    }
    
    
    static func appGroupWebImageDirectory() -> URL?
    {
        guard let destination = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: WidgetShareData.ShareGroupName) else {
            return nil
        }
        
        let dirPathURL = destination.appendingPathComponent(WidgetShareData.webImageFileDirName)
        var isDirectory = ObjCBool(false)
        
        if FileManager.default.fileExists(atPath: dirPathURL.path, isDirectory: &isDirectory) == false {
            
            do {
                try FileManager.default.createDirectory(atPath: dirPathURL.path, withIntermediateDirectories: true)
                BuzDebugLog("create image dir in App Group success)")

            }catch {
                BuzDebugLog("create image dir error = \(error)")
            }

        }else{
            BuzDebugLog("no need create image dir in App Group , because had exist")
        }
        
        return dirPathURL
    }
}
