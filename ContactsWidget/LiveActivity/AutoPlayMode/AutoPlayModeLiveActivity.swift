//
//  AutoPlayModeLiveActivity.swift
//  buz
//
//  Created by 彭智鑫 on 2024/8/1.
//  Copyright © 2024 lizhi. All rights reserved.
//

import SwiftUI
import WidgetKit
import ActivityKit

@available(iOS 17.0 , *)
struct AutoPlayModeLiveActivity: Widget {
    
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: AutoPlayModeLiveActivityAttributes.self) { context in
            AutoPlayModelLockScreenWidget(isAutoPlay: context.state.isAutoPlay)
                .activityBackgroundTint(.black.opacity(0.7))
        } dynamicIsland: { context in
            DynamicIsland {
                DynamicIslandExpandedRegion(.center) {
                    AutoPlayModelLockScreenWidget(isAutoPlay: context.state.isAutoPlay)
                        .activityBackgroundTint(.black.opacity(0.7))
                }
            } compactLeading: {
                
            } compactTrailing: {
                
            } minimal: {
                Image("buz_widget_minimal_logo")
                    .resizable()
                    .frame(width: 20.0 , height: 20.0)
            }
            .contentMargins(.all , 12.0 , for: .expanded)
        }
    }
}

struct AutoPlayModeLiveActivityAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        var isAutoPlay: Bool = false
    }
}

extension Notification.Name {
    static let PersonalStatusDidUpdate: Notification.Name = Notification.Name.init("PersonalStatusDidUpdate")
    
    
    static let notificationPhoneBindSuccess: Notification.Name = Notification.Name.init("kNotificationPhoneBindSuccess")
    
}
