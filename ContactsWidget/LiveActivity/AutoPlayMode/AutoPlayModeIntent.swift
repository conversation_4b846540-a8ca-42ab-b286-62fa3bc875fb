//
//  AutoPlayModeIntent.swift
//  buz
//
//  Created by 彭智鑫 on 2024/8/1.
//  Copyright © 2024 lizhi. All rights reserved.
//

import AppIntents

@available(iOS 17.0, *)
struct AutoPlayModeIntent: LiveActivityIntent {
    
    static var title: LocalizedStringResource = "isAutoPlay"

    @Parameter(title: "isAutoPlay")
    var isAutoPlay: Bool

    public func perform() async throws -> some IntentResult & ReturnsValue<Bool>  {

        NotificationCenter.default.post(name: .PersonalStatusDidUpdate, object: true)
        
        return .result(value: true)
    }
}
