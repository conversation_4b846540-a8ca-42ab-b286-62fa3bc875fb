//
//  AutoPlayModelLockScreenWidget.swift
//  buz
//
//  Created by 彭智鑫 on 2024/8/1.
//  Copyright © 2024 lizhi. All rights reserved.
//

import SwiftUI
import Localizable
import BuzLocalizable

@available(iOS 17.0 , *)
struct AutoPlayModelLockScreenWidget: View {
    
    let isAutoPlay: Bool
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 6) {
                Image("buz_widget_logo_3")
                    .frame(height: 20)
                Text(isAutoPlay ? Localizable.notification_quiet_mode_off : Localizable.notification_quiet_mode_on)
                    .foregroundStyle(.white.opacity(0.6))
                    .font(.system(size: 16))
            }
            Spacer()
            Button(intent: AutoPlayModeIntent()) {
                Image(isAutoPlay ? "available_mode" : "quiet_mode")
                    .frame(width: 46, height: 32)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.horizontal, 20)
        .frame(height: 90)
        .background(.black)
    }
}
