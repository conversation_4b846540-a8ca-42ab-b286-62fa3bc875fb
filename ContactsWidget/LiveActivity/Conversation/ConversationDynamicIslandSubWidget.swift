//
//  ConversationDynamicIslandSubWidget.swift
//  buz
//
//  Created by l<PERSON><PERSON> on 2023/1/10.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import WidgetKit
import SwiftUI


var ScreenWidth : CGFloat {
    get{
    
        let width = UIScreen.main.bounds.width
        if width >= 408.0 {
            return 408.0
        }else if width >= 371.0 {
            return 371.0
        }else{
            if UIDevice.current.userInterfaceIdiom == .pad {
                return 408.0
            }
            return 371.0
            
        }
    }
}

struct BuzDynamicIslandRecentsWidget : View{
    let title : String
    let color : Color
    var body: some View {
        HStack(alignment: .center , spacing: 0) {
            Text(title)
                .foregroundColor(color)
                .font(.system(size: 11.0))
                .frame(height: 32.0)
            Image("buz_widget_logo_12")
                .resizable()
                .frame(width: 12.0 , height: 12.0).padding(.leading , 6.0)
        }
    }
}

struct BuzDynamicIslandLogoWidget : View{
    let height : CGFloat
    let width : CGFloat
    let iconHeight : CGFloat
    let iconWidth : CGFloat
    var body: some View {
        VStack(alignment: .center, content: {
            Spacer()
            Image("buz_widget_logo")
                .resizable()
                .frame(width: iconWidth, height: iconHeight , alignment: .bottom)
                .padding(.bottom , 20)
        }).frame(width: width , height: height)
    }
}

@available(iOS 16.1 , *)
struct BuzDynamicIslandConversationWidget : View{
    let width : CGFloat
    let conversation : LiveActivityConversationModel?

    var body: some View {
        VStack(spacing: 0) {
            ZStack{
                if conversation == nil || conversation?.isGroup ?? false == true {
                    //1.9.0版本先不显示群头像，暂时屏蔽
//                    if let portrait = conversation?.groupInfo?.portrait , portrait.count > 0, let image = WidgetImageLoader.syncLoadImage(urlStr: portrait){
//                        image.frame(maxWidth: .infinity , maxHeight: .infinity)
//                    }else{
                        Text(conversation?.getDisplayGroupName(width: 36.0, fontSize: 13.0 , maxHeight : 40.0) ?? " ")
                            .padding(8)
                            .lineLimit(2).lineSpacing(0)
                            .font(.system(size: 13.0)).bold()
                            .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.6))
                            .multilineTextAlignment(.center)
                            .frame(maxWidth: .infinity , maxHeight: .infinity)
                            .background(Color(hex: 0xffffff, alpha: 0.1))
                            .overlay(Circle().stroke(Color(hex: 0xffffff, alpha: 0.04), lineWidth: conversation == nil ? 0 : 4))
//                    }
                }else{
                    BuzDynamicIslandPortraitWidget(portrait: conversation?.speakerUserInfo?.portrait).frame(width: 52.0 , height: 52.0)
                }
            }.frame(width: 52 , height: 52)
                .cornerRadius(26.0)
                .padding(.top , 2)
            Text(conversation?.displayName ?? "")
                .font(.system(size: 16.0).bold())
                .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.8))
                .padding(.top , 10.0).lineLimit(1).padding([.leading , .trailing] , 1)
            if conversation?.msgTimestamp ?? 0 > 0 {
                Text("\(conversation?.timeString ?? "")")
                    .font(.system(size: 11.0))
                    .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.3))
                    .padding(.top , 4)
            }else{
                if conversation != nil{
                    Text(conversation?.isGroup ?? false ? "New group" : "New friend")
                        .font(.system(size: 11.0))
                        .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.3))
                        .padding(.top , 4)
                }
            }
            Spacer()
        }.frame(width: width , height: 116).padding(0)
    }
}

@available(iOS 16.1 , *)
struct BuzDynamicIslandSpeakingWidget : View{
    let width : CGFloat
    let height : CGFloat
    let iconWidth = 82.0
    let circleWidth = 78.0
    let speakingConversation : LiveActivityConversationModel

    var body: some View {
        VStack(spacing: 0) {
            ZStack(alignment: .center, content: {
                if speakingConversation.isSpeaking {
                    Image("buz_widget_speaking").frame(width: circleWidth , height: circleWidth)
                }
                if speakingConversation.isGroup{
                    BuzDynamicIslandGroupPortraitWidget.init(portrait: speakingConversation.groupInfo?.portrait, speakingConversation: speakingConversation)
                        .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.6))
                        .frame(width: 80.0 , height: 80.0)
                        .background(Color(hex: 0xffffff, alpha: 0.1))
                        .overlay(Circle().stroke(Color(hex: 0xffffff, alpha: 0.04), lineWidth: 4))
                        .cornerRadius(80 * 0.5)
                        .scaleEffect(speakingConversation.isSpeaking ? (70.0 / 80.0) : 1.0)
                        .animation(.easeInOut(duration: 0.5))
                }else{
                    BuzDynamicIslandPortraitWidget(portrait: speakingConversation.speakerUserInfo?.portrait)
                        .frame(width: iconWidth , height : iconWidth)
                        .cornerRadius(iconWidth * 0.5)
                        .scaleEffect(speakingConversation.isSpeaking ? (70.0 / iconWidth) : 1.0)
                        .animation(.easeInOut(duration: 0.5))
                }
            }).frame(width: iconWidth , height: iconWidth)
                .padding(.top ,18.0)
            Text(speakingConversation.displayName)
                .font(.system(size: 16.0)).bold()
                .foregroundColor(speakingConversation.isSpeaking ? Color.init(hex: 0xB9FB65, alpha: 1.0) : Color.init(hex: 0xffffff, alpha: 0.8))
                .padding([.top , .leading , .trailing] , 8.0)
                .lineLimit(1)
            if speakingConversation.isSpeaking{
                Text("Speaking")
                    .font(.system(size: 11.0)).bold()
                    .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.8))
                    .padding(.top , 4)
            }else{
                if speakingConversation.msgTimestamp > 0 {
                    Text("\(speakingConversation.timeString)")
                        .font(.system(size: 11.0))
                        .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.3))
                        .padding(.top , 4)
                }else{
                    Text(speakingConversation.isGroup ? "New group" : "New friend")
                        .font(.system(size: 11.0))
                        .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.3))
                        .padding(.top , 4)
                }
            }
            Spacer()
        }.frame(width: width , height: height).padding(0)
    }
}


struct BuzDynamicIslandPortraitWidget : View{
    let portrait : String?
    var body: some View {
        if let image = WidgetImageLoader.syncLoadImage(urlStr: portrait){
            image
        }else{
            Image("buz_widget_logo")
        }
    }
}


///群头像
struct BuzDynamicIslandGroupPortraitWidget : View{
    let portrait : String?
    let speakingConversation : LiveActivityConversationModel
    var body: some View {
        //1.9.0版本先不显示群头像，暂时屏蔽
//        if let imageUrl = portrait , imageUrl.count > 0, let image = WidgetImageLoader.syncLoadImage(urlStr: imageUrl){
//            image
//        }else{
            Text(speakingConversation.getDisplayGroupName(width: 68.0, fontSize: 17.5 , maxHeight : 63.0))
                .font(.system(size: 17.5)).bold()
                .lineLimit(2).lineSpacing(0)
                .multilineTextAlignment(.center)
                .padding(6)
//        }
    }
}
