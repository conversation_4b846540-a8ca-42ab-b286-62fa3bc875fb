//
//  File.swift
//  buz
//
//  Created by lizhi on 2023/1/10.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import WidgetKit
import Swift<PERSON>

@available(iOS 16.1 , *)
struct ConversationDynamicIslandLockScreenWidget : View{

    let conversationArray : Array<LiveActivityConversationModel>
    let widgetHeight = 160.0
    let widgetWidth = ScreenWidth
    let leftViewWidth = 106.0
    let margin = 6.0
    
    var moreChatArray : Array<LiveActivityConversationModel>{
        let list = conversationArray.suffix(from: 1)
        return Array(list)
    }

    var body: some View {
        HStack(alignment: .center, spacing: 0.0 , content: {
            if let currentSpeakingConversation = conversationArray.first{
                Link(destination: URL(string: "widget://contacts?userId=\(currentSpeakingConversation.fromId)&isLiveActivity=1")!){
                    BuzDynamicIslandSpeakingWidget(width: leftViewWidth, height: 160, speakingConversation: currentSpeakingConversation)
                }
            }else{
                BuzDynamicIslandLogoWidget(height: widgetHeight , width: leftViewWidth , iconHeight: 25 , iconWidth: 40.0)
            }
            VStack(alignment: .leading, spacing: 0 , content: {
                HStack {
                    Spacer()
                    BuzDynamicIslandRecentsWidget(title: "RECENTS" , color: Color.init(hex: 0xffffff, alpha: 0.3))
                        .frame(height: 44.0)
                        .padding(.trailing , 16.0)
                }
                Spacer(minLength: 0)
                HStack(alignment: .top , spacing: 0) {
                    let count = 3
                    let margins = 12.0 + 6.0
                    let itmeWidth = (widgetWidth - leftViewWidth - margins) / Double(count)
                    ForEach(0..<count  , id : \.self){ index in
                        if index < moreChatArray.count {
                            let model = moreChatArray[index]
                            Link(destination: URL(string: "widget://contacts?userId=\(model.fromId)&isLiveActivity=1")!){
                                BuzDynamicIslandConversationWidget(width: itmeWidth, conversation: model)
                            }
                        }else{
                            BuzDynamicIslandConversationWidget(width: itmeWidth, conversation: nil)
                        }
                    }
                }
            })
        }).frame(width: widgetWidth - 18.0, height: widgetHeight , alignment: .topLeading)
            .padding([ .trailing , .bottom] , 6)
            .padding(.top , 0)
            .padding(.leading , 12)
    }
}



struct BuzDynamicIslandLockScreenDefaultWidget : View{
    var body: some View {
        HStack(alignment: .top, spacing: 0 , content: {
            HStack (alignment: .top, content: {
                Image("buz_widget_default_1")
                    .resizable()
                    .frame(width: 198.0 , height: 160.0)
            })
            HStack (alignment: .top, content: {
                VStack(alignment: .trailing, spacing: 0 ,content: {
                    BuzDynamicIslandRecentsWidget(title: "BUZ" , color: Color.init(hex: 0xffffff, alpha: 0.3)).frame(height: 32.0).padding(.top , 6)
                    Text("Buz Live Activity")
                        .font(.system(size:16.0).bold())
                        .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.8))
                        .padding(.top , 17.0)
                        .lineLimit(1)
                        .minimumScaleFactor(0.5)
                    HStack {
                        Text("View Buz voice message updates at a glance")
                            .lineSpacing(1.34)
                            .padding(.top , 8)
                            .multilineTextAlignment(.trailing)
                            .font(.system(size: UIScreen.main.bounds.width > 390 ? 13.0 : 12.0 ))
                            .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.5))
                    }
                })
            }).padding(.trailing , 26.0)
        }).frame(width: ScreenWidth, height: 160.0 , alignment: .topLeading)
    }
}
