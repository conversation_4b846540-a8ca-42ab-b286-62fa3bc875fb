//
//  ConversationLiveActivityWidget.swift
//  buz
//
//  Created by lizhi on 2023/1/10.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import WidgetKit
import SwiftUI

@available(iOS 16.1 , *)
struct ConversationDynamicIslandExpandedWidget : View{
    
    let conversationArray : Array<LiveActivityConversationModel>
    let rightViewWidth = 76.0
    let widgetWidth = ScreenWidth - 24.0
    let widgetHeight = 160.0 - 12.0
    let itemMaxCount = 4
    let margin = 12.0

    var body: some View {
        VStack(alignment: .leading, spacing: 16.0 , content: {
            Text("RECENTS")
                .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.3))
                .font(.system(size: 11.0))
                .frame(height: 13.0)
                .padding(.leading , 27.0 - margin)
                .padding(.top , 15.0 - margin)
            HStack(alignment: .top , spacing: 0) {
                let count = min(conversationArray.count, itemMaxCount)
                let width = (widgetWidth) / Double(itemMaxCount)
                ForEach(0..<count , id : \.self){ index in
                    let model = conversationArray[index]
                    Link(destination: URL(string: "widget://contacts?userId=\(model.fromId)&isLiveActivity=1")!){
                        BuzDynamicIslandConversationWidget(width: width, conversation: model)
                    }
                }
                Spacer(minLength: 0)
//                if let currentSpeakingConversation = speakingConversation {
//                    BuzDynamicIslandSpeakingWidget(width: rightViewWidth, height: widgetHeight - 32.0,speakingConversation: currentSpeakingConversation)
//                }else{
//                    BuzDynamicIslandLogoWidget(height: widgetHeight - 32.0 , width: rightViewWidth , iconHeight: 22 , iconWidth: 34)
//                }
//                BuzDynamicIslandLogoWidget(height: widgetHeight - 32.0 , width: rightViewWidth , iconHeight: 22 , iconWidth: 34)
            }
        }).frame(width: widgetWidth, height: widgetHeight , alignment: .topLeading)
    }
}

struct BuzDynamicIslandExpendDefaultWidget : View{
    var body: some View {
        HStack(alignment: .center, content: {
            Image("buz_widget_default")
                .frame(width: 198.0 , height: 93.0)
                .padding(.leading , 0.0)
            Spacer()
            VStack(alignment: .trailing, spacing: 0 ,content: {
                Text("Keep connected!")
                    .font(.system(size: 16.0).bold())
                    .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.8))
                HStack {
                    Text("Buz voice messages will continue to autoplay")
                        .foregroundColor(Color.init(hex: 0xffffff, alpha: 0.5))
                        .lineSpacing(1.34)
                }.padding(.top , 5.0)
                    .frame(width: 151.0)
                    .multilineTextAlignment(.trailing)
                    .font(.system(size: 13.0))
                
                Spacer()
            }).padding(.trailing ,12.0 )
                .padding(.top , 47.0)
        }).frame(width: ScreenWidth - 24.0, height: 160.0 - 24.0 , alignment: .center)
    }
}



extension Color {
    init(hex: Int, alpha: Double = 1) {

    let components = (
    R: Double((hex >> 16) & 0xff) / 255,
    G: Double((hex >> 08) & 0xff) / 255,
    B: Double((hex >> 00) & 0xff) / 255
    )
    self.init(
    .sRGB,
    red: components.R,
    green: components.G,
    blue: components.B,
    opacity: alpha
    )
    }
}
