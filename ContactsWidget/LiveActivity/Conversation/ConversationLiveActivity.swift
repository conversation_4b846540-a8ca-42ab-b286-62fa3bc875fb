//
//  ConversationLiveActivity.swift
//  LiveActivity
//
//  Created by liuyufeng on 2023/1/9.
//  Copyright © 2023 lizhi. All rights reserved.
//

import ActivityKit
import WidgetKit
import SwiftUI

struct ConversationLiveActivityAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        // Dynamic stateful properties about your activity go here!
        ///会话数组，数据最多4个。前三个显示在界面上。
        var conversationArray : [LiveActivityConversationModel] = []
    }

    // Fixed non-changing properties about your activity go here!
//    var name: String
}
@available(iOS 16.1 , *)
struct ConversationLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: ConversationLiveActivityAttributes.self) { context in
            // Lock screen/banner UI goes here
//            VStack {
//                Text("Hello")
//            }
//            .activityBackgroundTint(Color.cyan)
//            .activitySystemActionForegroundColor(Color.black)
            if context.state.conversationArray.count > 0 {
                ConversationDynamicIslandLockScreenWidget(conversationArray: context.state.conversationArray)
                    .activityBackgroundTint(Color.init(hex: 0x000000, alpha: 0.7))
            }else{
                BuzDynamicIslandLockScreenDefaultWidget()
                    .activityBackgroundTint(Color.init(hex: 0x000000, alpha: 0.7))
            }
        } dynamicIsland: { context in
            DynamicIsland {
                // Expanded UI goes here.  Compose the expanded UI through
                // various regions, like leading/trailing/center/bottom
                DynamicIslandExpandedRegion(.leading) {
                    if context.state.conversationArray.count > 0 {
                        ConversationDynamicIslandExpandedWidget(conversationArray: context.state.conversationArray)
                    }else{
                        BuzDynamicIslandExpendDefaultWidget()
                    }
//                    Text("Leading")
                }
                DynamicIslandExpandedRegion(.trailing) {
//                    Text("Trailing")
                }
                DynamicIslandExpandedRegion(.bottom) {
//                    Text("Bottom")
                    // more content
                }
            } compactLeading: {
//                Text("L")
            } compactTrailing: {
//                Text("T")
            } minimal: {
                Image("buz_widget_minimal_logo")
                    .resizable()
                    .frame(width: 20.0 , height: 20.0)
            }.contentMargins(.all , 12.0 , for: .expanded)
            .widgetURL(URL(string: "http://www.apple.com"))
        }
    }
}
