//
//  StringUtil.swift
//  buz
//
//  Created by liuyufeng on 2023/1/11.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation
import CommonCrypto
import UIKit

extension String {
    
    var md5: String {
        let str = self.cString(using: String.Encoding.utf8)
        let strLen = CUnsignedInt(self.lengthOfBytes(using: String.Encoding.utf8))
        let digestLen = Int(CC_MD5_DIGEST_LENGTH)

        let result = UnsafeMutablePointer<CUnsignedChar>.allocate(capacity: digestLen)
        CC_MD5(str!, strLen, result)
        let hash = NSMutableString()
        for i in 0 ..< digestLen {
            hash.appendFormat("%02x", result[i])
        }
        result.deallocate()

        return String(format: hash as String)
    }
    
    func getStringHeight(width: CGFloat, font: UIFont) -> CGFloat {
        
        let rect = NSString(string: self).boundingRect(with: CGSize(width: width, height: CGFloat(MAXFLOAT)), options: .usesLineFragmentOrigin, attributes: [NSAttributedString.Key.font: font], context: nil)
        return ceil(rect.height)
    }
    static func isNotEmptyStr(str:String?)->Bool{
        if str == nil {
            return false
        }
        return str?.count ?? 0 > 0
    }
}
