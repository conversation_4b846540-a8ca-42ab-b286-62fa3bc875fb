//
//  WidgetImageLoader.swift
//  buz
//
//  Created by liuyufeng on 2023/1/12.
//  Copyright © 2023 lizhi. All rights reserved.
//

import SwiftUI


class WidgetImageLoader
{
    static func syncLoadImage(urlStr : String?) -> Image? {
     
        guard let urlStr = urlStr,
              let localFilePathURL : URL = WidgetShareData.appGroupWebImageFilePath(urlStr: urlStr),
              let uiImage = UIImage(contentsOfFile: localFilePathURL.path)
        else {
            return nil
        }
        ///超大图片限制，防止图片过大导致系统无法加载
        if uiImage.size.width * uiImage.size.height > (300 * 300){
            return nil
        }
        return Image(uiImage: uiImage).resizable()
    }
    
    static func syncChatLoadImage() -> Image? {
     
        guard let localFilePathURL : URL = WidgetShareData.chatImageFilePath(),
              let uiImage = UIImage(contentsOfFile: localFilePathURL.path)
        else {
            return nil
        }
        ///超大图片限制，防止图片过大导致系统无法加载
        if uiImage.size.width * uiImage.size.height > (300 * 300){
            return nil
        }
        return Image(uiImage: uiImage).resizable()
    }
}
