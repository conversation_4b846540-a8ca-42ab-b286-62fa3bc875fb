//
//  BuzDebugLog.swift
//  buz
//
//  Created by liuyufeng on 2023/1/12.
//  Copyright © 2023 lizhi. All rights reserved.
//

import Foundation

public func BuzDebugLog(_ message: @autoclosure () -> String,
                        _ file: String = #file,
                        _ function: String = #function,
                        _ line: Int = #line) {
    #if DEBUG
        let gFormatter = DateFormatter()
        gFormatter.dateFormat = "HH:mm:ss"
        let timestamp = gFormatter.string(from: Date())
        let queue = Thread.isMainThread ? "UI" : "BG"
        let fileURL = NSURL(string: file)?.lastPathComponent ?? "Unknown file"

        NSLog("✅ \(timestamp) {\(queue)} \(fileURL):\(line): \(message())")
    #endif
}
