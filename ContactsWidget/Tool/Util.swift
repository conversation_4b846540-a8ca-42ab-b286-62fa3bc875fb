//
//  Util.swift
//  ContactsWidgetExtension
//
//  Created by yutao on 2022/7/28.
//  Copyright © 2022 lizhi. All rights reserved.
//

import Foundation
import SwiftUI

extension Color {
    
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }

        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue:  Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}

extension URL {
    
    static func resizeImage(url: URL, width: Int, height: Int) -> URL {
        
        var resizeURL = url
        let pathExtension = url.pathExtension
        
        if let lastComponent = url.absoluteString.components(separatedBy: "_").last,
           let originalSize = lastComponent.components(separatedBy: ".").first,
           originalSize.components(separatedBy: "x").count == 2 {
            
            let resizeURLString = url.absoluteString.replacingOccurrences(of: lastComponent,
                                                                          with: "\(width)x\(height).\(pathExtension)")
            if let value = URL(string: resizeURLString) {
                resizeURL = value
            }
        }else {
            
            let resizeURLString = url.absoluteString.replacingOccurrences(of: ".\(pathExtension)",
                                                                          with: "_\(width)x\(height).\(pathExtension)")
            if let value = URL(string: resizeURLString) {
                resizeURL = value
            }
        }

        return resizeURL
    }
}

extension UIImage {
    
    func drawImageBy(size: CGSize) -> UIImage? {
        
        UIGraphicsBeginImageContext(size);
        draw(in: CGRect.init(x: 0, y: 0, width: size.width, height: size.height))
        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return image
    }
}

struct SizeInfo {

    static var widgetSize: CGSize = CGSize(width: 329, height: 155)

    static func adaptive(width: CGFloat) -> CGFloat {
        return width * widgetSize.width / 329;
    }

    static func adaptive(height: CGFloat) -> CGFloat {
        return height * widgetSize.height / 155;
    }
}
